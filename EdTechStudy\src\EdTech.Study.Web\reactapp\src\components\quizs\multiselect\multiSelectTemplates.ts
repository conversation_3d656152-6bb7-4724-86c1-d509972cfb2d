import { Guid } from 'guid-typescript';
import { MultiSelectQuestion } from '../../../interfaces/quizs/multiSelectQuiz.interface';
import { QuestionType } from '../../../interfaces/quizs/questionBase';
import { ContentFormatType } from '../../../interfaces/exams/examEnums';

export const createMultiSelectQuestionTemplate = (
  title: string = 'Câu hỏi nhiều lựa chọn'
): MultiSelectQuestion => {
  const id = Guid.create().toString();
  return {
    difficulty: 1,
    syncQuestion: false,
    clientId: id,
    type: 'multiselect',
    questionType: QuestionType.MultipleChoice,
    title,
    content: 'Chọn tất cả các đáp án đúng',
    options: [
      {
        id: undefined,
        content: 'Đáp án A',
        isCorrect: true,
        clientId: Guid.create().toString(),
        contentFormat: ContentFormatType.Html,
        order: 0,
      },
      {
        id: undefined,
        content: 'Đáp án B',
        isCorrect: true,
        clientId: Guid.create().toString(),
        contentFormat: ContentFormatType.Html,
        order: 1,
      },
      {
        id: undefined,
        content: 'Đáp án C',
        isCorrect: false,
        clientId: Guid.create().toString(),
        contentFormat: ContentFormatType.Html,
        order: 2,
      },
      {
        id: undefined,
        content: 'Đáp án D',
        isCorrect: false,
        clientId: Guid.create().toString(),
        contentFormat: ContentFormatType.Html,
        order: 3,
      },
    ],
    explanation: 'Đáp án đúng là A và B vì...',
    points: 1,
  };
};
