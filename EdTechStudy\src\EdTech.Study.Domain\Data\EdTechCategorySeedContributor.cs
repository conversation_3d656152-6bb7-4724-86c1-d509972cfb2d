﻿using EdTech.Study.Grade;
using EdTech.Study.Subject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;

namespace EdTech.Study.Data
{
    public class EdTechCategorySeedContributor : ITransientDependency
    {
        private readonly IRepository<LessonGrade, Guid> _lessonGradeRepository;
        private readonly IRepository<Subject.Subject, Guid> _subjectRepository;

        public EdTechCategorySeedContributor(IRepository<LessonGrade, Guid> lessonGradeRepository, IRepository<Subject.Subject, Guid> subjectRepository)
        {
            _lessonGradeRepository = lessonGradeRepository;
            _subjectRepository = subjectRepository;
        }

        [UnitOfWork]
        public async Task SeedAsync(DataSeedContext context)
        {
            if (!await _lessonGradeRepository.AnyAsync())
            {
                await SeedGrades();
            }
            else
            {
                var lessonGradeUnknown = await _lessonGradeRepository.FirstOrDefaultAsync(x => x.Code == "Unknown");
                if (lessonGradeUnknown == null)
                {
                    await _lessonGradeRepository.InsertAsync(new LessonGrade
                    {
                        Name = "Chưa xác định",
                        Code = "Unknown"
                    });
                }
            }

            if (!await _subjectRepository.AnyAsync())
            {
                await SeedSubjects();
            }
            else
            {
                var subjectUnknown = await _subjectRepository.FirstOrDefaultAsync(x => x.Code == "Unknown");
                if (subjectUnknown == null)
                {
                    await _subjectRepository.InsertAsync(new Subject.Subject
                    {
                        Name = "Chưa xác định",
                        Code = "Unknown"
                    });
                }
            }
        }

        private async Task SeedGrades()
        {
            // seed grade 6 to 12

            await _lessonGradeRepository.InsertAsync(new LessonGrade
            {
                Name = "Chưa xác định",
                Code = "Unknown"
            });

            await _lessonGradeRepository.InsertAsync(new LessonGrade
            {
                Name = "Lớp 6",
                Code = "Grade6",
                Order = 6
            });
            await _lessonGradeRepository.InsertAsync(new LessonGrade
            {
                Name = "Lớp 7",
                Code = "Grade7",
                Order = 7
            });
            await _lessonGradeRepository.InsertAsync(new LessonGrade
            {
                Name = "Lớp 8",
                Code = "Grade8",
                Order = 8
            });
            await _lessonGradeRepository.InsertAsync(new LessonGrade
            {
                Name = "Lớp 9",
                Code = "Grade9",
                Order = 9
            });
            await _lessonGradeRepository.InsertAsync(new LessonGrade
            {
                Name = "Lớp 10",
                Code = "Grade10",
                Order = 10
            });
            await _lessonGradeRepository.InsertAsync(new LessonGrade
            {
                Name = "Lớp 11",
                Code = "Grade11",
                Order = 11
            });
            await _lessonGradeRepository.InsertAsync(new LessonGrade
            {
                Name = "Lớp 12",
                Code = "Grade12",
                Order = 12
            });
        }

        private async Task SeedSubjects()
        {
            await _subjectRepository.InsertAsync(new Subject.Subject
            {
                Name = "Chưa xác định",
                Code = "Unknown"
            });

            await _subjectRepository.InsertAsync(new Subject.Subject
            {
                Name = "Toán",
                Code = "Math"
            });
            await _subjectRepository.InsertAsync(new Subject.Subject
            {
                Name = "Vật lý",
                Code = "Physics"
            });
            await _subjectRepository.InsertAsync(new Subject.Subject
            {
                Name = "Hóa học",
                Code = "Chemistry"
            });
            await _subjectRepository.InsertAsync(new Subject.Subject
            {
                Name = "Sinh học",
                Code = "Biology"
            });
            await _subjectRepository.InsertAsync(new Subject.Subject
            {
                Name = "Địa lý",
                Code = "Geography"
            });
            await _subjectRepository.InsertAsync(new Subject.Subject
            {
                Name = "Lịch sử",
                Code = "History"
            });
            await _subjectRepository.InsertAsync(new Subject.Subject
            {
                Name = "Tiếng anh",
                Code = "English"
            });
        }
    }
}