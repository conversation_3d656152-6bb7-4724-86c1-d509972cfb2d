﻿using EdTech.Study.Localization;
using Microsoft.CodeAnalysis;
using System;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Authorization.Permissions;

namespace EdTech.Study.Permissions
{
    public class ExamPermissionDefinitionProvider : PermissionDefinitionProvider
    {
        public override void Define(IPermissionDefinitionContext context)
        {
            var menuGroup = context.AddGroup(ExamPermissions.GroupName, L("Permission:ExamManagement"));
            var menusPermission = menuGroup.AddPermission(ExamPermissions.Exams.Default, L("Permission:Exams"));
            //menusPermission.AddChild(ExamPermissions.Exams, L("Permission:Menus.Create"));
        }
        private static Volo.Abp.Localization.LocalizableString L(string name)
        {
            return Volo.Abp.Localization.LocalizableString.Create<StudyResource>(name);
        }
    }
}
