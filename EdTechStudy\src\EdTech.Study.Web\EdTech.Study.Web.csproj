﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<Import Project="..\..\common.props" />

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<Nullable>enable</Nullable>
		<AssetTargetFallback>$(AssetTargetFallback);portable-net45+win8+wp8+wpa81;</AssetTargetFallback>
		<IsPackable>true</IsPackable>
		<OutputType>Library</OutputType>
		<RootNamespace>EdTech.Study.Web</RootNamespace>
		<GenerateEmbeddedFilesManifest>true</GenerateEmbeddedFilesManifest>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNet.SignalR.Client" Version="2.4.3" />
		<PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="8.0.14" />
		<PackageReference Include="Volo.Abp.AspNetCore.Components" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.AspNetCore.Components.Server" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.AutoMapper" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.BlazoriseUI" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.BlobStoring.FileSystem" Version="8.1.4" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\EdTech.Study.Application.Contracts\EdTech.Study.Application.Contracts.csproj" />
		<ProjectReference Include="..\EdTech.Study.Application\EdTech.Study.Application.csproj" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.Extensions.FileProviders.Embedded" Version="8.0.0" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="Pages\**\*.css" />
		<EmbeddedResource Include="Pages\**\*.js" />
		<EmbeddedResource Include="wwwroot\**\*.*" />
		<Content Remove="Pages\**\*.css" />
		<Content Remove="Pages\**\*.js" />
	</ItemGroup>

	<ItemGroup>
		<Content Remove="wwwroot\css\home\styles.css" />
		<Content Remove="wwwroot\images\classten\geography\mapreading\agriculture-gps.png" />
		<Content Remove="wwwroot\images\classten\geography\mapreading\emergency-gps.png" />
		<Content Remove="wwwroot\images\classten\geography\mapreading\tourism-gps.png" />
		<Content Remove="wwwroot\js\common\questions-init-module.js" />
		<Content Remove="wwwroot\js\config\lesson-config.js" />
		<Content Remove="wwwroot\js\home\main.js" />
		<Content Remove="wwwroot\js\menu\index.js" />
		<Content Remove="wwwroot\js\store\lesson-config-db-module.js" />
		<Content Remove="wwwroot\template\index.html" />
	</ItemGroup>

	<ItemGroup>
	  <Content Update="StaticJsonConfig\default.json">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </Content>
	</ItemGroup>
</Project>
