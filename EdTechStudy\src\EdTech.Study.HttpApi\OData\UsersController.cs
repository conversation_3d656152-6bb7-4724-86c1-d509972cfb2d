﻿using EdTech.Study.Users;
using Microsoft.AspNetCore.OData.Formatter;
using Microsoft.AspNetCore.OData.Query;
using Microsoft.AspNetCore.OData.Results;
using Microsoft.AspNetCore.OData.Routing.Controllers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;

namespace EdTech.Study.OData
{
    public class UsersController : ODataController
    {
        protected readonly IRepository<IdentityUser> _repository;

        public UsersController(IRepository<IdentityUser> repository)
        {
            _repository = repository;
        }

        [EnableQuery]
        public async Task<IQueryable<IdentityUserDto>> Get()
        {
            var query = (await _repository.GetQueryableAsync()).Select(x => new IdentityUserDto()
            {
                Id = x.Id,
                TenantId = x.TenantId,
                UserName = x.UserName,
                Name = x.Name,
                Surname = x.Surname,
                Email = x.Email,
                PhoneNumber = x.PhoneNumber,
                IsActive = x.IsActive
            });

            return query;
        }

        [EnableQuery]
        public async Task<SingleResult<IdentityUserDto>> Get([FromODataUri] Guid key)
        {
            var query = (await _repository.GetQueryableAsync()).Where(c => c.Id.Equals(key)).Take(1).Select(x => new IdentityUserDto()
            {
                Id = x.Id,
                TenantId = x.TenantId,
                UserName = x.UserName,
                Name = x.Name,
                Surname = x.Surname,
                Email = x.Email,
                PhoneNumber = x.PhoneNumber,
                IsActive = x.IsActive
            });
            return SingleResult.Create(query);
        }
    }
}
