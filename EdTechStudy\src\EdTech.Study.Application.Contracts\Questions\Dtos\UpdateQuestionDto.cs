using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using EdTech.Study.Enum;
using EdTech.Study.Exams;

namespace EdTech.Study.Questions.Dtos
{
    public class UpdateQuestionDto
    {
        [Required]
        public string Title { get; set; }

        [Required]
        public string Content { get; set; }

        public ContentFormatType ContentFormat { get; set; }

        public QuestionType QuestionType { get; set; }

        public int Difficulty { get; set; }

        public ExamStatus Status { get; set; }

        public string? Comment { get; set; }

        public Guid? SubjectId { get; set; }

        public Guid? GradeId { get; set; }

        public bool ShuffleOptions { get; set; }

        public string? Explanation { get; set; }

        public ExamSourceType SourceType { get; set; }

        public string? Topics { get; set; }

        public string? Tags { get; set; }

        public List<UpdateQuestionOptionDto>? Options { get; set; }
    }
}