import { Subject } from '../interfaces/lessons/subject';
import { axiosClient } from '../services/axiosClient';
import { camelCaseKeys } from '../utils/parsers';

export interface SubjectParams {
  page?: number;
  pageSize?: number;
  sortField?: string;
  sortOrder?: string;
  searchText?: string;
  [key: string]: any;
}

class SubjectApi {
  private getCommonODataParams(): Record<string, string> {
    return {
      $count: 'true',
    };
  }

  public async getSubjects(params: SubjectParams = {}) {
    // Use standard OData parameters for pagination
    const skip =
      params.page && params.pageSize ? (params.page - 1) * params.pageSize : 0;
    const top = params.pageSize || 1000;

    // Build sorting
    let orderby = '';
    if (params.sortField) {
      const sortDir = params.sortOrder || 'asc';
      orderby = `${params.sortField} ${sortDir}`;
    }

    // Build filtering
    const filters = [];

    // Add search text filter if provided
    if (params.searchText) {
      filters.push(
        `contains(Name,'${params.searchText}') or contains(Code,'${params.searchText}')`
      );
    }

    // Combine all filters with AND operator
    const filter = filters.length > 0 ? filters.join(' and ') : '';

    // Build the request parameters
    const requestParams: Record<string, any> = {
      ...this.getCommonODataParams(),
      $skip: skip,
      $top: top,
    };

    if (orderby) {
      requestParams['$orderby'] = orderby;
    }

    if (filter) {
      requestParams['$filter'] = filter;
    }

    try {
      const response = await axiosClient({
        method: 'get',
        url: '/odata/Subject',
        params: requestParams,
      });

      // Extract total count and items from OData response
      const total = response.data['@odata.count'] || response.data.length || 0;
      const items = camelCaseKeys(
        response.data.value || response.data || []
      ) as Subject[];

      return {
        success: true,
        data: items,
        total: total,
      };
    } catch (error) {
      return {
        success: false,
        data: [],
        total: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  public async getSubjectById(id: string) {
    try {
      const response = await axiosClient({
        method: 'get',
        url: `/odata/Subject(${id})`,
        params: this.getCommonODataParams(),
      });

      return {
        success: true,
        data: camelCaseKeys(response.data),
      };
    } catch (error) {
      console.error(`Error fetching subject with id ${id}:`, error);
      return {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
}

export default new SubjectApi();
