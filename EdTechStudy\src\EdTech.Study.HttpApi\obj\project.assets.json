{"version": 3, "targets": {"net8.0": {"Asp.Versioning.Abstractions/8.1.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Asp.Versioning.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Asp.Versioning.Abstractions.dll": {"related": ".xml"}}}, "Asp.Versioning.Http/8.1.0": {"type": "package", "dependencies": {"Asp.Versioning.Abstractions": "8.1.0"}, "compile": {"lib/net8.0/Asp.Versioning.Http.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Asp.Versioning.Http.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Asp.Versioning.Mvc/8.1.0": {"type": "package", "dependencies": {"Asp.Versioning.Http": "8.1.0"}, "compile": {"lib/net8.0/Asp.Versioning.Mvc.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Asp.Versioning.Mvc.dll": {"related": ".xml"}}}, "Asp.Versioning.Mvc.ApiExplorer/8.1.0": {"type": "package", "dependencies": {"Asp.Versioning.Mvc": "8.1.0"}, "compile": {"lib/net8.0/Asp.Versioning.Mvc.ApiExplorer.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Asp.Versioning.Mvc.ApiExplorer.dll": {"related": ".xml"}}}, "AsyncKeyedLock/6.3.4": {"type": "package", "compile": {"lib/net8.0/AsyncKeyedLock.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/AsyncKeyedLock.dll": {"related": ".xml"}}}, "AutoMapper/12.0.1": {"type": "package", "dependencies": {"Microsoft.CSharp": "4.7.0"}, "compile": {"lib/netstandard2.1/AutoMapper.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/AutoMapper.dll": {"related": ".xml"}}}, "ConfigureAwait.Fody/3.3.1": {"type": "package", "dependencies": {"Fody": "6.0.2"}, "compile": {"lib/netstandard2.0/ConfigureAwait.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/ConfigureAwait.dll": {"related": ".xml"}}, "build": {"build/ConfigureAwait.Fody.props": {}}}, "Fody/6.5.3": {"type": "package", "build": {"build/Fody.targets": {}}}, "IdentityModel/6.2.0": {"type": "package", "compile": {"lib/net6.0/IdentityModel.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/IdentityModel.dll": {"related": ".pdb;.xml"}}}, "JetBrains.Annotations/2023.3.0": {"type": "package", "compile": {"lib/netstandard2.0/JetBrains.Annotations.dll": {"related": ".deps.json;.xml"}}, "runtime": {"lib/netstandard2.0/JetBrains.Annotations.dll": {"related": ".deps.json;.xml"}}}, "Microsoft.AspNetCore.Authentication.OpenIdConnect/8.0.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.0.3"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Authentication.OpenIdConnect.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authentication.OpenIdConnect.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Microsoft.AspNetCore.Authorization/8.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Metadata": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Cryptography.Internal/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/8.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Metadata/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.AspNetCore.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Metadata.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Mvc.Razor.Extensions/6.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Razor.Language": "6.0.0", "Microsoft.CodeAnalysis.Razor": "6.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.Extensions.dll": {}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.Extensions.dll": {}}}, "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation/8.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Mvc.Razor.Extensions": "6.0.0", "Microsoft.CodeAnalysis.Razor": "6.0.0", "Microsoft.Extensions.DependencyModel": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"], "build": {"buildTransitive/net8.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.targets": {}}}, "Microsoft.AspNetCore.OData/8.2.5": {"type": "package", "dependencies": {"Microsoft.OData.Core": "[7.20.0, 8.0.0)", "Microsoft.OData.Edm": "[7.20.0, 8.0.0)", "Microsoft.OData.ModelBuilder": "[1.0.9, 2.0.0)", "Microsoft.Spatial": "[7.20.0, 8.0.0)"}, "compile": {"lib/net6.0/Microsoft.AspNetCore.OData.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.OData.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Razor.Language/6.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {}}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "compile": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.2": {"type": "package", "build": {"build/_._": {}}}, "Microsoft.CodeAnalysis.Common/4.0.0": {"type": "package", "dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.2", "System.Collections.Immutable": "5.0.0", "System.Memory": "4.5.4", "System.Reflection.Metadata": "5.0.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Text.Encoding.CodePages": "4.5.1", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.0.0": {"type": "package", "dependencies": {"Microsoft.CodeAnalysis.Common": "[4.0.0]"}, "compile": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Razor/6.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Razor.Language": "6.0.0", "Microsoft.CodeAnalysis.CSharp": "4.0.0", "Microsoft.CodeAnalysis.Common": "4.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll": {}}, "runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll": {}}}, "Microsoft.CSharp/4.7.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "build": {"buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets": {}}}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "System.Text.Json": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Configuration.UserSecrets.props": {}, "buildTransitive/net6.0/Microsoft.Extensions.Configuration.UserSecrets.targets": {}}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyModel/8.0.0": {"type": "package", "dependencies": {"System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Composite/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Composite.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Composite.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Embedded/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Embedded.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Embedded.dll": {"related": ".xml"}}, "build": {"build/netstandard2.0/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Identity.Core/8.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Cryptography.KeyDerivation": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Identity.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Identity.Core.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Localization/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Localization.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Localization.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Localization.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Localization.Abstractions/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.Localization.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Localization.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Options/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.IdentityModel.Abstractions/7.0.3": {"type": "package", "compile": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.JsonWebTokens/7.0.3": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "7.0.3"}, "compile": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Logging/7.0.3": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "7.0.3"}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols/7.0.3": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Logging": "7.0.3", "Microsoft.IdentityModel.Tokens": "7.0.3"}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.0.3": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Protocols": "7.0.3", "System.IdentityModel.Tokens.Jwt": "7.0.3"}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Tokens/7.0.3": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Logging": "7.0.3"}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}}, "Microsoft.NETCore.Platforms/2.1.2": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.OData.Core/7.20.0": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.OData.Edm": "[7.20.0]", "Microsoft.Spatial": "[7.20.0]"}, "compile": {"lib/netcoreapp3.1/Microsoft.OData.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.OData.Core.dll": {"related": ".xml"}}}, "Microsoft.OData.Edm/7.20.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.6.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.6.0"}, "compile": {"lib/netstandard2.0/Microsoft.OData.Edm.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.OData.Edm.dll": {"related": ".xml"}}}, "Microsoft.OData.ModelBuilder/1.0.9": {"type": "package", "dependencies": {"Microsoft.OData.Edm": "[7.9.0, 8.0.0)", "Microsoft.Spatial": "[7.9.0, 8.0.0)", "System.ComponentModel.Annotations": "4.6.0"}, "compile": {"lib/net6.0/Microsoft.OData.ModelBuilder.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.OData.ModelBuilder.dll": {"related": ".xml"}}}, "Microsoft.Spatial/7.20.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Spatial.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Spatial.dll": {"related": ".xml"}}}, "Nito.AsyncEx.Context/5.1.2": {"type": "package", "dependencies": {"Nito.AsyncEx.Tasks": "5.1.2"}, "compile": {"lib/netstandard2.0/Nito.AsyncEx.Context.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Context.dll": {"related": ".xml"}}}, "Nito.AsyncEx.Tasks/5.1.2": {"type": "package", "dependencies": {"Nito.Disposables": "2.2.1"}, "compile": {"lib/netstandard2.0/Nito.AsyncEx.Tasks.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Tasks.dll": {"related": ".xml"}}}, "Nito.Disposables/2.2.1": {"type": "package", "dependencies": {"System.Collections.Immutable": "1.7.1"}, "compile": {"lib/netstandard2.1/Nito.Disposables.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Nito.Disposables.dll": {"related": ".xml"}}}, "NUglify/1.21.0": {"type": "package", "compile": {"lib/net5.0/NUglify.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/NUglify.dll": {"related": ".xml"}}}, "System.Collections/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Collections.dll": {"related": ".xml"}}}, "System.Collections.Immutable/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.ComponentModel.Annotations/4.6.0": {"type": "package", "compile": {"ref/netstandard2.1/System.ComponentModel.Annotations.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/System.ComponentModel.Annotations.dll": {"related": ".xml"}}}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "System.Diagnostics.DiagnosticSource/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Globalization/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "System.IdentityModel.Tokens.Jwt/7.0.3": {"type": "package", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "7.0.3", "Microsoft.IdentityModel.Tokens": "7.0.3"}, "compile": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}}, "System.IO/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.5/System.IO.dll": {"related": ".xml"}}}, "System.Linq/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}, "compile": {"ref/netstandard1.6/System.Linq.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/System.Linq.dll": {}}}, "System.Linq.Dynamic.Core/1.3.5": {"type": "package", "compile": {"lib/net7.0/System.Linq.Dynamic.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/System.Linq.Dynamic.Core.dll": {"related": ".pdb;.xml"}}}, "System.Linq.Expressions/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.6/System.Linq.Expressions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/System.Linq.Expressions.dll": {}}}, "System.Linq.Queryable/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Linq.Queryable.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Linq.Queryable.dll": {}}}, "System.Memory/4.5.4": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.ObjectModel/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.ObjectModel.dll": {}}}, "System.Reflection/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Reflection.dll": {"related": ".xml"}}}, "System.Reflection.Emit/4.3.0": {"type": "package", "dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.1/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.dll": {}}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.ILGeneration.dll": {}}}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.Lightweight.dll": {}}}, "System.Reflection.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}}, "System.Reflection.Metadata/5.0.0": {"type": "package", "compile": {"lib/netstandard2.0/System.Reflection.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Reflection.Metadata.dll": {"related": ".xml"}}}, "System.Reflection.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Primitives.dll": {"related": ".xml"}}}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.5/System.Reflection.TypeExtensions.dll": {}}}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}}, "System.Runtime/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"ref/netstandard1.5/System.Runtime.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"type": "package", "compile": {"ref/netstandard2.1/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Runtime.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/_._": {"related": ".xml"}}}, "System.Runtime.Loader/4.3.0": {"type": "package", "dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Runtime.Loader.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.5/System.Runtime.Loader.dll": {}}}, "System.Text.Encoding/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.dll": {"related": ".xml"}}}, "System.Text.Encoding.CodePages/4.5.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "System.Runtime.CompilerServices.Unsafe": "4.5.2"}, "compile": {"lib/netstandard2.0/System.Text.Encoding.CodePages.dll": {}}, "runtime": {"lib/netstandard2.0/System.Text.Encoding.CodePages.dll": {}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Text.Encoding.CodePages.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll": {"assetType": "runtime", "rid": "browser"}}}, "System.Text.Json/8.0.0": {"type": "package", "dependencies": {"System.Text.Encodings.Web": "8.0.0"}, "compile": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/System.Text.Json.targets": {}}}, "System.Threading/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Threading.dll": {}}}, "System.Threading.Tasks/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Threading.Tasks.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "TimeZoneConverter/6.1.0": {"type": "package", "compile": {"lib/net6.0/TimeZoneConverter.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/TimeZoneConverter.dll": {"related": ".xml"}}}, "Volo.Abp.ApiVersioning.Abstractions/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.ApiVersioning.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.ApiVersioning.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.AspNetCore/8.1.4": {"type": "package", "dependencies": {"IdentityModel": "6.2.0", "Microsoft.AspNetCore.Authentication.OpenIdConnect": "8.0.0", "Volo.Abp.Auditing": "8.1.4", "Volo.Abp.Authorization": "8.1.4", "Volo.Abp.ExceptionHandling": "8.1.4", "Volo.Abp.Http": "8.1.4", "Volo.Abp.Security": "8.1.4", "Volo.Abp.Uow": "8.1.4", "Volo.Abp.Validation": "8.1.4", "Volo.Abp.VirtualFileSystem": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.AspNetCore.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.AspNetCore.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"], "contentFiles": {"contentFiles/any/any/_._": {"buildAction": "None", "codeLanguage": "any", "copyToOutput": false}}}, "Volo.Abp.AspNetCore.Mvc/8.1.4": {"type": "package", "dependencies": {"Asp.Versioning.Mvc": "8.1.0", "Asp.Versioning.Mvc.ApiExplorer": "8.1.0", "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation": "8.0.0", "Volo.Abp.ApiVersioning.Abstractions": "8.1.4", "Volo.Abp.AspNetCore": "8.1.4", "Volo.Abp.AspNetCore.Mvc.Contracts": "8.1.4", "Volo.Abp.Ddd.Application": "8.1.4", "Volo.Abp.GlobalFeatures": "8.1.4", "Volo.Abp.Localization": "8.1.4", "Volo.Abp.UI.Navigation": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.dll": {"related": ".pdb;.xml"}}, "contentFiles": {"contentFiles/any/net8.0/Volo.Abp.AspNetCore.Mvc.abppkg.analyze.json": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": false}}}, "Volo.Abp.AspNetCore.Mvc.Contracts/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Ddd.Application.Contracts": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.Contracts.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.Contracts.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.AspNetCore.Mvc.UI/8.1.4": {"type": "package", "dependencies": {"NUglify": "1.21.0", "Volo.Abp.AspNetCore.Mvc": "8.1.4", "Volo.Abp.UI.Navigation": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.AspNetCore.Mvc.UI.Bootstrap/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.AspNetCore.Mvc.UI": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.AspNetCore.Mvc.UI.Bundling/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.AspNetCore.Mvc.UI.Bootstrap": "8.1.4", "Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions": "8.1.4", "Volo.Abp.Minify": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Bundling.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Bundling.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.AspNetCore.Mvc.UI.Packages/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions": "8.1.4", "Volo.Abp.Localization": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Packages.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Packages.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.AspNetCore.Mvc.UI.Bootstrap": "8.1.4", "Volo.Abp.AspNetCore.Mvc.UI.Packages": "8.1.4", "Volo.Abp.AspNetCore.Mvc.UI.Widgets": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.dll": {"related": ".pdb;.xml"}}, "contentFiles": {"contentFiles/any/any/_._": {"buildAction": "None", "codeLanguage": "any", "copyToOutput": false}}}, "Volo.Abp.AspNetCore.Mvc.UI.Widgets/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.AspNetCore.Mvc.UI.Bundling": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Widgets.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Widgets.dll": {"related": ".pdb;.xml"}}, "contentFiles": {"contentFiles/any/any/_._": {"buildAction": "None", "codeLanguage": "any", "copyToOutput": false}}}, "Volo.Abp.Auditing/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Auditing.Contracts": "8.1.4", "Volo.Abp.Data": "8.1.4", "Volo.Abp.Json": "8.1.4", "Volo.Abp.MultiTenancy": "8.1.4", "Volo.Abp.Security": "8.1.4", "Volo.Abp.Threading": "8.1.4", "Volo.Abp.Timing": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.Auditing.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Auditing.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Auditing.Contracts/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.Auditing.Contracts.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Auditing.Contracts.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Authorization/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Authorization.Abstractions": "8.1.4", "Volo.Abp.Localization": "8.1.4", "Volo.Abp.MultiTenancy": "8.1.4", "Volo.Abp.Security": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.Authorization.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Authorization.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Authorization.Abstractions/8.1.4": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authorization": "8.0.0", "Volo.Abp.MultiTenancy.Abstractions": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.Authorization.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Authorization.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.AutoMapper/8.1.4": {"type": "package", "dependencies": {"AutoMapper": "12.0.1", "Volo.Abp.Auditing": "8.1.4", "Volo.Abp.ObjectExtending": "8.1.4", "Volo.Abp.ObjectMapping": "8.1.4"}, "compile": {"lib/netstandard2.1/Volo.Abp.AutoMapper.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.1/Volo.Abp.AutoMapper.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.BackgroundWorkers/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Threading": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.BackgroundWorkers.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.BackgroundWorkers.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.BlobStoring/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.1.4", "Volo.Abp.MultiTenancy": "8.1.4", "Volo.Abp.Threading": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.BlobStoring.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.BlobStoring.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Caching/8.1.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Memory": "8.0.0", "Volo.Abp.Json": "8.1.4", "Volo.Abp.MultiTenancy": "8.1.4", "Volo.Abp.Serialization": "8.1.4", "Volo.Abp.Threading": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.Caching.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Caching.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Core/8.1.4": {"type": "package", "dependencies": {"JetBrains.Annotations": "2023.3.0", "Microsoft.Extensions.Configuration.CommandLine": "8.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "8.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Localization": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0", "Nito.AsyncEx.Context": "5.1.2", "System.Collections.Immutable": "8.0.0", "System.Linq.Dynamic.Core": "1.3.5", "System.Linq.Queryable": "4.3.0", "System.Runtime.Loader": "4.3.0", "System.Text.Encodings.Web": "8.0.0"}, "compile": {"lib/net8.0/Volo.Abp.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Core.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Data/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.EventBus.Abstractions": "8.1.4", "Volo.Abp.ObjectExtending": "8.1.4", "Volo.Abp.Uow": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.Data.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Data.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Ddd.Application/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Authorization": "8.1.4", "Volo.Abp.Ddd.Application.Contracts": "8.1.4", "Volo.Abp.Ddd.Domain": "8.1.4", "Volo.Abp.Features": "8.1.4", "Volo.Abp.GlobalFeatures": "8.1.4", "Volo.Abp.Http.Abstractions": "8.1.4", "Volo.Abp.Localization": "8.1.4", "Volo.Abp.ObjectMapping": "8.1.4", "Volo.Abp.Security": "8.1.4", "Volo.Abp.Settings": "8.1.4", "Volo.Abp.Validation": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.Ddd.Application.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Ddd.Application.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Ddd.Application.Contracts/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Auditing.Contracts": "8.1.4", "Volo.Abp.Data": "8.1.4", "Volo.Abp.Localization": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.Ddd.Application.Contracts.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Ddd.Application.Contracts.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Ddd.Domain/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Auditing": "8.1.4", "Volo.Abp.Caching": "8.1.4", "Volo.Abp.Data": "8.1.4", "Volo.Abp.Ddd.Domain.Shared": "8.1.4", "Volo.Abp.EventBus": "8.1.4", "Volo.Abp.ExceptionHandling": "8.1.4", "Volo.Abp.Guids": "8.1.4", "Volo.Abp.ObjectMapping": "8.1.4", "Volo.Abp.Specifications": "8.1.4", "Volo.Abp.Timing": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.Ddd.Domain.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Ddd.Domain.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Ddd.Domain.Shared/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.EventBus.Abstractions": "8.1.4", "Volo.Abp.MultiTenancy.Abstractions": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.Ddd.Domain.Shared.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Ddd.Domain.Shared.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.DistributedLocking.Abstractions/8.1.4": {"type": "package", "dependencies": {"AsyncKeyedLock": "6.3.4", "Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Volo.Abp.Core": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.DistributedLocking.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.DistributedLocking.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.EventBus/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.BackgroundWorkers": "8.1.4", "Volo.Abp.DistributedLocking.Abstractions": "8.1.4", "Volo.Abp.EventBus.Abstractions": "8.1.4", "Volo.Abp.Guids": "8.1.4", "Volo.Abp.Json": "8.1.4", "Volo.Abp.MultiTenancy": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.EventBus.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.EventBus.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.EventBus.Abstractions/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.ObjectExtending": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.EventBus.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.EventBus.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.ExceptionHandling/8.1.4": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "8.0.0", "Volo.Abp.Data": "8.1.4", "Volo.Abp.Localization": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.ExceptionHandling.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.ExceptionHandling.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Features/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Authorization.Abstractions": "8.1.4", "Volo.Abp.Localization": "8.1.4", "Volo.Abp.MultiTenancy": "8.1.4", "Volo.Abp.Validation": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.Features.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Features.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.GlobalFeatures/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Authorization.Abstractions": "8.1.4", "Volo.Abp.Localization": "8.1.4", "Volo.Abp.VirtualFileSystem": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.GlobalFeatures.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.GlobalFeatures.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Guids/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.Guids.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Guids.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Http/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Http.Abstractions": "8.1.4", "Volo.Abp.Json": "8.1.4", "Volo.Abp.Minify": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.Http.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Http.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Http.Abstractions/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.Http.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Http.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Identity.Domain/8.1.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Identity.Core": "8.0.0", "Volo.Abp.AutoMapper": "8.1.4", "Volo.Abp.Ddd.Domain": "8.1.4", "Volo.Abp.Identity.Domain.Shared": "8.1.4", "Volo.Abp.Users.Domain": "8.1.4"}, "compile": {"lib/netstandard2.1/Volo.Abp.Identity.Domain.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.1/Volo.Abp.Identity.Domain.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Identity.Domain.Shared/8.1.4": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "8.0.0", "Volo.Abp.Auditing.Contracts": "8.1.4", "Volo.Abp.Features": "8.1.4", "Volo.Abp.Users.Domain.Shared": "8.1.4", "Volo.Abp.Validation": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.Identity.Domain.Shared.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Identity.Domain.Shared.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Json/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Json.SystemTextJson": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.Json.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Json.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Json.Abstractions/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.Json.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Json.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Json.SystemTextJson/8.1.4": {"type": "package", "dependencies": {"System.Text.Json": "8.0.0", "Volo.Abp.Data": "8.1.4", "Volo.Abp.Json.Abstractions": "8.1.4", "Volo.Abp.Timing": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.Json.SystemTextJson.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Json.SystemTextJson.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Localization/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Localization.Abstractions": "8.1.4", "Volo.Abp.Settings": "8.1.4", "Volo.Abp.Threading": "8.1.4", "Volo.Abp.VirtualFileSystem": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.Localization.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Localization.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Localization.Abstractions/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.Localization.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Localization.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Minify/8.1.4": {"type": "package", "dependencies": {"NUglify": "1.21.0", "Volo.Abp.Core": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.Minify.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Minify.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.MultiTenancy/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Data": "8.1.4", "Volo.Abp.EventBus.Abstractions": "8.1.4", "Volo.Abp.MultiTenancy.Abstractions": "8.1.4", "Volo.Abp.Security": "8.1.4", "Volo.Abp.Settings": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.MultiTenancy.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.MultiTenancy.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.MultiTenancy.Abstractions/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Localization": "8.1.4", "Volo.Abp.VirtualFileSystem": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.MultiTenancy.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.MultiTenancy.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.ObjectExtending/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Localization.Abstractions": "8.1.4", "Volo.Abp.Validation.Abstractions": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.ObjectExtending.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.ObjectExtending.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.ObjectMapping/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.ObjectMapping.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.ObjectMapping.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Security/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.Security.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Security.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Serialization/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.Serialization.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Serialization.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Settings/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Data": "8.1.4", "Volo.Abp.Localization.Abstractions": "8.1.4", "Volo.Abp.Security": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.Settings.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Settings.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Specifications/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.Specifications.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Specifications.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Threading/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.Threading.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Threading.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Timing/8.1.4": {"type": "package", "dependencies": {"TimeZoneConverter": "6.1.0", "Volo.Abp.Localization": "8.1.4", "Volo.Abp.Settings": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.Timing.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Timing.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.UI/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.ExceptionHandling": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.UI.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.UI.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.UI.Navigation/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Authorization": "8.1.4", "Volo.Abp.MultiTenancy": "8.1.4", "Volo.Abp.UI": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.UI.Navigation.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.UI.Navigation.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Uow/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.Uow.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Uow.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Users.Abstractions/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.EventBus": "8.1.4", "Volo.Abp.MultiTenancy": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.Users.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Users.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Users.Domain/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Ddd.Domain": "8.1.4", "Volo.Abp.Users.Abstractions": "8.1.4", "Volo.Abp.Users.Domain.Shared": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.Users.Domain.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Users.Domain.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Users.Domain.Shared/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.Users.Domain.Shared.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Users.Domain.Shared.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Validation/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Localization": "8.1.4", "Volo.Abp.Validation.Abstractions": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.Validation.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Validation.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Validation.Abstractions/8.1.4": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.Validation.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Validation.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.VirtualFileSystem/8.1.4": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Composite": "8.0.0", "Microsoft.Extensions.FileProviders.Embedded": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Volo.Abp.Core": "8.1.4"}, "compile": {"lib/net8.0/Volo.Abp.VirtualFileSystem.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.VirtualFileSystem.dll": {"related": ".pdb;.xml"}}}, "EdTech.Study.Application.Contracts/0.1.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"EdTech.Study.Domain": "0.1.0", "EdTech.Study.Domain.Shared": "0.1.0", "Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared": "8.1.4", "Volo.Abp.Authorization": "8.1.4", "Volo.Abp.Ddd.Application.Contracts": "8.1.4"}, "compile": {"bin/placeholder/EdTech.Study.Application.Contracts.dll": {}}, "runtime": {"bin/placeholder/EdTech.Study.Application.Contracts.dll": {}}}, "EdTech.Study.Domain/0.1.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"EdTech.Study.Domain.Shared": "0.1.0", "Volo.Abp.BlobStoring": "8.1.4", "Volo.Abp.Ddd.Domain": "8.1.4", "Volo.Abp.Identity.Domain": "8.1.4", "Volo.Abp.VirtualFileSystem": "8.1.4"}, "compile": {"bin/placeholder/EdTech.Study.Domain.dll": {}}, "runtime": {"bin/placeholder/EdTech.Study.Domain.dll": {}}}, "EdTech.Study.Domain.Shared/0.1.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "8.0.0", "Volo.Abp.Ddd.Domain.Shared": "8.1.4", "Volo.Abp.Validation": "8.1.4"}, "compile": {"bin/placeholder/EdTech.Study.Domain.Shared.dll": {}}, "runtime": {"bin/placeholder/EdTech.Study.Domain.Shared.dll": {}}}}}, "libraries": {"Asp.Versioning.Abstractions/8.1.0": {"sha512": "mpeNZyMdvrHztJwR1sXIUQ+3iioEU97YMBnFA9WLbsPOYhGwDJnqJMmEd8ny7kcmS9OjTHoEuX/bSXXY3brIFA==", "type": "package", "path": "asp.versioning.abstractions/8.1.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "asp.versioning.abstractions.8.1.0.nupkg.sha512", "asp.versioning.abstractions.nuspec", "icon.png", "lib/net8.0/Asp.Versioning.Abstractions.dll", "lib/net8.0/Asp.Versioning.Abstractions.xml", "lib/netstandard1.0/Asp.Versioning.Abstractions.dll", "lib/netstandard1.0/Asp.Versioning.Abstractions.xml", "lib/netstandard2.0/Asp.Versioning.Abstractions.dll", "lib/netstandard2.0/Asp.Versioning.Abstractions.xml"]}, "Asp.Versioning.Http/8.1.0": {"sha512": "Xu4xF62Cu9JqYi/CTa2TiK5kyHoa4EluPynj/bPFWDmlTIPzuJQbBI5RgFYVRFHjFVvWMoA77acRaFu7i7Wzqg==", "type": "package", "path": "asp.versioning.http/8.1.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "asp.versioning.http.8.1.0.nupkg.sha512", "asp.versioning.http.nuspec", "icon.png", "lib/net8.0/Asp.Versioning.Http.dll", "lib/net8.0/Asp.Versioning.Http.xml"]}, "Asp.Versioning.Mvc/8.1.0": {"sha512": "BMAJM2sGsTUw5FQ9upKQt6GFoldWksePgGpYjl56WSRvIuE3UxKZh0gAL+wDTIfLshUZm97VCVxlOGyrcjWz9Q==", "type": "package", "path": "asp.versioning.mvc/8.1.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "asp.versioning.mvc.8.1.0.nupkg.sha512", "asp.versioning.mvc.nuspec", "icon.png", "lib/net8.0/Asp.Versioning.Mvc.dll", "lib/net8.0/Asp.Versioning.Mvc.xml"]}, "Asp.Versioning.Mvc.ApiExplorer/8.1.0": {"sha512": "a90gW/4TF/14Bjiwg9LqNtdKGC4G3gu02+uynq3bCISfQm48km5chny4Yg5J4hixQPJUwwJJ9Do1G+jM8L9h3g==", "type": "package", "path": "asp.versioning.mvc.apiexplorer/8.1.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "asp.versioning.mvc.apiexplorer.8.1.0.nupkg.sha512", "asp.versioning.mvc.apiexplorer.nuspec", "icon.png", "lib/net8.0/Asp.Versioning.Mvc.ApiExplorer.dll", "lib/net8.0/Asp.Versioning.Mvc.ApiExplorer.xml"]}, "AsyncKeyedLock/6.3.4": {"sha512": "+0YPXvamnopzHSgBpv0vg+9/NJH+6jm1ZODuMMs7p43yH7hA/NxNi+/U7T7eazZqW+V+0ZsbreYruhu6i7N4PA==", "type": "package", "path": "asynckeyedlock/6.3.4", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "asynckeyedlock.6.3.4.nupkg.sha512", "asynckeyedlock.nuspec", "lib/net5.0/AsyncKeyedLock.dll", "lib/net5.0/AsyncKeyedLock.xml", "lib/net8.0/AsyncKeyedLock.dll", "lib/net8.0/AsyncKeyedLock.xml", "lib/netstandard2.0/AsyncKeyedLock.dll", "lib/netstandard2.0/AsyncKeyedLock.xml", "lib/netstandard2.1/AsyncKeyedLock.dll", "lib/netstandard2.1/AsyncKeyedLock.xml", "logo.png"]}, "AutoMapper/12.0.1": {"sha512": "hvV62vl6Hp/WfQ24yzo3Co9+OPl8wH8hApwVtgWpiAynVJkUcs7xvehnSftawL8Pe8FrPffBRM3hwzLQqWDNjA==", "type": "package", "path": "automapper/12.0.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "automapper.12.0.1.nupkg.sha512", "automapper.nuspec", "icon.png", "lib/netstandard2.1/AutoMapper.dll", "lib/netstandard2.1/AutoMapper.xml"]}, "ConfigureAwait.Fody/3.3.1": {"sha512": "R9PQYf0AT4RBZcUXm22xWkCpSmNHdTzQ0dOyLIsxIK6dwXH4S9pY/rZdXU/63i8vZvSzZ99sB1kP7xer8MCe6w==", "type": "package", "path": "configureawait.fody/3.3.1", "files": [".nupkg.metadata", ".signature.p7s", "build/ConfigureAwait.Fody.props", "configureawait.fody.3.3.1.nupkg.sha512", "configureawait.fody.nuspec", "lib/net452/ConfigureAwait.dll", "lib/net452/ConfigureAwait.xml", "lib/netstandard2.0/ConfigureAwait.dll", "lib/netstandard2.0/ConfigureAwait.xml", "weaver/ConfigureAwait.Fody.dll", "weaver/ConfigureAwait.Fody.xcf"]}, "Fody/6.5.3": {"sha512": "sRkrGVPJWG5vVKF/3kExAwZhFMUzK/Zksgcv113ehyuYuTDMuqBC4lr6y0qqZ6ga5nT1uueebDzrsRZsNIrqLg==", "type": "package", "path": "fody/6.5.3", "files": [".nupkg.metadata", ".signature.p7s", "License.txt", "build/Fody.targets", "fody.6.5.3.nupkg.sha512", "fody.nuspec", "netclassictask/Fody.dll", "netclassictask/FodyCommon.dll", "netclassictask/FodyHelpers.dll", "netclassictask/FodyIsolated.dll", "netclassictask/Mono.Cecil.Pdb.dll", "netclassictask/Mono.Cecil.Pdb.pdb", "netclassictask/Mono.Cecil.Rocks.dll", "netclassictask/Mono.Cecil.Rocks.pdb", "netclassictask/Mono.Cecil.dll", "netclassictask/Mono.Cecil.pdb", "netstandardtask/Fody.dll", "netstandardtask/FodyCommon.dll", "netstandardtask/FodyHelpers.dll", "netstandardtask/FodyIsolated.dll", "netstandardtask/Mono.Cecil.Pdb.dll", "netstandardtask/Mono.Cecil.Pdb.pdb", "netstandardtask/Mono.Cecil.Rocks.dll", "netstandardtask/Mono.Cecil.Rocks.pdb", "netstandardtask/Mono.Cecil.dll", "netstandardtask/Mono.Cecil.pdb"]}, "IdentityModel/6.2.0": {"sha512": "4AXZ6Tp+DNwrSSeBziiX/231i8ZpD77A9nEMyc68gLSCWG0kgWsIBeFquYcBebiIPkfB7GEXzCYuuLeR1QZJIQ==", "type": "package", "path": "identitymodel/6.2.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.jpg", "identitymodel.6.2.0.nupkg.sha512", "identitymodel.nuspec", "lib/net461/IdentityModel.dll", "lib/net461/IdentityModel.pdb", "lib/net461/IdentityModel.xml", "lib/net472/IdentityModel.dll", "lib/net472/IdentityModel.pdb", "lib/net472/IdentityModel.xml", "lib/net6.0/IdentityModel.dll", "lib/net6.0/IdentityModel.pdb", "lib/net6.0/IdentityModel.xml", "lib/netstandard2.0/IdentityModel.dll", "lib/netstandard2.0/IdentityModel.pdb", "lib/netstandard2.0/IdentityModel.xml"]}, "JetBrains.Annotations/2023.3.0": {"sha512": "PHfnvdBUdGaTVG9bR/GEfxgTwWM0Z97Y6X3710wiljELBISipSfF5okn/vz+C2gfO+ihoEyVPjaJwn8ZalVukA==", "type": "package", "path": "jetbrains.annotations/2023.3.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "jetbrains.annotations.2023.3.0.nupkg.sha512", "jetbrains.annotations.nuspec", "lib/net20/JetBrains.Annotations.dll", "lib/net20/JetBrains.Annotations.xml", "lib/netstandard1.0/JetBrains.Annotations.deps.json", "lib/netstandard1.0/JetBrains.Annotations.dll", "lib/netstandard1.0/JetBrains.Annotations.xml", "lib/netstandard2.0/JetBrains.Annotations.deps.json", "lib/netstandard2.0/JetBrains.Annotations.dll", "lib/netstandard2.0/JetBrains.Annotations.xml", "lib/portable40-net40+sl5+win8+wp8+wpa81/JetBrains.Annotations.dll", "lib/portable40-net40+sl5+win8+wp8+wpa81/JetBrains.Annotations.xml"]}, "Microsoft.AspNetCore.Authentication.OpenIdConnect/8.0.0": {"sha512": "vBAcj4GpCpvJXIXFnYXDVF0yQtsRAaGib+DiMc79KZNyb/TKhxpoHQwOP7v3aMAoIqUC0HUbf1RQJUoOygakbQ==", "type": "package", "path": "microsoft.aspnetcore.authentication.openidconnect/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net8.0/Microsoft.AspNetCore.Authentication.OpenIdConnect.dll", "lib/net8.0/Microsoft.AspNetCore.Authentication.OpenIdConnect.xml", "microsoft.aspnetcore.authentication.openidconnect.8.0.0.nupkg.sha512", "microsoft.aspnetcore.authentication.openidconnect.nuspec"]}, "Microsoft.AspNetCore.Authorization/8.0.0": {"sha512": "OGIGJMnlWvQgcweHcv1Mq/P24Zx/brUHeEdD05NzqkSXmQSnFomTvVyCuBtCXT4JPfv2m70y1RSocmd9bIbJRg==", "type": "package", "path": "microsoft.aspnetcore.authorization/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.AspNetCore.Authorization.dll", "lib/net462/Microsoft.AspNetCore.Authorization.xml", "lib/net8.0/Microsoft.AspNetCore.Authorization.dll", "lib/net8.0/Microsoft.AspNetCore.Authorization.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.xml", "microsoft.aspnetcore.authorization.8.0.0.nupkg.sha512", "microsoft.aspnetcore.authorization.nuspec"]}, "Microsoft.AspNetCore.Cryptography.Internal/8.0.0": {"sha512": "buuMMCTxFcVkOkEftb2OafYxrveNGre9KJF4Oi1DkR4rxIj6oLam7Wq3g0Fp9hNVpJteKEPiupsxYnPrD/oUGA==", "type": "package", "path": "microsoft.aspnetcore.cryptography.internal/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.AspNetCore.Cryptography.Internal.dll", "lib/net462/Microsoft.AspNetCore.Cryptography.Internal.xml", "lib/net8.0/Microsoft.AspNetCore.Cryptography.Internal.dll", "lib/net8.0/Microsoft.AspNetCore.Cryptography.Internal.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Cryptography.Internal.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Cryptography.Internal.xml", "microsoft.aspnetcore.cryptography.internal.8.0.0.nupkg.sha512", "microsoft.aspnetcore.cryptography.internal.nuspec"]}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/8.0.0": {"sha512": "65w93R5wqUUs35R9wjHHDf75GqAbxJsNByKZo5TbQOWSXcUbLWrDUWBQHv78iXIT0PL1pXNqKQz7OHiHMvo0/A==", "type": "package", "path": "microsoft.aspnetcore.cryptography.keyderivation/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll", "lib/net462/Microsoft.AspNetCore.Cryptography.KeyDerivation.xml", "lib/net8.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll", "lib/net8.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.xml", "microsoft.aspnetcore.cryptography.keyderivation.8.0.0.nupkg.sha512", "microsoft.aspnetcore.cryptography.keyderivation.nuspec"]}, "Microsoft.AspNetCore.Metadata/8.0.0": {"sha512": "OmuSztiZMitRTYlbMNDkBk3BinSsVcOApSNBAsrw+KYNJh6ALarPhWLlKdtvMgrKzpyCY06xtLAjTmQLURHSlQ==", "type": "package", "path": "microsoft.aspnetcore.metadata/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.AspNetCore.Metadata.dll", "lib/net462/Microsoft.AspNetCore.Metadata.xml", "lib/net8.0/Microsoft.AspNetCore.Metadata.dll", "lib/net8.0/Microsoft.AspNetCore.Metadata.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Metadata.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Metadata.xml", "microsoft.aspnetcore.metadata.8.0.0.nupkg.sha512", "microsoft.aspnetcore.metadata.nuspec"]}, "Microsoft.AspNetCore.Mvc.Razor.Extensions/6.0.0": {"sha512": "M0h+ChPgydX2xY17agiphnAVa/Qh05RAP8eeuqGGhQKT10claRBlLNO6d2/oSV8zy0RLHzwLnNZm5xuC/gckGA==", "type": "package", "path": "microsoft.aspnetcore.mvc.razor.extensions/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.Extensions.dll", "microsoft.aspnetcore.mvc.razor.extensions.6.0.0.nupkg.sha512", "microsoft.aspnetcore.mvc.razor.extensions.nuspec"]}, "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation/8.0.0": {"sha512": "HyLDtyWwpavSEFBOL0qOdymY8f+VwN5QhhE7gj3wBw53j9EA0ZcYkbfTvkhvMeV9PavgCcMIe4sAsBGEE/YnNA==", "type": "package", "path": "microsoft.aspnetcore.mvc.razor.runtimecompilation/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "build/net8.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.targets", "buildTransitive/net8.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.targets", "lib/net8.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.dll", "lib/net8.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.xml", "microsoft.aspnetcore.mvc.razor.runtimecompilation.8.0.0.nupkg.sha512", "microsoft.aspnetcore.mvc.razor.runtimecompilation.nuspec"]}, "Microsoft.AspNetCore.OData/8.2.5": {"sha512": "zAgHw+d/wZjuN2AjmYw8+O6y8Bi7s2L5E/unRz1PTsaJ1z8UWrdh/vAReIwlxnSXYBUCAaQMKQPERqKGe+8uGw==", "type": "package", "path": "microsoft.aspnetcore.odata/8.2.5", "files": [".nupkg.metadata", ".signature.p7s", "images/odata.png", "lib/net5.0/Microsoft.AspNetCore.OData.dll", "lib/net5.0/Microsoft.AspNetCore.OData.xml", "lib/net6.0/Microsoft.AspNetCore.OData.dll", "lib/net6.0/Microsoft.AspNetCore.OData.xml", "lib/netcoreapp3.1/Microsoft.AspNetCore.OData.dll", "lib/netcoreapp3.1/Microsoft.AspNetCore.OData.xml", "microsoft.aspnetcore.odata.8.2.5.nupkg.sha512", "microsoft.aspnetcore.odata.nuspec"]}, "Microsoft.AspNetCore.Razor.Language/6.0.0": {"sha512": "yCtBr1GSGzJrrp1NJUb4ltwFYMKHw/tJLnIDvg9g/FnkGIEzmE19tbCQqXARIJv5kdtBgsoVIdGLL+zmjxvM/A==", "type": "package", "path": "microsoft.aspnetcore.razor.language/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll", "microsoft.aspnetcore.razor.language.6.0.0.nupkg.sha512", "microsoft.aspnetcore.razor.language.nuspec"]}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"sha512": "3WA9q9yVqJp222P3x1wYIGDAkpjAku0TMUaaQV22g6L67AI0LdOIrVS7Ht2vJfLHGSPVuqN94vIr15qn+HEkHw==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Bcl.AsyncInterfaces.targets", "buildTransitive/net462/_._", "lib/net462/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net462/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.CodeAnalysis.Analyzers/3.3.2": {"sha512": "7xt6zTlIEizUgEsYAIgm37EbdkiMmr6fP6J9pDoKEpiGM4pi32BCPGr/IczmSJI9Zzp0a6HOzpr9OvpMP+2veA==", "type": "package", "path": "microsoft.codeanalysis.analyzers/3.3.2", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "EULA.rtf", "ThirdPartyNotices.rtf", "analyzers/dotnet/cs/Microsoft.CodeAnalysis.Analyzers.dll", "analyzers/dotnet/cs/Microsoft.CodeAnalysis.CSharp.Analyzers.dll", "analyzers/dotnet/cs/cs/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/de/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/es/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/it/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/Microsoft.CodeAnalysis.Analyzers.dll", "analyzers/dotnet/vb/Microsoft.CodeAnalysis.VisualBasic.Analyzers.dll", "analyzers/dotnet/vb/cs/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/de/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/es/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/fr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/it/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/ja/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/ko/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/pl/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/pt-BR/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/ru/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/tr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/zh-<PERSON>/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/zh-Hant/Microsoft.CodeAnalysis.Analyzers.resources.dll", "build/Microsoft.CodeAnalysis.Analyzers.props", "build/Microsoft.CodeAnalysis.Analyzers.targets", "build/config/AnalysisLevel_2_9_8_AllDisabledByDefault.editorconfig", "build/config/AnalysisLevel_2_9_8_AllEnabledByDefault.editorconfig", "build/config/AnalysisLevel_2_9_8_Default.editorconfig", "build/config/AnalysisLevel_3_3_AllDisabledByDefault.editorconfig", "build/config/AnalysisLevel_3_3_AllEnabledByDefault.editorconfig", "build/config/AnalysisLevel_3_3_Default.editorconfig", "build/config/AnalysisLevel_3_AllDisabledByDefault.editorconfig", "build/config/AnalysisLevel_3_AllEnabledByDefault.editorconfig", "build/config/AnalysisLevel_3_Default.editorconfig", "documentation/Analyzer Configuration.md", "documentation/Microsoft.CodeAnalysis.Analyzers.md", "documentation/Microsoft.CodeAnalysis.Analyzers.sarif", "editorconfig/AllRulesDefault/.editorconfig", "editorconfig/AllRulesDisabled/.editorconfig", "editorconfig/AllRulesEnabled/.editorconfig", "editorconfig/CorrectnessRulesDefault/.editorconfig", "editorconfig/CorrectnessRulesEnabled/.editorconfig", "editorconfig/DataflowRulesDefault/.editorconfig", "editorconfig/DataflowRulesEnabled/.editorconfig", "editorconfig/LibraryRulesDefault/.editorconfig", "editorconfig/LibraryRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCompatibilityRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCompatibilityRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCorrectnessRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCorrectnessRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDesignRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDesignRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDocumentationRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDocumentationRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisLocalizationRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisLocalizationRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisPerformanceRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisPerformanceRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisReleaseTrackingRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisReleaseTrackingRulesEnabled/.editorconfig", "editorconfig/PortedFromFxCopRulesDefault/.editorconfig", "editorconfig/PortedFromFxCopRulesEnabled/.editorconfig", "microsoft.codeanalysis.analyzers.3.3.2.nupkg.sha512", "microsoft.codeanalysis.analyzers.nuspec", "rulesets/AllRulesDefault.ruleset", "rulesets/AllRulesDisabled.ruleset", "rulesets/AllRulesEnabled.ruleset", "rulesets/CorrectnessRulesDefault.ruleset", "rulesets/CorrectnessRulesEnabled.ruleset", "rulesets/DataflowRulesDefault.ruleset", "rulesets/DataflowRulesEnabled.ruleset", "rulesets/LibraryRulesDefault.ruleset", "rulesets/LibraryRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisCompatibilityRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisCompatibilityRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisCorrectnessRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisCorrectnessRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisDesignRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisDesignRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisDocumentationRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisDocumentationRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisLocalizationRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisLocalizationRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisPerformanceRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisPerformanceRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisReleaseTrackingRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisReleaseTrackingRulesEnabled.ruleset", "rulesets/PortedFromFxCopRulesDefault.ruleset", "rulesets/PortedFromFxCopRulesEnabled.ruleset", "tools/install.ps1", "tools/uninstall.ps1"]}, "Microsoft.CodeAnalysis.Common/4.0.0": {"sha512": "d02ybMhUJl1r/dI6SkJPHrTiTzXBYCZeJdOLMckV+jyoMU/GGkjqFX/sRbv1K0QmlpwwKuLTiYVQvfYC+8ox2g==", "type": "package", "path": "microsoft.codeanalysis.common/4.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.pdb", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.xml", "lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll", "microsoft.codeanalysis.common.4.0.0.nupkg.sha512", "microsoft.codeanalysis.common.nuspec"]}, "Microsoft.CodeAnalysis.CSharp/4.0.0": {"sha512": "2UVTGtyQGgTCazvnT6t82f+7AV2L+kqJdyb61rT9GQed4yK+tVh5IkaKcsm70VqyZQhBbDqsfZFNHnY65xhrRw==", "type": "package", "path": "microsoft.codeanalysis.csharp/4.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.pdb", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.xml", "lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll", "microsoft.codeanalysis.csharp.4.0.0.nupkg.sha512", "microsoft.codeanalysis.csharp.nuspec"]}, "Microsoft.CodeAnalysis.Razor/6.0.0": {"sha512": "uqdzuQXxD7XrJCbIbbwpI/LOv0PBJ9VIR0gdvANTHOfK5pjTaCir+XcwvYvBZ5BIzd0KGzyiamzlEWw1cK1q0w==", "type": "package", "path": "microsoft.codeanalysis.razor/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll", "microsoft.codeanalysis.razor.6.0.0.nupkg.sha512", "microsoft.codeanalysis.razor.nuspec"]}, "Microsoft.CSharp/4.7.0": {"sha512": "pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "type": "package", "path": "microsoft.csharp/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/Microsoft.CSharp.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.3/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.xml", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/uap10.0.16299/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "microsoft.csharp.4.7.0.nupkg.sha512", "microsoft.csharp.nuspec", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/Microsoft.CSharp.dll", "ref/netcore50/Microsoft.CSharp.xml", "ref/netcore50/de/Microsoft.CSharp.xml", "ref/netcore50/es/Microsoft.CSharp.xml", "ref/netcore50/fr/Microsoft.CSharp.xml", "ref/netcore50/it/Microsoft.CSharp.xml", "ref/netcore50/ja/Microsoft.CSharp.xml", "ref/netcore50/ko/Microsoft.CSharp.xml", "ref/netcore50/ru/Microsoft.CSharp.xml", "ref/netcore50/zh-hans/Microsoft.CSharp.xml", "ref/netcore50/zh-hant/Microsoft.CSharp.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/Microsoft.CSharp.dll", "ref/netstandard1.0/Microsoft.CSharp.xml", "ref/netstandard1.0/de/Microsoft.CSharp.xml", "ref/netstandard1.0/es/Microsoft.CSharp.xml", "ref/netstandard1.0/fr/Microsoft.CSharp.xml", "ref/netstandard1.0/it/Microsoft.CSharp.xml", "ref/netstandard1.0/ja/Microsoft.CSharp.xml", "ref/netstandard1.0/ko/Microsoft.CSharp.xml", "ref/netstandard1.0/ru/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hans/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hant/Microsoft.CSharp.xml", "ref/netstandard2.0/Microsoft.CSharp.dll", "ref/netstandard2.0/Microsoft.CSharp.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/uap10.0.16299/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"sha512": "3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "type": "package", "path": "microsoft.extensions.caching.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Abstractions.targets", "lib/net462/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net462/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.xml", "microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.caching.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"sha512": "7pqivmrZDzo1ADPkRwjy+8jtRKWRCPag9qPI+p7sgu7Q4QreWhcvbiWXsbhP+yY8XSiDvZpu2/LWdBv7PnmOpQ==", "type": "package", "path": "microsoft.extensions.caching.memory/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Memory.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Memory.targets", "lib/net462/Microsoft.Extensions.Caching.Memory.dll", "lib/net462/Microsoft.Extensions.Caching.Memory.xml", "lib/net6.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net6.0/Microsoft.Extensions.Caching.Memory.xml", "lib/net7.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net7.0/Microsoft.Extensions.Caching.Memory.xml", "lib/net8.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net8.0/Microsoft.Extensions.Caching.Memory.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.xml", "microsoft.extensions.caching.memory.8.0.0.nupkg.sha512", "microsoft.extensions.caching.memory.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration/8.0.0": {"sha512": "0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "type": "package", "path": "microsoft.extensions.configuration/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.targets", "lib/net462/Microsoft.Extensions.Configuration.dll", "lib/net462/Microsoft.Extensions.Configuration.xml", "lib/net6.0/Microsoft.Extensions.Configuration.dll", "lib/net6.0/Microsoft.Extensions.Configuration.xml", "lib/net7.0/Microsoft.Extensions.Configuration.dll", "lib/net7.0/Microsoft.Extensions.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"sha512": "3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"sha512": "mBMoXLsr5s1y2zOHWmKsE9veDcx8h1x/c3rz4baEdQKTeDcmQAPNbB54Pi/lhFO3K431eEq6PFbMgLaa6PHFfA==", "type": "package", "path": "microsoft.extensions.configuration.binder/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.dll", "analyzers/dotnet/cs/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/de/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/es/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/it/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets", "lib/net462/Microsoft.Extensions.Configuration.Binder.dll", "lib/net462/Microsoft.Extensions.Configuration.Binder.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.xml", "microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.binder.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"sha512": "NZuZMz3Q8Z780nKX3ifV1fE7lS+6pynDHK71OfU4OZ1ItgvDOhyOC7E6z+JMZrAj63zRpwbdldYFk499t3+1dQ==", "type": "package", "path": "microsoft.extensions.configuration.commandline/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.CommandLine.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.CommandLine.targets", "lib/net462/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net462/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/net6.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net6.0/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/net7.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net7.0/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.xml", "microsoft.extensions.configuration.commandline.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.commandline.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"sha512": "plvZ0ZIpq+97gdPNNvhwvrEZ92kNml9hd1pe3idMA7svR0PztdzVLkoWLcRFgySYXUJc3kSM3Xw3mNFMo/bxRA==", "type": "package", "path": "microsoft.extensions.configuration.environmentvariables/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.EnvironmentVariables.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.targets", "lib/net462/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net462/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/net6.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net6.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/net7.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net7.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "microsoft.extensions.configuration.environmentvariables.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.environmentvariables.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"sha512": "McP+Lz/EKwvtCv48z0YImw+L1gi1gy5rHhNaNIY2CrjloV+XY8gydT8DjMR6zWeL13AFK+DioVpppwAuO1Gi1w==", "type": "package", "path": "microsoft.extensions.configuration.fileextensions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.FileExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.FileExtensions.targets", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net6.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net6.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net7.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net7.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "microsoft.extensions.configuration.fileextensions.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.fileextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"sha512": "C2wqUoh9OmRL1akaCcKSTmRU8z0kckfImG7zLNI8uyi47Lp+zd5LWAD17waPQEqCz3ioWOCrFUo+JJuoeZLOBw==", "type": "package", "path": "microsoft.extensions.configuration.json/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Json.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Json.targets", "lib/net462/Microsoft.Extensions.Configuration.Json.dll", "lib/net462/Microsoft.Extensions.Configuration.Json.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Json.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Json.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.xml", "microsoft.extensions.configuration.json.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.json.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"sha512": "ihDHu2dJYQird9pl2CbdwuNDfvCZdOS0S7SPlNfhPt0B81UTT+yyZKz2pimFZGUp3AfuBRnqUCxB2SjsZKHVUw==", "type": "package", "path": "microsoft.extensions.configuration.usersecrets/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/net462/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/net462/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/net6.0/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/net6.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "lib/net462/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net462/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/net6.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net6.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/net7.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net7.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "microsoft.extensions.configuration.usersecrets.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.usersecrets.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"sha512": "V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "type": "package", "path": "microsoft.extensions.dependencyinjection/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"sha512": "cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyModel/8.0.0": {"sha512": "NSmDw3K0ozNDgShSIpsZcbFIzBX4w28nDag+TfaQujkXGazBm+lid5onlWoCBy4VsLxqnnKjEBbGSJVWJMf43g==", "type": "package", "path": "microsoft.extensions.dependencymodel/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyModel.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyModel.targets", "lib/net462/Microsoft.Extensions.DependencyModel.dll", "lib/net462/Microsoft.Extensions.DependencyModel.xml", "lib/net6.0/Microsoft.Extensions.DependencyModel.dll", "lib/net6.0/Microsoft.Extensions.DependencyModel.xml", "lib/net7.0/Microsoft.Extensions.DependencyModel.dll", "lib/net7.0/Microsoft.Extensions.DependencyModel.xml", "lib/net8.0/Microsoft.Extensions.DependencyModel.dll", "lib/net8.0/Microsoft.Extensions.DependencyModel.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.xml", "microsoft.extensions.dependencymodel.8.0.0.nupkg.sha512", "microsoft.extensions.dependencymodel.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"sha512": "JHYCQG7HmugNYUhOl368g+NMxYE/N/AiclCYRNlgCY9eVyiBkOHMwK4x60RYMxv9EL3+rmj1mqHvdCiPpC+D4Q==", "type": "package", "path": "microsoft.extensions.diagnostics.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Diagnostics.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Diagnostics.Abstractions.targets", "lib/net462/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net462/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.diagnostics.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"sha512": "ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Abstractions.targets", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Composite/8.0.0": {"sha512": "ynMjdZ5B3Fd3A9GxJaNhIcTrjLY1bXDQltyVIMVOxbT0ssTOCpFYWc977bVBAocB62fYWu/RN6/1HLnX/HjVuQ==", "type": "package", "path": "microsoft.extensions.fileproviders.composite/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Composite.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Composite.targets", "lib/net462/Microsoft.Extensions.FileProviders.Composite.dll", "lib/net462/Microsoft.Extensions.FileProviders.Composite.xml", "lib/net6.0/Microsoft.Extensions.FileProviders.Composite.dll", "lib/net6.0/Microsoft.Extensions.FileProviders.Composite.xml", "lib/net7.0/Microsoft.Extensions.FileProviders.Composite.dll", "lib/net7.0/Microsoft.Extensions.FileProviders.Composite.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Composite.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Composite.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Composite.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Composite.xml", "microsoft.extensions.fileproviders.composite.8.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.composite.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Embedded/8.0.0": {"sha512": "TuRh62KcoOvaSDCbtHT8K0WYptZysYQHPRRNfOgqF7ZUtUL4O0WMV8RdxbtDFJDsg3jv9bgHwXbrgwTeI9+5uQ==", "type": "package", "path": "microsoft.extensions.fileproviders.embedded/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.props", "build/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.targets", "buildMultiTargeting/Microsoft.Extensions.FileProviders.Embedded.props", "buildMultiTargeting/Microsoft.Extensions.FileProviders.Embedded.targets", "lib/net462/Microsoft.Extensions.FileProviders.Embedded.dll", "lib/net462/Microsoft.Extensions.FileProviders.Embedded.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Embedded.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Embedded.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.xml", "microsoft.extensions.fileproviders.embedded.8.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.embedded.nuspec", "tasks/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.Manifest.Task.dll"]}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"sha512": "UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "type": "package", "path": "microsoft.extensions.fileproviders.physical/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Physical.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Physical.targets", "lib/net462/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net462/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net6.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net6.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net7.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net7.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.xml", "microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.physical.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"sha512": "OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "type": "package", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileSystemGlobbing.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileSystemGlobbing.targets", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net7.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net7.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.xml", "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512", "microsoft.extensions.filesystemglobbing.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"sha512": "AG7HWwVRdCHlaA++1oKDxLsXIBxmDpMPb3VoyOoAghEWnkUvEAdYQUwnV4jJbAaa/nMYNiEh5ByoLauZBEiovg==", "type": "package", "path": "microsoft.extensions.hosting.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Hosting.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Hosting.Abstractions.targets", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.xml", "microsoft.extensions.hosting.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.hosting.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Identity.Core/8.0.0": {"sha512": "hnXHyIQc+uc2uNMcIbr43+oNBAPEhMpW6lE8ux3MOegRz50WBna4AItlZDY7Y+Id1LLBbf73osUqeTw7CQ371w==", "type": "package", "path": "microsoft.extensions.identity.core/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.Extensions.Identity.Core.dll", "lib/net462/Microsoft.Extensions.Identity.Core.xml", "lib/net8.0/Microsoft.Extensions.Identity.Core.dll", "lib/net8.0/Microsoft.Extensions.Identity.Core.xml", "lib/netstandard2.0/Microsoft.Extensions.Identity.Core.dll", "lib/netstandard2.0/Microsoft.Extensions.Identity.Core.xml", "microsoft.extensions.identity.core.8.0.0.nupkg.sha512", "microsoft.extensions.identity.core.nuspec"]}, "Microsoft.Extensions.Localization/8.0.0": {"sha512": "I4xyK9RaXyJBkU5+jQpkZITR/54tXGbdt5CsBmogf8Irrz+RXXsp1zJgrmQzxEOpxj5VMAA/GQ4SGZMeM5nVSg==", "type": "package", "path": "microsoft.extensions.localization/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.Extensions.Localization.dll", "lib/net462/Microsoft.Extensions.Localization.xml", "lib/net8.0/Microsoft.Extensions.Localization.dll", "lib/net8.0/Microsoft.Extensions.Localization.xml", "lib/netstandard2.0/Microsoft.Extensions.Localization.dll", "lib/netstandard2.0/Microsoft.Extensions.Localization.xml", "microsoft.extensions.localization.8.0.0.nupkg.sha512", "microsoft.extensions.localization.nuspec"]}, "Microsoft.Extensions.Localization.Abstractions/8.0.0": {"sha512": "LIMzXjjzv7o3tx3XXvQVHNcrMNPfSoUp4R/YJucdyBixJGRp5p8v2rF1CnRbo+wwtzB4Se/gJYyUpNN8F/TMGw==", "type": "package", "path": "microsoft.extensions.localization.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.Extensions.Localization.Abstractions.dll", "lib/net462/Microsoft.Extensions.Localization.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Localization.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Localization.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Localization.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Localization.Abstractions.xml", "microsoft.extensions.localization.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.localization.abstractions.nuspec"]}, "Microsoft.Extensions.Logging/8.0.0": {"sha512": "tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "type": "package", "path": "microsoft.extensions.logging/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets", "lib/net462/Microsoft.Extensions.Logging.dll", "lib/net462/Microsoft.Extensions.Logging.xml", "lib/net6.0/Microsoft.Extensions.Logging.dll", "lib/net6.0/Microsoft.Extensions.Logging.xml", "lib/net7.0/Microsoft.Extensions.Logging.dll", "lib/net7.0/Microsoft.Extensions.Logging.xml", "lib/net8.0/Microsoft.Extensions.Logging.dll", "lib/net8.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.8.0.0.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"sha512": "arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "type": "package", "path": "microsoft.extensions.logging.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options/8.0.0": {"sha512": "JOVOfqpnqlVLUzINQ2fox8evY2SKLYJ3BV8QDe/Jyp21u1T7r45x/R/5QdteURMR5r01GxeJSBBUOCOyaNXA3g==", "type": "package", "path": "microsoft.extensions.options/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/Microsoft.Extensions.Options.targets", "buildTransitive/net6.0/Microsoft.Extensions.Options.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net6.0/Microsoft.Extensions.Options.dll", "lib/net6.0/Microsoft.Extensions.Options.xml", "lib/net7.0/Microsoft.Extensions.Options.dll", "lib/net7.0/Microsoft.Extensions.Options.xml", "lib/net8.0/Microsoft.Extensions.Options.dll", "lib/net8.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.8.0.0.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"sha512": "0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "type": "package", "path": "microsoft.extensions.options.configurationextensions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Options.ConfigurationExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.ConfigurationExtensions.targets", "lib/net462/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net462/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net6.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net6.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net7.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net7.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512", "microsoft.extensions.options.configurationextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/8.0.0": {"sha512": "bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "type": "package", "path": "microsoft.extensions.primitives/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net6.0/Microsoft.Extensions.Primitives.dll", "lib/net6.0/Microsoft.Extensions.Primitives.xml", "lib/net7.0/Microsoft.Extensions.Primitives.dll", "lib/net7.0/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.8.0.0.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.IdentityModel.Abstractions/7.0.3": {"sha512": "cfPUWdjigLIRIJSKz3uaZxShgf86RVDXHC1VEEchj1gnY25akwPYpbrfSoIGDCqA9UmOMdlctq411+2pAViFow==", "type": "package", "path": "microsoft.identitymodel.abstractions/7.0.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.Abstractions.dll", "lib/net461/Microsoft.IdentityModel.Abstractions.xml", "lib/net462/Microsoft.IdentityModel.Abstractions.dll", "lib/net462/Microsoft.IdentityModel.Abstractions.xml", "lib/net472/Microsoft.IdentityModel.Abstractions.dll", "lib/net472/Microsoft.IdentityModel.Abstractions.xml", "lib/net6.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net6.0/Microsoft.IdentityModel.Abstractions.xml", "lib/net8.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net8.0/Microsoft.IdentityModel.Abstractions.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.xml", "microsoft.identitymodel.abstractions.7.0.3.nupkg.sha512", "microsoft.identitymodel.abstractions.nuspec"]}, "Microsoft.IdentityModel.JsonWebTokens/7.0.3": {"sha512": "vxjHVZbMKD3rVdbvKhzAW+7UiFrYToUVm3AGmYfKSOAwyhdLl/ELX1KZr+FaLyyS5VReIzWRWJfbOuHM9i6ywg==", "type": "package", "path": "microsoft.identitymodel.jsonwebtokens/7.0.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net462/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net462/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.xml", "microsoft.identitymodel.jsonwebtokens.7.0.3.nupkg.sha512", "microsoft.identitymodel.jsonwebtokens.nuspec"]}, "Microsoft.IdentityModel.Logging/7.0.3": {"sha512": "b6GbGO+2LOTBEccHhqoJsOsmemG4A/MY+8H0wK/ewRhiG+DCYwEnucog1cSArPIY55zcn+XdZl0YEiUHkpDISQ==", "type": "package", "path": "microsoft.identitymodel.logging/7.0.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.Logging.dll", "lib/net461/Microsoft.IdentityModel.Logging.xml", "lib/net462/Microsoft.IdentityModel.Logging.dll", "lib/net462/Microsoft.IdentityModel.Logging.xml", "lib/net472/Microsoft.IdentityModel.Logging.dll", "lib/net472/Microsoft.IdentityModel.Logging.xml", "lib/net6.0/Microsoft.IdentityModel.Logging.dll", "lib/net6.0/Microsoft.IdentityModel.Logging.xml", "lib/net8.0/Microsoft.IdentityModel.Logging.dll", "lib/net8.0/Microsoft.IdentityModel.Logging.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.xml", "microsoft.identitymodel.logging.7.0.3.nupkg.sha512", "microsoft.identitymodel.logging.nuspec"]}, "Microsoft.IdentityModel.Protocols/7.0.3": {"sha512": "BtwR+tctBYhPNygyZmt1Rnw74GFrJteW+1zcdIgyvBCjkek6cNwPPqRfdhzCv61i+lwyNomRi8+iI4QKd4YCKA==", "type": "package", "path": "microsoft.identitymodel.protocols/7.0.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.Protocols.dll", "lib/net461/Microsoft.IdentityModel.Protocols.xml", "lib/net462/Microsoft.IdentityModel.Protocols.dll", "lib/net462/Microsoft.IdentityModel.Protocols.xml", "lib/net472/Microsoft.IdentityModel.Protocols.dll", "lib/net472/Microsoft.IdentityModel.Protocols.xml", "lib/net6.0/Microsoft.IdentityModel.Protocols.dll", "lib/net6.0/Microsoft.IdentityModel.Protocols.xml", "lib/net8.0/Microsoft.IdentityModel.Protocols.dll", "lib/net8.0/Microsoft.IdentityModel.Protocols.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.xml", "microsoft.identitymodel.protocols.7.0.3.nupkg.sha512", "microsoft.identitymodel.protocols.nuspec"]}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.0.3": {"sha512": "W97TraHApDNArLwpPcXfD+FZH7njJsfEwZE9y9BoofeXMS8H0LBBobz0VOmYmMK4mLdOKxzN7SFT3Ekg0FWI3Q==", "type": "package", "path": "microsoft.identitymodel.protocols.openidconnect/7.0.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net461/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net462/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net462/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "microsoft.identitymodel.protocols.openidconnect.7.0.3.nupkg.sha512", "microsoft.identitymodel.protocols.openidconnect.nuspec"]}, "Microsoft.IdentityModel.Tokens/7.0.3": {"sha512": "wB+LlbDjhnJ98DULjmFepqf9eEMh/sDs6S6hFh68iNRHmwollwhxk+nbSSfpA5+j+FbRyNskoaY4JsY1iCOKCg==", "type": "package", "path": "microsoft.identitymodel.tokens/7.0.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.Tokens.dll", "lib/net461/Microsoft.IdentityModel.Tokens.xml", "lib/net462/Microsoft.IdentityModel.Tokens.dll", "lib/net462/Microsoft.IdentityModel.Tokens.xml", "lib/net472/Microsoft.IdentityModel.Tokens.dll", "lib/net472/Microsoft.IdentityModel.Tokens.xml", "lib/net6.0/Microsoft.IdentityModel.Tokens.dll", "lib/net6.0/Microsoft.IdentityModel.Tokens.xml", "lib/net8.0/Microsoft.IdentityModel.Tokens.dll", "lib/net8.0/Microsoft.IdentityModel.Tokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.xml", "microsoft.identitymodel.tokens.7.0.3.nupkg.sha512", "microsoft.identitymodel.tokens.nuspec"]}, "Microsoft.NETCore.Platforms/2.1.2": {"sha512": "mOJy3M0UN+LUG21dLGMxaWZEP6xYpQEpLuvuEQBaownaX4YuhH6NmNUlN9si+vNkAS6dwJ//N1O4DmLf2CikVg==", "type": "package", "path": "microsoft.netcore.platforms/2.1.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.2.1.2.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.NETCore.Targets/1.1.0": {"sha512": "aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "type": "package", "path": "microsoft.netcore.targets/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.targets.1.1.0.nupkg.sha512", "microsoft.netcore.targets.nuspec", "runtime.json"]}, "Microsoft.OData.Core/7.20.0": {"sha512": "CY+Bx64a2ytxD1/gw/qU3iDfbMqoDLhjm+G1Lte3At2YYgJt3vSLFvNJzHZuREq7Lnsmxb/KHlTRsWY7iMwZ7A==", "type": "package", "path": "microsoft.odata.core/7.20.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.OData.Core.dll", "lib/net45/Microsoft.OData.Core.xml", "lib/netcoreapp3.1/Microsoft.OData.Core.dll", "lib/netcoreapp3.1/Microsoft.OData.Core.xml", "lib/netstandard1.1/Microsoft.OData.Core.dll", "lib/netstandard1.1/Microsoft.OData.Core.xml", "lib/netstandard2.0/Microsoft.OData.Core.dll", "lib/netstandard2.0/Microsoft.OData.Core.xml", "microsoft.odata.core.7.20.0.nupkg.sha512", "microsoft.odata.core.nuspec"]}, "Microsoft.OData.Edm/7.20.0": {"sha512": "F4jt0Ojqjc2bTmOcGT7LqIlwxnAuVjMbdqN1taFOwZ21uQY4k2IjFFwakcG8B5YpaiS6uGD6mJXlO3DtMmMsHw==", "type": "package", "path": "microsoft.odata.edm/7.20.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.OData.Edm.dll", "lib/net45/Microsoft.OData.Edm.xml", "lib/netstandard1.1/Microsoft.OData.Edm.dll", "lib/netstandard1.1/Microsoft.OData.Edm.xml", "lib/netstandard2.0/Microsoft.OData.Edm.dll", "lib/netstandard2.0/Microsoft.OData.Edm.xml", "microsoft.odata.edm.7.20.0.nupkg.sha512", "microsoft.odata.edm.nuspec"]}, "Microsoft.OData.ModelBuilder/1.0.9": {"sha512": "Qt2AVqW4cFiICWiGdY08c7q2pHdFJTc0jth7oO9T6xb+0SZSHFloScqTMdMNv8M9Wfc16wyWqxYQyVyWiODWDg==", "type": "package", "path": "microsoft.odata.modelbuilder/1.0.9", "files": [".nupkg.metadata", ".signature.p7s", "images/odata.png", "lib/net6.0/Microsoft.OData.ModelBuilder.dll", "lib/net6.0/Microsoft.OData.ModelBuilder.xml", "lib/netstandard2.0/Microsoft.OData.ModelBuilder.dll", "lib/netstandard2.0/Microsoft.OData.ModelBuilder.xml", "microsoft.odata.modelbuilder.1.0.9.nupkg.sha512", "microsoft.odata.modelbuilder.nuspec"]}, "Microsoft.Spatial/7.20.0": {"sha512": "nJLDCOv3qemN0XI5kmUN9h38Qw+ZQBoYtWwSGhPkDEiiZSDcAwLMgqbGNKmK3myhVWASCqXtOqrj5jUnGZrH/Q==", "type": "package", "path": "microsoft.spatial/7.20.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.Spatial.dll", "lib/net45/Microsoft.Spatial.xml", "lib/netstandard1.1/Microsoft.Spatial.dll", "lib/netstandard1.1/Microsoft.Spatial.xml", "lib/netstandard2.0/Microsoft.Spatial.dll", "lib/netstandard2.0/Microsoft.Spatial.xml", "microsoft.spatial.7.20.0.nupkg.sha512", "microsoft.spatial.nuspec"]}, "Nito.AsyncEx.Context/5.1.2": {"sha512": "rMwL7Nj3oNyvFu/jxUzQ/YBobEkM2RQHe+5mpCDRyq6mfD7vCj7Z3rjB6XgpM6Mqcx1CA2xGv0ascU/2Xk8IIg==", "type": "package", "path": "nito.asyncex.context/5.1.2", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net461/Nito.AsyncEx.Context.dll", "lib/net461/Nito.AsyncEx.Context.xml", "lib/netstandard1.3/Nito.AsyncEx.Context.dll", "lib/netstandard1.3/Nito.AsyncEx.Context.xml", "lib/netstandard2.0/Nito.AsyncEx.Context.dll", "lib/netstandard2.0/Nito.AsyncEx.Context.xml", "nito.asyncex.context.5.1.2.nupkg.sha512", "nito.asyncex.context.nuspec"]}, "Nito.AsyncEx.Tasks/5.1.2": {"sha512": "jEkCfR2/M26OK/U4G7SEN063EU/F4LiVA06TtpZILMdX/quIHCg+wn31Zerl2LC+u1cyFancjTY3cNAr2/89PA==", "type": "package", "path": "nito.asyncex.tasks/5.1.2", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net461/Nito.AsyncEx.Tasks.dll", "lib/net461/Nito.AsyncEx.Tasks.xml", "lib/netstandard1.3/Nito.AsyncEx.Tasks.dll", "lib/netstandard1.3/Nito.AsyncEx.Tasks.xml", "lib/netstandard2.0/Nito.AsyncEx.Tasks.dll", "lib/netstandard2.0/Nito.AsyncEx.Tasks.xml", "nito.asyncex.tasks.5.1.2.nupkg.sha512", "nito.asyncex.tasks.nuspec"]}, "Nito.Disposables/2.2.1": {"sha512": "6sZ5uynQeAE9dPWBQGKebNmxbY4xsvcc5VplB5WkYEESUS7oy4AwnFp0FhqxTSKm/PaFrFqLrYr696CYN8cugg==", "type": "package", "path": "nito.disposables/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net461/Nito.Disposables.dll", "lib/net461/Nito.Disposables.xml", "lib/netstandard1.0/Nito.Disposables.dll", "lib/netstandard1.0/Nito.Disposables.xml", "lib/netstandard2.0/Nito.Disposables.dll", "lib/netstandard2.0/Nito.Disposables.xml", "lib/netstandard2.1/Nito.Disposables.dll", "lib/netstandard2.1/Nito.Disposables.xml", "nito.disposables.2.2.1.nupkg.sha512", "nito.disposables.nuspec"]}, "NUglify/1.21.0": {"sha512": "9J44o89PRdcS4GOyj0obkLfjCAuJItI4FrNmwALkjRKlzvHVlTB2ALbC9aigIoCMqzy0Xlc0mIVD/jO9WVDHiA==", "type": "package", "path": "nuglify/1.21.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net35/NUglify.dll", "lib/net35/NUglify.xml", "lib/net40/NUglify.dll", "lib/net40/NUglify.xml", "lib/net5.0/NUglify.dll", "lib/net5.0/NUglify.xml", "lib/netstandard1.3/NUglify.dll", "lib/netstandard1.3/NUglify.xml", "lib/netstandard2.0/NUglify.dll", "lib/netstandard2.0/NUglify.xml", "nuglify.1.21.0.nupkg.sha512", "nuglify.nuspec", "nuglify.png"]}, "System.Collections/4.3.0": {"sha512": "3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "type": "package", "path": "system.collections/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Collections.dll", "ref/netcore50/System.Collections.xml", "ref/netcore50/de/System.Collections.xml", "ref/netcore50/es/System.Collections.xml", "ref/netcore50/fr/System.Collections.xml", "ref/netcore50/it/System.Collections.xml", "ref/netcore50/ja/System.Collections.xml", "ref/netcore50/ko/System.Collections.xml", "ref/netcore50/ru/System.Collections.xml", "ref/netcore50/zh-hans/System.Collections.xml", "ref/netcore50/zh-hant/System.Collections.xml", "ref/netstandard1.0/System.Collections.dll", "ref/netstandard1.0/System.Collections.xml", "ref/netstandard1.0/de/System.Collections.xml", "ref/netstandard1.0/es/System.Collections.xml", "ref/netstandard1.0/fr/System.Collections.xml", "ref/netstandard1.0/it/System.Collections.xml", "ref/netstandard1.0/ja/System.Collections.xml", "ref/netstandard1.0/ko/System.Collections.xml", "ref/netstandard1.0/ru/System.Collections.xml", "ref/netstandard1.0/zh-hans/System.Collections.xml", "ref/netstandard1.0/zh-hant/System.Collections.xml", "ref/netstandard1.3/System.Collections.dll", "ref/netstandard1.3/System.Collections.xml", "ref/netstandard1.3/de/System.Collections.xml", "ref/netstandard1.3/es/System.Collections.xml", "ref/netstandard1.3/fr/System.Collections.xml", "ref/netstandard1.3/it/System.Collections.xml", "ref/netstandard1.3/ja/System.Collections.xml", "ref/netstandard1.3/ko/System.Collections.xml", "ref/netstandard1.3/ru/System.Collections.xml", "ref/netstandard1.3/zh-hans/System.Collections.xml", "ref/netstandard1.3/zh-hant/System.Collections.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.collections.4.3.0.nupkg.sha512", "system.collections.nuspec"]}, "System.Collections.Immutable/8.0.0": {"sha512": "AurL6Y5BA1WotzlEvVaIDpqzpIPvYnnldxru8oXJU2yFxFUy3+pNXjXd1ymO+RA0rq0+590Q8gaz2l3Sr7fmqg==", "type": "package", "path": "system.collections.immutable/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Collections.Immutable.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Collections.Immutable.targets", "lib/net462/System.Collections.Immutable.dll", "lib/net462/System.Collections.Immutable.xml", "lib/net6.0/System.Collections.Immutable.dll", "lib/net6.0/System.Collections.Immutable.xml", "lib/net7.0/System.Collections.Immutable.dll", "lib/net7.0/System.Collections.Immutable.xml", "lib/net8.0/System.Collections.Immutable.dll", "lib/net8.0/System.Collections.Immutable.xml", "lib/netstandard2.0/System.Collections.Immutable.dll", "lib/netstandard2.0/System.Collections.Immutable.xml", "system.collections.immutable.8.0.0.nupkg.sha512", "system.collections.immutable.nuspec", "useSharedDesignerContext.txt"]}, "System.ComponentModel.Annotations/4.6.0": {"sha512": "pOd+UhZ3X8xfwKDlgAzowUJNjp8VYVmOHZm++vCd0kq1HZ0zK3mNo2yRXjYgv7Ik/Xi43fmJfND2PLEsQSALCg==", "type": "package", "path": "system.componentmodel.annotations/4.6.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net461/System.ComponentModel.Annotations.dll", "lib/netcore50/System.ComponentModel.Annotations.dll", "lib/netstandard1.4/System.ComponentModel.Annotations.dll", "lib/netstandard2.0/System.ComponentModel.Annotations.dll", "lib/netstandard2.1/System.ComponentModel.Annotations.dll", "lib/netstandard2.1/System.ComponentModel.Annotations.xml", "lib/portable-net45+win8/_._", "lib/win8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net461/System.ComponentModel.Annotations.dll", "ref/net461/System.ComponentModel.Annotations.xml", "ref/netcore50/System.ComponentModel.Annotations.dll", "ref/netcore50/System.ComponentModel.Annotations.xml", "ref/netcore50/de/System.ComponentModel.Annotations.xml", "ref/netcore50/es/System.ComponentModel.Annotations.xml", "ref/netcore50/fr/System.ComponentModel.Annotations.xml", "ref/netcore50/it/System.ComponentModel.Annotations.xml", "ref/netcore50/ja/System.ComponentModel.Annotations.xml", "ref/netcore50/ko/System.ComponentModel.Annotations.xml", "ref/netcore50/ru/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hans/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/System.ComponentModel.Annotations.dll", "ref/netstandard1.1/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/System.ComponentModel.Annotations.dll", "ref/netstandard1.3/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/System.ComponentModel.Annotations.dll", "ref/netstandard1.4/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard2.0/System.ComponentModel.Annotations.dll", "ref/netstandard2.0/System.ComponentModel.Annotations.xml", "ref/netstandard2.1/System.ComponentModel.Annotations.dll", "ref/netstandard2.1/System.ComponentModel.Annotations.xml", "ref/portable-net45+win8/_._", "ref/win8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.annotations.4.6.0.nupkg.sha512", "system.componentmodel.annotations.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Diagnostics.Debug/4.3.0": {"sha512": "ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "type": "package", "path": "system.diagnostics.debug/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Diagnostics.Debug.dll", "ref/netcore50/System.Diagnostics.Debug.xml", "ref/netcore50/de/System.Diagnostics.Debug.xml", "ref/netcore50/es/System.Diagnostics.Debug.xml", "ref/netcore50/fr/System.Diagnostics.Debug.xml", "ref/netcore50/it/System.Diagnostics.Debug.xml", "ref/netcore50/ja/System.Diagnostics.Debug.xml", "ref/netcore50/ko/System.Diagnostics.Debug.xml", "ref/netcore50/ru/System.Diagnostics.Debug.xml", "ref/netcore50/zh-hans/System.Diagnostics.Debug.xml", "ref/netcore50/zh-hant/System.Diagnostics.Debug.xml", "ref/netstandard1.0/System.Diagnostics.Debug.dll", "ref/netstandard1.0/System.Diagnostics.Debug.xml", "ref/netstandard1.0/de/System.Diagnostics.Debug.xml", "ref/netstandard1.0/es/System.Diagnostics.Debug.xml", "ref/netstandard1.0/fr/System.Diagnostics.Debug.xml", "ref/netstandard1.0/it/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ja/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ko/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ru/System.Diagnostics.Debug.xml", "ref/netstandard1.0/zh-hans/System.Diagnostics.Debug.xml", "ref/netstandard1.0/zh-hant/System.Diagnostics.Debug.xml", "ref/netstandard1.3/System.Diagnostics.Debug.dll", "ref/netstandard1.3/System.Diagnostics.Debug.xml", "ref/netstandard1.3/de/System.Diagnostics.Debug.xml", "ref/netstandard1.3/es/System.Diagnostics.Debug.xml", "ref/netstandard1.3/fr/System.Diagnostics.Debug.xml", "ref/netstandard1.3/it/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ja/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ko/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ru/System.Diagnostics.Debug.xml", "ref/netstandard1.3/zh-hans/System.Diagnostics.Debug.xml", "ref/netstandard1.3/zh-hant/System.Diagnostics.Debug.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.diagnostics.debug.4.3.0.nupkg.sha512", "system.diagnostics.debug.nuspec"]}, "System.Diagnostics.DiagnosticSource/8.0.0": {"sha512": "c9xLpVz6PL9lp/djOWtk5KPDZq3cSYpmXoJQY524EOtuFl5z9ZtsotpsyrDW40U1DRnQSYvcPKEUV0X//u6gkQ==", "type": "package", "path": "system.diagnostics.diagnosticsource/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.DiagnosticSource.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.DiagnosticSource.targets", "lib/net462/System.Diagnostics.DiagnosticSource.dll", "lib/net462/System.Diagnostics.DiagnosticSource.xml", "lib/net6.0/System.Diagnostics.DiagnosticSource.dll", "lib/net6.0/System.Diagnostics.DiagnosticSource.xml", "lib/net7.0/System.Diagnostics.DiagnosticSource.dll", "lib/net7.0/System.Diagnostics.DiagnosticSource.xml", "lib/net8.0/System.Diagnostics.DiagnosticSource.dll", "lib/net8.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.8.0.0.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt"]}, "System.Globalization/4.3.0": {"sha512": "kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "type": "package", "path": "system.globalization/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Globalization.dll", "ref/netcore50/System.Globalization.xml", "ref/netcore50/de/System.Globalization.xml", "ref/netcore50/es/System.Globalization.xml", "ref/netcore50/fr/System.Globalization.xml", "ref/netcore50/it/System.Globalization.xml", "ref/netcore50/ja/System.Globalization.xml", "ref/netcore50/ko/System.Globalization.xml", "ref/netcore50/ru/System.Globalization.xml", "ref/netcore50/zh-hans/System.Globalization.xml", "ref/netcore50/zh-hant/System.Globalization.xml", "ref/netstandard1.0/System.Globalization.dll", "ref/netstandard1.0/System.Globalization.xml", "ref/netstandard1.0/de/System.Globalization.xml", "ref/netstandard1.0/es/System.Globalization.xml", "ref/netstandard1.0/fr/System.Globalization.xml", "ref/netstandard1.0/it/System.Globalization.xml", "ref/netstandard1.0/ja/System.Globalization.xml", "ref/netstandard1.0/ko/System.Globalization.xml", "ref/netstandard1.0/ru/System.Globalization.xml", "ref/netstandard1.0/zh-hans/System.Globalization.xml", "ref/netstandard1.0/zh-hant/System.Globalization.xml", "ref/netstandard1.3/System.Globalization.dll", "ref/netstandard1.3/System.Globalization.xml", "ref/netstandard1.3/de/System.Globalization.xml", "ref/netstandard1.3/es/System.Globalization.xml", "ref/netstandard1.3/fr/System.Globalization.xml", "ref/netstandard1.3/it/System.Globalization.xml", "ref/netstandard1.3/ja/System.Globalization.xml", "ref/netstandard1.3/ko/System.Globalization.xml", "ref/netstandard1.3/ru/System.Globalization.xml", "ref/netstandard1.3/zh-hans/System.Globalization.xml", "ref/netstandard1.3/zh-hant/System.Globalization.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.globalization.4.3.0.nupkg.sha512", "system.globalization.nuspec"]}, "System.IdentityModel.Tokens.Jwt/7.0.3": {"sha512": "caEe+OpQNYNiyZb+DJpUVROXoVySWBahko2ooNfUcllxa9ZQUM8CgM/mDjP6AoFn6cQU9xMmG+jivXWub8cbGg==", "type": "package", "path": "system.identitymodel.tokens.jwt/7.0.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/System.IdentityModel.Tokens.Jwt.dll", "lib/net461/System.IdentityModel.Tokens.Jwt.xml", "lib/net462/System.IdentityModel.Tokens.Jwt.dll", "lib/net462/System.IdentityModel.Tokens.Jwt.xml", "lib/net472/System.IdentityModel.Tokens.Jwt.dll", "lib/net472/System.IdentityModel.Tokens.Jwt.xml", "lib/net6.0/System.IdentityModel.Tokens.Jwt.dll", "lib/net6.0/System.IdentityModel.Tokens.Jwt.xml", "lib/net8.0/System.IdentityModel.Tokens.Jwt.dll", "lib/net8.0/System.IdentityModel.Tokens.Jwt.xml", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.xml", "system.identitymodel.tokens.jwt.7.0.3.nupkg.sha512", "system.identitymodel.tokens.jwt.nuspec"]}, "System.IO/4.3.0": {"sha512": "3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "type": "package", "path": "system.io/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.IO.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.IO.dll", "ref/netcore50/System.IO.dll", "ref/netcore50/System.IO.xml", "ref/netcore50/de/System.IO.xml", "ref/netcore50/es/System.IO.xml", "ref/netcore50/fr/System.IO.xml", "ref/netcore50/it/System.IO.xml", "ref/netcore50/ja/System.IO.xml", "ref/netcore50/ko/System.IO.xml", "ref/netcore50/ru/System.IO.xml", "ref/netcore50/zh-hans/System.IO.xml", "ref/netcore50/zh-hant/System.IO.xml", "ref/netstandard1.0/System.IO.dll", "ref/netstandard1.0/System.IO.xml", "ref/netstandard1.0/de/System.IO.xml", "ref/netstandard1.0/es/System.IO.xml", "ref/netstandard1.0/fr/System.IO.xml", "ref/netstandard1.0/it/System.IO.xml", "ref/netstandard1.0/ja/System.IO.xml", "ref/netstandard1.0/ko/System.IO.xml", "ref/netstandard1.0/ru/System.IO.xml", "ref/netstandard1.0/zh-hans/System.IO.xml", "ref/netstandard1.0/zh-hant/System.IO.xml", "ref/netstandard1.3/System.IO.dll", "ref/netstandard1.3/System.IO.xml", "ref/netstandard1.3/de/System.IO.xml", "ref/netstandard1.3/es/System.IO.xml", "ref/netstandard1.3/fr/System.IO.xml", "ref/netstandard1.3/it/System.IO.xml", "ref/netstandard1.3/ja/System.IO.xml", "ref/netstandard1.3/ko/System.IO.xml", "ref/netstandard1.3/ru/System.IO.xml", "ref/netstandard1.3/zh-hans/System.IO.xml", "ref/netstandard1.3/zh-hant/System.IO.xml", "ref/netstandard1.5/System.IO.dll", "ref/netstandard1.5/System.IO.xml", "ref/netstandard1.5/de/System.IO.xml", "ref/netstandard1.5/es/System.IO.xml", "ref/netstandard1.5/fr/System.IO.xml", "ref/netstandard1.5/it/System.IO.xml", "ref/netstandard1.5/ja/System.IO.xml", "ref/netstandard1.5/ko/System.IO.xml", "ref/netstandard1.5/ru/System.IO.xml", "ref/netstandard1.5/zh-hans/System.IO.xml", "ref/netstandard1.5/zh-hant/System.IO.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.4.3.0.nupkg.sha512", "system.io.nuspec"]}, "System.Linq/4.3.0": {"sha512": "5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "type": "package", "path": "system.linq/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Linq.dll", "lib/netcore50/System.Linq.dll", "lib/netstandard1.6/System.Linq.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Linq.dll", "ref/netcore50/System.Linq.dll", "ref/netcore50/System.Linq.xml", "ref/netcore50/de/System.Linq.xml", "ref/netcore50/es/System.Linq.xml", "ref/netcore50/fr/System.Linq.xml", "ref/netcore50/it/System.Linq.xml", "ref/netcore50/ja/System.Linq.xml", "ref/netcore50/ko/System.Linq.xml", "ref/netcore50/ru/System.Linq.xml", "ref/netcore50/zh-hans/System.Linq.xml", "ref/netcore50/zh-hant/System.Linq.xml", "ref/netstandard1.0/System.Linq.dll", "ref/netstandard1.0/System.Linq.xml", "ref/netstandard1.0/de/System.Linq.xml", "ref/netstandard1.0/es/System.Linq.xml", "ref/netstandard1.0/fr/System.Linq.xml", "ref/netstandard1.0/it/System.Linq.xml", "ref/netstandard1.0/ja/System.Linq.xml", "ref/netstandard1.0/ko/System.Linq.xml", "ref/netstandard1.0/ru/System.Linq.xml", "ref/netstandard1.0/zh-hans/System.Linq.xml", "ref/netstandard1.0/zh-hant/System.Linq.xml", "ref/netstandard1.6/System.Linq.dll", "ref/netstandard1.6/System.Linq.xml", "ref/netstandard1.6/de/System.Linq.xml", "ref/netstandard1.6/es/System.Linq.xml", "ref/netstandard1.6/fr/System.Linq.xml", "ref/netstandard1.6/it/System.Linq.xml", "ref/netstandard1.6/ja/System.Linq.xml", "ref/netstandard1.6/ko/System.Linq.xml", "ref/netstandard1.6/ru/System.Linq.xml", "ref/netstandard1.6/zh-hans/System.Linq.xml", "ref/netstandard1.6/zh-hant/System.Linq.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.linq.4.3.0.nupkg.sha512", "system.linq.nuspec"]}, "System.Linq.Dynamic.Core/1.3.5": {"sha512": "G8aRAVHSQ/Jmt6dekX+z4K3L6ecX3CizaIYSQoDUolPRIPBYmBCabT+/pq5fMAFM57aOchFn8PUufuVfLjvEZQ==", "type": "package", "path": "system.linq.dynamic.core/1.3.5", "files": [".nupkg.metadata", ".signature.p7s", "PackageReadme.md", "lib/net35/System.Linq.Dynamic.Core.dll", "lib/net35/System.Linq.Dynamic.Core.pdb", "lib/net35/System.Linq.Dynamic.Core.xml", "lib/net40/System.Linq.Dynamic.Core.dll", "lib/net40/System.Linq.Dynamic.Core.pdb", "lib/net40/System.Linq.Dynamic.Core.xml", "lib/net45/System.Linq.Dynamic.Core.dll", "lib/net45/System.Linq.Dynamic.Core.pdb", "lib/net45/System.Linq.Dynamic.Core.xml", "lib/net452/System.Linq.Dynamic.Core.dll", "lib/net452/System.Linq.Dynamic.Core.pdb", "lib/net452/System.Linq.Dynamic.Core.xml", "lib/net46/System.Linq.Dynamic.Core.dll", "lib/net46/System.Linq.Dynamic.Core.pdb", "lib/net46/System.Linq.Dynamic.Core.xml", "lib/net5.0/System.Linq.Dynamic.Core.dll", "lib/net5.0/System.Linq.Dynamic.Core.pdb", "lib/net5.0/System.Linq.Dynamic.Core.xml", "lib/net6.0/System.Linq.Dynamic.Core.dll", "lib/net6.0/System.Linq.Dynamic.Core.pdb", "lib/net6.0/System.Linq.Dynamic.Core.xml", "lib/net7.0/System.Linq.Dynamic.Core.dll", "lib/net7.0/System.Linq.Dynamic.Core.pdb", "lib/net7.0/System.Linq.Dynamic.Core.xml", "lib/netcoreapp2.1/System.Linq.Dynamic.Core.dll", "lib/netcoreapp2.1/System.Linq.Dynamic.Core.pdb", "lib/netcoreapp2.1/System.Linq.Dynamic.Core.xml", "lib/netcoreapp3.1/System.Linq.Dynamic.Core.dll", "lib/netcoreapp3.1/System.Linq.Dynamic.Core.pdb", "lib/netcoreapp3.1/System.Linq.Dynamic.Core.xml", "lib/netstandard1.3/System.Linq.Dynamic.Core.dll", "lib/netstandard1.3/System.Linq.Dynamic.Core.pdb", "lib/netstandard1.3/System.Linq.Dynamic.Core.xml", "lib/netstandard2.0/System.Linq.Dynamic.Core.dll", "lib/netstandard2.0/System.Linq.Dynamic.Core.pdb", "lib/netstandard2.0/System.Linq.Dynamic.Core.xml", "lib/netstandard2.1/System.Linq.Dynamic.Core.dll", "lib/netstandard2.1/System.Linq.Dynamic.Core.pdb", "lib/netstandard2.1/System.Linq.Dynamic.Core.xml", "lib/uap10.0.10240/System.Linq.Dynamic.Core.dll", "lib/uap10.0.10240/System.Linq.Dynamic.Core.pdb", "lib/uap10.0.10240/System.Linq.Dynamic.Core.pri", "lib/uap10.0.10240/System.Linq.Dynamic.Core.xml", "logo.png", "system.linq.dynamic.core.1.3.5.nupkg.sha512", "system.linq.dynamic.core.nuspec"]}, "System.Linq.Expressions/4.3.0": {"sha512": "PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "type": "package", "path": "system.linq.expressions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Linq.Expressions.dll", "lib/netcore50/System.Linq.Expressions.dll", "lib/netstandard1.6/System.Linq.Expressions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Linq.Expressions.dll", "ref/netcore50/System.Linq.Expressions.dll", "ref/netcore50/System.Linq.Expressions.xml", "ref/netcore50/de/System.Linq.Expressions.xml", "ref/netcore50/es/System.Linq.Expressions.xml", "ref/netcore50/fr/System.Linq.Expressions.xml", "ref/netcore50/it/System.Linq.Expressions.xml", "ref/netcore50/ja/System.Linq.Expressions.xml", "ref/netcore50/ko/System.Linq.Expressions.xml", "ref/netcore50/ru/System.Linq.Expressions.xml", "ref/netcore50/zh-hans/System.Linq.Expressions.xml", "ref/netcore50/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.0/System.Linq.Expressions.dll", "ref/netstandard1.0/System.Linq.Expressions.xml", "ref/netstandard1.0/de/System.Linq.Expressions.xml", "ref/netstandard1.0/es/System.Linq.Expressions.xml", "ref/netstandard1.0/fr/System.Linq.Expressions.xml", "ref/netstandard1.0/it/System.Linq.Expressions.xml", "ref/netstandard1.0/ja/System.Linq.Expressions.xml", "ref/netstandard1.0/ko/System.Linq.Expressions.xml", "ref/netstandard1.0/ru/System.Linq.Expressions.xml", "ref/netstandard1.0/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.0/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.3/System.Linq.Expressions.dll", "ref/netstandard1.3/System.Linq.Expressions.xml", "ref/netstandard1.3/de/System.Linq.Expressions.xml", "ref/netstandard1.3/es/System.Linq.Expressions.xml", "ref/netstandard1.3/fr/System.Linq.Expressions.xml", "ref/netstandard1.3/it/System.Linq.Expressions.xml", "ref/netstandard1.3/ja/System.Linq.Expressions.xml", "ref/netstandard1.3/ko/System.Linq.Expressions.xml", "ref/netstandard1.3/ru/System.Linq.Expressions.xml", "ref/netstandard1.3/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.3/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.6/System.Linq.Expressions.dll", "ref/netstandard1.6/System.Linq.Expressions.xml", "ref/netstandard1.6/de/System.Linq.Expressions.xml", "ref/netstandard1.6/es/System.Linq.Expressions.xml", "ref/netstandard1.6/fr/System.Linq.Expressions.xml", "ref/netstandard1.6/it/System.Linq.Expressions.xml", "ref/netstandard1.6/ja/System.Linq.Expressions.xml", "ref/netstandard1.6/ko/System.Linq.Expressions.xml", "ref/netstandard1.6/ru/System.Linq.Expressions.xml", "ref/netstandard1.6/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.6/zh-hant/System.Linq.Expressions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Linq.Expressions.dll", "system.linq.expressions.4.3.0.nupkg.sha512", "system.linq.expressions.nuspec"]}, "System.Linq.Queryable/4.3.0": {"sha512": "In1Bmmvl/j52yPu3xgakQSI0YIckPUr870w4K5+Lak3JCCa8hl+my65lABOuKfYs4ugmZy25ScFerC4nz8+b6g==", "type": "package", "path": "system.linq.queryable/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/monoandroid10/_._", "lib/monotouch10/_._", "lib/net45/_._", "lib/netcore50/System.Linq.Queryable.dll", "lib/netstandard1.3/System.Linq.Queryable.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/monoandroid10/_._", "ref/monotouch10/_._", "ref/net45/_._", "ref/netcore50/System.Linq.Queryable.dll", "ref/netcore50/System.Linq.Queryable.xml", "ref/netcore50/de/System.Linq.Queryable.xml", "ref/netcore50/es/System.Linq.Queryable.xml", "ref/netcore50/fr/System.Linq.Queryable.xml", "ref/netcore50/it/System.Linq.Queryable.xml", "ref/netcore50/ja/System.Linq.Queryable.xml", "ref/netcore50/ko/System.Linq.Queryable.xml", "ref/netcore50/ru/System.Linq.Queryable.xml", "ref/netcore50/zh-hans/System.Linq.Queryable.xml", "ref/netcore50/zh-hant/System.Linq.Queryable.xml", "ref/netstandard1.0/System.Linq.Queryable.dll", "ref/netstandard1.0/System.Linq.Queryable.xml", "ref/netstandard1.0/de/System.Linq.Queryable.xml", "ref/netstandard1.0/es/System.Linq.Queryable.xml", "ref/netstandard1.0/fr/System.Linq.Queryable.xml", "ref/netstandard1.0/it/System.Linq.Queryable.xml", "ref/netstandard1.0/ja/System.Linq.Queryable.xml", "ref/netstandard1.0/ko/System.Linq.Queryable.xml", "ref/netstandard1.0/ru/System.Linq.Queryable.xml", "ref/netstandard1.0/zh-hans/System.Linq.Queryable.xml", "ref/netstandard1.0/zh-hant/System.Linq.Queryable.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.linq.queryable.4.3.0.nupkg.sha512", "system.linq.queryable.nuspec"]}, "System.Memory/4.5.4": {"sha512": "1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "type": "package", "path": "system.memory/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.4.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ObjectModel/4.3.0": {"sha512": "bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "type": "package", "path": "system.objectmodel/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.ObjectModel.dll", "lib/netstandard1.3/System.ObjectModel.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.ObjectModel.dll", "ref/netcore50/System.ObjectModel.xml", "ref/netcore50/de/System.ObjectModel.xml", "ref/netcore50/es/System.ObjectModel.xml", "ref/netcore50/fr/System.ObjectModel.xml", "ref/netcore50/it/System.ObjectModel.xml", "ref/netcore50/ja/System.ObjectModel.xml", "ref/netcore50/ko/System.ObjectModel.xml", "ref/netcore50/ru/System.ObjectModel.xml", "ref/netcore50/zh-hans/System.ObjectModel.xml", "ref/netcore50/zh-hant/System.ObjectModel.xml", "ref/netstandard1.0/System.ObjectModel.dll", "ref/netstandard1.0/System.ObjectModel.xml", "ref/netstandard1.0/de/System.ObjectModel.xml", "ref/netstandard1.0/es/System.ObjectModel.xml", "ref/netstandard1.0/fr/System.ObjectModel.xml", "ref/netstandard1.0/it/System.ObjectModel.xml", "ref/netstandard1.0/ja/System.ObjectModel.xml", "ref/netstandard1.0/ko/System.ObjectModel.xml", "ref/netstandard1.0/ru/System.ObjectModel.xml", "ref/netstandard1.0/zh-hans/System.ObjectModel.xml", "ref/netstandard1.0/zh-hant/System.ObjectModel.xml", "ref/netstandard1.3/System.ObjectModel.dll", "ref/netstandard1.3/System.ObjectModel.xml", "ref/netstandard1.3/de/System.ObjectModel.xml", "ref/netstandard1.3/es/System.ObjectModel.xml", "ref/netstandard1.3/fr/System.ObjectModel.xml", "ref/netstandard1.3/it/System.ObjectModel.xml", "ref/netstandard1.3/ja/System.ObjectModel.xml", "ref/netstandard1.3/ko/System.ObjectModel.xml", "ref/netstandard1.3/ru/System.ObjectModel.xml", "ref/netstandard1.3/zh-hans/System.ObjectModel.xml", "ref/netstandard1.3/zh-hant/System.ObjectModel.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.objectmodel.4.3.0.nupkg.sha512", "system.objectmodel.nuspec"]}, "System.Reflection/4.3.0": {"sha512": "KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "type": "package", "path": "system.reflection/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Reflection.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Reflection.dll", "ref/netcore50/System.Reflection.dll", "ref/netcore50/System.Reflection.xml", "ref/netcore50/de/System.Reflection.xml", "ref/netcore50/es/System.Reflection.xml", "ref/netcore50/fr/System.Reflection.xml", "ref/netcore50/it/System.Reflection.xml", "ref/netcore50/ja/System.Reflection.xml", "ref/netcore50/ko/System.Reflection.xml", "ref/netcore50/ru/System.Reflection.xml", "ref/netcore50/zh-hans/System.Reflection.xml", "ref/netcore50/zh-hant/System.Reflection.xml", "ref/netstandard1.0/System.Reflection.dll", "ref/netstandard1.0/System.Reflection.xml", "ref/netstandard1.0/de/System.Reflection.xml", "ref/netstandard1.0/es/System.Reflection.xml", "ref/netstandard1.0/fr/System.Reflection.xml", "ref/netstandard1.0/it/System.Reflection.xml", "ref/netstandard1.0/ja/System.Reflection.xml", "ref/netstandard1.0/ko/System.Reflection.xml", "ref/netstandard1.0/ru/System.Reflection.xml", "ref/netstandard1.0/zh-hans/System.Reflection.xml", "ref/netstandard1.0/zh-hant/System.Reflection.xml", "ref/netstandard1.3/System.Reflection.dll", "ref/netstandard1.3/System.Reflection.xml", "ref/netstandard1.3/de/System.Reflection.xml", "ref/netstandard1.3/es/System.Reflection.xml", "ref/netstandard1.3/fr/System.Reflection.xml", "ref/netstandard1.3/it/System.Reflection.xml", "ref/netstandard1.3/ja/System.Reflection.xml", "ref/netstandard1.3/ko/System.Reflection.xml", "ref/netstandard1.3/ru/System.Reflection.xml", "ref/netstandard1.3/zh-hans/System.Reflection.xml", "ref/netstandard1.3/zh-hant/System.Reflection.xml", "ref/netstandard1.5/System.Reflection.dll", "ref/netstandard1.5/System.Reflection.xml", "ref/netstandard1.5/de/System.Reflection.xml", "ref/netstandard1.5/es/System.Reflection.xml", "ref/netstandard1.5/fr/System.Reflection.xml", "ref/netstandard1.5/it/System.Reflection.xml", "ref/netstandard1.5/ja/System.Reflection.xml", "ref/netstandard1.5/ko/System.Reflection.xml", "ref/netstandard1.5/ru/System.Reflection.xml", "ref/netstandard1.5/zh-hans/System.Reflection.xml", "ref/netstandard1.5/zh-hant/System.Reflection.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.4.3.0.nupkg.sha512", "system.reflection.nuspec"]}, "System.Reflection.Emit/4.3.0": {"sha512": "228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "type": "package", "path": "system.reflection.emit/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/monotouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.dll", "lib/netstandard1.3/System.Reflection.Emit.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/net45/_._", "ref/netstandard1.1/System.Reflection.Emit.dll", "ref/netstandard1.1/System.Reflection.Emit.xml", "ref/netstandard1.1/de/System.Reflection.Emit.xml", "ref/netstandard1.1/es/System.Reflection.Emit.xml", "ref/netstandard1.1/fr/System.Reflection.Emit.xml", "ref/netstandard1.1/it/System.Reflection.Emit.xml", "ref/netstandard1.1/ja/System.Reflection.Emit.xml", "ref/netstandard1.1/ko/System.Reflection.Emit.xml", "ref/netstandard1.1/ru/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hans/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hant/System.Reflection.Emit.xml", "ref/xamarinmac20/_._", "system.reflection.emit.4.3.0.nupkg.sha512", "system.reflection.emit.nuspec"]}, "System.Reflection.Emit.ILGeneration/4.3.0": {"sha512": "59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "type": "package", "path": "system.reflection.emit.ilgeneration/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.ILGeneration.dll", "lib/netstandard1.3/System.Reflection.Emit.ILGeneration.dll", "lib/portable-net45+wp8/_._", "lib/wp80/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netstandard1.0/System.Reflection.Emit.ILGeneration.dll", "ref/netstandard1.0/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/de/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/es/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/fr/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/it/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ja/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ko/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ru/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Emit.ILGeneration.xml", "ref/portable-net45+wp8/_._", "ref/wp80/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/_._", "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "system.reflection.emit.ilgeneration.nuspec"]}, "System.Reflection.Emit.Lightweight/4.3.0": {"sha512": "oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "type": "package", "path": "system.reflection.emit.lightweight/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.Lightweight.dll", "lib/netstandard1.3/System.Reflection.Emit.Lightweight.dll", "lib/portable-net45+wp8/_._", "lib/wp80/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.dll", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/de/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/es/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/fr/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/it/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ja/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ko/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ru/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Emit.Lightweight.xml", "ref/portable-net45+wp8/_._", "ref/wp80/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/_._", "system.reflection.emit.lightweight.4.3.0.nupkg.sha512", "system.reflection.emit.lightweight.nuspec"]}, "System.Reflection.Extensions/4.3.0": {"sha512": "rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "type": "package", "path": "system.reflection.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Extensions.dll", "ref/netcore50/System.Reflection.Extensions.xml", "ref/netcore50/de/System.Reflection.Extensions.xml", "ref/netcore50/es/System.Reflection.Extensions.xml", "ref/netcore50/fr/System.Reflection.Extensions.xml", "ref/netcore50/it/System.Reflection.Extensions.xml", "ref/netcore50/ja/System.Reflection.Extensions.xml", "ref/netcore50/ko/System.Reflection.Extensions.xml", "ref/netcore50/ru/System.Reflection.Extensions.xml", "ref/netcore50/zh-hans/System.Reflection.Extensions.xml", "ref/netcore50/zh-hant/System.Reflection.Extensions.xml", "ref/netstandard1.0/System.Reflection.Extensions.dll", "ref/netstandard1.0/System.Reflection.Extensions.xml", "ref/netstandard1.0/de/System.Reflection.Extensions.xml", "ref/netstandard1.0/es/System.Reflection.Extensions.xml", "ref/netstandard1.0/fr/System.Reflection.Extensions.xml", "ref/netstandard1.0/it/System.Reflection.Extensions.xml", "ref/netstandard1.0/ja/System.Reflection.Extensions.xml", "ref/netstandard1.0/ko/System.Reflection.Extensions.xml", "ref/netstandard1.0/ru/System.Reflection.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.extensions.4.3.0.nupkg.sha512", "system.reflection.extensions.nuspec"]}, "System.Reflection.Metadata/5.0.0": {"sha512": "5NecZgXktdGg34rh1OenY1rFNDCI8xSjFr+Z4OU4cU06AQHUdRnIIEeWENu3Wl4YowbzkymAIMvi3WyK9U53pQ==", "type": "package", "path": "system.reflection.metadata/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Reflection.Metadata.dll", "lib/net461/System.Reflection.Metadata.xml", "lib/netstandard1.1/System.Reflection.Metadata.dll", "lib/netstandard1.1/System.Reflection.Metadata.xml", "lib/netstandard2.0/System.Reflection.Metadata.dll", "lib/netstandard2.0/System.Reflection.Metadata.xml", "lib/portable-net45+win8/System.Reflection.Metadata.dll", "lib/portable-net45+win8/System.Reflection.Metadata.xml", "system.reflection.metadata.5.0.0.nupkg.sha512", "system.reflection.metadata.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection.Primitives/4.3.0": {"sha512": "5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "type": "package", "path": "system.reflection.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Primitives.dll", "ref/netcore50/System.Reflection.Primitives.xml", "ref/netcore50/de/System.Reflection.Primitives.xml", "ref/netcore50/es/System.Reflection.Primitives.xml", "ref/netcore50/fr/System.Reflection.Primitives.xml", "ref/netcore50/it/System.Reflection.Primitives.xml", "ref/netcore50/ja/System.Reflection.Primitives.xml", "ref/netcore50/ko/System.Reflection.Primitives.xml", "ref/netcore50/ru/System.Reflection.Primitives.xml", "ref/netcore50/zh-hans/System.Reflection.Primitives.xml", "ref/netcore50/zh-hant/System.Reflection.Primitives.xml", "ref/netstandard1.0/System.Reflection.Primitives.dll", "ref/netstandard1.0/System.Reflection.Primitives.xml", "ref/netstandard1.0/de/System.Reflection.Primitives.xml", "ref/netstandard1.0/es/System.Reflection.Primitives.xml", "ref/netstandard1.0/fr/System.Reflection.Primitives.xml", "ref/netstandard1.0/it/System.Reflection.Primitives.xml", "ref/netstandard1.0/ja/System.Reflection.Primitives.xml", "ref/netstandard1.0/ko/System.Reflection.Primitives.xml", "ref/netstandard1.0/ru/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.primitives.4.3.0.nupkg.sha512", "system.reflection.primitives.nuspec"]}, "System.Reflection.TypeExtensions/4.3.0": {"sha512": "7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "type": "package", "path": "system.reflection.typeextensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Reflection.TypeExtensions.dll", "lib/net462/System.Reflection.TypeExtensions.dll", "lib/netcore50/System.Reflection.TypeExtensions.dll", "lib/netstandard1.5/System.Reflection.TypeExtensions.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Reflection.TypeExtensions.dll", "ref/net462/System.Reflection.TypeExtensions.dll", "ref/netstandard1.3/System.Reflection.TypeExtensions.dll", "ref/netstandard1.3/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/de/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/es/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/fr/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/it/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ja/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ko/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ru/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/zh-hans/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/zh-hant/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/System.Reflection.TypeExtensions.dll", "ref/netstandard1.5/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/de/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/es/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/fr/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/it/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ja/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ko/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ru/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/zh-hans/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/zh-hant/System.Reflection.TypeExtensions.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Reflection.TypeExtensions.dll", "system.reflection.typeextensions.4.3.0.nupkg.sha512", "system.reflection.typeextensions.nuspec"]}, "System.Resources.ResourceManager/4.3.0": {"sha512": "/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "type": "package", "path": "system.resources.resourcemanager/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Resources.ResourceManager.dll", "ref/netcore50/System.Resources.ResourceManager.xml", "ref/netcore50/de/System.Resources.ResourceManager.xml", "ref/netcore50/es/System.Resources.ResourceManager.xml", "ref/netcore50/fr/System.Resources.ResourceManager.xml", "ref/netcore50/it/System.Resources.ResourceManager.xml", "ref/netcore50/ja/System.Resources.ResourceManager.xml", "ref/netcore50/ko/System.Resources.ResourceManager.xml", "ref/netcore50/ru/System.Resources.ResourceManager.xml", "ref/netcore50/zh-hans/System.Resources.ResourceManager.xml", "ref/netcore50/zh-hant/System.Resources.ResourceManager.xml", "ref/netstandard1.0/System.Resources.ResourceManager.dll", "ref/netstandard1.0/System.Resources.ResourceManager.xml", "ref/netstandard1.0/de/System.Resources.ResourceManager.xml", "ref/netstandard1.0/es/System.Resources.ResourceManager.xml", "ref/netstandard1.0/fr/System.Resources.ResourceManager.xml", "ref/netstandard1.0/it/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ja/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ko/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ru/System.Resources.ResourceManager.xml", "ref/netstandard1.0/zh-hans/System.Resources.ResourceManager.xml", "ref/netstandard1.0/zh-hant/System.Resources.ResourceManager.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.resources.resourcemanager.4.3.0.nupkg.sha512", "system.resources.resourcemanager.nuspec"]}, "System.Runtime/4.3.0": {"sha512": "JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "type": "package", "path": "system.runtime/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.dll", "lib/portable-net45+win8+wp80+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.dll", "ref/netcore50/System.Runtime.dll", "ref/netcore50/System.Runtime.xml", "ref/netcore50/de/System.Runtime.xml", "ref/netcore50/es/System.Runtime.xml", "ref/netcore50/fr/System.Runtime.xml", "ref/netcore50/it/System.Runtime.xml", "ref/netcore50/ja/System.Runtime.xml", "ref/netcore50/ko/System.Runtime.xml", "ref/netcore50/ru/System.Runtime.xml", "ref/netcore50/zh-hans/System.Runtime.xml", "ref/netcore50/zh-hant/System.Runtime.xml", "ref/netstandard1.0/System.Runtime.dll", "ref/netstandard1.0/System.Runtime.xml", "ref/netstandard1.0/de/System.Runtime.xml", "ref/netstandard1.0/es/System.Runtime.xml", "ref/netstandard1.0/fr/System.Runtime.xml", "ref/netstandard1.0/it/System.Runtime.xml", "ref/netstandard1.0/ja/System.Runtime.xml", "ref/netstandard1.0/ko/System.Runtime.xml", "ref/netstandard1.0/ru/System.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.xml", "ref/netstandard1.2/System.Runtime.dll", "ref/netstandard1.2/System.Runtime.xml", "ref/netstandard1.2/de/System.Runtime.xml", "ref/netstandard1.2/es/System.Runtime.xml", "ref/netstandard1.2/fr/System.Runtime.xml", "ref/netstandard1.2/it/System.Runtime.xml", "ref/netstandard1.2/ja/System.Runtime.xml", "ref/netstandard1.2/ko/System.Runtime.xml", "ref/netstandard1.2/ru/System.Runtime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.xml", "ref/netstandard1.3/System.Runtime.dll", "ref/netstandard1.3/System.Runtime.xml", "ref/netstandard1.3/de/System.Runtime.xml", "ref/netstandard1.3/es/System.Runtime.xml", "ref/netstandard1.3/fr/System.Runtime.xml", "ref/netstandard1.3/it/System.Runtime.xml", "ref/netstandard1.3/ja/System.Runtime.xml", "ref/netstandard1.3/ko/System.Runtime.xml", "ref/netstandard1.3/ru/System.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Runtime.xml", "ref/netstandard1.5/System.Runtime.dll", "ref/netstandard1.5/System.Runtime.xml", "ref/netstandard1.5/de/System.Runtime.xml", "ref/netstandard1.5/es/System.Runtime.xml", "ref/netstandard1.5/fr/System.Runtime.xml", "ref/netstandard1.5/it/System.Runtime.xml", "ref/netstandard1.5/ja/System.Runtime.xml", "ref/netstandard1.5/ko/System.Runtime.xml", "ref/netstandard1.5/ru/System.Runtime.xml", "ref/netstandard1.5/zh-hans/System.Runtime.xml", "ref/netstandard1.5/zh-hant/System.Runtime.xml", "ref/portable-net45+win8+wp80+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.4.3.0.nupkg.sha512", "system.runtime.nuspec"]}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"sha512": "ZD9TMpsmYJLrxbbmdvhwt9YEgG5WntEnZ/d1eH8JBX9LBp+Ju8BSBhUGbZMNVHHomWo2KVImJhTDl2hIgw/6MA==", "type": "package", "path": "system.runtime.compilerservices.unsafe/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net45/System.Runtime.CompilerServices.Unsafe.dll", "lib/net45/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/net461/System.Runtime.CompilerServices.Unsafe.dll", "ref/net461/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard2.1/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard2.1/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.Extensions/4.3.0": {"sha512": "guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "type": "package", "path": "system.runtime.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.Extensions.dll", "ref/netcore50/System.Runtime.Extensions.dll", "ref/netcore50/System.Runtime.Extensions.xml", "ref/netcore50/de/System.Runtime.Extensions.xml", "ref/netcore50/es/System.Runtime.Extensions.xml", "ref/netcore50/fr/System.Runtime.Extensions.xml", "ref/netcore50/it/System.Runtime.Extensions.xml", "ref/netcore50/ja/System.Runtime.Extensions.xml", "ref/netcore50/ko/System.Runtime.Extensions.xml", "ref/netcore50/ru/System.Runtime.Extensions.xml", "ref/netcore50/zh-hans/System.Runtime.Extensions.xml", "ref/netcore50/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.0/System.Runtime.Extensions.dll", "ref/netstandard1.0/System.Runtime.Extensions.xml", "ref/netstandard1.0/de/System.Runtime.Extensions.xml", "ref/netstandard1.0/es/System.Runtime.Extensions.xml", "ref/netstandard1.0/fr/System.Runtime.Extensions.xml", "ref/netstandard1.0/it/System.Runtime.Extensions.xml", "ref/netstandard1.0/ja/System.Runtime.Extensions.xml", "ref/netstandard1.0/ko/System.Runtime.Extensions.xml", "ref/netstandard1.0/ru/System.Runtime.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.3/System.Runtime.Extensions.dll", "ref/netstandard1.3/System.Runtime.Extensions.xml", "ref/netstandard1.3/de/System.Runtime.Extensions.xml", "ref/netstandard1.3/es/System.Runtime.Extensions.xml", "ref/netstandard1.3/fr/System.Runtime.Extensions.xml", "ref/netstandard1.3/it/System.Runtime.Extensions.xml", "ref/netstandard1.3/ja/System.Runtime.Extensions.xml", "ref/netstandard1.3/ko/System.Runtime.Extensions.xml", "ref/netstandard1.3/ru/System.Runtime.Extensions.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.5/System.Runtime.Extensions.dll", "ref/netstandard1.5/System.Runtime.Extensions.xml", "ref/netstandard1.5/de/System.Runtime.Extensions.xml", "ref/netstandard1.5/es/System.Runtime.Extensions.xml", "ref/netstandard1.5/fr/System.Runtime.Extensions.xml", "ref/netstandard1.5/it/System.Runtime.Extensions.xml", "ref/netstandard1.5/ja/System.Runtime.Extensions.xml", "ref/netstandard1.5/ko/System.Runtime.Extensions.xml", "ref/netstandard1.5/ru/System.Runtime.Extensions.xml", "ref/netstandard1.5/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.5/zh-hant/System.Runtime.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.extensions.4.3.0.nupkg.sha512", "system.runtime.extensions.nuspec"]}, "System.Runtime.Loader/4.3.0": {"sha512": "DHMaRn8D8YCK2GG2pw+UzNxn/OHVfaWx7OTLBD/hPegHZZgcZh3H6seWegrC4BYwsfuGrywIuT+MQs+rPqRLTQ==", "type": "package", "path": "system.runtime.loader/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/_._", "lib/netstandard1.5/System.Runtime.Loader.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/netstandard1.5/System.Runtime.Loader.dll", "ref/netstandard1.5/System.Runtime.Loader.xml", "ref/netstandard1.5/de/System.Runtime.Loader.xml", "ref/netstandard1.5/es/System.Runtime.Loader.xml", "ref/netstandard1.5/fr/System.Runtime.Loader.xml", "ref/netstandard1.5/it/System.Runtime.Loader.xml", "ref/netstandard1.5/ja/System.Runtime.Loader.xml", "ref/netstandard1.5/ko/System.Runtime.Loader.xml", "ref/netstandard1.5/ru/System.Runtime.Loader.xml", "ref/netstandard1.5/zh-hans/System.Runtime.Loader.xml", "ref/netstandard1.5/zh-hant/System.Runtime.Loader.xml", "system.runtime.loader.4.3.0.nupkg.sha512", "system.runtime.loader.nuspec"]}, "System.Text.Encoding/4.3.0": {"sha512": "BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "type": "package", "path": "system.text.encoding/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.dll", "ref/netcore50/System.Text.Encoding.xml", "ref/netcore50/de/System.Text.Encoding.xml", "ref/netcore50/es/System.Text.Encoding.xml", "ref/netcore50/fr/System.Text.Encoding.xml", "ref/netcore50/it/System.Text.Encoding.xml", "ref/netcore50/ja/System.Text.Encoding.xml", "ref/netcore50/ko/System.Text.Encoding.xml", "ref/netcore50/ru/System.Text.Encoding.xml", "ref/netcore50/zh-hans/System.Text.Encoding.xml", "ref/netcore50/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.0/System.Text.Encoding.dll", "ref/netstandard1.0/System.Text.Encoding.xml", "ref/netstandard1.0/de/System.Text.Encoding.xml", "ref/netstandard1.0/es/System.Text.Encoding.xml", "ref/netstandard1.0/fr/System.Text.Encoding.xml", "ref/netstandard1.0/it/System.Text.Encoding.xml", "ref/netstandard1.0/ja/System.Text.Encoding.xml", "ref/netstandard1.0/ko/System.Text.Encoding.xml", "ref/netstandard1.0/ru/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.3/System.Text.Encoding.dll", "ref/netstandard1.3/System.Text.Encoding.xml", "ref/netstandard1.3/de/System.Text.Encoding.xml", "ref/netstandard1.3/es/System.Text.Encoding.xml", "ref/netstandard1.3/fr/System.Text.Encoding.xml", "ref/netstandard1.3/it/System.Text.Encoding.xml", "ref/netstandard1.3/ja/System.Text.Encoding.xml", "ref/netstandard1.3/ko/System.Text.Encoding.xml", "ref/netstandard1.3/ru/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.encoding.4.3.0.nupkg.sha512", "system.text.encoding.nuspec"]}, "System.Text.Encoding.CodePages/4.5.1": {"sha512": "4J2JQXbftjPMppIHJ7IC+VXQ9XfEagN92vZZNoG12i+zReYlim5dMoXFC1Zzg7tsnKDM7JPo5bYfFK4Jheq44w==", "type": "package", "path": "system.text.encoding.codepages/4.5.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Text.Encoding.CodePages.dll", "lib/net461/System.Text.Encoding.CodePages.dll", "lib/netstandard1.3/System.Text.Encoding.CodePages.dll", "lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/win/lib/net461/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netcoreapp2.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netstandard1.3/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "system.text.encoding.codepages.4.5.1.nupkg.sha512", "system.text.encoding.codepages.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Encodings.Web/8.0.0": {"sha512": "yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "type": "package", "path": "system.text.encodings.web/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Text.Encodings.Web.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Text.Encodings.Web.targets", "lib/net462/System.Text.Encodings.Web.dll", "lib/net462/System.Text.Encodings.Web.xml", "lib/net6.0/System.Text.Encodings.Web.dll", "lib/net6.0/System.Text.Encodings.Web.xml", "lib/net7.0/System.Text.Encodings.Web.dll", "lib/net7.0/System.Text.Encodings.Web.xml", "lib/net8.0/System.Text.Encodings.Web.dll", "lib/net8.0/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net7.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net7.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.xml", "system.text.encodings.web.8.0.0.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Json/8.0.0": {"sha512": "OdrZO2WjkiEG6ajEFRABTRCi/wuXQPxeV6g8xvUJqdxMvvuCCEk86zPla8UiIQJz3durtUEbNyY/3lIhS0yZvQ==", "type": "package", "path": "system.text.json/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net6.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net6.0/System.Text.Json.dll", "lib/net6.0/System.Text.Json.xml", "lib/net7.0/System.Text.Json.dll", "lib/net7.0/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.8.0.0.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading/4.3.0": {"sha512": "VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "type": "package", "path": "system.threading/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Threading.dll", "lib/netstandard1.3/System.Threading.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.dll", "ref/netcore50/System.Threading.xml", "ref/netcore50/de/System.Threading.xml", "ref/netcore50/es/System.Threading.xml", "ref/netcore50/fr/System.Threading.xml", "ref/netcore50/it/System.Threading.xml", "ref/netcore50/ja/System.Threading.xml", "ref/netcore50/ko/System.Threading.xml", "ref/netcore50/ru/System.Threading.xml", "ref/netcore50/zh-hans/System.Threading.xml", "ref/netcore50/zh-hant/System.Threading.xml", "ref/netstandard1.0/System.Threading.dll", "ref/netstandard1.0/System.Threading.xml", "ref/netstandard1.0/de/System.Threading.xml", "ref/netstandard1.0/es/System.Threading.xml", "ref/netstandard1.0/fr/System.Threading.xml", "ref/netstandard1.0/it/System.Threading.xml", "ref/netstandard1.0/ja/System.Threading.xml", "ref/netstandard1.0/ko/System.Threading.xml", "ref/netstandard1.0/ru/System.Threading.xml", "ref/netstandard1.0/zh-hans/System.Threading.xml", "ref/netstandard1.0/zh-hant/System.Threading.xml", "ref/netstandard1.3/System.Threading.dll", "ref/netstandard1.3/System.Threading.xml", "ref/netstandard1.3/de/System.Threading.xml", "ref/netstandard1.3/es/System.Threading.xml", "ref/netstandard1.3/fr/System.Threading.xml", "ref/netstandard1.3/it/System.Threading.xml", "ref/netstandard1.3/ja/System.Threading.xml", "ref/netstandard1.3/ko/System.Threading.xml", "ref/netstandard1.3/ru/System.Threading.xml", "ref/netstandard1.3/zh-hans/System.Threading.xml", "ref/netstandard1.3/zh-hant/System.Threading.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Threading.dll", "system.threading.4.3.0.nupkg.sha512", "system.threading.nuspec"]}, "System.Threading.Tasks/4.3.0": {"sha512": "LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "type": "package", "path": "system.threading.tasks/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.Tasks.dll", "ref/netcore50/System.Threading.Tasks.xml", "ref/netcore50/de/System.Threading.Tasks.xml", "ref/netcore50/es/System.Threading.Tasks.xml", "ref/netcore50/fr/System.Threading.Tasks.xml", "ref/netcore50/it/System.Threading.Tasks.xml", "ref/netcore50/ja/System.Threading.Tasks.xml", "ref/netcore50/ko/System.Threading.Tasks.xml", "ref/netcore50/ru/System.Threading.Tasks.xml", "ref/netcore50/zh-hans/System.Threading.Tasks.xml", "ref/netcore50/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.0/System.Threading.Tasks.dll", "ref/netstandard1.0/System.Threading.Tasks.xml", "ref/netstandard1.0/de/System.Threading.Tasks.xml", "ref/netstandard1.0/es/System.Threading.Tasks.xml", "ref/netstandard1.0/fr/System.Threading.Tasks.xml", "ref/netstandard1.0/it/System.Threading.Tasks.xml", "ref/netstandard1.0/ja/System.Threading.Tasks.xml", "ref/netstandard1.0/ko/System.Threading.Tasks.xml", "ref/netstandard1.0/ru/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.3/System.Threading.Tasks.dll", "ref/netstandard1.3/System.Threading.Tasks.xml", "ref/netstandard1.3/de/System.Threading.Tasks.xml", "ref/netstandard1.3/es/System.Threading.Tasks.xml", "ref/netstandard1.3/fr/System.Threading.Tasks.xml", "ref/netstandard1.3/it/System.Threading.Tasks.xml", "ref/netstandard1.3/ja/System.Threading.Tasks.xml", "ref/netstandard1.3/ko/System.Threading.Tasks.xml", "ref/netstandard1.3/ru/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hant/System.Threading.Tasks.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.4.3.0.nupkg.sha512", "system.threading.tasks.nuspec"]}, "System.Threading.Tasks.Extensions/4.5.4": {"sha512": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "type": "package", "path": "system.threading.tasks.extensions/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Threading.Tasks.Extensions.dll", "lib/net461/System.Threading.Tasks.Extensions.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/netstandard2.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard2.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netcoreapp2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.extensions.4.5.4.nupkg.sha512", "system.threading.tasks.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "TimeZoneConverter/6.1.0": {"sha512": "UGdtyKWJqXXinyvGB9X6NVoIYbTAidoZYmn3aXzxeEYC9+OL8vF36eDt1qjb6RqBkWDl4v7iE84ecI+dFhA80A==", "type": "package", "path": "timezoneconverter/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/TimeZoneConverter.dll", "lib/net462/TimeZoneConverter.xml", "lib/net6.0/TimeZoneConverter.dll", "lib/net6.0/TimeZoneConverter.xml", "lib/netstandard2.0/TimeZoneConverter.dll", "lib/netstandard2.0/TimeZoneConverter.xml", "timezoneconverter.6.1.0.nupkg.sha512", "timezoneconverter.nuspec"]}, "Volo.Abp.ApiVersioning.Abstractions/8.1.4": {"sha512": "qJYic/gwI/3TvmYgEgz1x3u/OYOGbdKzNQkM8Pp9/cM76i/VDsobdz6u4OMnt/uap2rqI2VusFlRpco092jAsQ==", "type": "package", "path": "volo.abp.apiversioning.abstractions/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.ApiVersioning.Abstractions.abppkg", "content/Volo.Abp.ApiVersioning.Abstractions.abppkg.analyze.json", "lib/net8.0/Volo.Abp.ApiVersioning.Abstractions.dll", "lib/net8.0/Volo.Abp.ApiVersioning.Abstractions.pdb", "lib/net8.0/Volo.Abp.ApiVersioning.Abstractions.xml", "lib/netstandard2.0/Volo.Abp.ApiVersioning.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.ApiVersioning.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.ApiVersioning.Abstractions.xml", "lib/netstandard2.1/Volo.Abp.ApiVersioning.Abstractions.dll", "lib/netstandard2.1/Volo.Abp.ApiVersioning.Abstractions.pdb", "lib/netstandard2.1/Volo.Abp.ApiVersioning.Abstractions.xml", "volo.abp.apiversioning.abstractions.8.1.4.nupkg.sha512", "volo.abp.apiversioning.abstractions.nuspec"]}, "Volo.Abp.AspNetCore/8.1.4": {"sha512": "t7jkUDhM4vuVCYlUEj+TnTs8JFlyhUo+gY0lizyont8ja/lsC+hFZHgBKmu2oaOY39jkA2V0M/+RYS3AE2f3xQ==", "type": "package", "path": "volo.abp.aspnetcore/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.AspNetCore.abppkg", "content/Volo.Abp.AspNetCore.abppkg.analyze.json", "contentFiles/any/net8.0/Volo.Abp.AspNetCore.abppkg.analyze.json", "lib/net8.0/Volo.Abp.AspNetCore.dll", "lib/net8.0/Volo.Abp.AspNetCore.pdb", "lib/net8.0/Volo.Abp.AspNetCore.xml", "volo.abp.aspnetcore.8.1.4.nupkg.sha512", "volo.abp.aspnetcore.nuspec"]}, "Volo.Abp.AspNetCore.Mvc/8.1.4": {"sha512": "+EFG8X82aHi7mBlYgwxopQq7hmSJpmX0f4vhPO9uncDXQXIPO5a2kDntjrFbz2qGpIHMgEDvZmT/VKV2YjwT2g==", "type": "package", "path": "volo.abp.aspnetcore.mvc/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.AspNetCore.Mvc.abppkg", "content/Volo.Abp.AspNetCore.Mvc.abppkg.analyze.json", "contentFiles/any/net8.0/Volo.Abp.AspNetCore.Mvc.abppkg.analyze.json", "lib/net8.0/Volo.Abp.AspNetCore.Mvc.dll", "lib/net8.0/Volo.Abp.AspNetCore.Mvc.pdb", "lib/net8.0/Volo.Abp.AspNetCore.Mvc.xml", "volo.abp.aspnetcore.mvc.8.1.4.nupkg.sha512", "volo.abp.aspnetcore.mvc.nuspec"]}, "Volo.Abp.AspNetCore.Mvc.Contracts/8.1.4": {"sha512": "I2tRJ5AuCs99KY5Hn+e1hekcSRmOTnCKoSxjzDcpqrgs3YTCoPSBjfkk7ArZwKmGCmcUwAJxW5yWjnyN9ZlyZA==", "type": "package", "path": "volo.abp.aspnetcore.mvc.contracts/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.AspNetCore.Mvc.Contracts.abppkg", "content/Volo.Abp.AspNetCore.Mvc.Contracts.abppkg.analyze.json", "lib/net8.0/Volo.Abp.AspNetCore.Mvc.Contracts.dll", "lib/net8.0/Volo.Abp.AspNetCore.Mvc.Contracts.pdb", "lib/net8.0/Volo.Abp.AspNetCore.Mvc.Contracts.xml", "lib/netstandard2.0/Volo.Abp.AspNetCore.Mvc.Contracts.dll", "lib/netstandard2.0/Volo.Abp.AspNetCore.Mvc.Contracts.pdb", "lib/netstandard2.0/Volo.Abp.AspNetCore.Mvc.Contracts.xml", "lib/netstandard2.1/Volo.Abp.AspNetCore.Mvc.Contracts.dll", "lib/netstandard2.1/Volo.Abp.AspNetCore.Mvc.Contracts.pdb", "lib/netstandard2.1/Volo.Abp.AspNetCore.Mvc.Contracts.xml", "volo.abp.aspnetcore.mvc.contracts.8.1.4.nupkg.sha512", "volo.abp.aspnetcore.mvc.contracts.nuspec"]}, "Volo.Abp.AspNetCore.Mvc.UI/8.1.4": {"sha512": "SUJxBtVZZVaZ+eFX6PsKRszvVtaggrpyETM88SDEfY8f9VLtOp/5ME7H7aS9ieHWqPLfya5zHNZpYufRT/8cCA==", "type": "package", "path": "volo.abp.aspnetcore.mvc.ui/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.AspNetCore.Mvc.UI.abppkg", "lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.dll", "lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.pdb", "lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.xml", "volo.abp.aspnetcore.mvc.ui.8.1.4.nupkg.sha512", "volo.abp.aspnetcore.mvc.ui.nuspec"]}, "Volo.Abp.AspNetCore.Mvc.UI.Bootstrap/8.1.4": {"sha512": "pzhkzbPyjY7/gQN16TQ4XXycrZS5y/aPy4yGE6gTIuF2KAlv80K+mWUxo8ngpBM6DkWdBGDcdDxjHigB/5Xtqw==", "type": "package", "path": "volo.abp.aspnetcore.mvc.ui.bootstrap/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.abppkg", "lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.dll", "lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.pdb", "lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.xml", "volo.abp.aspnetcore.mvc.ui.bootstrap.8.1.4.nupkg.sha512", "volo.abp.aspnetcore.mvc.ui.bootstrap.nuspec"]}, "Volo.Abp.AspNetCore.Mvc.UI.Bundling/8.1.4": {"sha512": "/1RsgYyVjSf8wMV8dHqkVp2EG7A3HSVdFYddQno1cp5dDoNcpLHgQy86CU/q5IFmQn9SbgDMYEI/lJA6UW9kgw==", "type": "package", "path": "volo.abp.aspnetcore.mvc.ui.bundling/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.AspNetCore.Mvc.UI.Bundling.abppkg", "lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Bundling.dll", "lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Bundling.pdb", "lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Bundling.xml", "volo.abp.aspnetcore.mvc.ui.bundling.8.1.4.nupkg.sha512", "volo.abp.aspnetcore.mvc.ui.bundling.nuspec"]}, "Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions/8.1.4": {"sha512": "I+ft81UfNlx3W19hHi9Rw2trZI3I0ri2foR47hpMziemGKt0raG4CIIM7aQsBE6uZ+Eh5qmshj8a3+15hmh1mg==", "type": "package", "path": "volo.abp.aspnetcore.mvc.ui.bundling.abstractions/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions.abppkg", "lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions.dll", "lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions.pdb", "lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions.xml", "volo.abp.aspnetcore.mvc.ui.bundling.abstractions.8.1.4.nupkg.sha512", "volo.abp.aspnetcore.mvc.ui.bundling.abstractions.nuspec"]}, "Volo.Abp.AspNetCore.Mvc.UI.Packages/8.1.4": {"sha512": "+XE8qrak67sA5XNPW10o13McoawYh+jTFmm+XHiwQOVSUWHzBFRruBbEX1xKfckRb838VomVll70Mt6WRp22UQ==", "type": "package", "path": "volo.abp.aspnetcore.mvc.ui.packages/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.AspNetCore.Mvc.UI.Packages.abppkg", "lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Packages.dll", "lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Packages.pdb", "lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Packages.xml", "volo.abp.aspnetcore.mvc.ui.packages.8.1.4.nupkg.sha512", "volo.abp.aspnetcore.mvc.ui.packages.nuspec"]}, "Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared/8.1.4": {"sha512": "zHXWxJbfEWoZoyIPzsQsiRJf0qqxtOQdUb6LdRHOYja5U7+2kvCuMXWWbztT/q7ARk6AMsiauffrrZC8lAXBUg==", "type": "package", "path": "volo.abp.aspnetcore.mvc.ui.theme.shared/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.abppkg", "content/Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.abppkg.analyze.json", "contentFiles/any/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.abppkg.analyze.json", "lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.dll", "lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.pdb", "lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.xml", "volo.abp.aspnetcore.mvc.ui.theme.shared.8.1.4.nupkg.sha512", "volo.abp.aspnetcore.mvc.ui.theme.shared.nuspec"]}, "Volo.Abp.AspNetCore.Mvc.UI.Widgets/8.1.4": {"sha512": "MFaEKuRDNJjgQOox6Bril5zKg2ew/z1M84oHrrrvA7R+zbhFOmGtVoBP0KwMI2BPS0iVyk8U+PX+sJyh8oFZIQ==", "type": "package", "path": "volo.abp.aspnetcore.mvc.ui.widgets/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.AspNetCore.Mvc.UI.Widgets.abppkg", "content/Volo.Abp.AspNetCore.Mvc.UI.Widgets.abppkg.analyze.json", "contentFiles/any/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Widgets.abppkg.analyze.json", "lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Widgets.dll", "lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Widgets.pdb", "lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Widgets.xml", "volo.abp.aspnetcore.mvc.ui.widgets.8.1.4.nupkg.sha512", "volo.abp.aspnetcore.mvc.ui.widgets.nuspec"]}, "Volo.Abp.Auditing/8.1.4": {"sha512": "D+gHxVii18t2yXQfoSCFWvPIV+LcYXGP21O5kOShybr+7Ler8J6CXP8MDHuoBs7Tyzf0C9jPwIYXakkLVvit1Q==", "type": "package", "path": "volo.abp.auditing/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Auditing.abppkg", "content/Volo.Abp.Auditing.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Auditing.dll", "lib/net8.0/Volo.Abp.Auditing.pdb", "lib/net8.0/Volo.Abp.Auditing.xml", "lib/netstandard2.0/Volo.Abp.Auditing.dll", "lib/netstandard2.0/Volo.Abp.Auditing.pdb", "lib/netstandard2.0/Volo.Abp.Auditing.xml", "lib/netstandard2.1/Volo.Abp.Auditing.dll", "lib/netstandard2.1/Volo.Abp.Auditing.pdb", "lib/netstandard2.1/Volo.Abp.Auditing.xml", "volo.abp.auditing.8.1.4.nupkg.sha512", "volo.abp.auditing.nuspec"]}, "Volo.Abp.Auditing.Contracts/8.1.4": {"sha512": "bL2dXDgGK4aQRWkaMVBgkpyLbfdN+8plofW7pqfMZag+fgIwrnBD0jBZI2aMU9RhsvkbYf0pPArp2PpkxxkQeQ==", "type": "package", "path": "volo.abp.auditing.contracts/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Auditing.Contracts.abppkg", "content/Volo.Abp.Auditing.Contracts.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Auditing.Contracts.dll", "lib/net8.0/Volo.Abp.Auditing.Contracts.pdb", "lib/net8.0/Volo.Abp.Auditing.Contracts.xml", "lib/netstandard2.0/Volo.Abp.Auditing.Contracts.dll", "lib/netstandard2.0/Volo.Abp.Auditing.Contracts.pdb", "lib/netstandard2.0/Volo.Abp.Auditing.Contracts.xml", "lib/netstandard2.1/Volo.Abp.Auditing.Contracts.dll", "lib/netstandard2.1/Volo.Abp.Auditing.Contracts.pdb", "lib/netstandard2.1/Volo.Abp.Auditing.Contracts.xml", "volo.abp.auditing.contracts.8.1.4.nupkg.sha512", "volo.abp.auditing.contracts.nuspec"]}, "Volo.Abp.Authorization/8.1.4": {"sha512": "WIjMvIYwfWdzc7gjKCRNDOoPKZd9aoZjOj2M1W5ys2E3demqIMRg+5h32QDYsS/UIQAUtL+1Gy1CmYDlE9GCCg==", "type": "package", "path": "volo.abp.authorization/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Authorization.abppkg", "content/Volo.Abp.Authorization.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Authorization.dll", "lib/net8.0/Volo.Abp.Authorization.pdb", "lib/net8.0/Volo.Abp.Authorization.xml", "lib/netstandard2.0/Volo.Abp.Authorization.dll", "lib/netstandard2.0/Volo.Abp.Authorization.pdb", "lib/netstandard2.0/Volo.Abp.Authorization.xml", "lib/netstandard2.1/Volo.Abp.Authorization.dll", "lib/netstandard2.1/Volo.Abp.Authorization.pdb", "lib/netstandard2.1/Volo.Abp.Authorization.xml", "volo.abp.authorization.8.1.4.nupkg.sha512", "volo.abp.authorization.nuspec"]}, "Volo.Abp.Authorization.Abstractions/8.1.4": {"sha512": "zwhJm6ZZx7tU6145e98qUTSSNYYMefn2gfib26CKdXbCtr7r8GjbjUVrsn+nRhJ1kU8Va76/B7NvKk+3SPyTaA==", "type": "package", "path": "volo.abp.authorization.abstractions/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Authorization.Abstractions.abppkg", "content/Volo.Abp.Authorization.Abstractions.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Authorization.Abstractions.dll", "lib/net8.0/Volo.Abp.Authorization.Abstractions.pdb", "lib/net8.0/Volo.Abp.Authorization.Abstractions.xml", "lib/netstandard2.0/Volo.Abp.Authorization.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.Authorization.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.Authorization.Abstractions.xml", "lib/netstandard2.1/Volo.Abp.Authorization.Abstractions.dll", "lib/netstandard2.1/Volo.Abp.Authorization.Abstractions.pdb", "lib/netstandard2.1/Volo.Abp.Authorization.Abstractions.xml", "volo.abp.authorization.abstractions.8.1.4.nupkg.sha512", "volo.abp.authorization.abstractions.nuspec"]}, "Volo.Abp.AutoMapper/8.1.4": {"sha512": "wl58RP3TyVG7e9u7NZzMRKu5XQH33z2TwIh8Cmn6SCSFQNYrMepWIdx+p40pYHSMIWUfNnjftL8AOprZo/3DTg==", "type": "package", "path": "volo.abp.automapper/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.AutoMapper.abppkg", "content/Volo.Abp.AutoMapper.abppkg.analyze.json", "lib/netstandard2.1/Volo.Abp.AutoMapper.dll", "lib/netstandard2.1/Volo.Abp.AutoMapper.pdb", "lib/netstandard2.1/Volo.Abp.AutoMapper.xml", "volo.abp.automapper.8.1.4.nupkg.sha512", "volo.abp.automapper.nuspec"]}, "Volo.Abp.BackgroundWorkers/8.1.4": {"sha512": "uoRpquLWtqf6HpKI4CJ49O6LWstvlqJNYU1pUNB3bsUBaW59imJyIy1t4GjG0lDfrBMKysejqwHlTakSE/o0Bg==", "type": "package", "path": "volo.abp.backgroundworkers/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.BackgroundWorkers.abppkg", "content/Volo.Abp.BackgroundWorkers.abppkg.analyze.json", "lib/net8.0/Volo.Abp.BackgroundWorkers.dll", "lib/net8.0/Volo.Abp.BackgroundWorkers.pdb", "lib/net8.0/Volo.Abp.BackgroundWorkers.xml", "lib/netstandard2.0/Volo.Abp.BackgroundWorkers.dll", "lib/netstandard2.0/Volo.Abp.BackgroundWorkers.pdb", "lib/netstandard2.0/Volo.Abp.BackgroundWorkers.xml", "lib/netstandard2.1/Volo.Abp.BackgroundWorkers.dll", "lib/netstandard2.1/Volo.Abp.BackgroundWorkers.pdb", "lib/netstandard2.1/Volo.Abp.BackgroundWorkers.xml", "volo.abp.backgroundworkers.8.1.4.nupkg.sha512", "volo.abp.backgroundworkers.nuspec"]}, "Volo.Abp.BlobStoring/8.1.4": {"sha512": "X5iKQqfAhtdZbDe7DUWqyR4auOWisJ3tjL+CiHgro6+7IiWDMlSmBMTuPaQpuCXG68Q6Afifu5h5DFnlOwOxMg==", "type": "package", "path": "volo.abp.blobstoring/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.BlobStoring.abppkg", "content/Volo.Abp.BlobStoring.abppkg.analyze.json", "lib/net8.0/Volo.Abp.BlobStoring.dll", "lib/net8.0/Volo.Abp.BlobStoring.pdb", "lib/net8.0/Volo.Abp.BlobStoring.xml", "lib/netstandard2.0/Volo.Abp.BlobStoring.dll", "lib/netstandard2.0/Volo.Abp.BlobStoring.pdb", "lib/netstandard2.0/Volo.Abp.BlobStoring.xml", "lib/netstandard2.1/Volo.Abp.BlobStoring.dll", "lib/netstandard2.1/Volo.Abp.BlobStoring.pdb", "lib/netstandard2.1/Volo.Abp.BlobStoring.xml", "volo.abp.blobstoring.8.1.4.nupkg.sha512", "volo.abp.blobstoring.nuspec"]}, "Volo.Abp.Caching/8.1.4": {"sha512": "HHlzPtw2TqJfQyxEMqaiSgTovtBhcW+9MeboUuXTDWNz4YgczTRGHi/VX+/JIKoP2IQJnW+SAQRfzAu/8VtyJA==", "type": "package", "path": "volo.abp.caching/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Caching.abppkg", "content/Volo.Abp.Caching.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Caching.dll", "lib/net8.0/Volo.Abp.Caching.pdb", "lib/net8.0/Volo.Abp.Caching.xml", "lib/netstandard2.0/Volo.Abp.Caching.dll", "lib/netstandard2.0/Volo.Abp.Caching.pdb", "lib/netstandard2.0/Volo.Abp.Caching.xml", "lib/netstandard2.1/Volo.Abp.Caching.dll", "lib/netstandard2.1/Volo.Abp.Caching.pdb", "lib/netstandard2.1/Volo.Abp.Caching.xml", "volo.abp.caching.8.1.4.nupkg.sha512", "volo.abp.caching.nuspec"]}, "Volo.Abp.Core/8.1.4": {"sha512": "Shswqpv1ngZ/OyJEXEXVZWmNcaLoHTDELcngeDnUovMzrFn6QqNkZzV0+liKk4jt5LSDLuQJY31H72HxzLA6Pw==", "type": "package", "path": "volo.abp.core/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Core.abppkg", "content/Volo.Abp.Core.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Core.dll", "lib/net8.0/Volo.Abp.Core.pdb", "lib/net8.0/Volo.Abp.Core.xml", "lib/netstandard2.0/Volo.Abp.Core.dll", "lib/netstandard2.0/Volo.Abp.Core.pdb", "lib/netstandard2.0/Volo.Abp.Core.xml", "lib/netstandard2.1/Volo.Abp.Core.dll", "lib/netstandard2.1/Volo.Abp.Core.pdb", "lib/netstandard2.1/Volo.Abp.Core.xml", "volo.abp.core.8.1.4.nupkg.sha512", "volo.abp.core.nuspec"]}, "Volo.Abp.Data/8.1.4": {"sha512": "v6j4JaNSq70hvxe1T1+MC5e4YnsAeanjlxfiZLDo4NI1V0KINaS9hJuZunC1K3gLEXNAoa7pmQDPcdNOskK1Qg==", "type": "package", "path": "volo.abp.data/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Data.abppkg", "content/Volo.Abp.Data.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Data.dll", "lib/net8.0/Volo.Abp.Data.pdb", "lib/net8.0/Volo.Abp.Data.xml", "lib/netstandard2.0/Volo.Abp.Data.dll", "lib/netstandard2.0/Volo.Abp.Data.pdb", "lib/netstandard2.0/Volo.Abp.Data.xml", "lib/netstandard2.1/Volo.Abp.Data.dll", "lib/netstandard2.1/Volo.Abp.Data.pdb", "lib/netstandard2.1/Volo.Abp.Data.xml", "volo.abp.data.8.1.4.nupkg.sha512", "volo.abp.data.nuspec"]}, "Volo.Abp.Ddd.Application/8.1.4": {"sha512": "ZMxZUWZeSbclV7AWpZcqh/cv6AnpQrT8UKzI6jbYtQXKtNqUKrp7SvF7/tPM9b/Ei0N1mHC5RqqQCRfUrp2Anw==", "type": "package", "path": "volo.abp.ddd.application/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Ddd.Application.abppkg", "content/Volo.Abp.Ddd.Application.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Ddd.Application.dll", "lib/net8.0/Volo.Abp.Ddd.Application.pdb", "lib/net8.0/Volo.Abp.Ddd.Application.xml", "lib/netstandard2.0/Volo.Abp.Ddd.Application.dll", "lib/netstandard2.0/Volo.Abp.Ddd.Application.pdb", "lib/netstandard2.0/Volo.Abp.Ddd.Application.xml", "lib/netstandard2.1/Volo.Abp.Ddd.Application.dll", "lib/netstandard2.1/Volo.Abp.Ddd.Application.pdb", "lib/netstandard2.1/Volo.Abp.Ddd.Application.xml", "volo.abp.ddd.application.8.1.4.nupkg.sha512", "volo.abp.ddd.application.nuspec"]}, "Volo.Abp.Ddd.Application.Contracts/8.1.4": {"sha512": "i1RKDysHYGqjwN2rhih+Ugzc8wB5zNHMfO6ws6emVf4qfI0okSoIITJmOissEmzOyrUIkLWXcwdwZ0cQG6VrXw==", "type": "package", "path": "volo.abp.ddd.application.contracts/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Ddd.Application.Contracts.abppkg", "content/Volo.Abp.Ddd.Application.Contracts.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Ddd.Application.Contracts.dll", "lib/net8.0/Volo.Abp.Ddd.Application.Contracts.pdb", "lib/net8.0/Volo.Abp.Ddd.Application.Contracts.xml", "lib/netstandard2.0/Volo.Abp.Ddd.Application.Contracts.dll", "lib/netstandard2.0/Volo.Abp.Ddd.Application.Contracts.pdb", "lib/netstandard2.0/Volo.Abp.Ddd.Application.Contracts.xml", "lib/netstandard2.1/Volo.Abp.Ddd.Application.Contracts.dll", "lib/netstandard2.1/Volo.Abp.Ddd.Application.Contracts.pdb", "lib/netstandard2.1/Volo.Abp.Ddd.Application.Contracts.xml", "volo.abp.ddd.application.contracts.8.1.4.nupkg.sha512", "volo.abp.ddd.application.contracts.nuspec"]}, "Volo.Abp.Ddd.Domain/8.1.4": {"sha512": "XgC3RaZIVumyKBDlFmHPSab6Rl+H87dDz4ltK8dT6THc8YVzZWidb0xfrSRVesOK/TZAXahvupzQc+5f+rFN3w==", "type": "package", "path": "volo.abp.ddd.domain/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Ddd.Domain.abppkg", "content/Volo.Abp.Ddd.Domain.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Ddd.Domain.dll", "lib/net8.0/Volo.Abp.Ddd.Domain.pdb", "lib/net8.0/Volo.Abp.Ddd.Domain.xml", "lib/netstandard2.0/Volo.Abp.Ddd.Domain.dll", "lib/netstandard2.0/Volo.Abp.Ddd.Domain.pdb", "lib/netstandard2.0/Volo.Abp.Ddd.Domain.xml", "lib/netstandard2.1/Volo.Abp.Ddd.Domain.dll", "lib/netstandard2.1/Volo.Abp.Ddd.Domain.pdb", "lib/netstandard2.1/Volo.Abp.Ddd.Domain.xml", "volo.abp.ddd.domain.8.1.4.nupkg.sha512", "volo.abp.ddd.domain.nuspec"]}, "Volo.Abp.Ddd.Domain.Shared/8.1.4": {"sha512": "n+hmsXtv84JMQow5eYRE9fVLCZq1bolvy/XPod4QeurSx9GiJSm8N05SPwaLDg5NFhyNMEGwNuUckXlNbTNKOw==", "type": "package", "path": "volo.abp.ddd.domain.shared/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "lib/net8.0/Volo.Abp.Ddd.Domain.Shared.dll", "lib/net8.0/Volo.Abp.Ddd.Domain.Shared.pdb", "lib/net8.0/Volo.Abp.Ddd.Domain.Shared.xml", "lib/netstandard2.0/Volo.Abp.Ddd.Domain.Shared.dll", "lib/netstandard2.0/Volo.Abp.Ddd.Domain.Shared.pdb", "lib/netstandard2.0/Volo.Abp.Ddd.Domain.Shared.xml", "lib/netstandard2.1/Volo.Abp.Ddd.Domain.Shared.dll", "lib/netstandard2.1/Volo.Abp.Ddd.Domain.Shared.pdb", "lib/netstandard2.1/Volo.Abp.Ddd.Domain.Shared.xml", "volo.abp.ddd.domain.shared.8.1.4.nupkg.sha512", "volo.abp.ddd.domain.shared.nuspec"]}, "Volo.Abp.DistributedLocking.Abstractions/8.1.4": {"sha512": "lLU64EBd8vRCZ9fDC3uE2mnVQxvoqidXzMRqfkgxmIuKAy6euNQF10hbfPqkAMT6BTtjyol1dldj4IDkOwyLZQ==", "type": "package", "path": "volo.abp.distributedlocking.abstractions/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.DistributedLocking.Abstractions.abppkg", "content/Volo.Abp.DistributedLocking.Abstractions.abppkg.analyze.json", "lib/net8.0/Volo.Abp.DistributedLocking.Abstractions.dll", "lib/net8.0/Volo.Abp.DistributedLocking.Abstractions.pdb", "lib/net8.0/Volo.Abp.DistributedLocking.Abstractions.xml", "lib/netstandard2.0/Volo.Abp.DistributedLocking.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.DistributedLocking.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.DistributedLocking.Abstractions.xml", "lib/netstandard2.1/Volo.Abp.DistributedLocking.Abstractions.dll", "lib/netstandard2.1/Volo.Abp.DistributedLocking.Abstractions.pdb", "lib/netstandard2.1/Volo.Abp.DistributedLocking.Abstractions.xml", "volo.abp.distributedlocking.abstractions.8.1.4.nupkg.sha512", "volo.abp.distributedlocking.abstractions.nuspec"]}, "Volo.Abp.EventBus/8.1.4": {"sha512": "/tdUlQaCJWZ/9+dG0AWdwyJ0EidR3FOAow/Aduw2+Zrxj034m42A+/F8OcMx/QvqIkc8TQw+El1npUaCEw69Jg==", "type": "package", "path": "volo.abp.eventbus/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.EventBus.abppkg", "content/Volo.Abp.EventBus.abppkg.analyze.json", "lib/net8.0/Volo.Abp.EventBus.dll", "lib/net8.0/Volo.Abp.EventBus.pdb", "lib/net8.0/Volo.Abp.EventBus.xml", "lib/netstandard2.0/Volo.Abp.EventBus.dll", "lib/netstandard2.0/Volo.Abp.EventBus.pdb", "lib/netstandard2.0/Volo.Abp.EventBus.xml", "lib/netstandard2.1/Volo.Abp.EventBus.dll", "lib/netstandard2.1/Volo.Abp.EventBus.pdb", "lib/netstandard2.1/Volo.Abp.EventBus.xml", "volo.abp.eventbus.8.1.4.nupkg.sha512", "volo.abp.eventbus.nuspec"]}, "Volo.Abp.EventBus.Abstractions/8.1.4": {"sha512": "U6Ttfxi5GqvsL6HR9kbrCMABg3UzT3FaUETjsUYoz+wv+4WdbdpY3LbXD90UBnDXEaI9toN6ScGE4vEXdQDGXw==", "type": "package", "path": "volo.abp.eventbus.abstractions/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.EventBus.Abstractions.abppkg", "content/Volo.Abp.EventBus.Abstractions.abppkg.analyze.json", "lib/net8.0/Volo.Abp.EventBus.Abstractions.dll", "lib/net8.0/Volo.Abp.EventBus.Abstractions.pdb", "lib/net8.0/Volo.Abp.EventBus.Abstractions.xml", "lib/netstandard2.0/Volo.Abp.EventBus.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.EventBus.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.EventBus.Abstractions.xml", "lib/netstandard2.1/Volo.Abp.EventBus.Abstractions.dll", "lib/netstandard2.1/Volo.Abp.EventBus.Abstractions.pdb", "lib/netstandard2.1/Volo.Abp.EventBus.Abstractions.xml", "volo.abp.eventbus.abstractions.8.1.4.nupkg.sha512", "volo.abp.eventbus.abstractions.nuspec"]}, "Volo.Abp.ExceptionHandling/8.1.4": {"sha512": "0jflyVlbiIzSzA2j2DJwGKQ7imbwnZ3A8DCB/IS0gA7Wwl35COLOlEV9b+lxHOdKIswgwDXDP3xrMGyYRkOCAQ==", "type": "package", "path": "volo.abp.exceptionhandling/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.ExceptionHandling.abppkg", "content/Volo.Abp.ExceptionHandling.abppkg.analyze.json", "lib/net8.0/Volo.Abp.ExceptionHandling.dll", "lib/net8.0/Volo.Abp.ExceptionHandling.pdb", "lib/net8.0/Volo.Abp.ExceptionHandling.xml", "lib/netstandard2.0/Volo.Abp.ExceptionHandling.dll", "lib/netstandard2.0/Volo.Abp.ExceptionHandling.pdb", "lib/netstandard2.0/Volo.Abp.ExceptionHandling.xml", "lib/netstandard2.1/Volo.Abp.ExceptionHandling.dll", "lib/netstandard2.1/Volo.Abp.ExceptionHandling.pdb", "lib/netstandard2.1/Volo.Abp.ExceptionHandling.xml", "volo.abp.exceptionhandling.8.1.4.nupkg.sha512", "volo.abp.exceptionhandling.nuspec"]}, "Volo.Abp.Features/8.1.4": {"sha512": "FVR3ACMbV9I/JzRfsCYYGQQ2UrWbpK18weluBcMFOVWTu0oENJwX7JMj49oeZJnwDfGqNvHWZrkF+nLE7T5DkQ==", "type": "package", "path": "volo.abp.features/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Features.abppkg", "content/Volo.Abp.Features.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Features.dll", "lib/net8.0/Volo.Abp.Features.pdb", "lib/net8.0/Volo.Abp.Features.xml", "lib/netstandard2.0/Volo.Abp.Features.dll", "lib/netstandard2.0/Volo.Abp.Features.pdb", "lib/netstandard2.0/Volo.Abp.Features.xml", "lib/netstandard2.1/Volo.Abp.Features.dll", "lib/netstandard2.1/Volo.Abp.Features.pdb", "lib/netstandard2.1/Volo.Abp.Features.xml", "volo.abp.features.8.1.4.nupkg.sha512", "volo.abp.features.nuspec"]}, "Volo.Abp.GlobalFeatures/8.1.4": {"sha512": "pxJUtREEHf/sl+AefhmcQklZ8jQ7+hCVwFJPVSV9LcbpTwAU7SnDYT4ktQClWgGASCJfS3yBEDLXUM/ALVa8Nw==", "type": "package", "path": "volo.abp.globalfeatures/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.GlobalFeatures.abppkg", "content/Volo.Abp.GlobalFeatures.abppkg.analyze.json", "lib/net8.0/Volo.Abp.GlobalFeatures.dll", "lib/net8.0/Volo.Abp.GlobalFeatures.pdb", "lib/net8.0/Volo.Abp.GlobalFeatures.xml", "lib/netstandard2.0/Volo.Abp.GlobalFeatures.dll", "lib/netstandard2.0/Volo.Abp.GlobalFeatures.pdb", "lib/netstandard2.0/Volo.Abp.GlobalFeatures.xml", "lib/netstandard2.1/Volo.Abp.GlobalFeatures.dll", "lib/netstandard2.1/Volo.Abp.GlobalFeatures.pdb", "lib/netstandard2.1/Volo.Abp.GlobalFeatures.xml", "volo.abp.globalfeatures.8.1.4.nupkg.sha512", "volo.abp.globalfeatures.nuspec"]}, "Volo.Abp.Guids/8.1.4": {"sha512": "v79TQ7kt84h8F9wWi/RQbJUl03Ka1aU6yozpN/D+xC8ATzn06UOennDQw48aESWtMbPnlC99Yb+11tfY8QJwcQ==", "type": "package", "path": "volo.abp.guids/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Guids.abppkg", "content/Volo.Abp.Guids.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Guids.dll", "lib/net8.0/Volo.Abp.Guids.pdb", "lib/net8.0/Volo.Abp.Guids.xml", "lib/netstandard2.0/Volo.Abp.Guids.dll", "lib/netstandard2.0/Volo.Abp.Guids.pdb", "lib/netstandard2.0/Volo.Abp.Guids.xml", "lib/netstandard2.1/Volo.Abp.Guids.dll", "lib/netstandard2.1/Volo.Abp.Guids.pdb", "lib/netstandard2.1/Volo.Abp.Guids.xml", "volo.abp.guids.8.1.4.nupkg.sha512", "volo.abp.guids.nuspec"]}, "Volo.Abp.Http/8.1.4": {"sha512": "O3oFJuMceXoZMrb7R6z2gnsxJ4MTf/dlU5vXXWIToxdmh2ncsdUKNFYG45d+N9mSQskB4xGRGLSyTV0scUhyIg==", "type": "package", "path": "volo.abp.http/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Http.abppkg", "content/Volo.Abp.Http.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Http.dll", "lib/net8.0/Volo.Abp.Http.pdb", "lib/net8.0/Volo.Abp.Http.xml", "lib/netstandard2.0/Volo.Abp.Http.dll", "lib/netstandard2.0/Volo.Abp.Http.pdb", "lib/netstandard2.0/Volo.Abp.Http.xml", "lib/netstandard2.1/Volo.Abp.Http.dll", "lib/netstandard2.1/Volo.Abp.Http.pdb", "lib/netstandard2.1/Volo.Abp.Http.xml", "volo.abp.http.8.1.4.nupkg.sha512", "volo.abp.http.nuspec"]}, "Volo.Abp.Http.Abstractions/8.1.4": {"sha512": "qlb0lyPuPXWpTQ0XDyXFZrUvFu7odcHAMcrBIHW7HoppWeAxCcF9YwX88lHvN06StWQmMmlU1XNcX2p1VFWVfw==", "type": "package", "path": "volo.abp.http.abstractions/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Http.Abstractions.abppkg", "content/Volo.Abp.Http.Abstractions.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Http.Abstractions.dll", "lib/net8.0/Volo.Abp.Http.Abstractions.pdb", "lib/net8.0/Volo.Abp.Http.Abstractions.xml", "lib/netstandard2.0/Volo.Abp.Http.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.Http.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.Http.Abstractions.xml", "lib/netstandard2.1/Volo.Abp.Http.Abstractions.dll", "lib/netstandard2.1/Volo.Abp.Http.Abstractions.pdb", "lib/netstandard2.1/Volo.Abp.Http.Abstractions.xml", "volo.abp.http.abstractions.8.1.4.nupkg.sha512", "volo.abp.http.abstractions.nuspec"]}, "Volo.Abp.Identity.Domain/8.1.4": {"sha512": "dgPSfasv22KiznCUivBcfPkY6QYvMRhIt0x1UK17q1C6dunp53XLxP8GdyME1az080Cvv8Tht0eLxiEywHLhLw==", "type": "package", "path": "volo.abp.identity.domain/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Identity.Domain.abppkg", "content/Volo.Abp.Identity.Domain.abppkg.analyze.json", "lib/netstandard2.1/Volo.Abp.Identity.Domain.dll", "lib/netstandard2.1/Volo.Abp.Identity.Domain.pdb", "lib/netstandard2.1/Volo.Abp.Identity.Domain.xml", "volo.abp.identity.domain.8.1.4.nupkg.sha512", "volo.abp.identity.domain.nuspec"]}, "Volo.Abp.Identity.Domain.Shared/8.1.4": {"sha512": "PtAnufkyHM0RWEzWel2IOZ+oMQ1YB6qW99XXhVREOr3I9P5eSmgVQt2R35URg5WDZ4ixci13zyG5S7wHdCHeug==", "type": "package", "path": "volo.abp.identity.domain.shared/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Identity.Domain.Shared.abppkg", "content/Volo.Abp.Identity.Domain.Shared.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Identity.Domain.Shared.dll", "lib/net8.0/Volo.Abp.Identity.Domain.Shared.pdb", "lib/net8.0/Volo.Abp.Identity.Domain.Shared.xml", "lib/netstandard2.0/Volo.Abp.Identity.Domain.Shared.dll", "lib/netstandard2.0/Volo.Abp.Identity.Domain.Shared.pdb", "lib/netstandard2.0/Volo.Abp.Identity.Domain.Shared.xml", "lib/netstandard2.1/Volo.Abp.Identity.Domain.Shared.dll", "lib/netstandard2.1/Volo.Abp.Identity.Domain.Shared.pdb", "lib/netstandard2.1/Volo.Abp.Identity.Domain.Shared.xml", "volo.abp.identity.domain.shared.8.1.4.nupkg.sha512", "volo.abp.identity.domain.shared.nuspec"]}, "Volo.Abp.Json/8.1.4": {"sha512": "YNM6P+kPVD5pymRiAfPZTw1PCK1/vCDwHDNXYoJIEv9z0r3Hvjx89Y8o0CyaMn8dlIHGporl4laFcGlhvk4m/w==", "type": "package", "path": "volo.abp.json/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Json.abppkg", "content/Volo.Abp.Json.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Json.dll", "lib/net8.0/Volo.Abp.Json.pdb", "lib/net8.0/Volo.Abp.Json.xml", "lib/netstandard2.0/Volo.Abp.Json.dll", "lib/netstandard2.0/Volo.Abp.Json.pdb", "lib/netstandard2.0/Volo.Abp.Json.xml", "lib/netstandard2.1/Volo.Abp.Json.dll", "lib/netstandard2.1/Volo.Abp.Json.pdb", "lib/netstandard2.1/Volo.Abp.Json.xml", "volo.abp.json.8.1.4.nupkg.sha512", "volo.abp.json.nuspec"]}, "Volo.Abp.Json.Abstractions/8.1.4": {"sha512": "CnJZlden/3DcWHZG4QJM4CnlAH6WGCaAkQYIYabZRcYtPfgGShgVq48zb/YzJHB2hWe1/q14zOT+i1PreTgTxg==", "type": "package", "path": "volo.abp.json.abstractions/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Json.Abstractions.abppkg", "content/Volo.Abp.Json.Abstractions.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Json.Abstractions.dll", "lib/net8.0/Volo.Abp.Json.Abstractions.pdb", "lib/net8.0/Volo.Abp.Json.Abstractions.xml", "lib/netstandard2.0/Volo.Abp.Json.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.Json.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.Json.Abstractions.xml", "lib/netstandard2.1/Volo.Abp.Json.Abstractions.dll", "lib/netstandard2.1/Volo.Abp.Json.Abstractions.pdb", "lib/netstandard2.1/Volo.Abp.Json.Abstractions.xml", "volo.abp.json.abstractions.8.1.4.nupkg.sha512", "volo.abp.json.abstractions.nuspec"]}, "Volo.Abp.Json.SystemTextJson/8.1.4": {"sha512": "c5vjP86Yw7e5Uvl54n0AChKj5McWuEVfJxJQJRCDvv70lDZALeMMhMx2E0Lg76xDRPoIPCjaC5o0Sbbw152skg==", "type": "package", "path": "volo.abp.json.systemtextjson/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Json.SystemTextJson.abppkg", "content/Volo.Abp.Json.SystemTextJson.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Json.SystemTextJson.dll", "lib/net8.0/Volo.Abp.Json.SystemTextJson.pdb", "lib/net8.0/Volo.Abp.Json.SystemTextJson.xml", "lib/netstandard2.0/Volo.Abp.Json.SystemTextJson.dll", "lib/netstandard2.0/Volo.Abp.Json.SystemTextJson.pdb", "lib/netstandard2.0/Volo.Abp.Json.SystemTextJson.xml", "lib/netstandard2.1/Volo.Abp.Json.SystemTextJson.dll", "lib/netstandard2.1/Volo.Abp.Json.SystemTextJson.pdb", "lib/netstandard2.1/Volo.Abp.Json.SystemTextJson.xml", "volo.abp.json.systemtextjson.8.1.4.nupkg.sha512", "volo.abp.json.systemtextjson.nuspec"]}, "Volo.Abp.Localization/8.1.4": {"sha512": "Ht2BCTFsiaFgJ8/fR1xXbpppUXeRYFyYFSkOD4/AkLpCFRotFvlxdFDJKyGzUHCqH5NJJ3WM3umAdWf/Mh+S/Q==", "type": "package", "path": "volo.abp.localization/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Localization.abppkg", "content/Volo.Abp.Localization.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Localization.dll", "lib/net8.0/Volo.Abp.Localization.pdb", "lib/net8.0/Volo.Abp.Localization.xml", "lib/netstandard2.0/Volo.Abp.Localization.dll", "lib/netstandard2.0/Volo.Abp.Localization.pdb", "lib/netstandard2.0/Volo.Abp.Localization.xml", "lib/netstandard2.1/Volo.Abp.Localization.dll", "lib/netstandard2.1/Volo.Abp.Localization.pdb", "lib/netstandard2.1/Volo.Abp.Localization.xml", "volo.abp.localization.8.1.4.nupkg.sha512", "volo.abp.localization.nuspec"]}, "Volo.Abp.Localization.Abstractions/8.1.4": {"sha512": "JBsD9bIPodbuWKPWMCz1kSPZwyvSULMw9xzeXRi0yytOcniDTISs48PwCZL7zhurcxAcztNdDfvuhwOEETuf5g==", "type": "package", "path": "volo.abp.localization.abstractions/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Localization.Abstractions.abppkg", "content/Volo.Abp.Localization.Abstractions.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Localization.Abstractions.dll", "lib/net8.0/Volo.Abp.Localization.Abstractions.pdb", "lib/net8.0/Volo.Abp.Localization.Abstractions.xml", "lib/netstandard2.0/Volo.Abp.Localization.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.Localization.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.Localization.Abstractions.xml", "lib/netstandard2.1/Volo.Abp.Localization.Abstractions.dll", "lib/netstandard2.1/Volo.Abp.Localization.Abstractions.pdb", "lib/netstandard2.1/Volo.Abp.Localization.Abstractions.xml", "volo.abp.localization.abstractions.8.1.4.nupkg.sha512", "volo.abp.localization.abstractions.nuspec"]}, "Volo.Abp.Minify/8.1.4": {"sha512": "/+aYIKShAITMKCrv59WuzhcTPeOx4abpSokRycnwaH8jMT/axxfXqwMCMedEXCCsiCQziqltGSWeqmXPGFOEtQ==", "type": "package", "path": "volo.abp.minify/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Minify.abppkg", "content/Volo.Abp.Minify.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Minify.dll", "lib/net8.0/Volo.Abp.Minify.pdb", "lib/net8.0/Volo.Abp.Minify.xml", "lib/netstandard2.0/Volo.Abp.Minify.dll", "lib/netstandard2.0/Volo.Abp.Minify.pdb", "lib/netstandard2.0/Volo.Abp.Minify.xml", "lib/netstandard2.1/Volo.Abp.Minify.dll", "lib/netstandard2.1/Volo.Abp.Minify.pdb", "lib/netstandard2.1/Volo.Abp.Minify.xml", "volo.abp.minify.8.1.4.nupkg.sha512", "volo.abp.minify.nuspec"]}, "Volo.Abp.MultiTenancy/8.1.4": {"sha512": "EemIM2l2moTDFoEV/AJSaHFqWTE2LiXmPlupznky5nPChsJpdTuhljbmo7I/0wRR35xtuTKK79mnNBAk69Ux1w==", "type": "package", "path": "volo.abp.multitenancy/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.MultiTenancy.abppkg", "content/Volo.Abp.MultiTenancy.abppkg.analyze.json", "lib/net8.0/Volo.Abp.MultiTenancy.dll", "lib/net8.0/Volo.Abp.MultiTenancy.pdb", "lib/net8.0/Volo.Abp.MultiTenancy.xml", "lib/netstandard2.0/Volo.Abp.MultiTenancy.dll", "lib/netstandard2.0/Volo.Abp.MultiTenancy.pdb", "lib/netstandard2.0/Volo.Abp.MultiTenancy.xml", "lib/netstandard2.1/Volo.Abp.MultiTenancy.dll", "lib/netstandard2.1/Volo.Abp.MultiTenancy.pdb", "lib/netstandard2.1/Volo.Abp.MultiTenancy.xml", "volo.abp.multitenancy.8.1.4.nupkg.sha512", "volo.abp.multitenancy.nuspec"]}, "Volo.Abp.MultiTenancy.Abstractions/8.1.4": {"sha512": "TalCX6aBVbtne7rAc4MssuoyVSHgZL5fIeS55ZmnwVfm1XXHbeps2RPiKflyP7WfFnUFzRdGu0V+or8H6Oeydg==", "type": "package", "path": "volo.abp.multitenancy.abstractions/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "lib/net8.0/Volo.Abp.MultiTenancy.Abstractions.dll", "lib/net8.0/Volo.Abp.MultiTenancy.Abstractions.pdb", "lib/net8.0/Volo.Abp.MultiTenancy.Abstractions.xml", "lib/netstandard2.0/Volo.Abp.MultiTenancy.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.MultiTenancy.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.MultiTenancy.Abstractions.xml", "lib/netstandard2.1/Volo.Abp.MultiTenancy.Abstractions.dll", "lib/netstandard2.1/Volo.Abp.MultiTenancy.Abstractions.pdb", "lib/netstandard2.1/Volo.Abp.MultiTenancy.Abstractions.xml", "volo.abp.multitenancy.abstractions.8.1.4.nupkg.sha512", "volo.abp.multitenancy.abstractions.nuspec"]}, "Volo.Abp.ObjectExtending/8.1.4": {"sha512": "cnYkPQBi3WQgP+881wwJH0sMtfpBYPkLlt/sjZmgngS/HuQgUINWiTznY+8UJDxw5BOmuxSWEsIRfoOYHkg78w==", "type": "package", "path": "volo.abp.objectextending/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.ObjectExtending.abppkg", "content/Volo.Abp.ObjectExtending.abppkg.analyze.json", "lib/net8.0/Volo.Abp.ObjectExtending.dll", "lib/net8.0/Volo.Abp.ObjectExtending.pdb", "lib/net8.0/Volo.Abp.ObjectExtending.xml", "lib/netstandard2.0/Volo.Abp.ObjectExtending.dll", "lib/netstandard2.0/Volo.Abp.ObjectExtending.pdb", "lib/netstandard2.0/Volo.Abp.ObjectExtending.xml", "lib/netstandard2.1/Volo.Abp.ObjectExtending.dll", "lib/netstandard2.1/Volo.Abp.ObjectExtending.pdb", "lib/netstandard2.1/Volo.Abp.ObjectExtending.xml", "volo.abp.objectextending.8.1.4.nupkg.sha512", "volo.abp.objectextending.nuspec"]}, "Volo.Abp.ObjectMapping/8.1.4": {"sha512": "eXgVvXGv0rli7e+s73dYBZd1dQZ2J1x649V3fkR1HAx8TIwxCR++9FszJmq3ZchiSqQ7m/7d3+ZyloStU4RZng==", "type": "package", "path": "volo.abp.objectmapping/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.ObjectMapping.abppkg", "content/Volo.Abp.ObjectMapping.abppkg.analyze.json", "lib/net8.0/Volo.Abp.ObjectMapping.dll", "lib/net8.0/Volo.Abp.ObjectMapping.pdb", "lib/net8.0/Volo.Abp.ObjectMapping.xml", "lib/netstandard2.0/Volo.Abp.ObjectMapping.dll", "lib/netstandard2.0/Volo.Abp.ObjectMapping.pdb", "lib/netstandard2.0/Volo.Abp.ObjectMapping.xml", "lib/netstandard2.1/Volo.Abp.ObjectMapping.dll", "lib/netstandard2.1/Volo.Abp.ObjectMapping.pdb", "lib/netstandard2.1/Volo.Abp.ObjectMapping.xml", "volo.abp.objectmapping.8.1.4.nupkg.sha512", "volo.abp.objectmapping.nuspec"]}, "Volo.Abp.Security/8.1.4": {"sha512": "/FVWhbDjnHWvpquMiDpVSnnDFqxNXWuAtxuS8Iq177oMN0UQhUIERruepqXjk8X3i/EYm0Nsj6rpgcTqiipS2Q==", "type": "package", "path": "volo.abp.security/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Security.abppkg", "content/Volo.Abp.Security.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Security.dll", "lib/net8.0/Volo.Abp.Security.pdb", "lib/net8.0/Volo.Abp.Security.xml", "lib/netstandard2.0/Volo.Abp.Security.dll", "lib/netstandard2.0/Volo.Abp.Security.pdb", "lib/netstandard2.0/Volo.Abp.Security.xml", "lib/netstandard2.1/Volo.Abp.Security.dll", "lib/netstandard2.1/Volo.Abp.Security.pdb", "lib/netstandard2.1/Volo.Abp.Security.xml", "volo.abp.security.8.1.4.nupkg.sha512", "volo.abp.security.nuspec"]}, "Volo.Abp.Serialization/8.1.4": {"sha512": "DsxE6T54Zuudoc5N0TFTgrsrg07UffX48WqP+dPHEJkY3KaD9b0zxeFwmCIJMupaqi+IQyJ/qr+uiv+eGzKMrQ==", "type": "package", "path": "volo.abp.serialization/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Serialization.abppkg", "content/Volo.Abp.Serialization.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Serialization.dll", "lib/net8.0/Volo.Abp.Serialization.pdb", "lib/net8.0/Volo.Abp.Serialization.xml", "lib/netstandard2.0/Volo.Abp.Serialization.dll", "lib/netstandard2.0/Volo.Abp.Serialization.pdb", "lib/netstandard2.0/Volo.Abp.Serialization.xml", "lib/netstandard2.1/Volo.Abp.Serialization.dll", "lib/netstandard2.1/Volo.Abp.Serialization.pdb", "lib/netstandard2.1/Volo.Abp.Serialization.xml", "volo.abp.serialization.8.1.4.nupkg.sha512", "volo.abp.serialization.nuspec"]}, "Volo.Abp.Settings/8.1.4": {"sha512": "LhibNHGZpPKiLMhGSeDp9E2RGuWrw3GxeglF6Jws83jB4hnWMzyO+4xM7ZGQQc54ANCA1of+ARlZUNQWK39grw==", "type": "package", "path": "volo.abp.settings/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Settings.abppkg", "content/Volo.Abp.Settings.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Settings.dll", "lib/net8.0/Volo.Abp.Settings.pdb", "lib/net8.0/Volo.Abp.Settings.xml", "lib/netstandard2.0/Volo.Abp.Settings.dll", "lib/netstandard2.0/Volo.Abp.Settings.pdb", "lib/netstandard2.0/Volo.Abp.Settings.xml", "lib/netstandard2.1/Volo.Abp.Settings.dll", "lib/netstandard2.1/Volo.Abp.Settings.pdb", "lib/netstandard2.1/Volo.Abp.Settings.xml", "volo.abp.settings.8.1.4.nupkg.sha512", "volo.abp.settings.nuspec"]}, "Volo.Abp.Specifications/8.1.4": {"sha512": "WehbhDYwisIK4dqL8HEqcuyjUfXdj1Ps/5FJa6CLdO77tZXKxaw9+Um7jrECrSNOcL0r70bWiSudW0TH3C8ySQ==", "type": "package", "path": "volo.abp.specifications/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Specifications.abppkg", "content/Volo.Abp.Specifications.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Specifications.dll", "lib/net8.0/Volo.Abp.Specifications.pdb", "lib/net8.0/Volo.Abp.Specifications.xml", "lib/netstandard2.0/Volo.Abp.Specifications.dll", "lib/netstandard2.0/Volo.Abp.Specifications.pdb", "lib/netstandard2.0/Volo.Abp.Specifications.xml", "lib/netstandard2.1/Volo.Abp.Specifications.dll", "lib/netstandard2.1/Volo.Abp.Specifications.pdb", "lib/netstandard2.1/Volo.Abp.Specifications.xml", "volo.abp.specifications.8.1.4.nupkg.sha512", "volo.abp.specifications.nuspec"]}, "Volo.Abp.Threading/8.1.4": {"sha512": "aOxSG+kpyE0cpPO1av1sFVNbVjvOHpgEENPP55jAvsKWuw1NZB6j6vS4U5+fkGrjZFFe7ILjJjA7yLLcwp15kg==", "type": "package", "path": "volo.abp.threading/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Threading.abppkg", "content/Volo.Abp.Threading.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Threading.dll", "lib/net8.0/Volo.Abp.Threading.pdb", "lib/net8.0/Volo.Abp.Threading.xml", "lib/netstandard2.0/Volo.Abp.Threading.dll", "lib/netstandard2.0/Volo.Abp.Threading.pdb", "lib/netstandard2.0/Volo.Abp.Threading.xml", "lib/netstandard2.1/Volo.Abp.Threading.dll", "lib/netstandard2.1/Volo.Abp.Threading.pdb", "lib/netstandard2.1/Volo.Abp.Threading.xml", "volo.abp.threading.8.1.4.nupkg.sha512", "volo.abp.threading.nuspec"]}, "Volo.Abp.Timing/8.1.4": {"sha512": "nqX7H/MKfMd1fcs5mj3LGmX12A/DXyj8qXVBw7tRcu9b0Px/Bh5i3lqa9YiAb7bES1ezTLX011D46aHySrnmXw==", "type": "package", "path": "volo.abp.timing/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Timing.abppkg", "content/Volo.Abp.Timing.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Timing.dll", "lib/net8.0/Volo.Abp.Timing.pdb", "lib/net8.0/Volo.Abp.Timing.xml", "lib/netstandard2.0/Volo.Abp.Timing.dll", "lib/netstandard2.0/Volo.Abp.Timing.pdb", "lib/netstandard2.0/Volo.Abp.Timing.xml", "lib/netstandard2.1/Volo.Abp.Timing.dll", "lib/netstandard2.1/Volo.Abp.Timing.pdb", "lib/netstandard2.1/Volo.Abp.Timing.xml", "volo.abp.timing.8.1.4.nupkg.sha512", "volo.abp.timing.nuspec"]}, "Volo.Abp.UI/8.1.4": {"sha512": "7/2aiKPpkm3aBWcTke/WeDoKG6v5bNdN6Cvf7XvHbXHrA4Gaz4xJ11dR1rTtb8Nfu+DZRrsm4z4m9fzkLXZv7g==", "type": "package", "path": "volo.abp.ui/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.UI.abppkg", "content/Volo.Abp.UI.abppkg.analyze.json", "lib/net8.0/Volo.Abp.UI.dll", "lib/net8.0/Volo.Abp.UI.pdb", "lib/net8.0/Volo.Abp.UI.xml", "lib/netstandard2.0/Volo.Abp.UI.dll", "lib/netstandard2.0/Volo.Abp.UI.pdb", "lib/netstandard2.0/Volo.Abp.UI.xml", "lib/netstandard2.1/Volo.Abp.UI.dll", "lib/netstandard2.1/Volo.Abp.UI.pdb", "lib/netstandard2.1/Volo.Abp.UI.xml", "volo.abp.ui.8.1.4.nupkg.sha512", "volo.abp.ui.nuspec"]}, "Volo.Abp.UI.Navigation/8.1.4": {"sha512": "0OXhLo83Ee1CZgYYxWDs1/bBdlIb65+BE8Hkka0ztfKLEAPZ4IHJEW4C74RUO9FwuwDXI2G7K17RFnHW3yfcFA==", "type": "package", "path": "volo.abp.ui.navigation/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.UI.Navigation.abppkg", "content/Volo.Abp.UI.Navigation.abppkg.analyze.json", "lib/net8.0/Volo.Abp.UI.Navigation.dll", "lib/net8.0/Volo.Abp.UI.Navigation.pdb", "lib/net8.0/Volo.Abp.UI.Navigation.xml", "lib/netstandard2.0/Volo.Abp.UI.Navigation.dll", "lib/netstandard2.0/Volo.Abp.UI.Navigation.pdb", "lib/netstandard2.0/Volo.Abp.UI.Navigation.xml", "lib/netstandard2.1/Volo.Abp.UI.Navigation.dll", "lib/netstandard2.1/Volo.Abp.UI.Navigation.pdb", "lib/netstandard2.1/Volo.Abp.UI.Navigation.xml", "volo.abp.ui.navigation.8.1.4.nupkg.sha512", "volo.abp.ui.navigation.nuspec"]}, "Volo.Abp.Uow/8.1.4": {"sha512": "9UjVi0qRK2X6Fw3W5Dfjz+dZ1wycey+C0jIPM4PSNKXC3sDLIh+4erQqnP2vtcKHOAUSI7LYdbu5pTTKehotuA==", "type": "package", "path": "volo.abp.uow/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Uow.abppkg", "content/Volo.Abp.Uow.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Uow.dll", "lib/net8.0/Volo.Abp.Uow.pdb", "lib/net8.0/Volo.Abp.Uow.xml", "lib/netstandard2.0/Volo.Abp.Uow.dll", "lib/netstandard2.0/Volo.Abp.Uow.pdb", "lib/netstandard2.0/Volo.Abp.Uow.xml", "lib/netstandard2.1/Volo.Abp.Uow.dll", "lib/netstandard2.1/Volo.Abp.Uow.pdb", "lib/netstandard2.1/Volo.Abp.Uow.xml", "volo.abp.uow.8.1.4.nupkg.sha512", "volo.abp.uow.nuspec"]}, "Volo.Abp.Users.Abstractions/8.1.4": {"sha512": "iBxpFFtNoSaB0XUN39ijn93W/IbMFBGLKwlrysLQc/WspdLj35+dEszm4YAMLUlIF+qdFggc849S1FN50GI1jg==", "type": "package", "path": "volo.abp.users.abstractions/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Users.Abstractions.abppkg", "content/Volo.Abp.Users.Abstractions.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Users.Abstractions.dll", "lib/net8.0/Volo.Abp.Users.Abstractions.pdb", "lib/net8.0/Volo.Abp.Users.Abstractions.xml", "lib/netstandard2.0/Volo.Abp.Users.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.Users.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.Users.Abstractions.xml", "lib/netstandard2.1/Volo.Abp.Users.Abstractions.dll", "lib/netstandard2.1/Volo.Abp.Users.Abstractions.pdb", "lib/netstandard2.1/Volo.Abp.Users.Abstractions.xml", "volo.abp.users.abstractions.8.1.4.nupkg.sha512", "volo.abp.users.abstractions.nuspec"]}, "Volo.Abp.Users.Domain/8.1.4": {"sha512": "6s7ukjCYkERMsTGh4sIwmrpYWfS4BQl7RPkIN6pDFFk6KYrGwxHN2aoSYsYErHT95qi3lOXni/bb2NA2J+0BOA==", "type": "package", "path": "volo.abp.users.domain/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Users.Domain.abppkg", "content/Volo.Abp.Users.Domain.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Users.Domain.dll", "lib/net8.0/Volo.Abp.Users.Domain.pdb", "lib/net8.0/Volo.Abp.Users.Domain.xml", "lib/netstandard2.0/Volo.Abp.Users.Domain.dll", "lib/netstandard2.0/Volo.Abp.Users.Domain.pdb", "lib/netstandard2.0/Volo.Abp.Users.Domain.xml", "lib/netstandard2.1/Volo.Abp.Users.Domain.dll", "lib/netstandard2.1/Volo.Abp.Users.Domain.pdb", "lib/netstandard2.1/Volo.Abp.Users.Domain.xml", "volo.abp.users.domain.8.1.4.nupkg.sha512", "volo.abp.users.domain.nuspec"]}, "Volo.Abp.Users.Domain.Shared/8.1.4": {"sha512": "dZs1e8tl8I3DcAsULs40YIeH8NOvsuUENovrhIGJkpTBrbB3sbBglFR0eFN/do78hdeD8aolq6ieicaA/9uRZw==", "type": "package", "path": "volo.abp.users.domain.shared/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Users.Domain.Shared.abppkg", "content/Volo.Abp.Users.Domain.Shared.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Users.Domain.Shared.dll", "lib/net8.0/Volo.Abp.Users.Domain.Shared.pdb", "lib/net8.0/Volo.Abp.Users.Domain.Shared.xml", "lib/netstandard2.0/Volo.Abp.Users.Domain.Shared.dll", "lib/netstandard2.0/Volo.Abp.Users.Domain.Shared.pdb", "lib/netstandard2.0/Volo.Abp.Users.Domain.Shared.xml", "lib/netstandard2.1/Volo.Abp.Users.Domain.Shared.dll", "lib/netstandard2.1/Volo.Abp.Users.Domain.Shared.pdb", "lib/netstandard2.1/Volo.Abp.Users.Domain.Shared.xml", "volo.abp.users.domain.shared.8.1.4.nupkg.sha512", "volo.abp.users.domain.shared.nuspec"]}, "Volo.Abp.Validation/8.1.4": {"sha512": "RIKUhTUsLkdzDv3g2Oaywm1VnmdR0KqL8MYzY5p80/NNk6SVw9UVJcfxFGcbfPD26xqvDbn7tu0O2csDa1KVQw==", "type": "package", "path": "volo.abp.validation/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Validation.abppkg", "content/Volo.Abp.Validation.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Validation.dll", "lib/net8.0/Volo.Abp.Validation.pdb", "lib/net8.0/Volo.Abp.Validation.xml", "lib/netstandard2.0/Volo.Abp.Validation.dll", "lib/netstandard2.0/Volo.Abp.Validation.pdb", "lib/netstandard2.0/Volo.Abp.Validation.xml", "lib/netstandard2.1/Volo.Abp.Validation.dll", "lib/netstandard2.1/Volo.Abp.Validation.pdb", "lib/netstandard2.1/Volo.Abp.Validation.xml", "volo.abp.validation.8.1.4.nupkg.sha512", "volo.abp.validation.nuspec"]}, "Volo.Abp.Validation.Abstractions/8.1.4": {"sha512": "X9vjFMGbHVlZ6s9lcJAqnwtHoc0DsdHj36j0xj/d7Ra45dIPEesg66/t0NN6O0T8g0MDlhwNzShFmh9X5yeE+A==", "type": "package", "path": "volo.abp.validation.abstractions/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Validation.Abstractions.abppkg", "content/Volo.Abp.Validation.Abstractions.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Validation.Abstractions.dll", "lib/net8.0/Volo.Abp.Validation.Abstractions.pdb", "lib/net8.0/Volo.Abp.Validation.Abstractions.xml", "lib/netstandard2.0/Volo.Abp.Validation.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.Validation.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.Validation.Abstractions.xml", "lib/netstandard2.1/Volo.Abp.Validation.Abstractions.dll", "lib/netstandard2.1/Volo.Abp.Validation.Abstractions.pdb", "lib/netstandard2.1/Volo.Abp.Validation.Abstractions.xml", "volo.abp.validation.abstractions.8.1.4.nupkg.sha512", "volo.abp.validation.abstractions.nuspec"]}, "Volo.Abp.VirtualFileSystem/8.1.4": {"sha512": "KvbiYsrAZYRS2Vm+IsNOvDm0EUoY3RE5fx7PgMOilD11DrkTVziJT2MhQoukWrOAXW7qoN3HAET1YHYyqs9tNw==", "type": "package", "path": "volo.abp.virtualfilesystem/8.1.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.VirtualFileSystem.abppkg", "content/Volo.Abp.VirtualFileSystem.abppkg.analyze.json", "lib/net8.0/Volo.Abp.VirtualFileSystem.dll", "lib/net8.0/Volo.Abp.VirtualFileSystem.pdb", "lib/net8.0/Volo.Abp.VirtualFileSystem.xml", "lib/netstandard2.0/Volo.Abp.VirtualFileSystem.dll", "lib/netstandard2.0/Volo.Abp.VirtualFileSystem.pdb", "lib/netstandard2.0/Volo.Abp.VirtualFileSystem.xml", "lib/netstandard2.1/Volo.Abp.VirtualFileSystem.dll", "lib/netstandard2.1/Volo.Abp.VirtualFileSystem.pdb", "lib/netstandard2.1/Volo.Abp.VirtualFileSystem.xml", "volo.abp.virtualfilesystem.8.1.4.nupkg.sha512", "volo.abp.virtualfilesystem.nuspec"]}, "EdTech.Study.Application.Contracts/0.1.0": {"type": "project", "path": "../EdTech.Study.Application.Contracts/EdTech.Study.Application.Contracts.csproj", "msbuildProject": "../EdTech.Study.Application.Contracts/EdTech.Study.Application.Contracts.csproj"}, "EdTech.Study.Domain/0.1.0": {"type": "project", "path": "../EdTech.Study.Domain/EdTech.Study.Domain.csproj", "msbuildProject": "../EdTech.Study.Domain/EdTech.Study.Domain.csproj"}, "EdTech.Study.Domain.Shared/0.1.0": {"type": "project", "path": "../EdTech.Study.Domain.Shared/EdTech.Study.Domain.Shared.csproj", "msbuildProject": "../EdTech.Study.Domain.Shared/EdTech.Study.Domain.Shared.csproj"}}, "projectFileDependencyGroups": {"net8.0": ["ConfigureAwait.Fody >= 3.3.1", "EdTech.Study.Application.Contracts >= 0.1.0", "Fody >= 6.5.3", "Microsoft.AspNetCore.OData >= 8.2.5", "Volo.Abp.AspNetCore.Mvc >= 8.1.4"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "0.1.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.HttpApi\\EdTech.Study.HttpApi.csproj", "projectName": "EdTech.Study.HttpApi", "projectPath": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.HttpApi\\EdTech.Study.HttpApi.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.HttpApi\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.abp.io/22f283ef-73fc-4811-be2f-f87b17465859/v3/index.json": {}, "https://pkgs.dev.azure.com/bpn25106/_packaging/modules/nuget/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Application.Contracts\\EdTech.Study.Application.Contracts.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Application.Contracts\\EdTech.Study.Application.Contracts.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"ConfigureAwait.Fody": {"suppressParent": "All", "target": "Package", "version": "[3.3.1, )"}, "Fody": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[6.5.3, )"}, "Microsoft.AspNetCore.OData": {"target": "Package", "version": "[8.2.5, )"}, "Volo.Abp.AspNetCore.Mvc": {"target": "Package", "version": "[8.1.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100/PortableRuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1903", "level": "Warning", "warningLevel": 1, "message": "Package 'Microsoft.Extensions.Caching.Memory' 8.0.0 has a known high severity vulnerability, https://github.com/advisories/GHSA-qj66-m88j-hmgj", "libraryId": "Microsoft.Extensions.Caching.Memory", "targetGraphs": ["net8.0"]}, {"code": "NU1902", "level": "Warning", "warningLevel": 1, "message": "Package 'Microsoft.IdentityModel.JsonWebTokens' 7.0.3 has a known moderate severity vulnerability, https://github.com/advisories/GHSA-59j7-ghrg-fj52", "libraryId": "Microsoft.IdentityModel.JsonWebTokens", "targetGraphs": ["net8.0"]}, {"code": "NU1902", "level": "Warning", "warningLevel": 1, "message": "Package 'System.IdentityModel.Tokens.Jwt' 7.0.3 has a known moderate severity vulnerability, https://github.com/advisories/GHSA-59j7-ghrg-fj52", "libraryId": "System.IdentityModel.Tokens.Jwt", "targetGraphs": ["net8.0"]}, {"code": "NU1903", "level": "Warning", "warningLevel": 1, "message": "Package 'System.Linq.Dynamic.Core' 1.3.5 has a known high severity vulnerability, https://github.com/advisories/GHSA-4cv2-4hjh-77rx", "libraryId": "System.Linq.Dynamic.Core", "targetGraphs": ["net8.0"]}, {"code": "NU1903", "level": "Warning", "warningLevel": 1, "message": "Package 'System.Text.Json' 8.0.0 has a known high severity vulnerability, https://github.com/advisories/GHSA-8g4q-xg66-9fp4", "libraryId": "System.Text.Json", "targetGraphs": ["net8.0"]}, {"code": "NU1903", "level": "Warning", "warningLevel": 1, "message": "Package 'System.Text.Json' 8.0.0 has a known high severity vulnerability, https://github.com/advisories/GHSA-hh2w-p6rv-4g7w", "libraryId": "System.Text.Json", "targetGraphs": ["net8.0"]}]}