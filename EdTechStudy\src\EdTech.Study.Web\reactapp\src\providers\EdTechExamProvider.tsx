import React, { useEffect, useState } from 'react';
import { ConfigProvider } from 'antd';
import LoadingScreen from '../components/common/Loading/LoadingScreen';
import { IEdTechExamProviderProps } from './EdTechExamProvider.interface';
import { lazy } from 'react';
const ExamManagementRouter = lazy(
  () => import('../routing/ExamManagementRouting')
);

const EdTechExamProvider: React.FC<IEdTechExamProviderProps> = ({
  theme = {
    token: {
      colorPrimary: '#1677ff',
    },
  },
}) => {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate loading data
    const timer = setTimeout(() => {
      setLoading(false);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  if (loading) {
    return <LoadingScreen />;
  }

  return (
    <ConfigProvider theme={theme}>
      <ExamManagementRouter />
    </ConfigProvider>
  );
};

export default EdTechExamProvider;
