import { Suspense } from 'react';
import ReactDOM from 'react-dom/client';
import '../index.css';
import '../styles/modal-fixes.css';
import { Provider } from 'react-redux';
import LoadingScreen from '../components/common/Loading/LoadingScreen';
import { questionStore } from '../store/questionStore';
import EdTechQuestionProvider from '../providers/EdTechQuestionProvider';
import { registerLicenseSyncfusionBase } from '../utils/syncfusion-license';
import { MathJaxProvider } from '../components/common/MathJax/MathJaxWrapper';
import { useDevLicenseSyncfusion } from './syncfusion-license-custom';
import { CombinedThemeProvider } from '../providers/theme';

// Register Syncfusion license
registerLicenseSyncfusionBase();
useDevLicenseSyncfusion();

export const QuestionPage = () => {
  return (
    <Provider store={questionStore}>
      <CombinedThemeProvider>
        <Suspense fallback={<LoadingScreen />}>
          <MathJaxProvider>
            <EdTechQuestionProvider />
          </MathJaxProvider>
        </Suspense>
      </CombinedThemeProvider>
    </Provider>
  );
};

const rootQuestionPageElement = document.getElementById(
  'QuestionPage'
) as HTMLElement;

const rootQuestionPage = ReactDOM.createRoot(rootQuestionPageElement);
rootQuestionPage.render(<QuestionPage />);
