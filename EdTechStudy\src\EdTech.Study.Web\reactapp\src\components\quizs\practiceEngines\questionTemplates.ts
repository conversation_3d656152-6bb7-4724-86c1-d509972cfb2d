import { Guid } from 'guid-typescript';
import {
  QuizQuestion,
  MatchingQuestion,
  BaseQuestion,
  QuestionType,
} from '../../../interfaces/quizs/questionBase';
import { FillBlanksQuestion } from '../../../interfaces/quizs/fillblanks.interfaces';
import { createMultiSelectQuestionTemplate } from '../multiselect';
import { createEssayQuestionTemplate } from '../essay';
import { ContentFormatType } from '../../../interfaces/exams/examEnums';

export const createQuizQuestionTemplate = (
  title: string = 'Câu hỏi trắc nghiệm'
): QuizQuestion => {
  const id = Guid.create().toString();
  return {
    clientId: id,
    type: 'quiz',
    title,
    content: 'Nhập nội dung câu hỏi ở đây',
    difficulty: 1,
    syncQuestion: false,
    questionType: QuestionType.SingleChoice,
    options: [
      {
        id: undefined,
        isCorrect: true,
        clientId: Guid.create().toString(),
        content: 'Phương án A',
        contentFormat: ContentFormatType.Html,
        order: 0,
      },
      {
        id: undefined,
        content: 'Phương án B',
        isCorrect: false,
        clientId: Guid.create().toString(),
        contentFormat: ContentFormatType.Html,
        order: 1,
      },
      {
        id: undefined,
        content: 'Phương án C',
        isCorrect: false,
        clientId: Guid.create().toString(),
        contentFormat: ContentFormatType.Html,
        order: 2,
      },
      {
        id: undefined,
        content: 'Phương án D',
        isCorrect: false,
        clientId: Guid.create().toString(),
        contentFormat: ContentFormatType.Html,
        order: 3,
      },
    ],
    explanation: 'Thêm giải thích đáp án',
    points: 1,
  };
};

export const createMatchingQuestionTemplate = (
  title: string = 'Câu hỏi nối đáp án'
): MatchingQuestion => {
  const id = Guid.create().toString();
  return {
    clientId: id,
    type: 'matching',
    title,
    difficulty: 1,
    syncQuestion: false,
    questionType: QuestionType.Matching,
    content: 'Nối các mục từ cột A với các mục tương ứng trong cột B',
    leftItems: [
      {
        clientId: `${id}L1`,
        content: 'Mục 1',
        type: 'text',
        matchId: `${id}R1`,
      },
      {
        clientId: `${id}L2`,
        content: 'Mục 2',
        type: 'text',
        matchId: `${id}R2`,
      },
      {
        clientId: `${id}L3`,
        content: 'Mục 3',
        type: 'text',
        matchId: `${id}R3`,
      },
    ],
    rightItems: [
      {
        clientId: `${id}R1`,
        content: 'Mục 4',
        type: 'text',
        matchId: `${id}L1`,
      },
      {
        clientId: `${id}R2`,
        content: 'Mục 5',
        type: 'text',
        matchId: `${id}L2`,
      },
      {
        clientId: `${id}R3`,
        content: 'Mục 6',
        type: 'text',
        matchId: `${id}L3`,
      },
    ],
    shuffleItems: true,
    options: [
      {
        id: Guid.create().toString(),
        left: `${id}L1`,
        right: `${id}R1`,
        isCorrect: true,
        clientId: '',
        content: '',
        contentFormat: ContentFormatType.Html,
        order: 0,
      },
      {
        id: Guid.create().toString(),
        left: `${id}L2`,
        right: `${id}R2`,
        isCorrect: true,
        clientId: '',
        content: '',
        contentFormat: ContentFormatType.Html,
        order: 0,
      },
      {
        id: Guid.create().toString(),
        left: `${id}L3`,
        right: `${id}R3`,
        isCorrect: true,
        clientId: '',
        content: '',
        contentFormat: ContentFormatType.Html,
        order: 0,
      },
    ],
  };
};

export const createFillBlanksQuestionTemplate = (
  title: string = 'Câu hỏi điền từ'
): FillBlanksQuestion => {
  const id = Guid.create().toString();
  return {
    clientId: id,
    type: 'fillblanks',
    questionType: QuestionType.FillInBlank,
    difficulty: 1,
    syncQuestion: false,
    title,
    content: 'Hà Nội là ____ của Việt Nam.',
    blanks: [
      {
        clientId: Guid.create().toString(),
        correctAnswer: 'thủ đô',
        alternativeAnswers: ['Thủ đô'],
        hint: 'Trung tâm chính trị của đất nước',
      },
    ],
    explanation: 'Hà Nội là thủ đô của Việt Nam.',
    caseSensitive: false,
    showHints: true,
    points: 1,
  };
};

export { createEssayQuestionTemplate, createMultiSelectQuestionTemplate };

export class QuestionTemplateFactory {
  private static instance: QuestionTemplateFactory;
  private templateMaps: Record<string, (props?: any) => BaseQuestion>;

  private constructor() {
    this.templateMaps = {};
  }

  static getInstance(): QuestionTemplateFactory {
    if (!QuestionTemplateFactory.instance) {
      QuestionTemplateFactory.instance = new QuestionTemplateFactory();
    }
    return QuestionTemplateFactory.instance;
  }

  register(type: string, callback: (props?: any) => BaseQuestion) {
    this.templateMaps[type] = callback;
  }

  create(type: string): BaseQuestion | null {
    const creation = this.templateMaps[type];
    return creation ? creation() : null;
  }

  createWithProps(type: string, props: any): BaseQuestion | null {
    const creation = this.templateMaps[type];
    return creation ? creation(props) : null;
  }
}
