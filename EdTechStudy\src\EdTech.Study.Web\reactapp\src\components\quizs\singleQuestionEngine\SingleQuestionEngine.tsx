import React, { useMemo } from 'react';
import {
  BaseQuestion,
  getQuestionComponentRegistry,
} from '../../../interfaces/quizs/questionBase';
import HashHelper from '../../../utils/HashHelper';
import './SingleQuestionEngine.css';

export interface SingleQuestionEngineProps {
  question: BaseQuestion;
  configMode: boolean;
}

/**
 * SingleQuestionEngine - Optimized version of PracticeEngine for single question editing
 * This component is designed to render and handle a single question in edit or preview mode
 */
const SingleQuestionEngine: React.FC<SingleQuestionEngineProps> = ({
  question,
  configMode,
}) => {
  const isPreviewMode = useMemo(() => !configMode, [configMode]);

  // Memoize the question component and related values
  const questionComponent = useMemo(() => {
    if (!question) return null;

    // Get component from map based on question type
    const QuestionComponent = getQuestionComponentRegistry().getComponent(
      question.type
    )?.component;

    if (!QuestionComponent) {
      return (
        <div className="question-not-found">
          <PERSON>hông tìm thấy loại câu hỏi phù hợp: {question.type}
        </div>
      );
    }

    const componentId = HashHelper.computeHash([question.clientId]);

    return (
      <div className="single-question-container" id={question.clientId}>
        <QuestionComponent
          id={componentId}
          question={question}
          configMode={!isPreviewMode}
          options={{
            hideSaveButton: true,
            hideDeleteButton: false,
            hideFeedback: false,
          }}
        />
      </div>
    );
  }, [question, isPreviewMode]);

  return (
    <div className="single-question-engine">
      <div className="single-question-content">{questionComponent}</div>
    </div>
  );
};

export default SingleQuestionEngine;
