import React, { useMemo } from 'react';
import { Button, Progress, Space, Tag, Typography } from 'antd';
import { IExamCardProps } from '../../../interfaces/exams/examCard';
import { PracticeExamStatus } from '../../../interfaces/exams/practiceExam';
import { CardAntdCustom } from '../../customs/antd/CardAntdCustom';
import { getStatusConfig, IMappedExamData } from './practiceExamUtils';
import {
  ClockCircleOutlined,
  PlayCircleOutlined,
  MinusCircleOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons';
import {
  AppListDetailOutlineIcon,
  SubjectOutlineIcon,
} from '../../icons/IconRegister';

const { Title, Text } = Typography;

// Enhanced interface for ExamCard props that can accept mapped data
interface IEnhancedExamCardProps extends Omit<IExamCardProps, 'examData'> {
  examData: IMappedExamData | IExamCardProps['examData'];
}

// Type guard to check if exam data is already mapped
const isMappedExamData = (data: any): data is IMappedExamData => {
  return (
    data &&
    typeof data.questionCount === 'number' &&
    typeof data.userStatus === 'string' &&
    typeof data.userProgress === 'number'
  );
};

// Memoized ExamCard component to prevent unnecessary re-renders
const ExamCard: React.FC<IEnhancedExamCardProps> = ({
  examData,
  subject, // Optional - can be undefined if already in mapped data
  grade, // Optional - can be undefined if already in mapped data
  onStartExam,
  onContinueExam,
  onReviewExam,
  className = '',
}) => {
  const displayExam = useMemo(() => {
    // If data is already mapped, use it directly
    if (isMappedExamData(examData)) {
      return {
        id: examData.id || examData.clientId,
        clientId: examData.clientId,
        title: examData.title,
        description: examData.description || '',
        examType: examData.examType,
        questionCount: examData.questionCount,
        estimatedDuration: examData.estimatedDuration,
        duration: examData.duration,
        userProgress: examData.userProgress,
        userStatus: examData.userStatus,
        userBestScore: examData.userBestScore,
        subject:
          examData.subject ||
          (subject
            ? {
                id: subject.id,
                name: subject.name,
                code: subject.code || '',
              }
            : undefined),
        grade:
          examData.grade ||
          (grade
            ? {
                id: grade.id,
                name: grade.name,
                code: grade.code || '',
              }
            : undefined),
        sections: examData.sections || [],
      };
    }

    // Fallback: Map the data if not already mapped (backward compatibility)
    const questionCount =
      examData.sections?.reduce((total, section) => {
        const sectionQuestions = section.questions?.length || 0;
        const groupQuestions =
          section.groupQuestions?.reduce((groupTotal, group) => {
            return groupTotal + (group.questions?.length || 0);
          }, 0) || 0;
        return total + sectionQuestions + groupQuestions;
      }, 0) || 0;

    const userStatus = PracticeExamStatus.NOT_STARTED;

    return {
      id: examData.id || examData.clientId,
      clientId: examData.clientId,
      title: examData.title,
      description: examData.description || '',
      examType: examData.examType,
      questionCount,
      estimatedDuration: examData.duration || 0,
      duration: examData.duration,
      userProgress: 0,
      userStatus,
      userBestScore: 0,
      subject: subject
        ? {
            id: subject.id,
            name: subject.name,
            code: subject.code || '',
          }
        : undefined,
      grade: grade
        ? {
            id: grade.id,
            name: grade.name,
            code: grade.code || '',
          }
        : undefined,
      sections: examData.sections || [],
    };
  }, [examData, subject, grade]);

  // Memoized status config to prevent recreation on every render
  const statusConfig = useMemo(() => {
    const userStatus =
      displayExam?.userStatus || PracticeExamStatus.NOT_STARTED;
    return getStatusConfig(
      userStatus,
      () => onStartExam(examData),
      () => onContinueExam(examData),
      () => onReviewExam(examData)
    );
  }, [
    displayExam?.userStatus,
    examData,
    onStartExam,
    onContinueExam,
    onReviewExam,
  ]);

  return (
    <CardAntdCustom
      className={`exam-card ${className} tailwind-h-full tailwind-shadow-sm hover:tailwind-shadow-md tailwind-transition-shadow tailwind-duration-200 tailwind-flex tailwind-flex-col`}
      hoverable
      bodyStyle={{
        padding: '20px',
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
      }}
    >
      {/* Content Area - Takes available space */}
      <div className="tailwind-flex-1 tailwind-flex tailwind-flex-col">
        {/* Header */}
        <div className="tailwind-mb-4">
          <Space direction="vertical" size="small" className="tailwind-w-full">
            {/* Subject and Grade Tags */}
            <div className="tailwind-flex tailwind-flex-wrap tailwind-gap-2">
              <Tag
                style={{ border: 'none', borderRadius: '12px' }}
                className="tailwind-px-3 tailwind-font-medium tailwind-uppercase tailwind-py-1 tailwind-text-primary tailwind-color-primary tailwind-bg-selected tailwind-flex tailwind-items-center"
              >
                <SubjectOutlineIcon
                  height={14}
                  width={14}
                  className="tailwind-mr-1"
                  style={{ display: 'inline-flex', alignItems: 'center' }}
                />
                <span className="tailwind-leading-none">
                  {displayExam.subject?.name || 'Tổng hợp'}
                </span>
              </Tag>
              <Tag
                style={{ border: 'none', borderRadius: '12px' }}
                className="tailwind-px-3 tailwind-py-1 tailwind-font-medium tailwind-uppercase tailwind-bg-gray-100 tailwind-flex tailwind-items-center"
              >
                <span className="tailwind-leading-none">
                  {displayExam.grade?.name || '12'}
                </span>
              </Tag>
            </div>

            {/* Title */}
            <Title
              level={5}
              className="tailwind-m-0 tailwind-text-gray-800 tailwind-line-clamp-2"
              style={{ fontSize: '16px', lineHeight: '1.4' }}
            >
              {displayExam.title}
            </Title>
          </Space>
        </div>

        {/* Stats Row - Align left */}
        <div className="tailwind-flex tailwind-justify-start tailwind-gap-6 tailwind-mb-4">
          <div className="tailwind-flex tailwind-items-center tailwind-gap-2">
            <AppListDetailOutlineIcon
              style={{ fill: 'var(--edtt-color-primary)', fontSize: '16px' }}
            />
            <span className="tailwind-font-semibold tailwind-text-sm">
              {displayExam.questionCount || 0}
            </span>
          </div>
          <div className="tailwind-flex tailwind-items-center tailwind-gap-2">
            <ClockCircleOutlined
              style={{ color: 'var(--edtt-color-primary)', fontSize: '16px' }}
            />
            <span className="tailwind-font-semibold tailwind-text-sm">
              {displayExam.estimatedDuration || examData.duration || 0}p
            </span>
          </div>
        </div>

        {/* Spacer - Takes up remaining space */}
        <div className="tailwind-flex-1"></div>
      </div>

      {/* Progress Section - Always above button */}
      <div className="tailwind-mb-4">
        {/* Progress Header - Progress percentage and Status */}
        <div className="tailwind-flex tailwind-justify-between tailwind-items-center tailwind-mb-2">
          <Text className="tailwind-text-sm tailwind-text-gray-800 tailwind-font-medium">
            Tiến độ: {displayExam.userProgress}%
          </Text>
          <div className="tailwind-flex tailwind-items-center tailwind-gap-2">
            {displayExam.userProgress === 0 ? (
              <>
                <MinusCircleOutlined
                  style={{ color: '#ff4d4f', fontSize: '14px' }}
                />
                <Text
                  className="tailwind-text-sm tailwind-font-medium"
                  style={{ color: '#ff4d4f' }}
                >
                  Chưa làm
                </Text>
              </>
            ) : displayExam.userProgress >= 100 ? (
              <>
                <CheckCircleOutlined
                  style={{ color: '#52c41a', fontSize: '14px' }}
                />
                <Text
                  className="tailwind-text-sm tailwind-font-medium"
                  style={{ color: '#52c41a' }}
                >
                  Hoàn thành
                </Text>
              </>
            ) : (
              <>
                <ClockCircleOutlined
                  style={{ color: '#faad14', fontSize: '14px' }}
                />
                <Text
                  className="tailwind-text-sm tailwind-font-medium"
                  style={{ color: '#faad14' }}
                >
                  Đang làm
                </Text>
              </>
            )}
          </div>
        </div>
        {/* Progress Bar - Always show */}
        <Progress
          percent={displayExam.userProgress}
          strokeColor={
            displayExam.userProgress === 0
              ? '#d9d9d9'
              : displayExam.userProgress >= 100
              ? '#52c41a'
              : '#faad14'
          }
          trailColor="#f5f5f5"
          showInfo={false}
          strokeWidth={8}
        />
      </div>

      {/* Action Button - Always at bottom */}
      <Button
        type={statusConfig.buttonVariant === 'primary' ? 'primary' : 'default'}
        block
        icon={<PlayCircleOutlined />}
        onClick={statusConfig.buttonAction}
        className="tailwind-h-10 tailwind-mt-auto"
      >
        {statusConfig.buttonText}
      </Button>
    </CardAntdCustom>
  );
};

// Export memoized component with improved comparison
export default React.memo(ExamCard, (prevProps, nextProps) => {
  // Custom comparison to prevent unnecessary re-renders
  // Only check the essential properties that affect rendering
  try {
    const prevExam = prevProps.examData;
    const nextExam = nextProps.examData;

    // Check if mapped data properties have changed
    if (isMappedExamData(prevExam) && isMappedExamData(nextExam)) {
      return (
        prevExam.id === nextExam.id &&
        prevExam.title === nextExam.title &&
        prevExam.userStatus === nextExam.userStatus &&
        prevExam.userProgress === nextExam.userProgress &&
        prevExam.questionCount === nextExam.questionCount &&
        prevExam.estimatedDuration === nextExam.estimatedDuration &&
        prevProps.className === nextProps.className
      );
    }

    // Fallback comparison for non-mapped data
    return (
      prevExam?.id === nextExam?.id &&
      prevExam?.title === nextExam?.title &&
      prevExam?.duration === nextExam?.duration &&
      prevProps.subject?.id === nextProps.subject?.id &&
      prevProps.grade?.id === nextProps.grade?.id &&
      prevProps.className === nextProps.className
    );
  } catch (error) {
    console.warn('ExamCard memo comparison error:', error);
    // If comparison fails, allow re-render to be safe
    return false;
  }
});

// Also export non-memoized version if needed
export { ExamCard };
