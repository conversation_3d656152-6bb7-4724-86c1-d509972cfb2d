import { Breadcrumb } from 'antd';
import { useNavigate } from 'react-router-dom';

// Define the item type for breadcrumb items
export interface BreadcrumbItem {
  title: string;
  path?: string;
}

// Breadcrumb wrapper component
const BreadcrumbNav: React.FC<{
  items: BreadcrumbItem[];
  className?: string;
}> = ({ items, className = '' }) => {
  const navigate = useNavigate();

  // Convert our items format to Ant Design's items format
  const breadcrumbItems = items.map((item, index) => ({
    title: item.path ? (
      <a onClick={() => (item.path ? navigate(item.path) : null)}>
        {item.title}
      </a>
    ) : (
      item.title
    ),
    key: index.toString(),
  }));

  return (
    <Breadcrumb
      className={`tailwind-py-4 ${className}`}
      items={breadcrumbItems}
    />
  );
};

export default BreadcrumbNav;
