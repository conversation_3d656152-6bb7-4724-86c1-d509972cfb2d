// src/interfaces/exams/examBase.ts
import { LessonGrade } from '../lessons/lessonGrade';
import { Subject } from '../lessons/subject';
import { BaseAnswer, BaseQuestion, QuestionType } from '../quizs/questionBase';
import {
  ExamType,
  ExamStatus,
  ExamSourceType,
  ContentFormatType,
} from './examEnums';

/**
 * Base interface for tree structure in exam components
 */
export interface ExamTreeNode {
  clientId: string;
  parentId?: string;
  nodeType?: string;
}

/**
 * Main Exam interface that represents an exam in the system
 */
export interface ExamBase extends ExamTreeNode {
  // If id is 'undefined' --> backend creates new entity || else backend updates existing entity
  id?: string;

  // Essential information
  title: string;
  description?: string;
  examCode?: string;

  // Classification
  examType: ExamType;
  status?: ExamStatus;
  sourceType?: ExamSourceType;

  // Scheduling
  examPeriod?: string;
  examDate?: Date;
  duration?: number;

  // Grading
  totalScore?: number;

  // Subject and grade information
  subjectId?: string;
  subject?: Subject;
  gradeId?: string;
  grade?: ExamGrade;

  // Section structure
  sections?: ExamSection[];

  // Translated type string
  typeString?: string;

  // Audit information
  creationTime?: string;
  lastModificationTime?: string;
}

/**
 * Interface for group questions within an exam section
 */
export interface ExamGroupQuestion extends ExamTreeNode {
  id?: string;
  content?: string;
  contentFormat?: ContentFormatType;
  instructions?: string;
  order?: number;
  questions?: ExamQuestion[];
  idempotentKey?: string;
  groupQuestionId?: string;
  questionIds?: string[];
}

/**
 * Interface representing a section within an exam
 */
export interface ExamSection extends ExamTreeNode {
  id?: string;
  title: string;
  content: string;
  contentFormat?: ContentFormatType;
  orderIndex: number;
  sectionScore?: number;
  instructions?: string;
  questions: ExamQuestion[];
  groupQuestions: ExamGroupQuestion[];
  questionCount?: number; // For UI display purposes
}

/**
 * Interface for questions that are part of an exam
 */
export interface ExamQuestion extends BaseQuestion, ExamTreeNode {
  id?: string;
  content: string;
  contentFormat: ContentFormatType;
  questionType: QuestionType;
  difficulty: number;
  comment?: string;
  shuffleOptions: boolean;
  explanation?: string;
  sourceType?: ExamSourceType;
  topics?: string | string[];
  tags?: string | string[];
  options?: ExamQuestionOption[];
  matchingItems?: ExamMatchingItem[];
  matchingAnswers?: ExamMatchingAnswer[];
  fillInBlankAnswers?: ExamFillInBlankAnswer[];
  order?: number;
  correctAnswer?: string;
}

/**
 * Interface for an exam's grade information
 */
export interface ExamGrade extends LessonGrade {
  id: string;
  name: string;
  code: string;
  order?: number;
}

/**
 * Interface for options in a multiple choice question
 */
export interface ExamQuestionOption extends BaseAnswer {
  id?: string;
  clientId: string;
  content: string;
  contentFormat: ContentFormatType;
  isCorrect: boolean;
  order: number;
  explanation?: string;
  score?: number;
}

/**
 * Interface for matching items in a matching question
 */
export interface ExamMatchingItem {
  id?: string;
  clientId: string;
  type: number; // 0: Premise (left), 1: Response (right)
  content: string;
  contentFormat: ContentFormatType;
  order: number;
}

/**
 * Interface for matching answers that link left and right items
 */
export interface ExamMatchingAnswer {
  id?: string;
  clientId: string;
  premiseId: string;
  responseId: string;
}

/**
 * Interface for fill-in-blank answers
 */
export interface ExamFillInBlankAnswer {
  id?: string;
  clientId: string;
  blankIndex: number;
  correctAnswers: string[];
  caseSensitive: boolean;
  feedback?: string;
  score?: number;
}

/**
 * Type definition for question list item in sidebar
 */
export type QuestionListItem = {
  sectionIndex: number;
  questionIndex: number;
  questionType: 'independent' | 'group';
  groupIndex?: number;
  question: ExamQuestion;
  displayIndex: number;
  groupTitle?: string;
};

/**
 * Helper function to get a human-readable string for an exam type
 */
export const getExamTypeString = (type: ExamType): string => {
  switch (type) {
    case ExamType.Test15Minutes:
      return 'Test15Minutes';
    case ExamType.Test45Minutes:
      return 'Test45Minutes';
    case ExamType.MockExam:
      return 'MockExam';
    case ExamType.MidTermExam:
      return 'MidTermExam';
    case ExamType.MidTermExam:
      return 'MidTermExam';
    case ExamType.NationalHighSchoolExam:
      return 'NationalHighSchoolExam';
    case ExamType.QualificationExam:
      return 'QualificationExam';
    case ExamType.FinalExam:
      return 'FinalExam';
    case ExamType.Default:
    default:
      return 'Default';
  }
};
