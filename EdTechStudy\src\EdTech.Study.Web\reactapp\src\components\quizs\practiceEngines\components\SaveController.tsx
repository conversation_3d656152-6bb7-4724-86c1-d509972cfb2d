import React, { useState, useEffect } from 'react';
import { Button, Space, message, Tooltip } from 'antd';
import {
  SaveOutlined,
  UndoOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { questionService } from '../questionService';
import PopconfirmAntdCustom from '../../../customs/antd/PopconfirmAntdCustom';
import { BaseQuestion } from '../../../../interfaces/quizs/questionBase';

interface SaveControllerProps {
  questions: BaseQuestion[];
  originalQuestions: BaseQuestion[];
  onSave: (questions: BaseQuestion[]) => void;
  onReset: () => void;
}

/**
 * Component để quản lý và kiểm soát việc lưu câu hỏi
 */
const SaveController: React.FC<SaveControllerProps> = ({
  questions,
  originalQuestions,
  onSave,
  onReset,
}) => {
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Kiểm tra xem có thay đổi nào không
  useEffect(() => {
    const modified = questionService.hasQuestionsToSave(
      originalQuestions,
      questions
    );
    setHasChanges(modified);
  }, [questions, originalQuestions]);

  // Xử lý lưu
  const handleSave = async () => {
    try {
      setIsSaving(true);

      // Chuẩn bị câu hỏi trước khi lưu
      const preparedQuestions =
        questionService.prepareQuestionsForSave(questions);

      // Gọi hàm lưu
      await onSave(preparedQuestions);

      // Thông báo thành công
      message.success('Tất cả câu hỏi đã được lưu thành công!');
    } catch (error) {
      console.error('Lỗi khi lưu câu hỏi:', error);
      message.error('Đã xảy ra lỗi khi lưu câu hỏi. Vui lòng thử lại!');
    } finally {
      setIsSaving(false);
    }
  };

  // Xử lý reset
  const handleReset = () => {
    onReset();
    message.info('Đã hoàn tác tất cả thay đổi');
  };

  return (
    <div className="save-controller">
      <Space>
        <Tooltip
          title={
            hasChanges ? 'Lưu tất cả thay đổi' : 'Không có thay đổi để lưu'
          }
        >
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={handleSave}
            disabled={!hasChanges}
            loading={isSaving}
          >
            Lưu tất cả thay đổi
          </Button>
        </Tooltip>

        {hasChanges && (
          <PopconfirmAntdCustom
            title="Hoàn tác tất cả thay đổi?"
            description="Tất cả thay đổi chưa lưu sẽ bị mất. Bạn có chắc chắn muốn tiếp tục?"
            onConfirm={handleReset}
            okText="Đồng ý"
            cancelText="Hủy"
            icon={<ExclamationCircleOutlined style={{ color: 'orange' }} />}
          >
            <Button icon={<UndoOutlined />}>Hoàn tác thay đổi</Button>
          </PopconfirmAntdCustom>
        )}

        {isSaving && <span className="save-status">Đang lưu...</span>}
      </Space>
    </div>
  );
};

export default SaveController;
