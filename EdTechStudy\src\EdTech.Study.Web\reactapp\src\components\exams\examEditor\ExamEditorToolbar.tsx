import {
  ArrowLeftOutlined,
  ArrowRightOutlined,
  DownOutlined,
  FullscreenExitOutlined,
  FullscreenOutlined,
  GroupOutlined,
  MenuOutlined,
  PlusOutlined,
  QuestionCircleFilled,
} from '@ant-design/icons';
import { Button, Dropdown, MenuProps, Space } from 'antd';
import { memo, useCallback } from 'react';
import Pagination from '../../common/pagination/Pagination';

const ExamEditorToolbar = ({
  className = '',
  loading,
  isFullscreen,
  currentPosition,
  totalSections,
  currentQuestionIndex,
  totalQuestionsGroups,
  handleAddSection,
  handleAddQuestion,
  handleAddGroup,
  handleOpenModalSortSections: _handleOpenModalSortSections,
  handleToggleFullscreen,
  handleOpenMenu,
  handleMoveToPosition,
  handleNextSection,
  handlePrevSection,
  approvedQuestionIndexes,
  title,
}: {
  forceDefault?: boolean;
  className?: string;
  title?: JSX.Element | string;
  loading?: boolean;
  isFullscreen: boolean;
  currentQuestionIndex?: number;
  totalQuestionsGroups?: number;
  currentPosition?: { sectionIndex: number; questionOrGroupIndex: number };
  totalSections?: number;
  handleMoveToPosition?: (position: {
    sectionIndex: number;
    questionIndex: number;
  }) => any;
  handleSave: () => any;
  handleToggleFullscreen: () => any;
  handleAddSection?: () => any;
  handleAddQuestion?: () => any;
  handleAddGroup?: () => any;
  handleShowDemo?: () => any;
  handleOpenModalSortSections?: () => any;
  handleOpenMenu?: () => any;
  handleNextSection?: () => any;
  handlePrevSection?: () => any;
  approvedQuestionIndexes?: boolean[];
}) => {
  const items: MenuProps['items'] = [
    {
      label: 'Thêm phần thi',
      key: 'add-section',
      icon: <PlusOutlined />,
      disabled: !handleAddSection,
    },
    {
      label: 'Thêm câu hỏi',
      key: 'add-question',
      icon: <QuestionCircleFilled />,
      disabled: !handleAddQuestion,
    },
    {
      label: 'Thêm nhóm câu hỏi',
      key: 'add-group',
      icon: <GroupOutlined />,
      disabled: !handleAddGroup,
    },
  ];

  const handleMenuClick: MenuProps['onClick'] = (e) => {
    switch (e.key) {
      case 'add-question':
        handleAddQuestion && handleAddQuestion();
        break;
      case 'add-section':
        handleAddSection && handleAddSection();
        break;
      case 'add-group':
        handleAddGroup && handleAddGroup();
        break;
    }
  };

  const menuProps = {
    items,
    onClick: handleMenuClick,
  };

  const renderPageButton = useCallback(
    (pageNum: number, isCurrent: boolean) => {
      const isApproved = approvedQuestionIndexes?.[pageNum - 1];
      const buttonClass = isApproved
        ? `tailwind-border-2 tailwind-bg-${
            isCurrent ? 'blue' : 'green'
          }-500 tailwind-text-white`
        : isCurrent
        ? 'tailwind-bg-blue-600 tailwind-text-white tailwind-shadow'
        : 'tailwind-bg-transparent tailwind-text-blue-600';

      return (
        <Button
          key={pageNum}
          shape="circle"
          className={buttonClass}
          style={{ minWidth: 32, height: 32, padding: 0 }}
          onClick={() => {
            if (pageNum - 1 !== currentQuestionIndex && handleMoveToPosition) {
              handleMoveToPosition({
                sectionIndex: currentPosition?.sectionIndex ?? 0,
                questionIndex: pageNum - 1,
              });
            }
          }}
        >
          {pageNum}
        </Button>
      );
    },
    [
      approvedQuestionIndexes,
      currentQuestionIndex,
      currentPosition,
      handleMoveToPosition,
    ]
  );

  return (
    <div
      className={`tailwind-flex tailwind-items-center tailwind-justify-between tailwind-w-full tailwind-px-4 tailwind-py-3 tailwind-bg-default tailwind-shadow-md tailwind-transition-normal ${className}`}
    >
      <div className="tailwind-flex tailwind-items-center tailwind-gap-4">
        {title}
        <Dropdown menu={menuProps}>
          <Button>
            <Space>
              Thêm mới
              <DownOutlined />
            </Space>
          </Button>
        </Dropdown>
      </div>

      <div className="tailwind-grow tailwind-mx-auto tailwind-flex tailwind-justify-center tailwind-items-center tailwind-gap-2">
        {/* Section navigation */}
        {currentPosition != null && currentPosition.sectionIndex > 0 && (
          <Button
            // shape="circle"
            icon={<ArrowLeftOutlined />}
            onClick={handlePrevSection}
            loading={loading}
            className="tailwind-bg-gray-200"
          >
            Phần trước
          </Button>
        )}

        {/* Pagination */}
        <div className="tailwind-flex tailwind-items-center tailwind-gap-1 tailwind-px-3 tailwind-py-1 tailwind-rounded-full">
          <Pagination
            currentIndex={(currentQuestionIndex ?? 0) + 1}
            totalNumber={totalQuestionsGroups ?? 0}
            onClick={(pageNum) => {
              if (handleMoveToPosition) {
                handleMoveToPosition({
                  sectionIndex: currentPosition?.sectionIndex ?? 0,
                  questionIndex: pageNum - 1,
                });
              }
            }}
            renderPageButton={renderPageButton}
          />
        </div>

        {/* Section navigation */}
        {currentPosition != null &&
          currentPosition.sectionIndex < (totalSections ?? 0) - 1 && (
            <Button
              // shape="circle"
              icon={<ArrowRightOutlined />}
              onClick={handleNextSection}
              loading={loading}
              className="tailwind-bg-gray-200"
            >
              Phần tiếp
            </Button>
          )}
      </div>

      <div className="tailwind-flex tailwind-items-center tailwind-gap-2">
        {/* Fullscreen toggle */}
        <Button
          shape="circle"
          icon={
            isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />
          }
          onClick={handleToggleFullscreen}
          className="tailwind-bg-gray-200"
        />

        {/* Menu toggle */}
        <Button
          shape="circle"
          icon={<MenuOutlined />}
          onClick={handleOpenMenu}
          className="tailwind-bg-gray-200"
        />
      </div>
    </div>
  );
};

export default memo(ExamEditorToolbar);
