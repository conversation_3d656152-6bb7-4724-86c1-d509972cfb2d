﻿using EdTech.Study.Exams;
using EdTech.Study.Grade;
using EdTech.Study.GroupQuestions;
using EdTech.Study.Lessons;
using EdTech.Study.Menus;
using EdTech.Study.Questions;
using EdTech.Study.Files;
using Microsoft.EntityFrameworkCore;
using System.Reflection.Emit;
using Volo.Abp.Data;
using Volo.Abp.EntityFrameworkCore;

namespace EdTech.Study.EntityFrameworkCore;

[ConnectionStringName(StudyDbProperties.ConnectionStringName)]
public class StudyDbContext : AbpDbContext<StudyDbContext>, IStudyDbContext, IQuestionDraftDbContext
{
    /* Add DbSet for each Aggregate Root here. Example:
     * public DbSet<Question> Questions { get; set; }
     */

    public DbSet<LessonConfigEntity> LessonConfigs { get; set; }
    public DbSet<MenuEntity> Menus { get; set; }

    public DbSet<LessonGrade> LessonGrades { get; set; }

    public DbSet<Subject.Subject> Subjects { get; set; }

    public DbSet<DefaultLessonEntity> DefaultLessons { get; set; }

    public DbSet<QuestionDraftEntity> QuestionDrafts { get; set; }

    public DbSet<QuestionOptionDraftEntity> OptionDrafts { get; set; }
    
    public DbSet<TempFileEntity> TempFiles { get; set; }

    public DbSet<ExamEntity> Exams { get; set; }

    public DbSet<SectionEntity> Sections { get; set; }

    public DbSet<SectionQuestionEntity> SectionQuestions { get; set; }

    public DbSet<QuestionEntity> Questions { get; set; }

    public DbSet<QuestionOptionEntity> QuestionOptions { get; set; }

    public DbSet<FillInBlankAnswerEntity> FillInBlankAnswers { get; set; }

    public DbSet<MatchingAnswerEntity> MatchingAnswers { get; set; }

    public DbSet<MatchingItemEntity> MatchingItems { get; set; }

    public DbSet<GroupQuestionEntity> GroupQuestions { get; set; }
    public DbSet<SectionGroupQuestionEntity> SectionGroupQuestions { get; set; }
    public StudyDbContext(DbContextOptions<StudyDbContext> options)
        : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);
        builder.ConfigureStudy();
    }
}
