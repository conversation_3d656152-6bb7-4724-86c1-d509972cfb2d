﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;

namespace EdTech.Study.Questions.Dtos
{
    public class MatchingAnswerDto : EntityDto<Guid>
    {
        /// <summary>
        /// Liên kết đến câu hỏi cha.
        /// </summary>
        public Guid QuestionId { get; set; }

        public Guid ClientId { get; set; }


        /// <summary>
        /// Liên kết đến mục tiền đề.
        /// </summary>
        public Guid PremiseId { get; set; }

        /// <summary>
        /// Liên kết đến mục phản hồi.
        /// </summary>
        public Guid ResponseId { get; set; }

        /// <summary>
        /// Điểm số cho cặp nối này.
        /// </summary>
        public float? Score { get; set; }

        /// <summary>
        /// Phản hồi khi nối đúng cặp này.
        /// </summary>
        public string? Feedback { get; set; }
    }
}
