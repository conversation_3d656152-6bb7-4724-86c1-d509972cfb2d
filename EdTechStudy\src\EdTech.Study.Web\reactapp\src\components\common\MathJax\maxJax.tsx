import React, { useEffect, useRef } from 'react';

// Define the component props interface
interface MathJaxComponentProps {
  math: string;
  inline?: boolean;
  className?: string;
  style?: React.CSSProperties;
  onRenderSuccess?: () => void;
  onRenderError?: (error: Error) => void;
}

/**
 * A React component that renders mathematical expressions using MathJax
 *
 * @param math - The LaTeX mathematical expression to render
 * @param inline - Whether to render the math inline (true) or in display mode (false)
 * @param className - Optional CSS class name to apply to the container
 * @param style - Optional inline styles to apply to the container
 * @param onRenderSuccess - Optional callback function called after successful rendering
 * @param onRenderError - Optional callback function called if rendering fails
 */
const MathJaxComponent: React.FC<MathJaxComponentProps> = ({
  math,
  inline = false,
  className = '',
  style = {},
  onRenderSuccess,
  onRenderError,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Only proceed if MathJax is available and the container exists
    if (window.MathJax && containerRef.current) {
      // Reset the content with the appropriate delimiters based on mode
      containerRef.current.innerHTML = inline
        ? `\\(${math}\\)`
        : `\\[${math}\\]`;

      // Typeset the new content
      window.MathJax.typesetPromise([containerRef.current])
        .then(() => {
          // Call the success callback if provided
          if (onRenderSuccess) {
            onRenderSuccess();
          }
        })
        .catch((err: Error) => {
          console.error('MathJax typesetting failed:', err);
          // Call the error callback if provided
          if (onRenderError) {
            onRenderError(err);
          }
        });
    }
  }, [math, inline, onRenderSuccess, onRenderError]);

  return (
    <div
      ref={containerRef}
      className={className}
      style={style}
      data-testid="mathjax-container"
    />
  );
};

export default MathJaxComponent;

// MathJax Provider component for loading and configuring MathJax
export interface MathJaxContextProps {
  children: React.ReactNode;
  src?: string;
  config?: Record<string, any>;
  onLoad?: () => void;
  onError?: (error: Error) => void;
}

export const MathJaxContext: React.FC<MathJaxContextProps> = ({
  children,
  src = 'https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js',
  config = {
    tex: {
      inlineMath: [['\\(', '\\)']],
      displayMath: [['\\[', '\\]']],
    },
    startup: {
      typeset: false,
    },
  },
  onLoad,
  onError,
}) => {
  const [loaded, setLoaded] = React.useState<boolean>(false);

  useEffect(() => {
    // Skip if MathJax is already loaded
    if (window.MathJax) {
      setLoaded(true);
      if (onLoad) onLoad();
      return;
    }

    // Set up MathJax configuration
    (window as any).MathJax = config;

    // Create script element
    const script = document.createElement('script');
    script.src = src;
    script.async = true;

    // Handle load event
    script.onload = () => {
      if (window.MathJax.startup?.promise) {
        window.MathJax.startup.promise
          .then(() => {
            setLoaded(true);
            if (onLoad) onLoad();
          })
          .catch((err: Error) => {
            console.error('Error initializing MathJax:', err);
            if (onError) onError(err);
          });
      } else {
        setLoaded(true);
        if (onLoad) onLoad();
      }
    };

    // Handle error event
    script.onerror = (e) => {
      console.error('Error loading MathJax script:', e);
      if (onError) onError(new Error('Failed to load MathJax script'));
    };

    // Add script to document
    document.head.appendChild(script);

    // Cleanup function
    return () => {
      // Only remove if it's the script we added
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
    };
  }, [src, onLoad, onError]);

  return (
    <>
      {children}
      {!loaded && (
        <div style={{ display: 'none' }} aria-hidden="true">
          Loading MathJax...
        </div>
      )}
    </>
  );
};

// Example usage
/*
import { MathJaxContext, MathJaxComponent } from './MathJaxComponent';

const App: React.FC = () => {
  return (
    <MathJaxContext>
      <h1>MathJax Example</h1>
      <p>
        Inline equation: <MathJaxComponent math="x = \frac{-b \pm \sqrt{b^2 - 4ac}}{2a}" inline={true} />
      </p>
      <div>
        <h2>Display equation:</h2>
        <MathJaxComponent 
          math="\int_{a}^{b} f(x) \, dx = F(b) - F(a)" 
          className="my-math-display"
        />
      </div>
    </MathJaxContext>
  );
};
*/
