﻿using EdTech.Study.Exams;
using EdTech.Study.Questions.Dtos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;

namespace EdTech.Study.GroupQuestions
{
    public class GroupQuestionDto : EntityDto<Guid>
    {
        public Guid? ClientId { get; set; }
        /// <summary>
        /// Nội dung câu hỏi
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// Định dạng nội dung câu hỏi: html, text
        /// </summary>
        public ContentFormatType ContentFormat { get; set; } = ContentFormatType.Html;

        /// <summary>
        /// Hướng dẫn làm bài
        /// </summary>
        public string? Instructions { get; set; }

        public IList<QuestionDto>? Questions { get; set; }

        /// <summary>
        /// Đảm bảo duy nhất mỗi request thêm
        /// </summary>
        public string? IdempotentKey { get; set; }
    }
}
