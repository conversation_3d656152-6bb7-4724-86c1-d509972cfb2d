import React, {
  useState,
  useEffect,
  useCallback,
  memo,
  createContext,
  useContext,
} from 'react';
import { Card, Typography, Space, Drawer, Switch } from 'antd';
import {
  QuestionCircleOutlined,
  FlagOutlined,
  CloseOutlined,
  SyncOutlined,
} from '@ant-design/icons';
import { BaseQuestion } from '../../../interfaces/quizs/questionBase';
import { SaveControls } from './components';
import './styles/SaveControls.css';
import './styles/QuestionListView.css';
import useUpdateQuestion from '../hooks/useUpdateQuestion';
import { Subject } from '../../../interfaces/lessons/subject';
import { LessonGrade } from '../../../interfaces/lessons/lessonGrade';
import { connect } from 'react-redux';
import { IExamDataManagerState } from '../../../store/slices/ExamSlices/examDataManagerSlice';
import AddQuestionButton from './components/AddQuestionButton';
import { QuestionEditor } from '../questionEditor';

const { Text } = Typography;

type StyleContextType = {
  showConfigMode: boolean;
  showStyleDrawer: boolean;
  showSettingsDrawer: boolean;
  setShowStyleDrawer: (show: boolean) => void;
  setShowSettingsDrawer: (show: boolean) => void;
  handleStyleChange: () => void;
  handleSettingsChange: () => void;
};

type QuestionDataContextType = {
  engineId: string | undefined;
  questions: BaseQuestion[];
  currentQuestionId: string | undefined;
  subjects: Subject[];
  grades: LessonGrade[];
  onSelectQuestion: (questionId: string) => void;
  onAddQuestion: (position: number) => void;
  onSaveQuestions: () => void;
};

type ConfigurationContextType = {
  disableCreateQuestion: boolean;
  uiOptions: { visibleToolControls: boolean };
};
// UI/Layout context
const StyleContext = createContext<StyleContextType>({
  showConfigMode: false,
  showStyleDrawer: false,
  showSettingsDrawer: false,
  setShowStyleDrawer: () => {},
  setShowSettingsDrawer: () => {},
  handleStyleChange: () => {},
  handleSettingsChange: () => {},
});

// Question data context
const QuestionDataContext = createContext<QuestionDataContextType>({
  engineId: undefined,
  questions: [],
  currentQuestionId: undefined,
  subjects: [],
  grades: [],
  onSelectQuestion: () => {},
  onAddQuestion: () => {},
  onSaveQuestions: () => {},
});

// Configuration context
const ConfigurationContext = createContext<ConfigurationContextType>({
  disableCreateQuestion: false,
  uiOptions: { visibleToolControls: true },
});

const useStyleContext = () => useContext(StyleContext);
const useQuestionDataContext = () => useContext(QuestionDataContext);
const useConfigurationContext = () => useContext(ConfigurationContext);

export interface QuestionListViewProps extends QuestionListViewStateToProps {
  engineId?: string;
  questions: BaseQuestion[];
  showConfigMode: boolean;
  currentQuestionId?: string;
  disableCreateQuestion?: boolean;
  uiOptions: {
    visibleToolControls?: boolean;
  };

  onSelectQuestion: (questionId: string) => void;
  onAddQuestion: (position: number) => void;
  onSaveQuestions?: () => void;
}

export interface QuestionListViewStateToProps {
  subjects?: Subject[];
  grades?: LessonGrade[];
}

// Main component
const QuestionListView: React.FC<QuestionListViewProps> & {
  ListQuestion: React.FC;
  AdvancedSettings: React.FC;
  ToolControls: React.FC;
  EmptyQuestionList: React.FC;
} = ({
  engineId,
  questions,
  currentQuestionId,
  showConfigMode,
  disableCreateQuestion = false,
  subjects = [],
  grades = [],
  uiOptions = {
    visibleToolControls: true,
  },
  onSelectQuestion,
  onAddQuestion,
  onSaveQuestions,
}) => {
  const [showStyleDrawer, setShowStyleDrawer] = useState(false);
  const [showSettingsDrawer, setShowSettingsDrawer] = useState(false);

  // Memoize handlers to prevent re-creation on renders
  const handleStyleChange = useCallback(() => {
    setShowStyleDrawer((prev) => !prev);
    setShowSettingsDrawer(false);
  }, []);

  const handleSettingsChange = useCallback(() => {
    setShowSettingsDrawer((prev) => !prev);
    setShowStyleDrawer(false);
  }, []);

  // CSS class update effect
  useEffect(() => {
    if (showConfigMode) {
      document.body.classList.add('config-mode-active');
    } else {
      document.body.classList.remove('config-mode-active');
    }

    return () => {
      document.body.classList.remove('config-mode-active');
    };
  }, [showConfigMode]);

  const configValues = {
    disableCreateQuestion,
    uiOptions,
  } as ConfigurationContextType;

  const questionDataValues = {
    engineId,
    questions,
    currentQuestionId,
    subjects: subjects ?? [],
    grades: grades ?? [],
    onSelectQuestion,
    onAddQuestion,
    onSaveQuestions,
  } as QuestionDataContextType;

  const styleValues = {
    showConfigMode,
    showStyleDrawer,
    showSettingsDrawer,
    setShowStyleDrawer,
    setShowSettingsDrawer,
    handleStyleChange,
    handleSettingsChange,
  } as StyleContextType;

  return (
    <ConfigurationContext.Provider value={configValues}>
      <QuestionDataContext.Provider value={questionDataValues}>
        <StyleContext.Provider value={styleValues}>
          <div
            className={`question-list-view ${
              showConfigMode ? 'config-mode-view' : ''
            }`}
          >
            {/* Tool controls */}
            <QuestionListView.ToolControls />

            <div className="question-list-content">
              {!disableCreateQuestion && (
                <AddQuestionButton position={0} onAddQuestion={onAddQuestion} />
              )}

              {/* List Question */}
              <QuestionListView.ListQuestion />

              {/* Show Empty Question List */}
              <QuestionListView.EmptyQuestionList />
            </div>

            {/* Advanced settings */}
          </div>
        </StyleContext.Provider>
      </QuestionDataContext.Provider>
    </ConfigurationContext.Provider>
  );
};

QuestionListView.ListQuestion = memo(() => {
  const {
    engineId,
    questions,
    currentQuestionId,
    onSelectQuestion,
    subjects,
    grades,
    onAddQuestion,
  } = useQuestionDataContext();
  const { showConfigMode } = useStyleContext();
  const { disableCreateQuestion } = useConfigurationContext();
  const { handleUpdateQuestion } = useUpdateQuestion();
  return (
    <div id={engineId + '-question-list'}>
      {questions.map((question, index) => {
        return (
          <React.Fragment key={question.clientId}>
            <Card
              id={question.clientId}
              className={`question-list-item ${
                currentQuestionId === question.clientId ? 'active' : ''
              }`}
              onClick={() => onSelectQuestion(question.clientId)}
            >
              <div className="question-list-header">
                <Space>
                  {question.isCompleted ? (
                    <FlagOutlined style={{ color: '#52c41a' }} />
                  ) : (
                    <QuestionCircleOutlined style={{ color: '#52c41a' }} />
                  )}
                  <Text strong>Câu hỏi {index + 1}</Text>
                </Space>
                <Switch
                  checkedChildren={<SyncOutlined />}
                  unCheckedChildren={<CloseOutlined />}
                  checked={question.syncQuestion}
                  title={
                    question.lastSyncQuestionId != null
                      ? 'Đồng bộ với ngân hàng câu hỏi'
                      : 'Không thể đồng bộ với ngân hàng câu hỏi do câu hỏi không nằm trong ngân hàng câu hỏi'
                  }
                  disabled={question.lastSyncQuestionId == null}
                  onChange={(checked) => {
                    handleUpdateQuestion({
                      ...question,
                      syncQuestion: checked,
                    });
                  }}
                />
              </div>

              <div
                className={`question-list-content ${
                  showConfigMode ? 'config-mode' : ''
                }`}
              >
                <QuestionEditor
                  key={question.clientId}
                  panels={['config']}
                  visible={true}
                  question={question}
                  subjects={subjects ?? []}
                  grades={grades ?? []}
                  onCancel={() => {}}
                  onSave={(updatedQuestion: BaseQuestion) => {
                    handleUpdateQuestion({
                      ...updatedQuestion,
                    });
                  }}
                />
              </div>
            </Card>

            {!disableCreateQuestion && (
              <AddQuestionButton
                position={index + 1}
                onAddQuestion={onAddQuestion}
              />
            )}
          </React.Fragment>
        );
      })}
    </div>
  );
});

QuestionListView.AdvancedSettings = memo(() => {
  const {
    showStyleDrawer,
    setShowStyleDrawer,
    showSettingsDrawer,
    setShowSettingsDrawer,
  } = useStyleContext();
  return (
    <React.Fragment>
      {/* Style Drawer */}
      <Drawer
        title="Thiết lập giao diện"
        placement="right"
        onClose={() => setShowStyleDrawer(false)}
        open={showStyleDrawer}
        width={350}
      >
        <div className="style-settings">
          <Text>Các tùy chỉnh giao diện sẽ được hiển thị ở đây</Text>
        </div>
      </Drawer>
      {/* Settings Drawer */}
      <Drawer
        title="Cài đặt"
        placement="right"
        onClose={() => setShowSettingsDrawer(false)}
        open={showSettingsDrawer}
        width={350}
      >
        <div className="practice-settings">
          <Text>Các tùy chỉnh bài luyện tập sẽ được hiển thị ở đây</Text>
        </div>
      </Drawer>
    </React.Fragment>
  );
});

QuestionListView.ToolControls = memo(() => {
  const { onSaveQuestions } = useQuestionDataContext();
  const { showConfigMode, handleStyleChange, handleSettingsChange } =
    useStyleContext();
  const { uiOptions } = useConfigurationContext();
  return (
    <>
      {uiOptions.visibleToolControls && showConfigMode && onSaveQuestions && (
        <SaveControls
          position="top"
          onSave={onSaveQuestions}
          onStyleChange={handleStyleChange}
          onSettingsChange={handleSettingsChange}
        />
      )}
    </>
  );
});

QuestionListView.EmptyQuestionList = memo(() => {
  const { questions } = useQuestionDataContext();
  return (
    <>
      {questions.length === 0 && (
        <div className="empty-question-list">
          <Text type="secondary">
            Chưa có câu hỏi nào. Hãy thêm câu hỏi đầu tiên!
          </Text>
        </div>
      )}
    </>
  );
});

const mapStateToProps = ({
  examDataManager,
}: {
  examDataManager: IExamDataManagerState;
}): QuestionListViewStateToProps => {
  return {
    subjects: examDataManager.subjects,
    grades: examDataManager.grades,
  };
};

const QuestionListViewContainer = connect(mapStateToProps)(QuestionListView);

export default QuestionListViewContainer;
