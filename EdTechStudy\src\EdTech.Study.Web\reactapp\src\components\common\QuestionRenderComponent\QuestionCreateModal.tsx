import { CloseOutlined, PlusOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, DatePicker, Modal, Select, Space, Spin, Splitter } from 'antd';
import React, { useCallback, useState } from 'react';
import { QuestionTemplateFactory } from '../../../components/quizs/practiceEngines/questionTemplates';
import { IQuestion } from '../../../interfaces/questions/question';
import {
  BaseQuestion,
  PracticeEngineContext,
} from '../../../interfaces/quizs/questionBase';
import { useQuestionContext } from '../../../providers/Questions/QuestionProvider';
import { mapBaseQuestionToQuestion } from '../../../utils/questionUtil';
import practiceLocalization from '../../quizs/localization';
import QuestionEditorInfo from './QuestionEditorInfo';
import QuestionPreview from './QuestionPreview';
import dayjs from 'dayjs';

export interface QuestionCreateModalProps {
  visible: boolean;
  onCancel: () => void;
  onSave: (newQuestion: IQuestion) => void;
}

const QuestionCreateModal: React.FC<QuestionCreateModalProps> = ({
  visible,
  onCancel,
  onSave,
}) => {
  const { subjects = [], lessonGrades = [], users = [] } = useQuestionContext();
  const [newQuestion, setNewQuestion] = useState<BaseQuestion | null>(null);

  // Initialize a new question when the modal becomes visible
  React.useEffect(() => {
    if (visible) {
      const factory = QuestionTemplateFactory.getInstance();
      const questionTemplate = factory.create(practiceLocalization.quiz);
      if (questionTemplate) {
        setNewQuestion(questionTemplate);
      }
    } else {
      setNewQuestion(null);
    }
  }, [visible]);

  // Handle saving the question
  const handleSave = useCallback(
    (createdQuestion: BaseQuestion) => {
      let questionMap = mapBaseQuestionToQuestion(createdQuestion);
      onSave(questionMap);
    },
    [onSave]
  );

  // Handle question change
  const handleQuestionChange = useCallback(
    (
      update: Partial<BaseQuestion> & {
        clientId: string;
        parentId: string;
      }
    ) => {
      console.log('🚀 ~ update:', update);

      setNewQuestion(update as BaseQuestion);
    },
    []
  );

  // If no question template is available yet, show loading
  if (!newQuestion) {
    return (
      <Modal
        title="Tạo câu hỏi mới"
        open={visible}
        onCancel={onCancel}
        footer={[
          <Button key="back" onClick={onCancel}>
            Đóng
          </Button>,
        ]}
        width="90%"
        centered
        className="question-editor-modal"
        closeIcon={<CloseOutlined />}
      >
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Spin>
            <div className="tailwind-h-20 tailwind-flex tailwind-items-center tailwind-justify-center">
              <span>Đang tải...</span>
            </div>
          </Spin>
        </div>
      </Modal>
    );
  }

  return (
    <>
      <Modal
        title={
          <div className="question-editor-header">
            <div className="title-with-tag">
              <span className="limited-title">Tạo câu hỏi mới</span>
              <Space style={{ marginLeft: 8 }}>
                <Select
                  placeholder="Chọn người xử lý"
                  value={newQuestion?.assignedUserId}
                  onChange={(value) =>
                    setNewQuestion((prev) =>
                      prev ? { ...prev, assignedUserId: value } : null
                    )
                  }
                  style={{ width: 200 }}
                  allowClear
                  showSearch
                  optionFilterProp="children"
                >
                  {users.map((user) => (
                    <Select.Option key={user.id} value={user.id}>
                      {user.name || user.userName} ({user.email})
                    </Select.Option>
                  ))}
                </Select>
                <DatePicker
                  placeholder="Chọn hạn xử lý"
                  value={
                    newQuestion?.dueDate ? dayjs(newQuestion.dueDate) : null
                  }
                  onChange={(date) =>
                    setNewQuestion((prev) =>
                      prev
                        ? {
                            ...prev,
                            dueDate: date
                              ? date.format('YYYY-MM-DD')
                              : undefined,
                          }
                        : null
                    )
                  }
                  format="DD/MM/YYYY"
                />
              </Space>
            </div>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => handleSave(newQuestion)}
            >
              Tạo mới
            </Button>
          </div>
        }
        open={visible}
        onCancel={onCancel}
        footer={null}
        width="90%"
        centered
        className="question-editor-modal"
        closeIcon={<CloseOutlined />}
      >
        <div className="question-editor-content">
          <Splitter>
            <Splitter.Panel defaultSize="70%" min="20%" max="90%">
              <PracticeEngineContext.Provider
                value={{
                  handleEditQuestion: handleQuestionChange,
                  handleDeleteQuestion: (id: string) => {
                    console.log('Delete question', id);
                  },
                  handleChangePosition: () => {},
                  handleToggleFullscreen: () => {},
                  isFullscreen: false,
                  handleDeleteGroupQuestion: () => {},
                  handleUpdateGroupQuestion: () => {},
                  handlePendingState: () => {},
                }}
              >
                <QuestionEditorInfo
                  question={{ ...newQuestion }}
                  subjects={subjects}
                  grades={lessonGrades}
                />
              </PracticeEngineContext.Provider>
            </Splitter.Panel>
            <Splitter.Panel>
              <QuestionPreview question={newQuestion} />
            </Splitter.Panel>
          </Splitter>
        </div>
      </Modal>
    </>
  );
};

export default QuestionCreateModal;
