import {
  QuestionComponentBaseP<PERSON>,
  BaseQuestion,
  BaseAnswer,
} from './questionBase';

// Interface for MultiSelect questions
export interface MultiSelectQuestion extends BaseQuestion {
  type: 'multiselect';
  content: string;
  options: MultiSelectAnswer[];
  explanation?: string;
  points?: number;
  userSelect?: MultiSelectAnswer[]; // IDs of selected answers
}

// Interface for MultiSelect answers
export interface MultiSelectAnswer extends BaseAnswer {
  content: string;
  isCorrect: boolean;
}

// Props for the MultiSelectQuiz component
export interface MultiSelectQuizComponentProps
  extends QuestionComponentBaseProps {
  question: MultiSelectQuestion;
  allowManyTimes?: boolean;
  disabled?: boolean;
  onComplete?: (questionId: string, answer?: MultiSelectAnswer[]) => void;
}
