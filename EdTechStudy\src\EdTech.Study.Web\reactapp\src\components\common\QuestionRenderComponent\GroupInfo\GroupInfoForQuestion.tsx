import { CloseOutlined, EditOutlined, SaveOutlined } from '@ant-design/icons';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Divider,
  message,
  Space,
  Spin,
  Typography,
} from 'antd';
import { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { ExamGroupQuestion } from '../../../../interfaces/exams/examBase';
import {
  fetchGroupInfoByQuestionId,
  getGroupForQuestion,
  QuestionDataManagerState,
  updateGroupInfo,
} from '../../../../store/slices/QuestionSlices/questionDataManagerSlice';
import CustomRichText from '../../elements/Text/RichTextEditorComponent/v0/CustomRichText';

const { Title, Text } = Typography;

export interface GroupInfoTabProps {
  questionId?: string;
  loading: boolean;
  groupInfo?: ExamGroupQuestion;
  fetchGroupInfoByQuestionId: (questionId: string) => any;
  updateGroupInfo: (groupInfo: {
    id: string;
    content?: string;
    instructions?: string;
    questionId?: string;
  }) => any;
}

const GroupInfoForQuestion: React.FC<GroupInfoTabProps> = ({
  questionId,
  groupInfo,
  loading,
  fetchGroupInfoByQuestionId,
  updateGroupInfo,
}) => {
  const [loadingGroup, setLoadingGroup] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editedContent, setEditedContent] = useState('');
  const [editedInstructions, setEditedInstructions] = useState('');
  const [savingChanges, setSavingChanges] = useState(false);

  useEffect(() => {
    if (questionId && !groupInfo) {
      // Only fetch if we don't have the data
      setLoadingGroup(true);
      fetchGroupInfoByQuestionId(questionId).finally(() =>
        setLoadingGroup(false)
      );
    }
  }, [questionId, groupInfo, fetchGroupInfoByQuestionId]);

  useEffect(() => {
    if (groupInfo) {
      setEditedContent(groupInfo.content || '');
      setEditedInstructions(groupInfo.instructions || '');
    }
  }, [groupInfo]);

  const handleStartEditing = () => {
    setIsEditing(true);
  };

  const handleCancelEditing = () => {
    setIsEditing(false);
    // Reset to original values
    if (groupInfo) {
      setEditedContent(groupInfo.content || '');
      setEditedInstructions(groupInfo.instructions || '');
    }
  };

  const handleSaveChanges = async () => {
    if (!questionId || !groupInfo) return;

    setSavingChanges(true);
    try {
      // Use the Redux action to update group info
      const result = await updateGroupInfo({
        id: groupInfo.id || '',
        content: editedContent,
        instructions: editedInstructions,
        questionId: questionId, // Pass questionId to update the correct entry in groupInfoByQuestionId
      });

      if (result.meta.requestStatus === 'fulfilled') {
        message.success('Cập nhật thông tin nhóm câu hỏi thành công');
        setIsEditing(false);
      } else {
        message.error('Cập nhật thông tin nhóm câu hỏi thất bại');
      }
    } catch (error) {
      console.error('Error updating group info:', error);
      message.error('Đã xảy ra lỗi khi cập nhật thông tin nhóm câu hỏi');
    } finally {
      setSavingChanges(false);
    }
  };

  if (!groupInfo) {
    return (
      <Alert message="Câu hỏi này không thuộc nhóm câu hỏi nào" type="info" />
    );
  }

  return (
    <div className="question-editor-info tailwind-p-4">
      <Card
        loading={loading || loadingGroup}
        title={
          <div className="tailwind-flex tailwind-justify-between tailwind-items-center">
            <Title level={4} className="tailwind-m-0">
              Thông tin nhóm câu hỏi
            </Title>
            {!isEditing ? (
              <Button
                type="primary"
                icon={<EditOutlined />}
                onClick={handleStartEditing}
              >
                Chỉnh sửa
              </Button>
            ) : (
              <Space>
                <Button
                  type="primary"
                  icon={<SaveOutlined />}
                  onClick={handleSaveChanges}
                  loading={savingChanges}
                >
                  Lưu
                </Button>
                <Button icon={<CloseOutlined />} onClick={handleCancelEditing}>
                  Hủy
                </Button>
              </Space>
            )}
          </div>
        }
        bordered={false}
      >
        <div className="tailwind-mb-4">
          <Text strong className="tailwind-block tailwind-mb-2">
            Nội dung:
          </Text>
          {isEditing ? (
            <CustomRichText
              value={editedContent}
              onChangeValue={(_, value) => setEditedContent(value || '')}
              height={200}
            />
          ) : (
            <div
              className="tailwind-p-4 tailwind-bg-gray-50 tailwind-rounded-lg"
              dangerouslySetInnerHTML={{
                __html: groupInfo.content || 'Không có nội dung',
              }}
            />
          )}
        </div>

        <Divider />

        <div className="tailwind-mb-4">
          <Text strong className="tailwind-block tailwind-mb-2">
            Hướng dẫn:
          </Text>
          {isEditing ? (
            <CustomRichText
              value={editedInstructions}
              onChangeValue={(_, value) => setEditedInstructions(value || '')}
              height={150}
            />
          ) : (
            <div
              className="tailwind-p-4 tailwind-bg-gray-50 tailwind-rounded-lg"
              dangerouslySetInnerHTML={{
                __html: groupInfo.instructions || 'Không có hướng dẫn',
              }}
            />
          )}
        </div>

        <Divider />

        <div className="tailwind-mb-4">
          <Text strong className="tailwind-block tailwind-mb-2">
            Thông tin khác:
          </Text>
          <div className="tailwind-grid tailwind-grid-cols-2 tailwind-gap-4">
            <div>
              <Text type="secondary">Số câu hỏi:</Text>
              <div>{groupInfo.questions?.length || 0}</div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

const mapStateToProps = (
  state: {
    questionDataManager: QuestionDataManagerState;
  },
  ownProps: { questionId?: string }
) => {
  return {
    loading: state.questionDataManager.loading,
    groupInfo: ownProps.questionId
      ? getGroupForQuestion(state.questionDataManager, ownProps.questionId)
      : undefined,
  };
};

const mapDispatchToProps = {
  fetchGroupInfoByQuestionId,
  updateGroupInfo,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(GroupInfoForQuestion);
