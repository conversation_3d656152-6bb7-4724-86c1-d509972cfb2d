import React, { useState, useCallback, memo } from 'react';
import { Select, Input } from 'antd';
import {
  ClockCircleOutlined,
  SearchOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import {
  IPracticeExamFilters,
  IExamFilterMetadata,
  PracticeExamStatus,
} from '../../../interfaces/exams/practiceExam';

const { Option } = Select;
const { Search } = Input;

interface IExamFilterSectionProps {
  filters: IPracticeExamFilters;
  filterMetadata: IExamFilterMetadata;
  onSearch: (value: string) => void;
  onFilterChange: (filterType: keyof IPracticeExamFilters, values: any) => void;
}

const ExamFilterSection: React.FC<IExamFilterSectionProps> = ({
  filters,
  filterMetadata,
  onSearch,
  onFilterChange,
}) => {
  // Local search state for immediate UI feedback
  const [searchQuery, setSearchQuery] = useState<string>(
    filters.searchQuery || ''
  );

  // Sync local search state with props when filters change externally
  React.useEffect(() => {
    setSearchQuery(filters.searchQuery || '');
  }, [filters.searchQuery]);

  // Handle search input change - immediate UI update
  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchQuery(e.target.value);
    },
    []
  );

  // Handle search submit - trigger API call
  const handleSearchSubmit = useCallback(
    (value: string) => {
      onSearch(value);
    },
    [onSearch]
  );

  // Handle subject filter change - support multiple values
  const handleSubjectChange = useCallback(
    (values: string) => {
      if (values) {
        onFilterChange('subjects', [values]);
      } else {
        onFilterChange('subjects', []);
      }
    },
    [onFilterChange]
  );

  // Handle grade filter change - support multiple values
  const handleGradeChange = useCallback(
    (values: string) => {
      if (values) {
        onFilterChange('grades', [values]);
      } else {
        onFilterChange('grades', []);
      }
    },
    [onFilterChange]
  );

  // Handle status filter change
  const handleStatusChange = useCallback(
    (values: PracticeExamStatus) => {
      console.log(values);
      if (values) {
        onFilterChange('statuses', [values]);
      } else {
        onFilterChange('statuses', []);
      }
    },
    [onFilterChange]
  );

  return (
    <div className="tailwind-sticky tailwind-top-0 tailwind-z-10">
      <div className="tailwind-grid tailwind-grid-cols-1 md:tailwind-grid-cols-12 tailwind-gap-4">
        {/* Search Section */}
        <div className="md:tailwind-col-span-12 lg:tailwind-col-span-6 xl:tailwind-col-span-6">
          <Search
            placeholder="Tìm kiếm đề thi theo tên..."
            allowClear
            enterButton={<SearchOutlined />}
            size="middle"
            onSearch={handleSearchSubmit}
            value={searchQuery}
            onChange={handleSearchChange}
            className="tailwind-rounded tailwind-w-full"
            style={{
              borderRadius: '8px',
            }}
          />
        </div>

        {/* Subject Filter */}
        <div className="md:tailwind-col-span-6 lg:tailwind-col-span-2 xl:tailwind-col-span-2">
          <Select
            // mode="multiple"
            placeholder="Chọn môn học"
            value={filters.subjects ? filters.subjects[0] : undefined}
            onChange={handleSubjectChange}
            style={{
              width: '100%',
              borderRadius: '8px',
            }}
            maxTagCount="responsive"
            allowClear
            filterOption={(input, option) =>
              (option?.children as string)
                ?.toLowerCase()
                .includes(input.toLowerCase())
            }
          >
            {filterMetadata.subjects.map((subject) => (
              <Option key={subject.value} value={subject.value}>
                {subject.label}
              </Option>
            ))}
          </Select>
        </div>

        {/* Grade Filter */}
        <div className="md:tailwind-col-span-6 lg:tailwind-col-span-2 xl:tailwind-col-span-2">
          <Select
            // mode="multiple"
            placeholder="Chọn lớp"
            value={filters.grades ? filters.grades[0] : undefined}
            onChange={handleGradeChange}
            style={{
              width: '100%',
              borderRadius: '8px',
            }}
            maxTagCount="responsive"
            allowClear
            filterOption={(input, option) =>
              (option?.children as string)
                ?.toLowerCase()
                .includes(input.toLowerCase())
            }
          >
            {filterMetadata.grades.map((grade) => (
              <Option key={grade.value} value={grade.value}>
                {grade.label}
              </Option>
            ))}
          </Select>
        </div>

        {/* Status Filter */}
        <div className="md:tailwind-col-span-6 lg:tailwind-col-span-2 xl:tailwind-col-span-2">
          <Select
            // mode="multiple"
            placeholder="Trạng thái"
            value={filters.statuses ? filters.statuses[0] : undefined}
            onChange={handleStatusChange}
            style={{
              width: '100%',
              borderRadius: '8px',
            }}
            maxTagCount="responsive"
            allowClear
          >
            <Option value={PracticeExamStatus.NOT_STARTED}>
              <div className="tailwind-flex tailwind-items-center tailwind-gap-2">
                <ExclamationCircleOutlined
                  style={{ color: '#ff4d4f', fontSize: '14px' }}
                />
                <span>Chưa làm</span>
              </div>
            </Option>
            <Option value={PracticeExamStatus.IN_PROGRESS}>
              <div className="tailwind-flex tailwind-items-center tailwind-gap-2">
                <ClockCircleOutlined
                  style={{ color: '#faad14', fontSize: '14px' }}
                />
                <span>Đang làm</span>
              </div>
            </Option>
            <Option value={PracticeExamStatus.COMPLETED}>
              <div className="tailwind-flex tailwind-items-center tailwind-gap-2">
                <CheckCircleOutlined
                  style={{ color: '#52c41a', fontSize: '14px' }}
                />
                <span>Hoàn thành</span>
              </div>
            </Option>
          </Select>
        </div>
      </div>
    </div>
  );
};

// Also export non-memoized version if needed
export default memo(ExamFilterSection);
