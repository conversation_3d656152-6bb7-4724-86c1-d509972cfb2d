﻿using EdTech.Study.Exams;
using EdTech.Study.Questions;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;
using Volo.Abp;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Entities.Auditing;

namespace EdTech.Study.GroupQuestions
{
    public class GroupQuestionEntity : AuditedEntity<Guid>
    {
        public GroupQuestionEntity()
        {
        }

        public GroupQuestionEntity(Guid key) : base(key)
        {
        }


        /// <summary>
        /// Nội dung câu hỏi
        /// </summary>
        [Required]
        public string Content { get; set; }

        /// <summary>
        /// Định dạng nội dung câu hỏi: html, text
        /// </summary>
        [Required]
        [MaxLength(50)]
        public ContentFormatType ContentFormat { get; set; }

        /// <summary>
        /// Hướng dẫn làm bài
        /// </summary>
        public string? Instructions { get; set; }

        public IList<QuestionEntity>? Questions { get; set; }

        /// <summary>
        /// Đ<PERSON>m bảo duy nhất mỗi request thêm
        /// </summary>
        [MaxLength(256)]
        public string? IdempotentKey { get; set; }
    }
}