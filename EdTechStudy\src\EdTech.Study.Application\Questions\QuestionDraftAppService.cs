using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using EdTech.Study.Enum;
using EdTech.Study.Questions.Dtos;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.ObjectMapping;

namespace EdTech.Study.Questions
{
    public class QuestionDraftAppService : 
        CrudAppService<
            QuestionDraftEntity,
            QuestionDraftDto,
            Guid,
            QuestionDraftPagedAndSortedResultRequestDto,
            CreateQuestionDraftDto,
            UpdateQuestionDraftDto>,
        IQuestionDraftAppService
    {
        private readonly IRepository<QuestionDraftEntity, Guid> _repository;

        public QuestionDraftAppService(IRepository<QuestionDraftEntity, Guid> repository)
            : base(repository)
        {
            _repository = repository;
        }

        protected override async Task<IQueryable<QuestionDraftEntity>> CreateFilteredQueryAsync(
            QuestionDraftPagedAndSortedResultRequestDto input)
        {
            var query = await base.CreateFilteredQueryAsync(input);

            return query
                .WhereIf(!input.Filter.IsNullOrWhiteSpace(), x => 
                    x.Content.Contains(input.Filter) ||
                    x.Description.Contains(input.Filter))
                .WhereIf(input.Type.HasValue, x => x.Type == input.Type.Value)
                .WhereIf(input.Status.HasValue, x => x.Status == input.Status.Value)
                .WhereIf(input.SubjectId.HasValue, x => x.SubjectId == input.SubjectId.Value);
        }

        public async Task<QuestionDraftDto> SubmitForReviewAsync(Guid id)
        {
            var entity = await _repository.GetAsync(id);
            
            if (entity.Status != ExamStatus.Draft)
                throw new UserFriendlyException(L["QuestionDraft:CannotSubmit"]);

            entity.Status = ExamStatus.Submitted;
            await _repository.UpdateAsync(entity);

            return ObjectMapper.Map<QuestionDraftEntity, QuestionDraftDto>(entity);
        }

        public async Task<QuestionDraftDto> ApproveAsync(Guid id, string comment = null)
        {
            var entity = await _repository.GetAsync(id);
            
            if (entity.Status != ExamStatus.Submitted)
                throw new UserFriendlyException(L["QuestionDraft:CannotApprove"]);

            entity.Status = ExamStatus.Approved;
            entity.Comment = comment;
            await _repository.UpdateAsync(entity);

            return ObjectMapper.Map<QuestionDraftEntity, QuestionDraftDto>(entity);
        }

        public async Task<QuestionDraftDto> RejectAsync(Guid id, string comment)
        {
            var entity = await _repository.GetAsync(id);
            
            if (entity.Status != ExamStatus.Submitted)
                throw new UserFriendlyException(L["QuestionDraft:CannotReject"]);

            entity.Status = ExamStatus.Rejected;
            entity.Comment = comment;
            await _repository.UpdateAsync(entity);

            return ObjectMapper.Map<QuestionDraftEntity, QuestionDraftDto>(entity);
        }

        public async Task<QuestionDraftDto> PublishAsync(Guid id)
        {
            var entity = await _repository.GetAsync(id);
            
            if (entity.Status != ExamStatus.Approved)
                throw new UserFriendlyException(L["QuestionDraft:CannotPublish"]);

            entity.Status = ExamStatus.Published;
            await _repository.UpdateAsync(entity);

            return ObjectMapper.Map<QuestionDraftEntity, QuestionDraftDto>(entity);
        }

        public async Task<IEnumerable<QuestionDraftDto>> UpdateListAsync(
            IEnumerable<UpdateQuestionDraftWithIdDto> updateQuestionDraftDto)
        {
            var tasks = new List<Task<QuestionDraftDto>>();
            var data = new List<QuestionDraftDto>();

            foreach (var item in updateQuestionDraftDto)
            {
                data.Add(await UpdateAsync(item.Id, item));
            }
            return data;
        }

        public async Task<int> GetPaginationInfo()
        {
            var query = await _repository.GetQueryableAsync();
            return query.Count();
        }

        public async Task<int> GetPaginationInfo(Expression<Func<QuestionDraftEntity, bool>> predicate)
        {
            IQueryable<QuestionDraftEntity>? query = null;
            query = (await _repository.GetQueryableAsync())
                .Where(predicate);

            return query.Count();
        }
    }
}