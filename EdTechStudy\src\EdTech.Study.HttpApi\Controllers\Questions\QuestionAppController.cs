﻿using EdTech.Study.Exams.Dtos;
using EdTech.Study.GroupQuestions;
using EdTech.Study.Questions;
using EdTech.Study.Questions.Dtos;
using EdTech.Study.Questions.Request;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;

namespace EdTech.Study.Controllers.Questions
{
    public class QuestionAppController : IQuestionAppService
    {
        private readonly IQuestionAppService _questionAppService;

        public QuestionAppController(IQuestionAppService questionAppService)
        {
            _questionAppService = questionAppService;
        }

        public Task<QuestionDto> CreateAsync(CreateUpdateQuestionDto input)
        {
            return _questionAppService.CreateAsync(input);
        }

        public Task<QuestionDto> UpdateAsync(Guid id, CreateUpdateQuestionDto input)
        {
            return _questionAppService.UpdateAsync(id, input);
        }

        public Task DeleteAsync(Guid id)
        {
            return _questionAppService.DeleteAsync(id);
        }

        public Task<QuestionDto> GetAsync(Guid id)
        {
            return _questionAppService.GetAsync(id);
        }

        public Task<List<QuestionDto>> GetByIdsAsync(List<Guid> ids)
        {
            return _questionAppService.GetByIdsAsync(ids);
        }

        public Task<PagedResultDto<QuestionDto>> GetListAsync(QuestionPagedAndSortedResultRequestDto input)
        {
            return _questionAppService.GetListAsync(input);
        }

        public Task<PagedResultDto<QuestionDto>> GetQuestionsBySubjectAsync(Guid subjectId, QuestionPagedAndSortedResultRequestDto input)
        {
            return _questionAppService.GetQuestionsBySubjectAsync(subjectId, input);
        }

        public Task<QuestionDto> PublishAsync(Guid id)
        {
            return _questionAppService.PublishAsync(id);
        }

        public Task<QuestionDto> UnpublishAsync(Guid id)
        {
            return _questionAppService.UnpublishAsync(id);
        }

        public Task<QuestionDto> UpdateStatusAsync(Guid id, [FromBody] UpdateQuestionStatusRequest request)
        {
            return _questionAppService.UpdateStatusAsync(id, request);
        }

        public Task<List<Guid>> DeleteManyAsync([FromBody] List<Guid> ids)
        {
            return _questionAppService.DeleteManyAsync(ids);
        }

        public Task<BatchUpdateStatusResultDto> UpdateBatchStatusAsync(BatchUpdateStatusRequestDto request)
        {
            return _questionAppService.UpdateBatchStatusAsync(request);
        }

        public Task<List<ExamDto>> GetExamsByQuestionIdAsync(Guid id)
        {
            return _questionAppService.GetExamsByQuestionIdAsync(id);
        }

        public Task<GroupQuestionDto> GetGroupQuestionByQuestionIdAsync(Guid questionId)
        {
            return _questionAppService.GetGroupQuestionByQuestionIdAsync(questionId);
        }

        public Task<GroupQuestionDto> UpdateGroupQuestionAsync(Guid groupQuestionId, [FromBody] UpdateGroupInfoDto input)
        {
            return _questionAppService.UpdateGroupQuestionAsync(groupQuestionId, input);
        }

        public Task<QuestionDto> AssignToUserAsync(Guid id, [FromBody] AssignQuestionDto input)
        {
            return _questionAppService.AssignToUserAsync(id, input);
        }

        public Task<QuestionDto> UnassignAsync(Guid id)
        {
            return _questionAppService.UnassignAsync(id);
        }

        public Task<List<QuestionDto>> GetQuestionsByAssignedUserAsync(Guid userId)
        {
            return _questionAppService.GetQuestionsByAssignedUserAsync(userId);
        }

        public Task<BatchAssignResultDto> BatchAssignToUserAsync([FromBody] BatchAssignQuestionsDto input)
        {
            return _questionAppService.BatchAssignToUserAsync(input);
        }

        public Task<BatchUnassignResultDto> BatchUnassignQuestionsAsync([FromBody] List<Guid> questionIds)
        {
            return _questionAppService.BatchUnassignQuestionsAsync(questionIds);
        }
    }
}
