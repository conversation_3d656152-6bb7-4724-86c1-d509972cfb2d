import { QuestionDraft } from '../../api/questionDraftApi';
import practiceLocalization from '../../components/quizs/localization';
import {
  ExamFillInBlankAnswer,
  ExamMatchingAnswer,
  ExamMatchingItem,
  ExamQuestionOption,
} from '../../interfaces/exams/examBase';
import {
  ContentFormatType,
  ExamStatus,
} from '../../interfaces/exams/examEnums';
import { IQuestion } from '../../interfaces/questions/question';
import { FillBlanksQuestion } from '../../interfaces/quizs/fillblanks.interfaces';
import {
  MultiSelectAnswer,
  MultiSelectQuestion,
} from '../../interfaces/quizs/multiSelectQuiz.interface';
import {
  BaseAnswer,
  BaseQuestion,
  MatchingItemType,
  MatchingQuestion,
  MatchingQuestionAnswer,
  QuestionType,
  QuizAnswer,
  QuizQuestion,
} from '../../interfaces/quizs/questionBase';

// Map server-side question types to client-side types
const mapQuestionType = (serverType: QuestionType): string => {
  switch (serverType) {
    case QuestionType.SingleChoice:
      return practiceLocalization.quiz;
    case QuestionType.MultipleChoice:
      return practiceLocalization.multiselect;
    case QuestionType.Essay:
      return practiceLocalization.essay;
    case QuestionType.Matching:
      return practiceLocalization.matching;
    case QuestionType.FillInBlank:
      return practiceLocalization.fillblanks;
    default:
      return '';
  }
};
export const mapQuestionStatusString = (
  serverStatus: string
): ExamStatus | undefined => {
  switch (serverStatus) {
    case 'Draft':
      return ExamStatus.Draft;
    case 'Published':
      return ExamStatus.Published;
    case 'Approved':
      return ExamStatus.Approved;
    case 'Submitted':
      return ExamStatus.Submitted;
    case 'Rejected':
      return ExamStatus.Rejected;
  }
};

export const mapQuestionStatus = (serverStatus: number) => {
  switch (serverStatus) {
    case 0:
      return ExamStatus.Draft;
    case 1:
      return ExamStatus.Published;
    case 2:
      return ExamStatus.Approved;
    case 3:
      return ExamStatus.Submitted;
    case 4:
      return ExamStatus.Rejected;
  }
};

// Helper function to process tags from a string or array
const processTags = (tags: string | string[] | undefined): string[] => {
  if (!tags) return [];
  if (Array.isArray(tags)) return tags;
  return tags
    .split(',')
    .map((tag) => tag.trim())
    .filter((tag) => tag !== '');
};

// Helper function to process topics from a string or array
const processTopics = (topics: string | string[] | undefined): string[] => {
  if (!topics) return [];
  if (Array.isArray(topics)) return topics;
  return topics
    .split(',')
    .map((topic) => topic.trim())
    .filter((topic) => topic !== '');
};

const formatTimestamp = (timestamp: string | undefined): string => {
  if (!timestamp) return '';
  // Create a Date object from the timestamp and format it to ISO string
  return new Date(timestamp).toISOString().substring(0, 19);
};

class QuestionDraftAdapter {
  /**
   * Convert server entity to React BaseQuestion
   */
  public toBaseQuestion(entity: QuestionDraft): BaseQuestion {
    const options = entity.options?.map(
      (option) =>
        ({
          id: option.id,
          content: option.content,
          contentFormat: option.contentFormat,
          isCorrect: option.isCorrect,
          order: 0,
        } as BaseAnswer)
    );
    // Process tags and topics for the question
    const tagsArray = processTags(entity.tags);
    const topicsArray = processTopics(entity.topics);

    const baseQuestion: BaseQuestion = {
      clientId: entity.id || '',
      title: entity.description || '',
      content: entity.content || '',
      type: mapQuestionType(entity.type),
      points: entity.difficultyLevel,
      statusEntity: entity.status,
      tags: tagsArray,
      topics: topicsArray,
      subjectId: entity.subjectId,
      subject: entity.subject,
      options: options,
      source: entity.source,
      creationTime: formatTimestamp(entity.creationTime),
      lastModificationTime: formatTimestamp(entity.lastModificationTime),
      contentFormat: entity.contentFormat,
      difficulty: entity.difficultyLevel,
      syncQuestion: false,
    };
    // Handle specific question types
    switch (entity.type) {
      case QuestionType.SingleChoice:
      case QuestionType.MultipleChoice:
        return this.toQuizQuestion(entity, baseQuestion);
      case QuestionType.Matching:
        return this.toMatchingQuestion(entity, baseQuestion);
      case QuestionType.FillInBlank:
        return this.toFillblankQuestion(entity, baseQuestion);
      case QuestionType.Essay:
        return this.toEssayQuestion(entity, baseQuestion);
      default:
        return baseQuestion;
    }
  }

  /**
   * Convert React BaseQuestion to server entity
   */
  public toQuestionEntity(question: BaseQuestion): IQuestion {
    const entity: IQuestion = {
      ...question,
      id: question.id,
      title: question.title || '',
      content: question.content || '',
      contentFormat: ContentFormatType.Html,
      difficultyLevel: question.points || 1,
      description: question.title || '',
      topics: question.metadata?.topics?.join(','),
      tags: Array.isArray(question.tags)
        ? question.tags?.join(',')
        : question.tags,
      source: question.metadata?.source,
      subjectId: question.subjectId,
      options: [],
      matchingItems: [],
      matchingAnswers: [],
      fillInBlankAnswers: [],
      type: question.type,
      questionType: question.questionType,
      status: question.status,
      statusEntity: question.statusEntity,
      statusEntityString: question.statusEntityString,
      clientId: question.clientId,
      difficulty: question.difficulty,
      syncQuestion: question.syncQuestion,
      order: question.order,
      score: question.score,
      questionId: question.questionId,
      lastSyncQuestionId: question.lastSyncQuestionId,
      creationTime: question.creationTime,
      lastModificationTime: question.lastModificationTime,
      comment: question.comment,
      explanation: question.explanation,
      shuffleOptions: question.shuffleOptions,
      sourceType: question.sourceType,
    };

    switch (question.type) {
      case practiceLocalization.singlechoice:
      case practiceLocalization.quiz:
        entity.options = question.options
          ? this.quizQuestionToOptions((question as QuizQuestion).options ?? [])
          : [];
        break;
      case practiceLocalization.multiplechoice:
      case practiceLocalization.multiselect:
        entity.options = question.options
          ? this.multiselectQuestionToOptions(
              (question as MultiSelectQuestion).options
            )
          : [];
        break;
      case practiceLocalization.matching:
        entity.matchingItems = this.matchingQuestionToMatchingItems(
          question as MatchingQuestion
        );

        entity.matchingAnswers = this.matchingQuestionToMatchingAnswers(
          question as MatchingQuestion
        );
        break;
      case practiceLocalization.fillblanks:
        entity.fillInBlankAnswers =
          this.fillInBlankQuestionToFillInBlankAnswers(
            question as FillBlanksQuestion
          );
        break;
      case practiceLocalization.essay:
        // Todo
        break;
      case practiceLocalization.essay:
        break;
      default:
        break;
    }
    return entity;
  }

  private fillInBlankQuestionToFillInBlankAnswers(
    arg0: FillBlanksQuestion
  ): ExamFillInBlankAnswer[] {
    let output = arg0.blanks.map((blank) => ({
      id: blank.clientId,
      clientId: blank.clientId,
      blankIndex: arg0.blanks.indexOf(blank),
      correctAnswers: blank.correctAnswer.split(','),
      caseSensitive: false,
    }));
    return output;
  }
  private matchingQuestionToMatchingAnswers(
    arg0: MatchingQuestion
  ): ExamMatchingAnswer[] {
    let output: ExamMatchingAnswer[] = [];

    if (arg0.options) {
      output = arg0.options.map(
        (answer: MatchingQuestionAnswer) =>
          ({
            id: answer.id,
            clientId: answer.id,
            premiseId: answer.left,
            responseId: answer.right,
          } as ExamMatchingAnswer)
      );
    }

    return output;
  }

  private matchingQuestionToMatchingItems(
    question: MatchingQuestion
  ): ExamMatchingItem[] | undefined {
    let output: ExamMatchingItem[] = [];

    if (question.leftItems) {
      output = question.leftItems.map(
        (item: MatchingItemType) =>
          ({
            id: item.clientId,
            clientId: item.clientId,
            type: 0,
            content: item.content,
            contentFormat: ContentFormatType.Html,
          } as ExamMatchingItem)
      );
    }

    if (question.rightItems) {
      output = output.concat(
        question.rightItems.map(
          (item: MatchingItemType) =>
            ({
              id: item.clientId,
              clientId: item.clientId,
              type: 1,
              content: item.content,
              contentFormat: ContentFormatType.Html,
            } as ExamMatchingItem)
        )
      );
    }

    return output;
  }

  /**
   * Convert a collection of server entities to React BaseQuestion[]
   */
  public toBaseQuestions(entities: QuestionDraft[]): BaseQuestion[] {
    return entities.map((entity) => this.toBaseQuestion(entity));
  }

  /**
   * Convert a collection of React BaseQuestion to server entities
   */
  public toQuestionEntities(questions: BaseQuestion[]): IQuestion[] {
    return questions.map((question) => this.toQuestionEntity(question));
  }

  // Private helper methods for specific question types
  private toQuizQuestion(
    entity: QuestionDraft,
    baseQuestion: BaseQuestion
  ): QuizQuestion {
    const quizQuestion = baseQuestion as QuizQuestion;
    quizQuestion.content = entity.content;
    quizQuestion.options =
      entity.options?.map((option) => ({
        id: option.id,
        isCorrect: option.isCorrect,
        feedback: '',
        score: 1,
        clientId: option.id ?? '',
        content: option.content,
        contentFormat: option.contentFormat,
        order: option.order,
      })) || [];
    quizQuestion.allowMultiple = entity.type === QuestionType.MultipleChoice;

    return quizQuestion;
  }

  private toMatchingQuestion(
    entity: QuestionDraft,
    baseQuestion: BaseQuestion
  ): MatchingQuestion {
    const matchingQuestion = baseQuestion as MatchingQuestion;

    // Process options to create left/right items for matching
    if (entity.options && entity.options.length > 0) {
      const leftItems: MatchingItemType[] = [];
      const rightItems: MatchingItemType[] = [];
      const matches: { left: string; right: string }[] = [];

      entity.options.forEach((option) => {
        const leftItem = {
          clientId: option.id,
          text: option.content,
          content: option.content,
          type: 'text',
          matchId: option.id,
        } as MatchingItemType;

        const rightItem = {
          clientId: option.id + '-match',
          text: '',
          content: '',
          type: 'text',
          matchId: option.id,
        } as MatchingItemType;

        leftItems.push(leftItem);
        rightItems.push(rightItem);

        matches.push({
          left: leftItem.clientId,
          right: rightItem.clientId,
        });
      });

      matchingQuestion.leftItems = leftItems;
      matchingQuestion.rightItems = rightItems;
      matchingQuestion.matches = matches;
    }

    return matchingQuestion;
  }

  private toFillblankQuestion(
    entity: QuestionDraft,
    baseQuestion: BaseQuestion
  ): BaseQuestion {
    // Implement fill-in-the-blank specific logic here
    // This depends on how your fill-in-the-blank questions are structured
    return {
      ...baseQuestion,
      blanks: entity.options?.map((option) => ({
        id: option.id,
        answer: option.content,
        score: 1,
      })),
    };
  }

  private toEssayQuestion(
    entity: QuestionDraft,
    baseQuestion: BaseQuestion
  ): BaseQuestion {
    // Implement essay specific logic here
    return {
      ...baseQuestion,
      modelAnswer: entity.options?.find((o) => o.isCorrect)?.content || '',
    };
  }

  public mapClientTypeToServerType(clientType: string): QuestionType {
    switch (clientType.toLocaleLowerCase()) {
      case practiceLocalization.quiz:
        return QuestionType.SingleChoice; // Default to single choice
      case practiceLocalization.singlechoice:
        return QuestionType.SingleChoice;
      case practiceLocalization.essay:
        return QuestionType.Essay;
      case practiceLocalization.matching:
        return QuestionType.Matching;
      case practiceLocalization.fillblanks:
        return QuestionType.FillInBlank;
      case practiceLocalization.multiselect:
        return QuestionType.MultipleChoice;
      case practiceLocalization.essay:
        return QuestionType.Essay;
      default:
        throw new Error(`Unsupported question type: ${clientType}`);
    }
  }

  public mapServerTypeToClientType(serverType: QuestionType): string {
    switch (serverType) {
      case QuestionType.SingleChoice:
        return practiceLocalization.quiz;
      case QuestionType.SingleChoice:
        return practiceLocalization.singlechoice;
      case QuestionType.Essay:
        return practiceLocalization.essay;
      case QuestionType.Matching:
        return practiceLocalization.matching;
      case QuestionType.FillInBlank:
        return practiceLocalization.fillblanks;
      case QuestionType.MultipleChoice:
        return practiceLocalization.multiselect;
      case QuestionType.Essay:
        return practiceLocalization.essay;
      default:
        throw new Error(`Unsupported question type: ${serverType}`);
    }
  }

  private quizQuestionToOptions(answers: QuizAnswer[]): ExamQuestionOption[] {
    return answers.map((answer) => ({
      ...answer,
      contentFormat:
        typeof answer.contentFormat === 'string'
          ? ContentFormatType[
              answer.contentFormat as keyof typeof ContentFormatType
            ]
          : answer.contentFormat,
    }));
  }

  private multiselectQuestionToOptions(
    answers: MultiSelectAnswer[]
  ): ExamQuestionOption[] {
    return answers.map((answer) => ({
      ...answer,
      contentFormat:
        typeof answer.contentFormat === 'string'
          ? ContentFormatType[
              answer.contentFormat as keyof typeof ContentFormatType
            ]
          : answer.contentFormat,
    }));
  }
}

export default new QuestionDraftAdapter();
