import React from 'react';
import { Drawer, Typography, Tag } from 'antd';
import {
  CloseOutlined,
  CheckCircleOutlined,
  QuestionCircleOutlined,
  FlagFilled,
} from '@ant-design/icons';
import { ExamSection } from '../../../interfaces/exams/examBase';
import { BaseAnswer } from '../../../interfaces/quizs/questionBase';
import { sortQuestionsByOrder } from './ExamPreviewUtils';

const { Text } = Typography;

interface QuestionSidebarProps {
  visible: boolean;
  onClose: () => void;
  sections: ExamSection[];
  currentSection: number;
  currentQuestionIndex: number;
  selectedAnswers: Record<string, BaseAnswer | BaseAnswer[]>;
  onSelectQuestion: (sectionIndex: number, questionIndex: number) => void;
  inExamMode?: boolean;
  markedQuestions?: string[]; // Danh sách ID câu hỏi được đánh dấu
  containerId?: string;
}

const QuestionSidebar: React.FC<QuestionSidebarProps> = ({
  visible,
  onClose,
  sections,
  currentSection,
  currentQuestionIndex,
  selectedAnswers,
  onSelectQuestion,
  inExamMode = false,
  markedQuestions = [], // Mặc định là mảng rỗng
  containerId,
}) => {
  // Hàm tính tổng số câu hỏi đã trả lời
  const getAnsweredCount = () => {
    return Object.keys(selectedAnswers).length;
  };

  // Hàm tính tổng số câu hỏi
  const getTotalQuestions = () => {
    return sections.reduce(
      (total, section) => total + section.questions.length,
      0
    );
  };

  // Hàm tính số câu hỏi chưa làm
  const getUnansweredCount = () => {
    return getTotalQuestions() - getAnsweredCount();
  };

  // Component thống kê mới theo design
  const renderStatistics = () => {
    const total = getTotalQuestions();
    const answered = getAnsweredCount();
    const unanswered = getUnansweredCount();

    return (
      <div className="tailwind-flex">
        <Tag className="exam-statistic-item total-tag">Tổng: {total}</Tag>
        <Tag className="exam-statistic-item answered-tag">
          Đã làm: {answered}
        </Tag>
        <Tag className="exam-statistic-item unanswered-tag">
          Chưa làm: {unanswered}
        </Tag>
      </div>
    );
  };

  // Hàm render icon ở đầu câu hỏi (chỉ 2 loại)
  const renderQuestionIcon = (
    isCurrentQuestion: boolean,
    isAnswered: boolean
  ) => {
    // Nếu là câu hỏi hiện tại
    if (isCurrentQuestion) {
      return <QuestionCircleOutlined style={{ color: '#EA4C89' }} />;
    }

    // Nếu câu hỏi đã được trả lời
    if (isAnswered) {
      return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
    }

    // Mặc định: hiển thị hình tròn trắng
    return (
      <div
        style={{
          width: 16,
          height: 14,
          borderRadius: '50%',
          border: '1px solid #d9d9d9',
          backgroundColor: 'white',
        }}
      />
    );
  };

  // Hàm render icon flag ở cuối câu hỏi
  const renderFlagIcon = (questionId: string) => {
    if (markedQuestions.includes(questionId)) {
      return <FlagFilled className="tailwind-text-primary" />;
    }
    return null;
  };

  return (
    <Drawer
      className="question-sidebar"
      title={
        <div className="tailwind-flex tailwind-flex-col tailwind-gap-3">
          {/* Hàng đầu tiên: Title và nút Close */}
          <div className="tailwind-flex tailwind-justify-between tailwind-items-center tailwind-w-full">
            <span className="tailwind-text-base tailwind-font-semibold">
              Danh sách câu hỏi
            </span>
            <CloseOutlined
              className="tailwind-cursor-pointer tailwind-text-gray-500 hover:tailwind-text-gray-700 tailwind-text-lg"
              onClick={onClose}
            />
          </div>
          {/* Hàng thứ hai: Thống kê - chỉ hiển thị trong chế độ làm bài */}
          {inExamMode && (
            <div className="question-sidebar-stats">{renderStatistics()}</div>
          )}
        </div>
      }
      placement="right"
      onClose={onClose}
      open={visible}
      width={330}
      closable={false}
      bodyStyle={{ padding: 0, overflowY: 'hidden' }}
      headerStyle={{
        borderBottom: '1px solid #f0f0f0',
      }}
      getContainer={() => {
        if (!containerId) return document.body;
        const container = document.getElementById(containerId);
        return container || document.body;
      }}
    >
      <div
        className="tailwind-overflow-y-auto tailwind-overflow-x-hidden"
        style={{ height: 'calc(100vh - 64px)' }}
      >
        {sections.map((section, sectionIndex) => (
          <div key={section.clientId} className="tailwind-mb-4">
            <div className="tailwind-px-6 tailwind-py-2 tailwind-bg-gray-50">
              <Text strong className="tailwind-text-pink-500">
                {section.title}
              </Text>
              {section.sectionScore && (
                <Tag color="blue" className="tailwind-ml-2">
                  {section.sectionScore} điểm
                </Tag>
              )}
            </div>

            {sortQuestionsByOrder(section.questions).map(
              (question, questionIndex) => {
                const isCurrentQuestion =
                  sectionIndex === currentSection &&
                  questionIndex === currentQuestionIndex;
                const isAnswered = !!selectedAnswers[question.clientId];

                return (
                  <div
                    key={question.clientId}
                    className={`question-sidebar-item tailwind-px-6 tailwind-py-3 tailwind-flex tailwind-items-center tailwind-cursor-pointer ${
                      isCurrentQuestion ? 'question-current' : ''
                    } tailwind-overflow-hidden`}
                    onClick={() => {
                      // Tìm vị trí thực tế của câu hỏi trong mảng gốc
                      const originalIndex = section.questions.findIndex(
                        (q) => q.clientId === question.clientId
                      );
                      onSelectQuestion(sectionIndex, originalIndex);
                    }}
                  >
                    <div className="tailwind-flex tailwind-items-center tailwind-w-full tailwind-overflow-hidden tailwind-gap-3">
                      {/* Icon ở đầu câu hỏi */}
                      <div className="tailwind-flex-shrink-0 tailwind-w-4 tailwind-flex tailwind-justify-center">
                        {renderQuestionIcon(isCurrentQuestion, isAnswered)}
                      </div>

                      {/* Nội dung câu hỏi */}
                      <div className="tailwind-flex-1 tailwind-min-w-0">
                        <Text
                          ellipsis={{ tooltip: true }}
                          className="question-sidebar-item-text"
                          style={{
                            maxWidth: '100%',
                            display: 'block',
                            whiteSpace: 'nowrap',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                          }}
                        >
                          {questionIndex + 1}.{' '}
                          {question.content || 'Nội dung câu hỏi'}
                        </Text>
                      </div>

                      {/* Điểm số (nếu có) */}
                      {question.points && (
                        <Tag color="blue" className="tailwind-flex-shrink-0">
                          {question.points} điểm
                        </Tag>
                      )}

                      {/* Icon flag ở cuối câu hỏi */}
                      {renderFlagIcon(question.clientId)}
                    </div>
                  </div>
                );
              }
            )}
          </div>
        ))}
      </div>
    </Drawer>
  );
};

export default QuestionSidebar;
