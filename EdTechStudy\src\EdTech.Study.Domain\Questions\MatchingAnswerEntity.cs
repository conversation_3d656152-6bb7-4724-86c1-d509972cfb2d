﻿using System;
using System.Collections.Generic;
using System.Text;
using Volo.Abp.Domain.Entities;

namespace EdTech.Study.Questions
{
    /// <summary>
    /// Đ<PERSON>i diện cho một cặp nối trong câu hỏi matching.
    /// </summary>
    public class MatchingAnswerEntity : Entity<Guid>
    {
        public MatchingAnswerEntity()
        {
            
        }

        public MatchingAnswerEntity(Guid id) : base(id)
        {

        }


        /// <summary>
        /// Liên kết đến câu hỏi cha.
        /// </summary>
        public Guid QuestionId { get; set; }

        public QuestionEntity Question { get; set; }

        /// <summary>
        /// Liên kết đến mục tiền đề.
        /// </summary>
        public Guid PremiseId { get; set; }

        /// <summary>
        /// Liên kết đến mục phản hồi.
        /// </summary>
        public Guid ResponseId { get; set; }

        /// <summary>
        /// Điểm số cho cặp nối này.
        /// </summary>
        public float? Score { get; set; }

        /// <summary>
        /// Phản hồi khi nối đúng cặp này.
        /// </summary>
        public string? Feedback { get; set; }
    }
}