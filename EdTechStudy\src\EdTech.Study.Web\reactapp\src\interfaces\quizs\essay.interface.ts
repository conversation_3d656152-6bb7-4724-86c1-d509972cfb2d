import {
  QuestionComponentBaseP<PERSON>,
  BaseQuestion,
  BaseAnswer,
} from './questionBase';

export interface EssayQuestion extends BaseQuestion {
  type: 'essay';
  content: string;
  correctAnswer: string;
  explanation?: string;
  points?: number;
  userAnswer?: EssayAnswer; // User's input answer
  caseSensitive?: boolean; // Whether to check case sensitivity
  allowPartialMatch?: boolean; // Whether to allow partial matches
  maxLength?: number; // Maximum length of the answer in characters
}

export interface EssayAnswer extends BaseAnswer {
  text: string;
}

export interface EssayComponentProps extends QuestionComponentBaseProps {
  question: EssayQuestion;
  allowManyTimes?: boolean;
  disabled?: boolean;
  onComplete?: (questionId: string, answer?: EssayAnswer) => void;
}
