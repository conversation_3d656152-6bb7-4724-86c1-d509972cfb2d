// src/interfaces/exams/examFilter.ts
import { IPracticeExamFilters, IExamFilterMetadata } from './practiceExam';

/**
 * Props interface for ExamFilter component
 */
export interface IExamFilterProps {
  filters: IPracticeExamFilters;
  onFilterChange: (newFilters: Partial<IPracticeExamFilters>) => void;
  onResetFilters: () => void;
  metadata?: IExamFilterMetadata;
  className?: string;
}

/**
 * Props for ExamSearch component
 */
export interface IExamSearchProps {
  value?: string;
  placeholder?: string;
  onSearch: (query: string) => void;
  onClear?: () => void;
  className?: string;
}

/**
 * Props for ExamSort component
 */
export interface IExamSortProps {
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  onSortChange: (sortBy: string, sortOrder: 'asc' | 'desc') => void;
  options: IExamSortOption[];
  className?: string;
}

/**
 * Sort option configuration
 */
export interface IExamSortOption {
  value: string;
  label: string;
  defaultOrder?: 'asc' | 'desc';
}

/**
 * Props for ExamViewMode component
 */
export interface IExamViewModeProps {
  viewMode: 'grid' | 'list';
  onViewModeChange: (mode: 'grid' | 'list') => void;
  className?: string;
}
