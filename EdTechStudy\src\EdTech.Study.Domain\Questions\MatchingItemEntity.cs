﻿using EdTech.Study.Exams;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;
using Volo.Abp.Domain.Entities;

namespace EdTech.Study.Questions
{
    /// <summary>
    /// Đại diện cho một mục trong câu hỏi nối cặp (premise hoặc response).
    /// </summary>
    public class MatchingItemEntity : Entity<Guid>
    {
        public MatchingItemEntity()
        {
            
        }

        public MatchingItemEntity(Guid id) : base(id)
        {

        }

        /// <summary>
        /// Liên kết đến câu hỏi cha.
        /// </summary>
        public Guid QuestionId { get; set; }

        public QuestionEntity Question { get; set; }

        /// <summary>
        /// Loại mục (Premise - tiền đề, hoặc Response - phản hồi).
        /// </summary>
        public MatchingItemType Type { get; set; }

        /// <summary>
        /// Nội dung của mục nối.
        /// </summary>
        [Required]
        public string Content { get; set; }

        /// <summary>
        /// Định dạng nội dung (Text hoặc HTML).
        /// </summary>
        public ContentFormatType ContentFormat { get; set; }

        /// <summary>
        /// Thứ tự sắp xếp của mục trong danh sách.
        /// </summary>
        public int Order { get; set; }
    }
}