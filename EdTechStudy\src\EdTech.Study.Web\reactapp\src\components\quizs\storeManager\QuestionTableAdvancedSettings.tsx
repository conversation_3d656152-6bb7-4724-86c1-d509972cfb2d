import { SettingOutlined, TableOutlined } from '@ant-design/icons';
import {
  Button,
  Checkbox,
  Divider,
  Drawer,
  InputNumber,
  Space,
  Typography,
} from 'antd';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';
import React, { useEffect, useState } from 'react';
import { allTableColumns } from './QuestionStoreManager';

const { Title, Text } = Typography;

export interface TableColumn {
  key: string;
  title: string;
  visible: boolean;
  width: number;
}

interface QuestionTableAdvancedSettingsProps {
  visible: boolean;
  columns: TableColumn[];
  pageSize: number;
  onColumnsChange: (columns: TableColumn[]) => void;
  onPageSizeChange: (pageSize: number) => void;
  onDrawerClose: () => void;
}

const QuestionTableAdvancedSettings: React.FC<
  QuestionTableAdvancedSettingsProps
> = ({
  visible,
  columns,
  pageSize,
  onColumnsChange,
  onPageSizeChange,
  onDrawerClose,
}) => {
  // Make sure we have all required columns - especially tags and topics
  // const ensureAllColumns = (cols: TableColumn[]): TableColumn[] => {
  //   const requiredColumns = allTableColumns;

  //   // Create a map of existing columns
  //   const existingColumnsMap = new Map<string, TableColumn>();
  //   cols.forEach((col) => {
  //     existingColumnsMap.set(col.key, col);
  //   });

  //   // Return updated columns ensuring all required ones exist
  //   return requiredColumns.map((requiredCol) => {
  //     return existingColumnsMap.get(requiredCol.key) || requiredCol;
  //   });
  // };

  // Initialize local columns with ensured columns
  const [localColumns, setLocalColumns] = useState<TableColumn[]>(columns);

  const [localPageSize, setLocalPageSize] = useState<number>(pageSize);

  // Update local state when props change, ensuring all columns exist
  useEffect(() => {
    setLocalColumns(columns);
  }, [columns]);

  useEffect(() => {
    setLocalPageSize(pageSize);
  }, [pageSize]);

  // Handle column visibility toggling
  const handleColumnVisibilityChange = (
    key: string,
    e: CheckboxChangeEvent
  ) => {
    const newColumns = localColumns.map((col) =>
      col.key === key ? { ...col, visible: e.target.checked } : col
    );
    setLocalColumns(newColumns);
    onColumnsChange(newColumns);
  };

  // Handle page size change
  const handlePageSizeChange = (value: number | null) => {
    if (value !== null) {
      setLocalPageSize(value);
      onPageSizeChange(value);
    }
  };

  return (
    <Drawer
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <SettingOutlined style={{ marginRight: 8 }} />
          <span>Cài đặt nâng cao</span>
        </div>
      }
      placement="right"
      onClose={onDrawerClose}
      open={visible}
      width={350}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        {/* Reset button */}
        <Button
          onClick={() => {
            // Reset to all columns visible
            setLocalColumns(allTableColumns);
            onColumnsChange(allTableColumns);
            setLocalPageSize(10);
            onPageSizeChange(10);
          }}
        >
          Đặt lại mặc định
        </Button>
        {/* Column visibility section */}
        <div>
          <Title level={5} style={{ display: 'flex', alignItems: 'center' }}>
            <TableOutlined style={{ marginRight: 8 }} />
            Cột hiển thị
          </Title>
          <div style={{ marginLeft: 8 }}>
            {localColumns.map((column) => (
              <div key={column.key} style={{ marginBottom: 8 }}>
                <Checkbox
                  checked={column.visible}
                  onChange={(e) => handleColumnVisibilityChange(column.key, e)}
                  disabled={column.key === 'action'} // Action column always visible
                >
                  {column.title}
                </Checkbox>
              </div>
            ))}
          </div>
        </div>

        <Divider />

        {/* Page size section */}
        <div>
          <Title level={5}>Số dòng mỗi trang</Title>
          <InputNumber
            min={10}
            max={100}
            value={localPageSize}
            onChange={handlePageSizeChange}
            style={{ width: 120 }}
          />
          <Text type="secondary" style={{ marginLeft: 8 }}>
            (10-100 dòng)
          </Text>
        </div>

        <Divider />
      </Space>
    </Drawer>
  );
};

export default QuestionTableAdvancedSettings;
