using EdTech.Study.Enum;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace EdTech.Study.Exams.Dtos
{
    public class CreateUpdateExamDto
    {
        public Guid? Id { get; set; }

        [Required]
        [MaxLength(256)]
        public string Title { get; set; }

        [MaxLength(512)]
        public string Description { get; set; }

        [MaxLength(128)]
        public string ExamCode { get; set; }

        public ExamType ExamType { get; set; }

        [MaxLength(128)]
        public string? ExamPeriod { get; set; }

        public DateTime? ExamDate { get; set; }

        public int? Duration { get; set; }

        public float? TotalScore { get; set; }

        public ExamStatus Status { get; set; }

        public Guid? SubjectId { get; set; }

        public Guid? GradeId { get; set; }

        public ExamSourceType SourceType { get; set; }

        public List<CreateUpdateExamSectionDto> Sections { get; set; } = new();

        public string? IdempotentKey { get; set; }

        public string CreateIdempotentKey()
        {
            return DateTime.Now.ToString("yyyyMMddHHmmssfff");
        }
    }
}