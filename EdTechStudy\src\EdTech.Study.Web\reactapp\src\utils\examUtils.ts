import { Guid } from 'guid-typescript';
import {
  ExamBase,
  ExamQuestion,
  ExamSection,
} from '../interfaces/exams/examBase';
import {
  ExamType,
  ExamStatus,
  ContentFormatType,
  ExamSourceType,
} from '../interfaces/exams/examEnums';
import { BaseQuestion, QuestionType } from '../interfaces/quizs/questionBase';
import questionDraftAdapter from './adapters/questionDraftAdapter';

/**
 * Creates a new empty exam with default values
 */
export const createNewExam = (): ExamBase => {
  return {
    clientId: Guid.create().toString(),
    title: 'Đề thi mới',
    examCode: generateExamCode(),
    examType: ExamType.Default,
    sections: [],
    description: '',
  };
};

/**
 * Creates a new section for an exam
 */
export const createNewSection = (parentExamId?: string): ExamSection => {
  return {
    clientId: Guid.create().toString(),
    parentId: parentExamId,
    title: '<PERSON>ần thi mới',
    content: '',
    questions: [],
    orderIndex: 0,
    groupQuestions: [],
  };
};

/**
 * Creates a new question for a section
 */
export const createNewQuestion = (
  sectionId: string,
  questionType: string = 'quiz'
): ExamQuestion => {
  return {
    clientId: Guid.create().toString(),
    parentId: sectionId,
    title: 'New Question',
    type: questionType,
    isCompleted: false,
    content: '',
    contentFormat: ContentFormatType.Html,
    isCorrect: false,
    order: 0,
    explanation: '',
    points: 1,
    questionType: QuestionType.SingleChoice,
    difficulty: 0,
    shuffleOptions: true,
    sourceType: ExamSourceType.Default,
    syncQuestion: false,
  };
};

/**
 * Converts exam type enum to readable string
 */
export const getExamTypeString = (type: ExamType): string => {
  switch (type) {
    case ExamType.Test15Minutes:
      return 'Test15Minutes';
    case ExamType.Test45Minutes:
      return 'Test45Minutes';
    case ExamType.MockExam:
      return 'MockExam';
    case ExamType.MidTermExam:
      return 'MidTermExam';
    case ExamType.MidTermExam:
      return 'MidTermExam';
    case ExamType.NationalHighSchoolExam:
      return 'NationalHighSchoolExam';
    case ExamType.QualificationExam:
      return 'QualificationExam';
    case ExamType.FinalExam:
      return 'FinalExam';
    case ExamType.Default:
    default:
      return 'Default';
  }
};

/**
 * Converts exam status enum to readable string
 */
export const getExamStatusString = (status: ExamStatus): string => {
  switch (status) {
    case ExamStatus.Submitted:
      return 'Submitted';
    case ExamStatus.Approved:
      return 'Approved';
    case ExamStatus.Published:
      return 'Published';
    case ExamStatus.Rejected:
      return 'Rejected';
    case ExamStatus.Draft:
    default:
      return 'Draft';
  }
};

export const getExamStatusDisplay = (status: ExamStatus): string => {
  return getExamStatusDisplayVN(status);
};

/**
 * Converts exam status to color for UI display
 */
export const getExamStatusColor = (status: ExamStatus): string => {
  switch (status) {
    case ExamStatus.Submitted:
      return 'orange';
    case ExamStatus.Approved:
      return 'blue';
    case ExamStatus.Published:
      return 'green';
    case ExamStatus.Rejected:
      return 'red';
    case ExamStatus.Draft:
    default:
      return 'gray';
  }
};

/**
 * Counts total questions in an exam
 */
export const countTotalQuestions = (exam: ExamBase): number => {
  return (
    exam.sections?.reduce(
      (total, section) => total + section.questions.length,
      0
    ) ?? 0
  );
};

/**
 * Calculates total points for an exam
 */
export const calculateTotalPoints = (exam: ExamBase): number => {
  return (
    exam.sections?.reduce(
      (totalPoints, section) =>
        totalPoints +
        section.questions.reduce(
          (sectionPoints, question) => sectionPoints + (question.points || 1),
          0
        ),
      0
    ) ?? 0
  );
};

/**
 * Validates an exam structure
 * Returns true if valid, or an error message if invalid
 */
export const validateExam = (exam: ExamBase): true | string => {
  if (!exam.title || exam.title.trim() === '') {
    return 'Exam title is required';
  }

  if (exam.sections && exam.sections.length === 0) {
    return 'Exam must have at least one section';
  }
  if (exam.sections) {
    for (const section of exam.sections) {
      if (!section.title || section.title.trim() === '') {
        return 'All sections must have a title';
      }

      if (section.questions.length === 0) {
        return `Section "${section.title}" must have at least one question`;
      }

      for (const question of section.questions) {
        if (!question.title || question.title.trim() === '') {
          return `All questions in section "${section.title}" must have content`;
        }
      }
    }
  }

  return true;
};

/**
 * Creates a deep copy of an exam
 */
export const cloneExam = (exam: ExamBase): ExamBase => {
  const newExam: ExamBase = {
    ...exam,
    clientId: Guid.create().toString(),
    title: `Copy of ${exam.title}`,
    id: undefined, // Remove ID so it will be created as new
  };

  // Clone sections with new IDs
  newExam.sections =
    exam.sections?.map((section) => ({
      ...section,
      clientId: Guid.create().toString(),
      id: undefined,
      parentId: newExam.clientId,
      // Clone questions with new IDs
      questions: section.questions.map((question) => ({
        ...question,
        clientId: Guid.create().toString(),
        id: undefined,
        parentId: undefined, // Will be set after section ID is assigned
      })),
    })) ?? [];

  // Update question parentIds
  newExam.sections.forEach((section) => {
    section.questions.forEach((question) => {
      question.parentId = section.clientId;
    });
  });

  return newExam;
};

/**
 * Reorders sections in an exam
 */
export const reorderSections = (
  exam: ExamBase,
  sourceIndex: number,
  destinationIndex: number
): ExamBase => {
  const newExam = { ...exam };
  if (!newExam?.sections) return newExam;

  const [removed] = newExam.sections.splice(sourceIndex, 1);
  newExam.sections.splice(destinationIndex, 0, removed);
  return newExam;
};

/**
 * Reorders questions within a section
 */
export const reorderQuestions = (
  section: ExamSection,
  sourceIndex: number,
  destinationIndex: number
): ExamSection => {
  const newSection = { ...section };
  const [removed] = newSection.questions.splice(sourceIndex, 1);
  newSection.questions.splice(destinationIndex, 0, removed);

  // Update order property
  newSection.questions.forEach((question, index) => {
    question.order = index;
  });

  return newSection;
};

/**
 * Moves a question from one section to another
 */
export const moveQuestion = (
  exam: ExamBase,
  sourceSection: string,
  destinationSection: string,
  questionId: string,
  destinationIndex: number
): ExamBase => {
  const newExam = { ...exam };
  if (!newExam.sections) return newExam;

  // Find source and destination sections
  const sourceSectionIndex = newExam.sections.findIndex(
    (section) => section.clientId === sourceSection
  );
  const destSectionIndex = newExam.sections.findIndex(
    (section) => section.clientId === destinationSection
  );

  if (sourceSectionIndex === -1 || destSectionIndex === -1) {
    return exam; // Section not found, return original exam
  }

  // Find question in source section
  const questionIndex = newExam.sections[
    sourceSectionIndex
  ].questions.findIndex((question) => question.clientId === questionId);

  if (questionIndex === -1) {
    return exam; // Question not found, return original exam
  }

  // Remove question from source section
  const [question] = newExam.sections[sourceSectionIndex].questions.splice(
    questionIndex,
    1
  );

  // Update question's parentId
  question.parentId = destinationSection;

  // Add question to destination section
  newExam.sections[destSectionIndex].questions.splice(
    destinationIndex,
    0,
    question
  );

  // Update order property in both sections
  newExam.sections[sourceSectionIndex].questions.forEach((q, index) => {
    q.order = index;
  });

  newExam.sections[destSectionIndex].questions.forEach((q, index) => {
    q.order = index;
  });

  return newExam;
};

/**
 * Gets a formatted exam duration string
 */
export const formatExamDuration = (minutes?: number): string => {
  if (!minutes) return 'Không giới hạn';

  if (minutes < 60) {
    return `${minutes} phút`;
  }

  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;

  if (remainingMinutes === 0) {
    return `${hours} giờ`;
  }

  return `${hours} giờ ${remainingMinutes} phút`;
};

export const parseBaseQuestionToExamQuestion = (question: BaseQuestion) => {
  let questionType: QuestionType =
    questionDraftAdapter.mapClientTypeToServerType(question.type);
  return {
    ...question,
    content: question.content,
    contentFormat: ContentFormatType.Html,
    questionType: questionType,
    difficulty: question.points,
    shuffleOptions: true,
    options: question.options?.map((x) => {
      return {
        clientId: Guid.create().toString(),
        contentFormat: ContentFormatType.Html,
        isCorrect: x.isCorrect,
        order: x.order,
      };
    }),
  } as ExamQuestion;
};

/**
 * Generates a unique exam code based on subject, type and date
 */
export const generateExamCode = (
  subjectCode: string = 'SUB',
  examType: ExamType = ExamType.Default,
  date: Date = new Date()
): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');

  const dateStr = `${year}${month}${day}`;
  const randomStr = Math.random().toString(36).substring(2, 6).toUpperCase();

  // Get exam type code
  const typeCode = getExamTypeCode(examType);

  return `${subjectCode}-${typeCode}-${dateStr}-${randomStr}`;
};

/**
 * Gets a short code for exam type
 */
export const getExamTypeCode = (type: ExamType): string => {
  switch (type) {
    case ExamType.Test15Minutes:
      return 'T15';
    case ExamType.Test45Minutes:
      return 'T45';
    case ExamType.MidTermExam:
      return 'MID';
    case ExamType.FinalExam:
      return 'FIN';
    case ExamType.MockExam:
      return 'MOC';
    case ExamType.QualificationExam:
      return 'QUA';
    case ExamType.NationalHighSchoolExam:
      return 'NAT';
    default:
      return 'DEF';
  }
};

/**
 * Helper function to get a human-readable string for an exam source type
 */
export const getExamSourceTypeString = (
  sourceType?: ExamSourceType
): string => {
  if (sourceType === undefined) return 'Unknown';

  switch (sourceType) {
    case ExamSourceType.Banked:
      return 'Ngân hàng đề thi';
    case ExamSourceType.Default:
    default:
      return 'Mặc định';
  }
};

/**
 * Helper function to get a color for an exam source type
 */
export const getExamSourceTypeColor = (sourceType?: ExamSourceType): string => {
  if (sourceType === undefined) return 'default';

  switch (sourceType) {
    case ExamSourceType.Banked:
      return 'purple';
    case ExamSourceType.Default:
    default:
      return 'blue';
  }
};

/**
 * Gets Vietnamese display text for exam type
 */
export const getExamTypeDisplayVN = (type: ExamType): string => {
  switch (type) {
    case ExamType.Test15Minutes:
      return 'Kiểm tra 15 phút';
    case ExamType.Test45Minutes:
      return 'Kiểm tra 45 phút';
    case ExamType.MidTermExam:
      return 'Giữa học kì';
    case ExamType.FinalExam:
      return 'Cuối học kì';
    case ExamType.MockExam:
      return 'Thi thử';
    case ExamType.QualificationExam:
      return 'Thi tuyển sinh';
    case ExamType.NationalHighSchoolExam:
      return 'THPT Quốc gia';
    case ExamType.Default:
    default:
      return 'Mặc định';
  }
};

/**
 * Gets Vietnamese display text for exam status
 */
export const getExamStatusDisplayVN = (status: ExamStatus): string => {
  switch (status) {
    case ExamStatus.Submitted:
      return 'Đang chờ duyệt';
    case ExamStatus.Approved:
      return 'Đã duyệt';
    case ExamStatus.Published:
      return 'Công bố';
    case ExamStatus.Rejected:
      return 'Từ chối';
    case ExamStatus.Draft:
    default:
      return 'Soạn thảo';
  }
};
