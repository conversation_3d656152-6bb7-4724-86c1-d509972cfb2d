﻿using Microsoft.AspNetCore.Mvc;

namespace EdTech.Study.Web.Controllers
{
    public class ExamManagementViewController: Controller
    {
        [Route("ExamManagement")]
        [Route("ExamManagement/edit/{id}")]
        [Route("ExamManagement/preview/{id}")]
        [HttpGet]
        public IActionResult Index()
        {
            return View("~/Pages/Exams/Index.cshtml", new EdTech.Study.Web.Pages.Exams.IndexModel());  
        }
    }
}
