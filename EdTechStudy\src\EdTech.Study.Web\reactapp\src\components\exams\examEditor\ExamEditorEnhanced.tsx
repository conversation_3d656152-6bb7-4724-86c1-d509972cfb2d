import React, {
  useState,
  useEffect,
  useCallback,
  useRef,
  useLayoutEffect,
  useMemo,
  createContext,
  useContext,
  memo,
  useTransition,
} from 'react';
import {
  Form,
  Input,
  Button,
  Space,
  Select,
  message,
  Modal,
  Empty,
  Typography,
  Tag,
  Dropdown,
} from 'antd';
import {
  SaveOutlined,
  ExclamationCircleOutlined,
  MenuOutlined,
  ReloadOutlined,
  CheckOutlined,
  GlobalOutlined,
  EditOutlined,
  CloseOutlined,
  DownOutlined,
  SendOutlined,
  QuestionCircleOutlined,
  EyeFilled,
  UpOutlined,
  FileTextOutlined,
  GroupOutlined,
} from '@ant-design/icons';
import {
  ExamSection,
  ExamQuestion,
  ExamGroupQuestion,
} from '../../../interfaces/exams/examBase';
import {
  ExamType,
  ExamStatus,
  ExamSourceType,
  ContentFormatType,
} from '../../../interfaces/exams/examEnums';
import { Subject } from '../../../interfaces/lessons/subject';
import { updateCurrentExamField } from '../../../store/slices/ExamSlices/examDataManagerSlice';
import {
  createNewExam,
  createNewSection,
  generateExamCode,
  getExamStatusColor,
  getExamStatusDisplay,
} from '../../../utils/examUtils';
import './examEditor.css';
import {
  BaseQuestion,
  PracticeEngineContext,
} from '../../../interfaces/quizs/questionBase';
import {
  buildSectionToUpdate,
  defaultExamEditorContext,
  ExamEditorContext,
  ExamEditorProps,
  MAIN_PARTS,
} from './examEditorEnhanced.interface';
import usePracticesLogic from '../../quizs/practiceEngines/hooks/usePracticesLogic';
import { IBookMarkItem } from '../../../interfaces/commonInterfaces';
import { getExamStatusIcon } from './helpers/examEditorHelper';
import ExamEditorToolbar from './ExamEditorToolbar';

import {
  getFullscreenElement,
  useFullscreenAdapter,
} from '../../common/Fullscreen';
import FullscreenContainer from '../../common/Fullscreen/FullscreenContainer';

import { useNavigate } from 'react-router-dom';

import { handleHtmlString } from '../../../utils/stringUtils';
import LoadingScreen from '../../common/Loading/LoadingScreen';
import { ExamSectionEditorRef } from './ExamSectionEditor';
import CustomRichText from '../../common/elements/Text/RichTextEditorComponent/v0/CustomRichText';
import DelayRender from '../../common/Loading/DelayRender';
import { useQuestionContext } from '../../../providers/Questions/QuestionProvider';
import { Guid } from 'guid-typescript';

import ExamSectionEditor from './ExamSectionEditor';
import SortSectionsModal from '../components/SortSectionsModal';
import ExamEditorSideMenu from './ExamEditorSideMenu';
import useDebouncedCallback from '../../../hooks/apps/useDebouncedCallback';

const { Title } = Typography;
const { confirm } = Modal;

const ExamEditorEnhancedContext = createContext<ExamEditorContext>(
  defaultExamEditorContext
);

export const useExamEditorEnhanced = () => {
  const context = useContext(ExamEditorEnhancedContext);
  if (!context) {
    throw new Error(
      'useExamEditorEnhanced must be used within a ExamEditorEnhanced'
    );
  }
  return context;
};

const ExamEditorEnhanced: React.FC<ExamEditorProps> & {
  BasicInformation: React.FC;
  ExamContent: React.FC;
  FormControl: React.FC<{ name: string }>;
} = ({
  examId,
  currentExam,
  loading,
  stateSavePending,
  error,
  onSaveSuccess,
  fetchExamById,
  setCurrentExam,
  updateCurrentExamField,
  addSection,
  updateSection,
  removeSection,
  addQuestion,
  updateQuestion,
  removeQuestion,
  createExam,
  updateExamData,
  updateExamStatus,
  setStateSavePending,
  updateGroupQuestion,
  removeGroupQuestion,
  addGroupQuestion,
}) => {
  const virtualExamId = useMemo(() => {
    return examId ?? Guid.create().toString();
  }, [examId]);

  const {
    isPending: isPracticeLogicRegisterPending,
    isInitialized: isInitializedPracticeEngines,
    error: errorPracticeLogic,
  } = usePracticesLogic({
    consumerId: virtualExamId,
  });

  const {
    isFullscreen: _isFullScreenBasicInfo,
    fullscreenRef: _fullscreenRefBasicInfo,
    handleToggleFullscreen: _handleToggleFullscreenBasicInfo,
  } = useFullscreenAdapter();

  const {
    isFullscreen: _isFullScreenExamContent,
    fullscreenRef: _fullscreenRefExamContent,
    handleToggleFullscreen: _handleToggleFullscreenExamContent,
  } = useFullscreenAdapter();

  const handleToggleFullscreenSection = useCallback(
    (name: string) => {
      if (name === MAIN_PARTS.BASIC_INFORMATION) {
        _handleToggleFullscreenBasicInfo();
      } else if (name === MAIN_PARTS.SECTIONS_QUESTIONS) {
        _handleToggleFullscreenExamContent();
      }
    },
    [_handleToggleFullscreenBasicInfo, _handleToggleFullscreenExamContent]
  );

  const [form] = Form.useForm();
  const [selectedSectionId, setSelectedSectionId] = useState<string | null>(
    null
  );
  const basicInformationRef = useRef<HTMLDivElement>(null);
  const examContentRef = useRef<HTMLDivElement>(null);

  const [editedSections, setEditedSections] = useState<ExamSection[]>(
    currentExam?.sections ?? []
  );
  const [currentPosition, setCurrentPosition] = useState<{
    sectionIndex: number;
    questionOrGroupIndex: number;
    subIndex?: number;
  }>({
    sectionIndex: 0,
    questionOrGroupIndex: 0,
    subIndex: 0,
  });

  const [sortSectionsModalVisible, setSortSectionsModalVisible] =
    useState<boolean>(false);

  const [showMenu, setShowMenu] = useState<boolean>(false);
  const [menuItems, setMenuItems] = useState<IBookMarkItem[]>([
    {
      id: MAIN_PARTS.BASIC_INFORMATION,
      displayName: 'Thông tin đề thi',
      icon: <FileTextOutlined />,
    },
    {
      id: MAIN_PARTS.SECTIONS_QUESTIONS,
      displayName: 'Phần thi & câu hỏi',
      icon: <FileTextOutlined />,
    },
  ]);
  const examEditorContentRef = useRef<HTMLDivElement>(null);
  const sectionQuestionContainerRef = useRef<HTMLDivElement>(null);
  const [currentCarouselIndex, setCurrentCarouselIndex] = useState(0);

  const [isTransitioning, startTransition] = useTransition();

  const navigate = useNavigate();

  // Handle form value changes
  const handleFormValueChange = (changedValues: any) => {
    console.log('changedValues', changedValues);
    Object.entries(changedValues).forEach(([field, value]) => {
      updateCurrentExamField({ field, value });
    });
  };

  // Save exam data
  const handleSave = async (cb?: (id: string) => void) => {
    try {
      // Wait for any pending state saves to complete
      if (stateSavePending) {
        message.loading('Đang lưu trạng thái...', 0);
        // Add timeout to prevent infinite loop
        const timeoutMs = 10000; // 10 seconds timeout
        const startTime = Date.now();

        while (stateSavePending) {
          if (Date.now() - startTime > timeoutMs) {
            message.destroy(); // Clear the loading message
            message.error(
              'Hết thời gian chờ lưu trạng thái. Vui lòng thử lại.'
            );
            break;
          }
          await new Promise((resolve) => setTimeout(resolve, 100)); // Check every 100ms
        }
        message.destroy(); // Clear the loading message
      }

      const values = await form.validateFields();

      if (!currentExam) {
        message.error('No exam data to save');
        return;
      }

      const sectionsToUpdate = editedSections.map((p) =>
        buildSectionToUpdate(p)
      );

      const examData = {
        ...currentExam,
        title: values.title,
        description: values.description,
        examCode: values.examCode,
        examType: values.examType,
        status: values.status,
        subjectId: values.subjectId,
        gradeId: values.gradeId,
        examPeriod: values.examPeriod,
        duration: values.duration,
        totalScore: values.totalScore,
        sourceType: values.sourceType,
        examDate: values.examDate,
        sections: sectionsToUpdate,
      };
      let createUpdateExamId = examId;
      
      if (examId) {
        // Update existing exam
        await updateExamData({ examId, examData });
        message.success('Đề thi đã được cập nhật');
      } else {
        // Create new exam
        let response = await createExam(examData);
        if (response.error && response.error.message) {
          message.error(response.error.message);
          return;
        }
        console.log('🚀 ~ handleSave ~ response:', response);
        message.success('Tạo đề thi mới thành công');

        createUpdateExamId = response.payload || '';
      }

      if (onSaveSuccess) {
        setTimeout(() => {
          onSaveSuccess();
        }, 300);
      }

      if (cb && createUpdateExamId) cb(createUpdateExamId);
      return createUpdateExamId;
    } catch (error) {
      console.error('Failed to save exam:', error);
      message.error('Failed to save exam');
    }
  };

  // Add a new section
  const handleAddSection = useCallback(() => {
    const newSection = createNewSection(currentExam?.clientId);
    addSection(newSection);
    setEditedSections((prev) => [...prev, newSection]);
    const menuItem = {
      id: newSection.clientId,
      displayName: newSection.title,
      parentId: 'Sections-Questions',
    } as IBookMarkItem;
    setCurrentPosition({
      sectionIndex: editedSections.length,
      questionOrGroupIndex: 0,
      subIndex: undefined,
    });
    setMenuItems((prev) => [...prev, menuItem]);
  }, [editedSections]);

  // Update section
  const handleUpdateSection = useDebouncedCallback(
    (sectionId: string, data: Partial<ExamSection>) => {
      setEditedSections((prev) =>
        prev.map((sec) =>
          sec.clientId == sectionId
            ? {
                ...sec,
                ...data,
              }
            : sec
        )
      );
      updateSection({ sectionId, sectionData: data });
      setMenuItems((prev) =>
        prev
          .map((item) => {
            if (item.id === sectionId) {
              return {
                id: item.id,
                displayName: data.title ?? item.displayName,
                parentId: item.parentId,
              };
            }
            return item;
          })
          .filter((item) => item.id)
      );
    },
    []
  );

  // Delete section
  const handleDeleteSection = useCallback(
    (sectionId: string, cb?: () => void) => {
      confirm({
        title: 'Bạn có chắc muốn xóa phần thi?',
        icon: <ExclamationCircleOutlined />,
        content:
          'Hành động này không thể hoàn tác. Tất cả câu hỏi trong phần thi này sẽ bị xóa.',
        onOk() {
          removeSection(sectionId);
          if (selectedSectionId === sectionId && currentExam?.sections) {
            const newSelectedId =
              currentExam.sections.find((s) => s.clientId !== sectionId)
                ?.clientId || null;
            setSelectedSectionId(newSelectedId);
          }
          if (cb) cb();
        },
      });
    },
    [selectedSectionId, currentExam?.sections, removeSection]
  );

  // Handle question update from Practice Engine
  const handleEditQuestion = useCallback(
    (
      updatedQuestion: Partial<BaseQuestion> & {
        clientId: string;
        parentId: string;
      }
    ) => {
      if (!editedSections[currentPosition.sectionIndex]) return;
      if (
        updatedQuestion.parentId ===
        editedSections[currentPosition.sectionIndex].clientId
      ) {
        // Cau hoi trong phan thi
        updateQuestion({
          sectionId: editedSections[currentPosition.sectionIndex].clientId,
          questionId: updatedQuestion.clientId,
          questionData: {
            ...updatedQuestion,
            contentFormat: updatedQuestion.contentFormat as ContentFormatType,
            options: updatedQuestion.options?.map((opt) => ({
              ...opt,
              contentFormat: opt.contentFormat as ContentFormatType,
            })),
          },
        });

        setEditedSections((prev) => {
          return prev.map((section) => {
            const newSection = { ...section };
            if (
              newSection.clientId ===
              editedSections[currentPosition.sectionIndex].clientId
            ) {
              const questions = newSection.questions.map((q) => {
                if (q.clientId === updatedQuestion.clientId) {
                  return {
                    ...q,
                    ...updatedQuestion,
                  } as ExamQuestion;
                }
                return q;
              });
              newSection.questions = questions;
              return newSection;
            }
            return section;
          });
        });
      } else if (editedSections[currentPosition.sectionIndex].groupQuestions) {
        // cau hoi trong nhom cau hoi
        const group = editedSections[
          currentPosition.sectionIndex
        ].groupQuestions.find((q) => q.clientId === updatedQuestion.parentId);
        if (group) {
          if (!group.questions) group.questions = [];

          // Update the group question in the store
          updateGroupQuestion({
            sectionId: editedSections[currentPosition.sectionIndex].clientId,
            groupQuestionId: group.clientId,
            groupQuestionData: {
              questions: group.questions.map((q) => {
                if (q.clientId === updatedQuestion.clientId) {
                  // Preserve required ExamQuestion properties while updating
                  return {
                    ...q,
                    ...updatedQuestion,
                    contentFormat: q.contentFormat,
                    shuffleOptions: q.shuffleOptions,
                    questionType: q.questionType,
                    difficulty: q.difficulty,
                  } as ExamQuestion;
                }
                return q as ExamQuestion;
              }),
            },
          });

          // Update local state
          setEditedSections((prev) =>
            prev.map((section) => {
              if (
                section.clientId ===
                editedSections[currentPosition.sectionIndex].clientId
              ) {
                return {
                  ...section,
                  groupQuestions: section.groupQuestions?.map((g) =>
                    g.clientId === group.clientId
                      ? {
                          ...g,
                          questions:
                            g.questions?.map((q) => {
                              if (q.clientId === updatedQuestion.clientId) {
                                // Preserve required ExamQuestion properties while updating
                                return {
                                  ...q,
                                  ...updatedQuestion,
                                  contentFormat: q.contentFormat,
                                  shuffleOptions: q.shuffleOptions,
                                  questionType: q.questionType,
                                  difficulty: q.difficulty,
                                } as ExamQuestion;
                              }
                              return q as ExamQuestion;
                            }) ?? [],
                        }
                      : g
                  ),
                };
              }
              return section;
            })
          );
        }
      }
    },
    [
      editedSections,
      currentPosition.sectionIndex,
      editedSections[currentPosition.sectionIndex]?.questions,
      editedSections[currentPosition.sectionIndex]?.groupQuestions,
      updateQuestion,
      updateGroupQuestion,
      setEditedSections,
    ]
  );

  // Handle question deletion from Practice Engine
  const handleDeleteQuestionFromEngine = useCallback(
    (questionId: string) => {
      if (editedSections[currentPosition.sectionIndex]) {
        removeQuestion({
          sectionId: editedSections[currentPosition.sectionIndex].clientId,
          questionId: questionId,
        });

        setEditedSections((prev) =>
          prev.map((s) => {
            if (
              s.clientId ===
              editedSections[currentPosition.sectionIndex].clientId
            ) {
              const newSection = { ...s };
              const questions = newSection.questions.filter(
                (q) => q.clientId !== questionId
              );
              newSection.questions = questions;
              return newSection;
            }
            return s;
          })
        );
      }
    },
    [editedSections, currentPosition.sectionIndex, removeQuestion]
  );

  // Handle question creation from Practice Engine
  const handleCreateQuestionFromEngine = useCallback(
    (newQuestion: ExamQuestion, position?: number) => {
      if (editedSections[currentPosition.sectionIndex]) {
        setEditedSections((prev) =>
          prev.map((s) => {
            if (
              s.clientId ===
              editedSections[currentPosition.sectionIndex].clientId
            ) {
              const newSection = { ...s };
              const questions = newSection.questions.map((q) =>
                q.clientId === newQuestion.clientId
                  ? ({
                      ...q,
                      ...newQuestion,
                      statusEntity:
                        newQuestion.statusEntity || ExamStatus.Draft,
                      orderIndex:
                        position || q.orderIndex || newSection.questions.length,
                    } as ExamQuestion)
                  : q
              );
              newSection.questions = questions;
              return newSection;
            }
            return s;
          })
        );
        addQuestion({
          sectionId: editedSections[currentPosition.sectionIndex].clientId,
          question: newQuestion,
        });
      }
    },
    [editedSections, currentPosition.sectionIndex, addQuestion]
  );

  const handleApproveExam = () => {
    if (currentExam && currentExam.id) {
      updateExamStatus({
        examId: currentExam.id,
        status: ExamStatus.Approved,
      });
      message.success('Đề thi đã được duyệt thành công');
    } else {
      // If the exam hasn't been saved yet, just update the local state
      updateCurrentExamField({
        field: 'status',
        value: ExamStatus.Approved,
      });
      message.success('Đề thi đã được duyệt. Hãy lưu để cập nhật.');
    }
  };

  const handlePublishExam = () => {
    if (currentExam && currentExam.id) {
      updateExamStatus({
        examId: currentExam.id,
        status: ExamStatus.Published,
      });
      message.success('Đề thi đã được công bố thành công');
    } else {
      // If the exam hasn't been saved yet, just update the local state
      updateCurrentExamField({
        field: 'status',
        value: ExamStatus.Published,
      });
      message.success('Đề thi đã được công bố. Hãy lưu để cập nhật.');
    }
  };

  const handleRevertToDraft = () => {
    if (currentExam) {
      Modal.confirm({
        title: 'Chuyển về trạng thái soạn thảo?',
        icon: <ExclamationCircleOutlined />,
        content: 'Bạn có chắc muốn chuyển đề thi này về trạng thái soạn thảo?',
        getContainer: getFullscreenElement as any,
        onOk: () => {
          if (currentExam.id) {
            updateExamStatus({
              examId: currentExam.id,
              status: ExamStatus.Draft,
            });
            message.success('Đề thi đã được chuyển về trạng thái soạn thảo');
          } else {
            // If the exam hasn't been saved yet, just update the local state
            updateCurrentExamField({
              field: 'status',
              value: ExamStatus.Draft,
            });
            message.success(
              'Đề thi đã được chuyển về trạng thái soạn thảo. Hãy lưu để cập nhật.'
            );
          }
        },
      });
    }
  };

  const handleSubmitExam = () => {
    if (currentExam && currentExam.id) {
      updateExamStatus({
        examId: currentExam.id,
        status: ExamStatus.Submitted,
      });
      message.success('Đề thi đã được gửi duyệt thành công');
    } else {
      // If the exam hasn't been saved yet, just update the local state
      updateCurrentExamField({
        field: 'status',
        value: ExamStatus.Submitted,
      });
      message.success('Đề thi đã được gửi duyệt. Hãy lưu để cập nhật.');
    }
  };

  const handleOpenModalSortSections = useCallback(() => {
    setSortSectionsModalVisible(true);
  }, []);

  const handleSortSections = useCallback(
    (reorderedSections: ExamSection[]) => {
      // Update local state
      setEditedSections(reorderedSections);

      // Update Redux state - reorder based on new section order
      // Dispatch reorderSections actions for each section move needed
      // For simplicity, we'll handle this as a single update

      // First, let's find if we need to update any section data
      reorderedSections.forEach((section, newIndex) => {
        const originalSection = currentExam?.sections?.find(
          (s) => s.clientId === section.clientId
        );
        if (originalSection && originalSection.orderIndex !== newIndex) {
          updateSection({
            sectionId: section.clientId,
            sectionData: { orderIndex: newIndex },
          });
        }
      });

      // Close the modal
      setSortSectionsModalVisible(false);

      // Show a success message
      message.success('Đã cập nhật thứ tự phần thi');
    },
    [currentExam?.sections]
  );

  const handleShowDemo = useCallback(() => {
    confirm({
      getContainer: getFullscreenElement as any,
      title: 'Lưu và xem demo',
      content: 'Bạn có chắc muốn lưu và chuyển tới trang xem đề thi',
      onOk: async () => {
        await handleSave((id) => {
          if (id)
            setTimeout(() => {
              navigate(`/ExamManagement/preview/${id}`);
            }, 300);
          else {
            message.error('Lưu đề thi thất bại');
          }
        });
      },
    });
  }, [examId, handleSave]);

  const handleOpenMenu = useCallback(() => {
    setShowMenu((prev) => !prev);
  }, []);

  const handleTitleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      handleUpdateSection(
        editedSections[currentPosition.sectionIndex].clientId,
        {
          title: e.target.value,
        }
      );
    },
    [editedSections, currentPosition.sectionIndex, handleUpdateSection]
  );

  const handleMovePosition = useCallback(
    (position: {
      sectionIndex: number;
      questionOrGroupIndex: number;
      subIndex?: number;
    }) => {
      setCurrentPosition(position);
    },
    [setCurrentPosition]
  );

  const handleMarkQuestion = useCallback(
    (id: string, marked: boolean) => {
      setMenuItems((prev) =>
        prev.map((item) => {
          if (item.id === id) {
            return {
              ...item,
              marked: marked,
            };
          }
          return item;
        })
      );
    },
    [editedSections, setMenuItems]
  );

  const handleUpdateGroupQuestion = useCallback(
    (groupQuestion: Partial<ExamGroupQuestion> & { clientId: string }) => {
      updateGroupQuestion({
        sectionId: editedSections[currentPosition.sectionIndex].clientId,
        groupQuestionId: groupQuestion.clientId,
        groupQuestionData: groupQuestion,
      });
    },
    [editedSections, currentPosition.sectionIndex, updateGroupQuestion]
  );

  const handleDeleteGroupQuestion = useCallback(
    (id: string) => {
      removeGroupQuestion({
        sectionId: editedSections[currentPosition.sectionIndex].clientId,
        groupQuestionId: id,
      });
    },
    [editedSections, currentPosition.sectionIndex]
  );

  const handleAddGroup = useCallback(
    (index?: number) => {
      if (editedSections[currentPosition.sectionIndex]) {
        let order = typeof index === 'number' ? index + 1 : 1;
        addGroupQuestion({
          sectionId: editedSections[currentPosition.sectionIndex].clientId,
          groupQuestion: {
            id: undefined,
            clientId: Guid.create().toString(),
            groupQuestionId: undefined,
            content: 'Nhập nội dung nhóm câu hỏi',
            contentFormat: ContentFormatType.Html,
            questions: [],
            order,
          },
        });
        setTimeout(() => {
          handleMovePosition({
            sectionIndex: currentPosition.sectionIndex,
            questionOrGroupIndex: order,
          });
        }, 500);
      }
    },
    [
      editedSections,
      currentPosition.sectionIndex,
      addGroupQuestion,
      handleMovePosition,
    ]
  );
  const handleDelete = useCallback(() => {
    handleDeleteSection(
      editedSections[currentPosition.sectionIndex].clientId,
      () => {
        setTimeout(() => {
          handleMovePosition({
            sectionIndex: Math.max(0, currentPosition.sectionIndex - 1),
            questionOrGroupIndex: 0,
            subIndex: undefined,
          });
        }, 300);
      }
    );
  }, [
    editedSections,
    currentPosition.sectionIndex,
    handleDeleteSection,
    handleMovePosition,
  ]);

  const handleMenuItemSelect = useCallback(
    (id: string) => {
      // Try to find section directly by id
      const sectionIndex = editedSections.findIndex(
        (sec) => sec.clientId === id
      );

      if (sectionIndex >= 0) {
        handleMovePosition({
          sectionIndex,
          questionOrGroupIndex: 0,
          subIndex: undefined,
        });
      } else {
        // If not a section, it must be a question - find its parent section
        let menuItem = menuItems.find((item) => item.id === id);
        if (!menuItem) return;

        // Find parent section by traversing up the hierarchy
        const maxDeepLevel = 3;
        let parentSectionIndex = -1;
        let currentItem = menuItem;

        for (
          let i = 0;
          i < maxDeepLevel && currentItem && parentSectionIndex < 0;
          i++
        ) {
          const parent = menuItems.find(
            (item) => item.id === currentItem.parentId
          );
          if (!parent) break;

          parentSectionIndex = editedSections.findIndex(
            (sec) => sec.clientId === parent.id
          );

          if (parentSectionIndex < 0) {
            currentItem = parent;
          }
        }

        let itemOrderToUpdate = 0;
        let subOrderToUpdate: number | undefined = undefined;

        if (parentSectionIndex < 0) return;

        // Find question or group directly in one step
        const section = editedSections[parentSectionIndex];
        const questionIndex = section.questions.findIndex(
          (q) => q.clientId === currentItem.id
        );

        if (questionIndex >= 0) {
          itemOrderToUpdate = section.questions[questionIndex].order || 0;
        } else {
          const groupIndex = section.groupQuestions.findIndex(
            (q) => q.clientId === currentItem.id
          );
          if (groupIndex >= 0) {
            itemOrderToUpdate = section.groupQuestions[groupIndex].order || 0;
            subOrderToUpdate =
              section.groupQuestions[groupIndex].questions?.findIndex(
                (q) => q.clientId === menuItem.id
              ) || 0;
          }
        }

        // Update both section and question position at once
        handleMovePosition({
          sectionIndex: parentSectionIndex,
          questionOrGroupIndex: Math.max(0, itemOrderToUpdate),
          subIndex: subOrderToUpdate,
        });
      }
      setShowMenu(false);
    },
    [editedSections, menuItems, setCurrentPosition, setShowMenu]
  );

  // Add a status transition dropdown menu with colors
  const statusMenuItems = [
    {
      key: 'draft',
      label: 'Chuyển về Soạn thảo',
      icon: <EditOutlined style={{ color: '#8c8c8c' }} />,
      onClick: () => handleRevertToDraft(),
      disabled: currentExam?.status === ExamStatus.Draft,
      style: { color: '#8c8c8c' }, // Gray for draft
    },
    {
      key: 'submit',
      label: 'Gửi Duyệt',
      icon: <SendOutlined style={{ color: '#faad14' }} />,
      onClick: () => handleSubmitExam(),
      disabled: currentExam?.status === ExamStatus.Submitted,
      style: { color: '#faad14' }, // Orange for submitted
    },
    {
      key: 'approve',
      label: 'Duyệt',
      icon: <CheckOutlined style={{ color: '#1890ff' }} />,
      onClick: () => handleApproveExam(),
      disabled: currentExam?.status === ExamStatus.Approved,
      style: { color: '#1890ff' }, // Blue for approved
    },
    {
      key: 'publish',
      label: 'Công bố',
      icon: <GlobalOutlined style={{ color: '#52c41a' }} />,
      onClick: () => handlePublishExam(),
      disabled: currentExam?.status === ExamStatus.Published,
      style: { color: '#52c41a' }, // Green for published
    },
    {
      key: 'reject',
      label: 'Từ chối',
      icon: <CloseOutlined style={{ color: '#f5222d' }} />,
      onClick: () => {
        Modal.confirm({
          title: 'Từ chối đề thi?',
          icon: <ExclamationCircleOutlined />,
          content: 'Bạn có chắc muốn từ chối đề thi này?',
          onOk: () => {
            if (currentExam?.id) {
              updateExamStatus({
                examId: currentExam.id,
                status: ExamStatus.Rejected,
              });
              message.success('Đề thi đã bị từ chối');
            } else {
              updateCurrentExamField({
                field: 'status',
                value: ExamStatus.Rejected,
              });
              message.success('Đề thi đã bị từ chối. Hãy lưu để cập nhật.');
            }
          },
        });
      },
      disabled: currentExam?.status === ExamStatus.Rejected,
      style: { color: '#f5222d' }, // Red for rejected
    },
  ];

  // Load exam data when component mounts or examId changes
  useEffect(() => {
    if (examId) {
      fetchExamById(examId);
    } else {
      const newExam = createNewExam();
      setCurrentExam(newExam);
    }

    return () => {
      setCurrentExam(null);
    };
  }, [examId]);

  useEffect(() => {
    setEditedSections(currentExam?.sections ?? []);
  }, [currentExam?.sections]);

  useEffect(() => {
    const currentSections = currentExam?.sections;
    if (currentSections) {
      startTransition(() => {
        setMenuItems((prev) => {
          // Create section menu items
          let sectionItems = currentSections.map(
            (sec) =>
              ({
                id: sec.clientId,
                displayName: sec.title,
                parentId: MAIN_PARTS.SECTIONS_QUESTIONS,
              } as IBookMarkItem)
          );

          // Create question menu items with their section as parent
          let questionItems: IBookMarkItem[] = [];
          currentSections.forEach((section) => {
            if (section.questions && section.questions.length > 0) {
              const sectionQuestionItems = section.questions.map(
                (question, index) =>
                  ({
                    id: question.clientId,
                    displayName: `Câu ${index + 1}: ${handleHtmlString(
                      question.content ?? ''
                    ).slice(0, 30)}`,
                    parentId: section.clientId,
                    icon: <QuestionCircleOutlined />,
                    order: question.order,
                  } as IBookMarkItem)
              );
              questionItems = [...questionItems, ...sectionQuestionItems];
            }

            // Add group questions and their questions
            if (section.groupQuestions && section.groupQuestions.length > 0) {
              section.groupQuestions.forEach((group, groupIndex) => {
                // Add the group as a menu item
                const groupItem: IBookMarkItem = {
                  id: group.clientId,
                  displayName: `Nhóm ${groupIndex + 1}: ${
                    handleHtmlString(group.content ?? '').slice(0, 30) ||
                    'Nhóm câu hỏi'
                  }`,
                  parentId: section.clientId,
                  icon: <GroupOutlined />,
                  order: group.order,
                };
                questionItems.push(groupItem);

                // Add questions within the group
                if (group.questions && group.questions.length > 0) {
                  const groupQuestionItems = group.questions.map(
                    (question, index) =>
                      ({
                        id: question.clientId,
                        displayName: `Câu ${index + 1}: ${handleHtmlString(
                          question.content ?? ''
                        ).slice(0, 30)}`,
                        parentId: group.clientId, // Parent is the group
                        icon: <QuestionCircleOutlined />,
                        order: question.order,
                      } as IBookMarkItem)
                  );
                  questionItems = [...questionItems, ...groupQuestionItems];
                }
              });
            }
          });

          // Combine both section and question items
          let newItems = [...sectionItems, ...questionItems];

          // Filter out items that already exist in the menu
          const existedIds = prev.map((p) => p.id);
          newItems = newItems.filter(
            (item) => !existedIds.includes(item.id) && item.id
          );

          return [...prev, ...newItems];
        });
      });
    }
  }, [currentExam?.sections]);

  // Update form values when currentExam changes
  useEffect(() => {
    if (currentExam) {
      form.setFieldsValue({
        title: currentExam.title,
        description: currentExam.description || '',
        examCode: currentExam.examCode || '',
        examType: currentExam.examType,
        status: currentExam.status,
        subjectId: currentExam.subjectId,
        gradeId: currentExam.gradeId,
        examPeriod: currentExam.examPeriod || '',
        duration: currentExam.duration || 60,
        totalScore: currentExam.totalScore || 10,
        sourceType: currentExam?.sourceType || ExamSourceType.Default,
        examDate: currentExam.examDate,
      });

      // If there are sections, select the first one by default
      if (
        currentExam.sections &&
        currentExam.sections?.length > 0 &&
        !selectedSectionId
      ) {
        setSelectedSectionId(currentExam.sections[0].clientId);
      }
    }
  }, [currentExam, form]);

  useLayoutEffect(() => {
    if (
      (loading || isPracticeLogicRegisterPending || _isFullScreenExamContent) &&
      examEditorContentRef.current
    ) {
      examEditorContentRef.current.style.maxHeight = `${
        (_isFullScreenExamContent ? window.outerHeight : window.innerHeight) -
        examEditorContentRef.current.offsetTop -
        20
      }px`;
    }
  }, [loading, isPracticeLogicRegisterPending, _isFullScreenExamContent]);

  useLayoutEffect(() => {
    if (currentCarouselIndex === 0) {
      basicInformationRef.current?.scrollIntoView({ behavior: 'smooth' });
    } else {
      examContentRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [currentCarouselIndex, _isFullScreenBasicInfo, _isFullScreenExamContent]);

  useEffect(() => {
    if (error) {
      Modal.error({
        title: 'Lỗi',
        content: error,
      });
    }
  }, [error]);

  // Show loading state while practice engine is initializing
  if (!isInitializedPracticeEngines || errorPracticeLogic) {
    return (
      <div className="tailwind-h-screen tailwind-w-full tailwind-flex tailwind-items-center tailwind-justify-center">
        {errorPracticeLogic ? (
          <div className="tailwind-text-red-500">
            Error initializing practice engine: {errorPracticeLogic.message}
            <Button onClick={() => window.location.reload()}>
              Tải lại trang
            </Button>
          </div>
        ) : (
          <div className="tailwind-text-gray-600">
            {import.meta.env.DEV ? (
              'Initializing practice engine...'
            ) : (
              <div className="tailwind-flex tailwind-items-center tailwind-justify-center tailwind-h-full tailwind-w-full">
                <LoadingScreen />
              </div>
            )}
          </div>
        )}
      </div>
    );
  }

  return (
    <ExamEditorEnhancedContext.Provider
      value={{
        examId,
        currentExam,
        form,
        editedSections,
        currentPosition,
        isFullscreen: _isFullScreenBasicInfo || _isFullScreenExamContent,
        sectionQuestionContainerRef,
        examEditorContentRef,
        menuItems,
        sortSectionsModalVisible,
        statusMenuItems,
        loading: isPracticeLogicRegisterPending || loading,
        isPending: isPracticeLogicRegisterPending,
        handleAddSection,
        handleSave,
        handleOpenModalSortSections,
        handleToggleFullscreen: handleToggleFullscreenSection,
        handleShowDemo,
        handleOpenMenu,
        handleCreateQuestionFromEngine,
        handleDelete,
        handleTitleChange,
        handleMovePosition,
        setMenuItems,
        setEditedSections,
        setSortSectionsModalVisible,
        handleUpdateSection,
        setStateSavePending,
        handleAddGroup,
      }}
    >
      <div className="tailwind-position-relative tailwind-p-2">
        <div className="tailwind-position-relative tailwind-flex tailwind-flex-nowrap tailwind-flex-col tailwind-gap-6 tailwind-overflow-hidden">
          <div
            ref={examEditorContentRef}
            style={{
              maxHeight: 'calc(100vh - 100px)',
            }}
            className="tailwind-w-full tailwind-overflow-y-hidden"
          >
            <Form
              id="exam-editor-form-container"
              form={form}
              layout="vertical"
              onValuesChange={handleFormValueChange}
              initialValues={{
                title: currentExam?.title || '',
                description: currentExam?.description || '',
                examCode: currentExam?.examCode || '',
                examType: currentExam?.examType || ExamType.Default,
                status: currentExam?.status || ExamStatus.Draft,
                subjectId: currentExam?.subjectId,
                gradeId: currentExam?.gradeId,
                examPeriod: currentExam?.examPeriod || '',
                duration: currentExam?.duration || 60,
                totalScore: currentExam?.totalScore || 10,
                sourceType: currentExam?.sourceType || ExamSourceType.Default,
                examDate: currentExam?.examDate,
              }}
              className="tailwind-max-h-full tailwind-flex-1"
            >
              <div className="tailwind-flex tailwind-flex-col tailwind-gap-6">
                <PracticeEngineContext.Provider
                  value={{
                    handleEditQuestion: handleEditQuestion,
                    handleDeleteQuestion: handleDeleteQuestionFromEngine,
                    handleToggleFullscreen: () => {},
                    handleChangePosition: () => {},
                    handleMarkQuestion: handleMarkQuestion,
                    isFullscreen: false,
                    markedIds: menuItems
                      .filter((item) => item.marked)
                      .map((item) => item.id),
                    handlePendingState: (pending: boolean) => {
                      setStateSavePending({ stateSavePending: pending });
                    },
                    handleUpdateGroupQuestion: handleUpdateGroupQuestion,
                    handleDeleteGroupQuestion: handleDeleteGroupQuestion,
                  }}
                >
                  <FullscreenContainer ref={_fullscreenRefBasicInfo}>
                    <div
                      ref={basicInformationRef}
                      style={{
                        opacity: currentCarouselIndex === 0 ? '1' : '0',
                      }}
                      className="tailwind-transition-opacity tailwind-duration-300"
                    >
                      <ExamEditorEnhanced.FormControl
                        name={MAIN_PARTS.BASIC_INFORMATION}
                      />
                      <ExamEditorEnhanced.BasicInformation />
                    </div>
                  </FullscreenContainer>
                  <FullscreenContainer ref={_fullscreenRefExamContent}>
                    <div
                      ref={examContentRef}
                      style={{
                        opacity: currentCarouselIndex === 1 ? '1' : '0',
                      }}
                      className="tailwind-transition-opacity tailwind-duration-300"
                    >
                      <ExamEditorEnhanced.FormControl
                        name={MAIN_PARTS.SECTIONS_QUESTIONS}
                      />
                      <DelayRender
                        delay={2}
                        fallback={
                          <div className="tailwind-h-screen tailwind-w-full tailwind-flex tailwind-items-center tailwind-justify-center">
                            <LoadingScreen />
                          </div>
                        }
                      >
                        <ExamEditorEnhanced.ExamContent />
                      </DelayRender>
                    </div>
                  </FullscreenContainer>

                  <Button
                    className={
                      'tailwind-fixed carousel-btn carousel-btn-top ' +
                      (currentCarouselIndex === 0 ? 'tailwind-hidden' : ' ')
                    }
                    icon={<UpOutlined />}
                    onClick={() => {
                      setCurrentCarouselIndex((prev) => prev - 1);
                    }}
                    disabled={currentCarouselIndex === 0}
                  ></Button>
                  <Button
                    className={
                      'tailwind-fixed carousel-btn carousel-btn-bottom ' +
                      (currentCarouselIndex === 1 ? 'tailwind-hidden' : '')
                    }
                    icon={<DownOutlined />}
                    onClick={() => {
                      setCurrentCarouselIndex((prev) => prev + 1);
                    }}
                    disabled={currentCarouselIndex === 1}
                  ></Button>
                </PracticeEngineContext.Provider>
              </div>
            </Form>
          </div>
        </div>

        <div
          className={
            'tailwind-fixed ' +
            (currentCarouselIndex === 0 ? 'tailwind-hidden' : '')
          }
          style={{
            bottom: '20%',
            left:
              `${
                (examEditorContentRef.current?.offsetLeft ?? 0) +
                (examEditorContentRef.current?.offsetWidth ?? 0) -
                50
              }px` || '80vw',
            zIndex: 500,
            transform: 'translate(0,-50%)',
            opacity: showMenu ? '0' : '1',
          }}
        >
          <Button
            className="tailwind-rounded-full tailwind-shadow-lg tailwind-bg-default"
            icon={<MenuOutlined />}
            size="large"
            onClick={() => setShowMenu((prev) => !prev)}
          ></Button>
        </div>

        {/* Section Sorting Modal */}
        <SortSectionsModal
          visible={sortSectionsModalVisible}
          sections={editedSections}
          onCancel={() => setSortSectionsModalVisible(false)}
          onSave={handleSortSections}
        />
        {!isTransitioning ? (
          <ExamEditorSideMenu
            showMenu={showMenu}
            items={menuItems}
            onClick={handleMenuItemSelect}
            onClose={() => setShowMenu(false)}
          />
        ) : (
          <></>
        )}
      </div>
    </ExamEditorEnhancedContext.Provider>
  );
};

//#region Basic Information
ExamEditorEnhanced.BasicInformation = memo(function BasicInformation() {
  const { examId: _examId, form } = useExamEditorEnhanced();
  const { subjects, lessonGrades: grades } = useQuestionContext();

  function handleChangeDescription(
    _: string | undefined,
    value: string | undefined
  ) {
    form?.setFieldsValue({ description: value });
    // TODO:
  }

  if (!form) return null;

  return (
    <div
      id={MAIN_PARTS.BASIC_INFORMATION}
      key="basic"
      className="tailwind-flex tailwind-flex-col tailwind-bg-white tailwind-rounded-lg tailwind-p-2"
      style={{
        height: 'calc(100vh - 150px)',
      }}
    >
      <Title level={4}>Thông tin đề thi</Title>
      <div className="tailwind-flex tailwind-flex-col tailwind-gap-4 tailwind-p-5 tailwind-grow tailwind-overflow-y-auto">
        {/* Title - Full width */}
        <Form.Item
          name="title"
          label="Tiêu đề Bài thi"
          rules={[{ required: true, message: 'Hãy nhập tiêu đề cho bài thi' }]}
          required
          className="tailwind-w-full"
        >
          <Input variant="underlined" placeholder="Nhập tiêu đề bài thi" />
        </Form.Item>

        {/* Row 1: Exam Code, Type, Subject */}
        <div className="tailwind-grid tailwind-grid-cols-3 tailwind-gap-4">
          <Form.Item name="examCode" label="Mã đề thi" required>
            <Input
              variant="underlined"
              placeholder="Ví dụ: MATH2023-01"
              addonAfter={
                <Button
                  type="text"
                  icon={<ReloadOutlined />}
                  onClick={() => {
                    const selectedSubject = subjects?.find(
                      (s) => s.id === form.getFieldValue('subjectId')
                    );
                    const subjectCode = selectedSubject?.code || 'SUB';
                    const examType =
                      form.getFieldValue('type') || ExamType.Default;
                    const newCode = generateExamCode(subjectCode, examType);

                    // Update form value
                    form.setFieldsValue({ examCode: newCode });

                    // Update Redux state directly
                    updateCurrentExamField({
                      field: 'examCode',
                      value: newCode,
                    });
                  }}
                  title="Tạo mã mới"
                />
              }
            />
          </Form.Item>

          <Form.Item name="examType" label="Loại bài thi">
            <Select variant="underlined">
              <Select.Option value={ExamType.Default}>Mặc định</Select.Option>
              <Select.Option value={ExamType.Test15Minutes}>
                Kiểm tra 15 phút
              </Select.Option>
              <Select.Option value={ExamType.Test45Minutes}>
                Kiểm tra 45 phút
              </Select.Option>
              <Select.Option value={ExamType.MidTermExam}>
                Giữa học kì
              </Select.Option>
              <Select.Option value={ExamType.FinalExam}>
                Cuối học kì
              </Select.Option>
              <Select.Option value={ExamType.MockExam}>Thi thử</Select.Option>
              <Select.Option value={ExamType.QualificationExam}>
                Thi tuyển sinh
              </Select.Option>
              <Select.Option value={ExamType.NationalHighSchoolExam}>
                THPT Quốc gia
              </Select.Option>
            </Select>
          </Form.Item>

          <Form.Item name="subjectId" label="Môn học">
            <Select variant="underlined" placeholder="Select a subject">
              {subjects?.map((subject: Subject) => (
                <Select.Option key={subject.id} value={subject.id}>
                  {subject.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </div>

        {/* Row 2: Grade, Source Type, Exam Period */}
        <div className="tailwind-grid tailwind-grid-cols-3 tailwind-gap-4">
          <Form.Item name="gradeId" label="Khối lớp">
            <Select variant="underlined" placeholder="Select a grade">
              {grades?.map((grade: any) => (
                <Select.Option key={grade.id} value={grade.id}>
                  {grade.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item name="sourceType" label="Nguồn gốc">
            <Select variant="underlined" placeholder="Chọn nguồn gốc">
              <Select.Option value={ExamSourceType.Default}>
                Mặc định
              </Select.Option>
              <Select.Option value={ExamSourceType.Banked}>
                Ngân hàng đề thi
              </Select.Option>
            </Select>
          </Form.Item>

          <Form.Item name="examPeriod" label="Kỳ thi">
            <Input
              variant="underlined"
              placeholder="Nhập kỳ thi (VD: Học kỳ 1)"
            />
          </Form.Item>
        </div>

        {/* Row 3: Exam Date, Duration, Total Score */}
        <div className="tailwind-grid tailwind-grid-cols-3 tailwind-gap-4">
          <Form.Item name="examDate" label="Ngày thi">
            <Input type="date" variant="underlined" />
          </Form.Item>

          <Form.Item name="duration" label="Thời gian làm bài (phút)">
            <Input
              type="number"
              variant="underlined"
              placeholder="Nhập thời gian làm bài"
            />
          </Form.Item>

          <Form.Item name="totalScore" label="Tổng điểm">
            <Input
              type="number"
              variant="underlined"
              placeholder="Nhập tổng điểm"
            />
          </Form.Item>
        </div>

        {/* Description - Full width */}
        <Form.Item name="description" label="Mô tả" className="tailwind-w-full">
          <CustomRichText
            // value={description}
            onChangeValue={handleChangeDescription}
            height={300}
          />
        </Form.Item>
      </div>
    </div>
  );
});
//#endregion

//#region Exam Content
ExamEditorEnhanced.ExamContent = memo(function ExamContent() {
  const {
    editedSections,
    currentPosition,
    isFullscreen: fullscreen,
    sectionQuestionContainerRef,
    handleUpdateSection,
    handleAddSection,
    handleSave,
    handleOpenModalSortSections,
    handleToggleFullscreen,
    handleShowDemo,
    handleOpenMenu,
    handleCreateQuestionFromEngine,
    handleDelete,
    handleTitleChange,
    handleMovePosition,
    handleAddGroup,
  } = useExamEditorEnhanced();
  const sectionEditorRef = useRef<ExamSectionEditorRef>(null);
  const handleAddQuestion = useCallback(() => {
    sectionEditorRef.current?.openModalCreateQuestion();
  }, []);

  const approvedQuestionIndexes = useMemo(
    () =>
      currentPosition.sectionIndex >= 0
        ? editedSections[currentPosition.sectionIndex]?.questions.map((q) =>
            q.statusEntity === ExamStatus.Approved ? true : false
          )
        : [],
    [editedSections, currentPosition.sectionIndex]
  );

  // TODO: log currentPosition
  useEffect(() => {
    console.log('currentPosition', currentPosition);
  }, [currentPosition]);

  return (
    <>
      <div
        id={MAIN_PARTS.SECTIONS_QUESTIONS}
        key="sections"
        ref={sectionQuestionContainerRef}
        className="tailwind-bg-white tailwind-rounded-lg tailwind-p-2 tailwind-grow tailwind-overflow-y-auto"
        style={{
          height: fullscreen ? 'calc(100vh - 50px)' : 'calc(100vh - 150px)',
        }}
      >
        <Title level={4}>Nội dung</Title>
        <div className="tailwind-flex tailwind-flex-col tailwind-gap-4 tailwind-p-5 tailwind-grow tailwind-overflow-y-auto">
          <ExamEditorToolbar
            forceDefault={true}
            className="exam-editor-toolbar"
            handleAddSection={handleAddSection}
            handleSave={handleSave}
            handleOpenModalSortSections={handleOpenModalSortSections}
            handleToggleFullscreen={() =>
              handleToggleFullscreen(MAIN_PARTS.SECTIONS_QUESTIONS)
            }
            handleShowDemo={handleShowDemo}
            handleOpenMenu={handleOpenMenu}
            handleMoveToPosition={({ questionIndex }) =>
              handleMovePosition({
                sectionIndex: currentPosition.sectionIndex,
                questionOrGroupIndex: questionIndex,
                subIndex: 0,
              })
            }
            isFullscreen={fullscreen}
            currentQuestionIndex={currentPosition.questionOrGroupIndex}
            totalQuestionsGroups={
              (editedSections[currentPosition.sectionIndex]?.questions
                ?.length ?? 0) +
              (editedSections[currentPosition.sectionIndex]?.groupQuestions
                ?.length ?? 0)
            }
            currentPosition={currentPosition}
            totalSections={editedSections.length}
            handleAddQuestion={
              editedSections.length > 0 ? handleAddQuestion : undefined
            }
            handleAddGroup={
              editedSections.length > 0
                ? () => handleAddGroup(currentPosition.questionOrGroupIndex)
                : undefined
            }
            handleNextSection={() =>
              handleMovePosition({
                sectionIndex: currentPosition.sectionIndex + 1,
                questionOrGroupIndex: 0,
                subIndex: 0,
              })
            }
            handlePrevSection={() =>
              handleMovePosition({
                sectionIndex: currentPosition.sectionIndex - 1,
                questionOrGroupIndex: 0,
                subIndex: 0,
              })
            }
            approvedQuestionIndexes={approvedQuestionIndexes}
          />

          {editedSections && editedSections.length === 0 ? (
            <div className="tailwind-min-h-[400px]">
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description="Chưa có phần thi nào. Thêm 1 phần thi mới để bắt đầu."
              />
            </div>
          ) : (
            <>
              {editedSections[currentPosition.sectionIndex] && (
                <ExamSectionEditor
                  ref={sectionEditorRef}
                  key={editedSections[currentPosition.sectionIndex].clientId}
                  questionOrGroupIndex={currentPosition.questionOrGroupIndex}
                  index={currentPosition.sectionIndex}
                  subGroupItemIndex={currentPosition.subIndex}
                  section={editedSections[currentPosition.sectionIndex]}
                  handleUpdateSection={handleUpdateSection}
                  handleCreateQuestion={(q, position) => {
                    const newQuestion = {
                      ...q,
                      clientId: q.clientId || Guid.create().toString(),
                      parentId:
                        editedSections[currentPosition.sectionIndex].clientId,
                      nodeType: 'question',
                    };
                    handleCreateQuestionFromEngine(
                      newQuestion as ExamQuestion,
                      position
                    );
                  }}
                  handleDelete={handleDelete}
                  handleTitleChange={handleTitleChange}
                  handleMoveQuestion={handleMovePosition}
                />
              )}
            </>
          )}
        </div>
      </div>
    </>
  );
});
//#endregion

//#region Form Control
ExamEditorEnhanced.FormControl = memo(function FormControl({
  name: _name,
}: {
  name: string;
}) {
  const {
    currentExam,
    examId,
    statusMenuItems,
    loading,
    isPending,
    handleSave,
    handleShowDemo,
  } = useExamEditorEnhanced();
  const navigate = useNavigate();
  return (
    <div className="tailwind-flex tailwind-justify-between tailwind-items-center tailwind-bg-white tailwind-rounded-lg tailwind-px-2 ">
      <div className="tailwind-flex tailwind-items-center">
        {currentExam?.status !== undefined && (
          <Tag
            icon={getExamStatusIcon(currentExam.status)}
            color={getExamStatusColor(currentExam.status)}
            className="tailwind-text-base tailwind-py-1 tailwind-px-3"
          >
            {getExamStatusDisplay(currentExam.status)}
          </Tag>
        )}
      </div>
      <Space>
        {/* Only show status dropdown for existing exams (with ID) */}
        <Dropdown menu={{ items: statusMenuItems }}>
          <Button>
            Trạng thái <DownOutlined />
          </Button>
        </Dropdown>
        <Button
          type="primary"
          icon={<EyeFilled />}
          onClick={() => {
            handleShowDemo();
          }}
          loading={loading || isPending}
          className="tailwind-bg-blue-600"
        >
          Lưu & Xem Demo
        </Button>
        <Button
          type="primary"
          icon={<SaveOutlined />}
          onClick={() =>
            handleSave((newId: string) => {
              if (examId != newId) {
                navigate(`/ExamManagement/preview/${newId}`);
              }
            })
          }
          loading={loading || isPending}
          className="tailwind-bg-blue-600"
        >
          Lưu
        </Button>
      </Space>
    </div>
  );
});
//#endregion

export default ExamEditorEnhanced;
