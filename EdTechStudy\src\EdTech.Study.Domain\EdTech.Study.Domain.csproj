﻿<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFrameworks>netstandard2.0;netstandard2.1;net8.0</TargetFrameworks>
    <Nullable>enable</Nullable>
    <RootNamespace>EdTech.Study</RootNamespace>
	<GenerateEmbeddedFilesManifest>true</GenerateEmbeddedFilesManifest>
  </PropertyGroup>

  <ItemGroup>
    <EmbeddedResource Include="Data\menu-entities-seed-data.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.BlobStoring" Version="8.1.4" />
    <PackageReference Include="Volo.Abp.Ddd.Domain" Version="8.1.4" />
    <PackageReference Include="Volo.Abp.VirtualFileSystem" Version="8.1.4" />
    <ProjectReference Include="..\EdTech.Study.Domain.Shared\EdTech.Study.Domain.Shared.csproj" />
  </ItemGroup>

  <ItemGroup Condition="'$(TargetFramework)' == 'netstandard2.1'">
    <PackageReference Include="Volo.Abp.Identity.Domain">
      <Version>8.1.4</Version>
    </PackageReference>
  </ItemGroup>

  <ItemGroup Condition="'$(TargetFramework)' == 'net8.0'">
    <PackageReference Include="Volo.Abp.Identity.Domain">
      <Version>8.1.4</Version>
    </PackageReference>
  </ItemGroup>

</Project>
