// src/store/slices/ExamSlices/examDataManagerSlice.ts
import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import {
  ExamBase,
  ExamSection,
  ExamQuestion,
  ExamGroupQuestion,
} from '../../../interfaces/exams/examBase';
import ExamsService from '../../../services/exams/ExamsService';
import { Guid } from 'guid-typescript';
import { GetExamListDto } from '../../../interfaces/exams/examDtos';
import { convertKeysToCamelCase } from '@tsp/utils';
import {
  createQuestionBankTemplate,
  QuestionDto,
} from '../../../components/quizs/questionBank/questionBankTemplate';
import {
  ExamStatus,
  ContentFormatType,
} from '../../../interfaces/exams/examEnums';
import practiceLocalization from '../../../components/quizs/localization';

/**
 * Interface for the Exam Data Manager state
 */
export interface IExamDataManagerState {
  // All available exams
  exams: ExamBase[];

  // Current exam being edited or viewed
  currentExam: ExamBase | null;

  // Current section being edited or viewed
  currentSection: ExamSection | null;

  // Current question being edited or viewed
  currentQuestion: ExamQuestion | null;

  // Loading state
  loading: boolean;
  stateSavePending: boolean;

  // Error message
  error: string | null;

  // Whether the current exam has unsaved changes
  isDirty: boolean;

  // Selected question for editing
  selectedQuestion: {
    sectionId: string;
    questionId: string;
  } | null;

  // Pagination
  totalCount: number;
  currentPage: number;
  pageSize: number;
  skipCount: number;
  maxResultCount: number;

  // Search parameters
  filter: string | undefined;
  examType: number | undefined;
  status: ExamStatus | undefined;
  subjectId: string | undefined;
  gradeId: string | undefined;
  startDate: string | undefined;
  endDate: string | undefined;
  sorting: string | undefined;
}

/**
 * Initial state for the Exam Data Manager
 */
const initialState: IExamDataManagerState = {
  exams: [],
  currentExam: null,
  currentSection: null,
  currentQuestion: null,
  loading: false,
  stateSavePending: false,
  error: null,
  isDirty: false,
  selectedQuestion: null,
  totalCount: 0,
  currentPage: 1,
  pageSize: 10,
  skipCount: 0,
  maxResultCount: 10,
  filter: undefined,
  examType: undefined,
  status: undefined,
  subjectId: undefined,
  gradeId: undefined,
  startDate: undefined,
  endDate: undefined,
  sorting: undefined,
};

// Create thunk actions to fetch data
/**
 * Fetch all exams
 */
export const fetchExams = createAsyncThunk(
  'examDataManager/fetchExams',
  async (params: GetExamListDto | undefined, thunk) => {
    return await ExamsService.fetchExams(params)(thunk.dispatch);
  }
);

/**
 * Fetch an exam by ID
 */
export const fetchExamById = createAsyncThunk(
  'examDataManager/fetchExamById',
  async (examId: string, { dispatch }) => {
    const response = await ExamsService.fetchExamById(examId)(dispatch);
    return response;
  }
);

/**
 * Create a new exam
 */
export const createExam = createAsyncThunk(
  'examDataManager/createExam',
  async (examData: Partial<ExamBase>, { dispatch }) => {
    return await ExamsService.createExam(examData)(dispatch);
  }
);

/**
 * Update an existing exam
 */
export const updateExamData = createAsyncThunk(
  'examDataManager/updateExamData',
  async (
    { examId, examData }: { examId: string; examData: Partial<ExamBase> },
    { dispatch }
  ) => {
    return await ExamsService.updateExamData(examId, examData)(dispatch);
  }
);

/**
 * Delete an exam
 */
export const deleteExam = createAsyncThunk(
  'examDataManager/deleteExam',
  async (examId: string, { dispatch }) => {
    return await ExamsService.deleteExam(examId)(dispatch);
  }
);

/**
 * Duplicate an exam
 */
export const duplicateExam = createAsyncThunk(
  'examDataManager/duplicateExam',
  async (
    { examId, newTitle }: { examId: string; newTitle: string },
    { dispatch }
  ) => {
    return await ExamsService.duplicateExam(examId, newTitle)(dispatch);
  }
);

/**
 * Update exam status
 */
export const updateExamStatus = createAsyncThunk(
  'examDataManager/updateExamStatus',
  async (
    { examId, status }: { examId: string; status: ExamStatus },
    { dispatch }
  ) => {
    return await ExamsService.updateExamStatus(examId, status)(dispatch);
  }
);

// Add these helper functions before the slice definition
export const mapQuestionFromApi = (
  question: any,
  examId: string | undefined,
  groupId: string | undefined,
  sectionId: string,
  index?: number
): ExamQuestion => {
  const newQuestion = convertKeysToCamelCase(question) as QuestionDto;
  newQuestion.QuestionType = MapQuestionTypeFromApi(question.questionType);
  newQuestion.Id = question.id;
  newQuestion.ClientId = question.clientId;

  const storedQuestion = createQuestionBankTemplate(
    newQuestion
  ) as ExamQuestion;
  storedQuestion.path = `${examId ?? ''}/${sectionId}/${
    groupId ? `${groupId}/` : ''
  }${question.id}`;
  storedQuestion.nodeType = 'question';
  storedQuestion.parentId = groupId || sectionId;
  storedQuestion.order = question.order ?? index ?? 0;
  return storedQuestion;
};

export const mapGroupQuestionFromApi = (
  groupQuestion: any,
  examId: string | undefined,
  sectionId: string,
  index?: number
) => ({
  ...groupQuestion,
  clientId: groupQuestion.id ?? Guid.create().toString(),
  nodeType: 'group',
  parentId: sectionId,
  order: groupQuestion.order ?? index ?? 0,
  questions: groupQuestion.questions
    ?.map((question: any, index: number) =>
      mapQuestionFromApi(question, examId, groupQuestion.id, sectionId, index)
    )
    .sort(
      (a: ExamQuestion, b: ExamQuestion) => (a.order ?? 0) - (b.order ?? 0)
    ),
});

export const mapSectionFromApi = (
  section: any,
  examId: string | undefined,
  index?: number
): ExamSection => ({
  ...section,
  order: section.order ?? index ?? 0,
  nodeType: 'section',
  clientId: section.id ?? Guid.create().toString(),
  parentId: examId,
  questions: section.questions
    ?.map((question: any) =>
      mapQuestionFromApi(question, examId, undefined, section.id)
    )
    .sort(
      (a: ExamQuestion, b: ExamQuestion) => (a.order ?? 0) - (b.order ?? 0)
    ),
  groupQuestions: section.groupQuestions
    ?.map((groupQuestion: any, index: number) =>
      mapGroupQuestionFromApi(groupQuestion, examId, section.id, index)
    )
    .sort(
      (a: ExamQuestion, b: ExamQuestion) => (a.order ?? 0) - (b.order ?? 0)
    ),
});

/**
 * Exam Data Manager slice
 */
const examDataManagerSlice = createSlice({
  name: 'examDataManager',
  initialState,
  reducers: {
    /**
     * Set all exams
     */
    setExams: (state, action: PayloadAction<ExamBase[]>) => {
      try {
        let payload = action.payload;
        payload = payload.map(
          (p) =>
            ({
              ...p,
              clientId: p.id ?? Guid.create().toString(),
            } as ExamBase)
        );
        state.exams = payload;
      } catch (error) {
        console.error(error);
      }
    },

    /**
     * Set total count for pagination
     */
    setTotalCount: (state, action: PayloadAction<number>) => {
      state.totalCount = action.payload;
    },

    /**
     * Set current page for pagination
     */
    setCurrentPage: (state, action: PayloadAction<number>) => {
      state.currentPage = action.payload;
    },

    /**
     * Set page size for pagination
     */
    setPageSize: (state, action: PayloadAction<number>) => {
      state.pageSize = action.payload;
      state.maxResultCount = action.payload;
    },

    /**
     * Set skip count for pagination
     */
    setSkipCount: (state, action: PayloadAction<number>) => {
      state.skipCount = action.payload;
    },

    /**
     * Set max result count for pagination
     */
    setMaxResultCount: (state, action: PayloadAction<number>) => {
      state.maxResultCount = action.payload;
    },

    /**
     * Update pagination settings
     */
    updatePagination: (
      state,
      action: PayloadAction<{
        page?: number;
        pageSize?: number;
        totalCount?: number;
      }>
    ) => {
      const { page, pageSize, totalCount } = action.payload;

      if (totalCount !== undefined) {
        state.totalCount = totalCount;
      }

      if (pageSize !== undefined) {
        state.pageSize = pageSize;
        state.maxResultCount = pageSize;
      }

      if (page !== undefined) {
        state.currentPage = page;
        state.skipCount = (page - 1) * state.pageSize;
      }
    },

    /**
     * Set loading state
     */
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },

    /**
     * Set error state
     */
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },

    /**
     * Set dirty state (unsaved changes)
     */
    setIsDirty: (state, action: PayloadAction<boolean>) => {
      state.isDirty = action.payload;
    },

    /**
     * Set current exam for editing or viewing
     */
    setCurrentExam: (state, action: PayloadAction<ExamBase | null>) => {
      state.currentExam = action.payload;
      state.isDirty = false;
    },

    /**
     * Add a new exam to the state
     */
    addExam: (state, action: PayloadAction<ExamBase>) => {
      state.exams.push(action.payload);
    },

    /**
     * Update an existing exam in the state
     */
    updateExam: (state, action: PayloadAction<ExamBase>) => {
      const index = state.exams.findIndex(
        (exam) =>
          exam.id === action.payload.id ||
          exam.clientId === action.payload.clientId
      );

      if (index !== -1) {
        state.exams[index] = action.payload;
      }

      // If this is the current exam being edited, update it too
      if (
        state.currentExam &&
        (state.currentExam.id === action.payload.id ||
          state.currentExam.clientId === action.payload.clientId)
      ) {
        state.currentExam = action.payload;
      }
    },

    /**
     * Update a specific field of the current exam
     */
    updateCurrentExamField: (
      state,
      action: PayloadAction<{ field: keyof ExamBase; value: any }>
    ) => {
      if (state.currentExam) {
        const { field, value } = action.payload;
        state.currentExam = {
          ...state.currentExam,
          [field]: value,
        };
        state.isDirty = true;
      }
    },

    /**
     * Remove an exam from the state
     */
    removeExam: (state, action: PayloadAction<string>) => {
      state.exams = state.exams.filter(
        (exam) => exam.id !== action.payload && exam.clientId !== action.payload
      );

      // If the removed exam was the current exam, clear it
      if (
        state.currentExam &&
        (state.currentExam.id === action.payload ||
          state.currentExam.clientId === action.payload)
      ) {
        state.currentExam = null;
      }
    },

    /**
     * Add a section to the current exam
     */
    addSection: (state, action: PayloadAction<ExamSection>) => {
      if (state.currentExam) {
        if (!state.currentExam.sections) state.currentExam.sections = [];
        state.currentExam.sections.push(action.payload);
        state.isDirty = true;
      }
    },

    /**
     * Update a section in the current exam
     */
    updateSection: (
      state,
      action: PayloadAction<{
        sectionId: string;
        sectionData: Partial<ExamSection>;
      }>
    ) => {
      if (state.currentExam) {
        const { sectionId, sectionData } = action.payload;
        if (!state.currentExam?.sections) {
          return;
        }
        const sectionIndex = state.currentExam.sections.findIndex(
          (section) =>
            section.id === sectionId || section.clientId === sectionId
        );

        if (sectionIndex !== -1) {
          state.currentExam.sections[sectionIndex] = {
            ...state.currentExam.sections[sectionIndex],
            ...sectionData,
          };
          state.isDirty = true;
        }
      }
    },

    /**
     * Remove a section from the current exam
     */
    removeSection: (state, action: PayloadAction<string>) => {
      if (state.currentExam && state.currentExam.sections) {
        state.currentExam.sections = state.currentExam.sections.filter(
          (section) =>
            section.id !== action.payload && section.clientId !== action.payload
        );
        state.isDirty = true;
      }
    },

    /**
     * Add a question to a section in the current exam
     */
    addQuestion: (
      state,
      action: PayloadAction<{
        sectionId: string;
        question: ExamQuestion;
      }>
    ) => {
      if (state.currentExam) {
        const { sectionId, question } = action.payload;
        if (!state.currentExam.sections) {
          state.error = 'Chưa có Phần thi nào được thêm';
          return;
        }

        const sectionIndex = state.currentExam.sections.findIndex(
          (section) =>
            section.id === sectionId || section.clientId === sectionId
        );

        if (sectionIndex !== -1) {
          const path = `${state.currentExam?.id ?? ''}/${sectionId}/${
            question.clientId
          }`;
          question.path = path;
          state.currentExam.sections[sectionIndex].questions.push(question);
          state.isDirty = true;
        }
      }
    },

    /**
     * Update a question in a section in the current exam
     */
    updateQuestion: (
      state,
      action: PayloadAction<{
        sectionId: string;
        questionId: string;
        questionData: Partial<ExamQuestion>;
      }>
    ) => {
      if (!state.currentExam?.sections) {
        state.error = 'Chưa có phần thi nào được thêm';
        return;
      }
      if (state.currentExam) {
        const { sectionId, questionId, questionData } = action.payload;

        // Remove all properties with undefined values
        const cleanedQuestionData = Object.fromEntries(
          Object.entries(questionData).filter(
            ([_, value]) => value !== undefined
          )
        ) as Partial<ExamQuestion>;

        const sectionIndex = state.currentExam.sections.findIndex(
          (section) =>
            section.id === sectionId || section.clientId === sectionId
        );
        if (sectionIndex !== -1) {
          const questionIndex = state.currentExam.sections[
            sectionIndex
          ].questions.findIndex(
            (question) =>
              question.id === questionId || question.clientId === questionId
          );

          if (questionIndex !== -1) {
            var oldQuestion =
              state.currentExam.sections[sectionIndex].questions[questionIndex];
            state.currentExam.sections[sectionIndex].questions[questionIndex] =
              {
                ...oldQuestion,
                ...cleanedQuestionData,
              };
            state.isDirty = true;
          }
        }
      }
    },

    /**
     * Remove a question from a section in the current exam
     */
    removeQuestion: (
      state,
      action: PayloadAction<{
        sectionId: string;
        questionId: string;
      }>
    ) => {
      if (state.currentExam) {
        const { sectionId, questionId } = action.payload;

        if (!state.currentExam.sections) {
          state.error = 'Chưa có phần thi nào được thêm';
          return;
        }
        const sectionIndex = state.currentExam.sections.findIndex(
          (section) =>
            section.id === sectionId || section.clientId === sectionId
        );

        if (sectionIndex !== -1) {
          state.currentExam.sections[sectionIndex].questions =
            state.currentExam.sections[sectionIndex].questions.filter(
              (question) =>
                question.id !== questionId && question.clientId !== questionId
            );
          state.isDirty = true;
        }
      }
    },

    /**
     * Add a group question to a section in the current exam
     */
    addGroupQuestion: (
      state,
      action: PayloadAction<{
        sectionId: string;
        groupQuestion: ExamGroupQuestion;
      }>
    ) => {
      if (state.currentExam) {
        const { sectionId, groupQuestion } = action.payload;
        if (!state.currentExam.sections) {
          state.error = 'Chưa có phần thi nào được thêm';
          return;
        }

        const sectionIndex = state.currentExam.sections.findIndex(
          (section) =>
            section.id === sectionId || section.clientId === sectionId
        );

        if (sectionIndex !== -1) {
          if (!state.currentExam.sections[sectionIndex].groupQuestions) {
            state.currentExam.sections[sectionIndex].groupQuestions = [];
          }

          const newGroupQuestion: ExamGroupQuestion = {
            ...groupQuestion,
            nodeType: 'group',
            questions: [],
          };
          newGroupQuestion.questions =
            groupQuestion.questions?.map((q) => ({
              ...q,
              path: `${state.currentExam?.id ?? ''}/${sectionId}/${
                groupQuestion.clientId
              }/${q.clientId}`,
            })) ?? [];

          const newGroups =
            state.currentExam.sections[sectionIndex].groupQuestions;

          newGroups.push(newGroupQuestion);
          state.currentExam.sections[sectionIndex].groupQuestions = newGroups;
          state.isDirty = true;
        }
      }
    },

    /**
     * Update a group question in a section in the current exam
     */
    updateGroupQuestion: (
      state,
      action: PayloadAction<{
        sectionId: string;
        groupQuestionId: string;
        groupQuestionData: Partial<ExamGroupQuestion>;
      }>
    ) => {
      if (!state.currentExam?.sections) {
        state.error = 'Chưa có phần thi nào được thêm';
        return;
      }

      const { sectionId, groupQuestionId, groupQuestionData } = action.payload;

      const cleanedGroupQuestionData = Object.fromEntries(
        Object.entries(groupQuestionData).filter(
          ([_, value]) => value !== undefined
        )
      ) as Partial<ExamGroupQuestion>;

      const sectionIndex = state.currentExam.sections.findIndex(
        (section) => section.id === sectionId || section.clientId === sectionId
      );

      if (
        sectionIndex !== -1 &&
        state.currentExam.sections[sectionIndex].groupQuestions
      ) {
        const groupQuestionIndex = state.currentExam.sections[
          sectionIndex
        ].groupQuestions.findIndex(
          (group) =>
            group.id === groupQuestionId || group.clientId === groupQuestionId
        );

        if (groupQuestionIndex !== -1) {
          const currentGroup =
            state.currentExam.sections[sectionIndex].groupQuestions[
              groupQuestionIndex
            ];

          let questions = currentGroup.questions;
          if (cleanedGroupQuestionData.questions && questions) {
            cleanedGroupQuestionData.questions.forEach((currentQuestion) => {
              const questionIndex = questions.findIndex(
                (question) =>
                  question.id === currentQuestion.id ||
                  question.clientId === currentQuestion.clientId
              );
              const cleanedQuestionData = Object.fromEntries(
                Object.entries(currentQuestion).filter(
                  ([_, value]) => value !== undefined
                )
              ) as Partial<ExamQuestion>;

              if (questionIndex !== -1) {
                questions[questionIndex] = {
                  ...questions[questionIndex],
                  ...cleanedQuestionData,
                };
              } else {
                questions.push({
                  ...currentQuestion,
                  content: currentQuestion.content ?? '',
                  contentFormat: ContentFormatType.Html,
                  order: questions.length,
                  path: `${
                    state.currentExam?.id ?? ''
                  }/${sectionId}/${groupQuestionId}/${
                    currentQuestion.clientId
                  }`,
                });
              }
            });
          }

          const updatedGroupQuestion: ExamGroupQuestion = {
            ...currentGroup,
            ...cleanedGroupQuestionData,
            content:
              cleanedGroupQuestionData.content ?? currentGroup.content ?? '',
            contentFormat: ContentFormatType.Html,
            questions: questions,
          };
          state.currentExam.sections[sectionIndex].groupQuestions[
            groupQuestionIndex
          ] = { ...updatedGroupQuestion } as ExamGroupQuestion;
          state.isDirty = true;
        }
      }
    },

    /**
     * Remove a group question from a section in the current exam
     */
    removeGroupQuestion: (
      state,
      action: PayloadAction<{
        sectionId: string;
        groupQuestionId: string;
      }>
    ) => {
      if (state.currentExam) {
        const { sectionId, groupQuestionId } = action.payload;

        if (!state.currentExam.sections) {
          state.error = 'Chưa có phần thi nào được thêm';
          return;
        }

        const sectionIndex = state.currentExam.sections.findIndex(
          (section) =>
            section.id === sectionId || section.clientId === sectionId
        );

        if (
          sectionIndex !== -1 &&
          state.currentExam.sections[sectionIndex].groupQuestions
        ) {
          state.currentExam.sections[sectionIndex].groupQuestions =
            state.currentExam.sections[sectionIndex].groupQuestions.filter(
              (group) =>
                group.id !== groupQuestionId &&
                group.clientId !== groupQuestionId
            );
          state.isDirty = true;
        }
      }
    },

    /**
     * Add a question to a group question
     */
    addQuestionToGroup: (
      state,
      action: PayloadAction<{
        sectionId: string;
        groupQuestionId: string;
        question: ExamQuestion;
      }>
    ) => {
      if (state.currentExam) {
        const { sectionId, groupQuestionId, question } = action.payload;
        if (!state.currentExam.sections) {
          state.error = 'Chưa có phần thi nào được thêm';
          return;
        }

        const sectionIndex = state.currentExam.sections.findIndex(
          (section) =>
            section.id === sectionId || section.clientId === sectionId
        );

        if (
          sectionIndex !== -1 &&
          state.currentExam.sections[sectionIndex].groupQuestions
        ) {
          const groupQuestionIndex = state.currentExam.sections[
            sectionIndex
          ].groupQuestions.findIndex(
            (group) =>
              group.id === groupQuestionId || group.clientId === groupQuestionId
          );

          if (groupQuestionIndex !== -1) {
            const groupQuestion =
              state.currentExam.sections[sectionIndex].groupQuestions[
                groupQuestionIndex
              ];
            if (!groupQuestion.questions) {
              groupQuestion.questions = [];
            }
            const updatedQuestion = {
              ...question,
              path: `${
                state.currentExam?.id ?? ''
              }/${sectionId}/${groupQuestionId}/${question.clientId}`,
            };

            groupQuestion.questions.push(updatedQuestion);
            state.isDirty = true;
          }
        }
      }
    },

    /**
     * Remove a question from a group question
     */
    removeQuestionFromGroup: (
      state,
      action: PayloadAction<{
        sectionId: string;
        groupQuestionId: string;
        questionId: string;
      }>
    ) => {
      if (state.currentExam) {
        const { sectionId, groupQuestionId, questionId } = action.payload;
        if (!state.currentExam.sections) {
          state.error = 'Chưa có phần thi nào được thêm';
          return;
        }

        const sectionIndex = state.currentExam.sections.findIndex(
          (section) =>
            section.id === sectionId || section.clientId === sectionId
        );

        if (
          sectionIndex !== -1 &&
          state.currentExam.sections[sectionIndex].groupQuestions
        ) {
          const groupQuestionIndex = state.currentExam.sections[
            sectionIndex
          ].groupQuestions.findIndex(
            (group) =>
              group.id === groupQuestionId || group.clientId === groupQuestionId
          );

          if (groupQuestionIndex !== -1) {
            const groupQuestion =
              state.currentExam.sections[sectionIndex].groupQuestions[
                groupQuestionIndex
              ];
            if (groupQuestion.questions) {
              groupQuestion.questions = groupQuestion.questions.filter(
                (question) =>
                  question.id !== questionId && question.clientId !== questionId
              );
              state.isDirty = true;
            }
          }
        }
      }
    },

    /**
     * Set the selected question for editing
     */
    setSelectedQuestion: (
      state,
      action: PayloadAction<{
        sectionId: string;
        questionId: string;
      } | null>
    ) => {
      state.selectedQuestion = action.payload;
    },

    /**
     * Reorder sections in the current exam
     */
    reorderSections: (
      state,
      action: PayloadAction<{
        sourceIndex: number;
        destinationIndex: number;
      }>
    ) => {
      if (state.currentExam) {
        const { sourceIndex, destinationIndex } = action.payload;
        const sections = [...(state.currentExam.sections ?? [])];
        const [removed] = sections.splice(sourceIndex, 1);
        sections.splice(destinationIndex, 0, removed);

        // Update all sections with new order indices
        state.currentExam.sections = sections.map((section, index) => ({
          ...section,
          orderIndex: index,
        }));

        state.isDirty = true;
      }
    },

    /**
     * Reorder questions within a section
     */
    reorderQuestions: (
      state,
      action: PayloadAction<{
        sectionId: string;
        sourceIndex: number;
        destinationIndex: number;
      }>
    ) => {
      if (state.currentExam) {
        const { sectionId, sourceIndex, destinationIndex } = action.payload;
        if (!state.currentExam.sections) {
          state.error = 'Chưa có phần thi nào được thêm';
          return;
        }
        const sectionIndex = state.currentExam.sections.findIndex(
          (section) =>
            section.id === sectionId || section.clientId === sectionId
        );

        if (sectionIndex !== -1) {
          const questions = [
            ...state.currentExam.sections[sectionIndex].questions,
          ];
          const [removed] = questions.splice(sourceIndex, 1);
          questions.splice(destinationIndex, 0, removed);

          // Update the questions array
          state.currentExam.sections[sectionIndex].questions = questions.map(
            (question, index) => ({
              ...question,
              order: index,
            })
          );

          state.isDirty = true;
        }
      }
    },

    /**
     * Move a question from one section to another
     */
    moveQuestion: (
      state,
      action: PayloadAction<{
        sourceSectionId: string;
        questionId: string;
        destinationSectionId: string;
        destinationIndex: number;
      }>
    ) => {
      if (state.currentExam) {
        const {
          sourceSectionId,
          questionId,
          destinationSectionId,
          destinationIndex,
        } = action.payload;
        if (!state.currentExam.sections) {
          state.error = 'Chưa có phần thi nào được thêm';
          return;
        }
        // Find the source section
        const sourceSectionIndex = state.currentExam.sections.findIndex(
          (section) =>
            section.id === sourceSectionId ||
            section.clientId === sourceSectionId
        );

        // Find the destination section
        const destSectionIndex = state.currentExam.sections.findIndex(
          (section) =>
            section.id === destinationSectionId ||
            section.clientId === destinationSectionId
        );

        if (sourceSectionIndex !== -1 && destSectionIndex !== -1) {
          // Find the question in the source section
          const questions =
            state.currentExam.sections[sourceSectionIndex].questions;
          const questionIndex = questions.findIndex(
            (q) => q.id === questionId || q.clientId === questionId
          );

          if (questionIndex !== -1) {
            // Remove the question from the source section
            const [question] = questions.splice(questionIndex, 1);

            // Add it to the destination section at the specified index
            state.currentExam.sections[destSectionIndex].questions.splice(
              destinationIndex,
              0,
              question
            );

            state.isDirty = true;
          }
        }
      }
    },

    /**
     * Duplicate a question within a section
     */
    duplicateQuestion: (
      state,
      action: PayloadAction<{
        sectionId: string;
        questionId: string;
      }>
    ) => {
      if (state.currentExam) {
        const { sectionId, questionId } = action.payload;
        if (!state.currentExam.sections) {
          state.error = 'Chưa có phần thi nào được thêm';
          return;
        }
        const sectionIndex = state.currentExam.sections.findIndex(
          (section) =>
            section.id === sectionId || section.clientId === sectionId
        );

        if (sectionIndex !== -1) {
          const questionIndex = state.currentExam.sections[
            sectionIndex
          ].questions.findIndex(
            (q) => q.id === questionId || q.clientId === questionId
          );

          if (questionIndex !== -1) {
            // Clone the question and assign a new clientId
            const questionToClone =
              state.currentExam.sections[sectionIndex].questions[questionIndex];
            const clonedQuestion = {
              ...questionToClone,
              id: undefined, // Remove ID so backend creates a new entity
              clientId: `${Date.now()}-${Math.random()
                .toString(36)
                .substr(2, 9)}`, // Generate a new clientId
              title: `Copy of ${questionToClone.title}`,
            };

            // Add the cloned question after the original
            state.currentExam.sections[sectionIndex].questions.splice(
              questionIndex + 1,
              0,
              clonedQuestion
            );

            state.isDirty = true;
          }
        }
      }
    },

    /**
     * Reset state to initial values
     */
    resetState: () => {
      return initialState;
    },

    /**
     * Update search parameters
     */
    updateSearchParams: (
      state,
      action: PayloadAction<Partial<GetExamListDto>>
    ) => {
      const {
        filter,
        examType,
        status,
        subjectId,
        gradeId,
        startDate,
        endDate,
        sorting,
      } = action.payload;

      if (filter !== undefined) state.filter = filter;
      if (examType !== undefined) state.examType = examType;
      if (status !== undefined) state.status = status;
      if (subjectId !== undefined) state.subjectId = subjectId;
      if (gradeId !== undefined) state.gradeId = gradeId;
      if (startDate !== undefined) state.startDate = startDate;
      if (endDate !== undefined) state.endDate = endDate;
      if (sorting !== undefined) state.sorting = sorting;
    },

    /**
     * Reset search parameters
     */
    resetSearchParams: (state) => {
      state.filter = undefined;
      state.examType = undefined;
      state.status = undefined;
      state.subjectId = undefined;
      state.gradeId = undefined;
      state.startDate = undefined;
      state.endDate = undefined;
      state.sorting = undefined;
    },

    setStateSavePending: (state, action: PayloadAction<boolean>) => {
      state.stateSavePending = action.payload;
    },
  },
  extraReducers: (builder) => {
    // Handle async thunk actions
    builder
      // fetchExams
      .addCase(fetchExams.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchExams.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(fetchExams.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch exams';
      })

      // fetchExamById
      .addCase(fetchExamById.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchExamById.fulfilled, (state, action) => {
        state.loading = false;
        if (action.payload) {
          state.currentExam = {
            ...action.payload,
            sections: action.payload.sections
              ?.map((section: any, index: number) =>
                mapSectionFromApi(section, state.currentExam?.id, index)
              )
              .sort(
                (a: ExamSection, b: ExamSection) => a.orderIndex - b.orderIndex
              ),
          } as ExamBase;
          state.isDirty = false;
        }
      })
      .addCase(fetchExamById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch exam';
      })

      // createExam
      .addCase(createExam.pending, (state) => {
        state.loading = true;
      })
      .addCase(createExam.fulfilled, (state) => {
        state.loading = false;
        state.isDirty = false;
      })
      .addCase(createExam.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to create exam';
      })

      // updateExamData
      .addCase(updateExamData.pending, (state) => {
        state.loading = true;
      })
      .addCase(updateExamData.fulfilled, (state, action) => {
        state.loading = false;
        if (action.payload) {
          state.isDirty = false;
        }
      })
      .addCase(updateExamData.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to update exam';
      })

      // deleteExam
      .addCase(deleteExam.pending, (state) => {
        state.loading = true;
      })
      .addCase(deleteExam.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(deleteExam.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to delete exam';
      })

      // duplicateExam
      .addCase(duplicateExam.pending, (state) => {
        state.loading = true;
      })
      .addCase(duplicateExam.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(duplicateExam.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to duplicate exam';
      })

      // updateExamStatus
      .addCase(updateExamStatus.pending, (state) => {
        state.loading = true;
      })
      .addCase(updateExamStatus.fulfilled, (state, action) => {
        state.loading = false;
        if (action.payload) {
          // If the updated exam is the current exam, update its status
          if (state.currentExam && state.currentExam.id === action.payload.id) {
            state.currentExam.status = action.payload.status;
          }
        }
      })
      .addCase(updateExamStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to update exam status';
      });
  },
});

// Export actions
export const {
  setExams,
  setLoading,
  setError,
  setIsDirty,
  setCurrentExam,
  addExam,
  updateExam,
  updateCurrentExamField,
  removeExam,
  addSection,
  updateSection,
  removeSection,
  addQuestion,
  updateQuestion,
  removeQuestion,
  setSelectedQuestion,
  reorderSections,
  reorderQuestions,
  moveQuestion,
  duplicateQuestion,
  resetState,
  setTotalCount,
  setCurrentPage,
  setPageSize,
  setSkipCount,
  setMaxResultCount,
  updatePagination,
  updateSearchParams,
  resetSearchParams,
  setStateSavePending,
  addGroupQuestion,
  updateGroupQuestion,
  removeGroupQuestion,
  addQuestionToGroup,
  removeQuestionFromGroup,
} = examDataManagerSlice.actions;

// Export reducer
export default examDataManagerSlice.reducer;
function MapQuestionTypeFromApi(type: any): any {
  switch (type) {
    case 0:
      return practiceLocalization.quiz;
    case 1:
      return practiceLocalization.multiselect;
    case 2:
      return practiceLocalization.essay;
    case 3:
      return practiceLocalization.matching;
    case 4:
      return practiceLocalization.fillblanks;
    default:
      return practiceLocalization.quiz;
  }
}
