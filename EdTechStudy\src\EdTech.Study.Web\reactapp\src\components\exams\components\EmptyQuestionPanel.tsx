import { QuestionCircleOutlined, PlusOutlined } from '@ant-design/icons';
import { Empty, Space, Button, Typography } from 'antd';

const { Title, Text } = Typography;

const EmptyQuestionPanel = ({
  openModalCreateQuestion,
}: {
  openModalCreateQuestion: () => void;
}) => {
  return (
    <div className="tailwind-h-full tailwind-flex">
      <div className="tailwind-text-center tailwind-m-auto">
        <Empty
          image={
            <QuestionCircleOutlined className="tailwind-text-blue-500 tailwind-text-7xl" />
          }
          description={null}
        />

        <Title level={4} className="tailwind-text-center tailwind-mb-1">
          Chưa có câu hỏi
        </Title>
        <Text className="tailwind-text-gray-500 tailwind-text-center mb-6">
          Bắt đầu bằng việc thêm 1 câu hỏi mới
        </Text>
        <br />

        <Space
          direction="vertical"
          size="middle"
          className="tailwind-w-full tailwind-max-w-md tailwind-mt-5"
        >
          <Button
            type="primary"
            size="large"
            onClick={openModalCreateQuestion}
            icon={<PlusOutlined />}
            className="tailwind-w-full tailwind-bg-blue-500 tailwind-hover:tailwind-bg-blue-600 tailwind-h-6 tailwind-flex tailwind-items-center tailwind-justify-center"
          >
            Thêm câu hỏi
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default EmptyQuestionPanel;
