export { default as MatchingComponent } from './MatchingComponent';
export { default as ClickableItem } from './components/ClickableItem';
export { default as ConnectionLines } from './components/ConnectionLines';
export { default as MatchingControls } from './components/MatchingControls';
export { default as MatchingFeedback } from './components/MatchingFeedback';
export { matchingCheckCorrectAnswer } from './matchingUtils';
export { useMatchingLogic } from './hooks/useMatchingLogic';
export { useMatchResults } from './hooks/useMatchResults';
export type { MatchingComponentProps } from './MatchingComponent';
export type {
  ItemProps,
  ClickableItemProps,
  MatchingControlsProps,
  MatchingFeedbackProps,
  MatchingLogicResult,
  MatchResultsResult,
} from '../../../interfaces/quizs/mapping.interface';
