using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;
using EdTech.Study.EntityFrameworkCore;
using EdTech.Study.Enum;
using System.Threading;

namespace EdTech.Study.Exams
{
    public class ExamRepository : EfCoreRepository<StudyDbContext, ExamEntity, Guid>, IExamRepository
    {
        public ExamRepository(IDbContextProvider<StudyDbContext> dbContextProvider) 
            : base(dbContextProvider)
        {
        }

        public async Task<ExamEntity> GetExamWithDetailsAsync(Guid id)
        {
            var dbContext = await GetDbContextAsync();
            
            return await dbContext.Exams
                .Include(e => e.Sections)
                    .ThenInclude(s => s.Questions)
                        .ThenInclude(q => q.Question)
                .Include(e => e.Subject)
                .Include(e => e.Grade)
                .FirstOrDefaultAsync(e => e.Id == id);
        }

        public async Task<List<ExamEntity>> GetListExamAsync(
            string? filter = null,
            ExamType? examType = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            ExamStatus? status = null,
            Guid? subjectId = null,
            Guid? gradeId = null,
            int maxResultCount = 10,
            int skipCount = 0,
            string? sorting = null,
            CancellationToken cancellationToken = default)
        {
            var dbContext = await GetDbContextAsync();
            var query = dbContext.Exams
                .WhereIf(!filter.IsNullOrWhiteSpace(), e => 
                    e.Title.Contains(filter) || 
                    e.Description.Contains(filter) ||
                    e.ExamCode.Contains(filter))
                .WhereIf(examType.HasValue, e => e.ExamType == examType)
                .WhereIf(startDate.HasValue, e => e.ExamDate >= startDate)
                .WhereIf(endDate.HasValue, e => e.ExamDate <= endDate)
                .WhereIf(status.HasValue, e => e.Status == status)
                .WhereIf(subjectId.HasValue, e => e.SubjectId == subjectId)
                .WhereIf(gradeId.HasValue, e => e.GradeId == gradeId);

            return await query
                .OrderBy(sorting ?? nameof(ExamEntity.CreationTime) + " desc")
                .PageBy(skipCount, maxResultCount)
                .ToListAsync(cancellationToken);
        }

        public async Task<long> GetCountAsync(
            string? filter = null,
            ExamType? examType = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            ExamStatus? status = null,
            Guid? subjectId = null,
            Guid? gradeId = null,
            CancellationToken cancellationToken = default)
        {
            var dbContext = await GetDbContextAsync();
            var query = dbContext.Exams
                .WhereIf(!filter.IsNullOrWhiteSpace(), e =>
                    e.Title.Contains(filter) ||
                    e.Description.Contains(filter) ||
                    e.ExamCode.Contains(filter))
                .WhereIf(examType.HasValue, e => e.ExamType == examType)
                .WhereIf(startDate.HasValue, e => e.ExamDate >= startDate)
                .WhereIf(endDate.HasValue, e => e.ExamDate <= endDate)
                .WhereIf(status.HasValue, e => e.Status == status)
                .WhereIf(subjectId.HasValue, e => e.SubjectId == subjectId)
                .WhereIf(gradeId.HasValue, e => e.GradeId == gradeId);
            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }
    }
}