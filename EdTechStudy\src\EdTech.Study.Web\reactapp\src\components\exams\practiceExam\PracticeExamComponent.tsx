import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { debounce } from 'lodash';
import { Row, Col, Pagination, Spin, Empty, Typography } from 'antd';
import { connect } from 'react-redux';
import {
  IPracticeExamFilters,
  IExamFilterMetadata,
  IPracticeExamComponentProps,
} from '../../../interfaces/exams/practiceExam';
import ExamCard from './ExamCard';
import { ExamBase } from '../../../interfaces/exams/examBase';
import { GetExamListDto } from '../../../interfaces/exams/examDtos';
import { useQuestionContext } from '../../../providers/Questions/QuestionProvider';
import ExamFilterSection from './ExamFilterSection';
import './PracticeExamComponent.css';
import {
  fetchPracticeExams,
  IPracticeExamState,
  updatePracticeExamSearchParams,
  updatePracticeExamPagination,
} from '../../../store/slices/ExamSlices/practiceExamSlice';
// Import ảnh background để đảm bảo Vite xử lý đúng đường dẫn
import practiceBackgroundImage from '../../../assets/images/practiceBackground.png';
// Import mapping utilities
import {
  mapExamsWithUserStatus,
  filterExamsByStatus,
  IMappedExamData,
} from './practiceExamUtils';

const { Title } = Typography;

// Redux interfaces
interface IPracticeExamStateToProps {
  exams: ExamBase[];
  totalCount: number;
  currentPage: number;
  pageSize: number;
  loading: boolean;
  // Add filter parameters from Redux state
  filter?: string;
  subjectId?: string;
  gradeId?: string;
  subjectIds?: string[];
  gradeIds?: string[];
}

interface IPracticeExamDispatchToProps {
  fetchExams: (params?: GetExamListDto) => any;
  updateSearchParams: (
    params: Partial<
      GetExamListDto & { subjectIds?: string[]; gradeIds?: string[] }
    >
  ) => void;
  updatePagination: (data: {
    page?: number;
    pageSize?: number;
    totalCount?: number;
  }) => void;
}

interface IPracticeExamComponentReduxProps
  extends IPracticeExamStateToProps,
    IPracticeExamDispatchToProps,
    IPracticeExamComponentProps {}

// Main Component
const PracticeExamComponent: React.FC<IPracticeExamComponentReduxProps> = ({
  exams,
  totalCount,
  currentPage,
  pageSize,
  loading,
  filter,
  subjectId,
  gradeId,
  subjectIds,
  gradeIds,
  fetchExams,
  updateSearchParams,
  updatePagination,
  onPreviewExam,
}) => {
  const { subjects = [], lessonGrades: grades = [] } = useQuestionContext();

  // Local state for filters - sync with Redux state
  const [filters, setFilters] = useState<IPracticeExamFilters>({
    subjects: subjectIds || (subjectId ? [subjectId] : []),
    grades: gradeIds || (gradeId ? [gradeId] : []),
    statuses: [],
    searchQuery: filter || '',
  });

  // Get current user ID (this should come from auth context in real app)
  const currentUserId = 'demo-user-id'; // Replace with actual user ID from auth context

  // Memoized filter metadata
  const filterMetadata = useMemo<IExamFilterMetadata>(
    () => ({
      subjects: subjects.map((subject) => ({
        value: subject.id,
        label: subject.name,
      })),
      grades: grades.map((grade) => ({
        value: grade.id,
        label: grade.name,
      })),
    }),
    [subjects, grades]
  );

  // Map exams with user status - This replaces the demo logic
  const mappedExams = useMemo<IMappedExamData[]>(() => {
    return mapExamsWithUserStatus(exams, subjects, grades, currentUserId);
  }, [exams, subjects, grades, currentUserId]);

  // Apply status filtering on mapped exams
  const filteredExams = useMemo<IMappedExamData[]>(() => {
    console.log(filters.statuses);
    return filterExamsByStatus(mappedExams, filters.statuses || []);
  }, [mappedExams, filters.statuses]);

  // Sync local filters with Redux state on component mount and when Redux state changes
  useEffect(() => {
    setFilters({
      subjects: subjectIds || (subjectId ? [subjectId] : []),
      grades: gradeIds || (gradeId ? [gradeId] : []),
      statuses: [],
      searchQuery: filter || '',
    });
  }, [filter, subjectId, gradeId, subjectIds, gradeIds]);

  // Build API parameters from filters
  const buildApiParams = useCallback(
    (
      currentFilters: IPracticeExamFilters,
      page: number,
      size: number
    ): GetExamListDto => {
      const params: GetExamListDto = {
        skipCount: (page - 1) * size,
        maxResultCount: size,
        filter: currentFilters.searchQuery || undefined,
        // For backward compatibility with single value APIs, use the first selected item
        subjectId:
          currentFilters.subjects && currentFilters.subjects.length > 0
            ? currentFilters.subjects[0]
            : undefined,
        gradeId:
          currentFilters.grades && currentFilters.grades.length > 0
            ? currentFilters.grades[0]
            : undefined,
      };

      // Status filtering is now handled client-side with mapped data
      if (currentFilters.statuses && currentFilters.statuses.length > 0) {
        console.log(
          'Status filters applied client-side:',
          currentFilters.statuses
        );
      }

      return params;
    },
    []
  );

  // Debounced search to prevent excessive API calls
  const debouncedSearch = useMemo(
    () =>
      debounce((searchValue: string) => {
        const newFilters = { ...filters, searchQuery: searchValue };
        setFilters(newFilters);

        // Update Redux state and trigger API call
        const apiParams = buildApiParams(newFilters, 1, pageSize);
        updateSearchParams({
          ...apiParams,
          subjectIds: newFilters.subjects,
          gradeIds: newFilters.grades,
        });
        updatePagination({ page: 1 });
        fetchExams(apiParams);
      }, 300),
    [
      filters,
      pageSize,
      buildApiParams,
      updateSearchParams,
      updatePagination,
      fetchExams,
    ]
  );

  const handleSearch = useCallback(
    (value: string) => {
      debouncedSearch(value);
    },
    [debouncedSearch]
  );

  // Load exams on component mount
  useEffect(() => {
    const apiParams = buildApiParams(filters, 1, pageSize);
    fetchExams(apiParams);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Only run on mount

  // Cleanup debounced function on unmount
  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  // Memoized event handlers
  const handleStartExam = useCallback(
    (exam: ExamBase) => {
      if (onPreviewExam) {
        onPreviewExam(exam);
      }
    },
    [onPreviewExam]
  );

  const handleContinueExam = useCallback(
    (exam: ExamBase) => {
      if (onPreviewExam) {
        onPreviewExam(exam);
      }
    },
    [onPreviewExam]
  );

  const handleReviewExam = useCallback(
    (exam: ExamBase) => {
      if (onPreviewExam) {
        onPreviewExam(exam);
      }
    },
    [onPreviewExam]
  );

  const handleFilterChange = useCallback(
    (filterType: keyof IPracticeExamFilters, values: any) => {
      const newFilters = {
        ...filters,
        [filterType]: values,
      };

      setFilters(newFilters);

      // For status filters, no need to call API since we handle it client-side
      if (filterType === 'statuses') {
        // Status filtering is handled by filteredExams memo
        return;
      }

      // Reset to page 1 when filtering (except for status)
      const apiParams = buildApiParams(newFilters, 1, pageSize);
      updateSearchParams({
        ...apiParams,
        subjectIds: newFilters.subjects,
        gradeIds: newFilters.grades,
      });
      updatePagination({ page: 1 });
      fetchExams(apiParams);
    },
    [
      filters,
      pageSize,
      buildApiParams,
      updateSearchParams,
      updatePagination,
      fetchExams,
    ]
  );

  const handlePageChange = useCallback(
    (page: number, newPageSize?: number) => {
      const size = newPageSize || pageSize;
      const apiParams = buildApiParams(filters, page, size);

      updatePagination({ page, pageSize: size });
      fetchExams(apiParams);
    },
    [filters, pageSize, buildApiParams, updatePagination, fetchExams]
  );

  // Memoized exam grid rendering with mapped data
  const renderExamGrid = useMemo(() => {
    if (filteredExams.length === 0) {
      const hasStatusFilter = filters.statuses && filters.statuses.length > 0;
      const hasAnyFilter =
        hasStatusFilter ||
        (filters.subjects && filters.subjects.length > 0) ||
        (filters.grades && filters.grades.length > 0) ||
        (filters.searchQuery && filters.searchQuery.trim());

      return (
        <Empty
          description={
            hasAnyFilter
              ? 'Không tìm thấy đề thi với bộ lọc đã chọn'
              : 'Không tìm thấy đề thi nào'
          }
          className="tailwind-my-16"
        />
      );
    }

    return (
      <Row gutter={[24, 24]} className="tailwind-mb-8">
        {filteredExams.map((exam) => (
          <Col
            key={exam.id || exam.clientId}
            xs={24}
            sm={12}
            md={8}
            lg={6}
            xl={6}
          >
            <ExamCard
              examData={exam}
              subject={
                exam.subject
                  ? {
                      id: exam.subject.id,
                      name: exam.subject.name,
                      code: exam.subject.code,
                    }
                  : undefined
              }
              grade={
                exam.grade
                  ? {
                      id: exam.grade.id,
                      name: exam.grade.name,
                      code: exam.grade.code,
                    }
                  : undefined
              }
              onStartExam={handleStartExam}
              onContinueExam={handleContinueExam}
              onReviewExam={handleReviewExam}
            />
          </Col>
        ))}
      </Row>
    );
  }, [
    filteredExams,
    filters.statuses,
    filters.subjects,
    filters.grades,
    filters.searchQuery,
    handleStartExam,
    handleContinueExam,
    handleReviewExam,
  ]);

  return (
    <div className="practice-exam-page tailwind-min-h-screen">
      <div className="practice-exam-header tailwind-px-12 tailwind-py-4 tailwind-bg-white tailwind-border tailwind-border-gray-100">
        <Title level={3} className="tailwind-color-black">
          Luyện đề
        </Title>
      </div>
      <div
        className="practice-exam-content tailwind-px-12 tailwind-py-4 backdrop-opacity-3"
        style={{
          backgroundImage: `url(${practiceBackgroundImage})`,
        }}
      >
        <div className="practice-exam-filters tailwind-py-4">
          <ExamFilterSection
            filters={filters}
            filterMetadata={filterMetadata}
            onSearch={handleSearch}
            onFilterChange={handleFilterChange}
          />
        </div>
        {loading ? (
          <div className="tailwind-flex tailwind-justify-center tailwind-items-center tailwind-min-h-96">
            <Spin size="large" />
          </div>
        ) : (
          <>
            {/* Content */}
            <div className="tailwind-py-4">
              {/* Exam Grid - Memoized */}
              <div className="practice-exam-grid">{renderExamGrid}</div>

              {/* Pagination */}
              <div className="tailwind-flex tailwind-justify-center tailwind-mt-8">
                <div className="practice-exam-pagination">
                  <Pagination
                    current={currentPage}
                    total={totalCount}
                    pageSize={pageSize}
                    onChange={handlePageChange}
                    size="default"
                  />
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

// Redux connection
const mapStateToProps = ({
  practiceExam,
}: {
  practiceExam: IPracticeExamState;
}): IPracticeExamStateToProps => {
  return {
    exams: practiceExam.exams,
    totalCount: practiceExam.totalCount,
    currentPage: practiceExam.currentPage,
    pageSize: practiceExam.pageSize,
    loading: practiceExam.loading,
    filter: practiceExam.filter,
    subjectId: practiceExam.subjectId,
    gradeId: practiceExam.gradeId,
    subjectIds: practiceExam.subjectIds,
    gradeIds: practiceExam.gradeIds,
  };
};

const mapDispatchToProps = (dispatch: any): IPracticeExamDispatchToProps => {
  return {
    fetchExams: (params) => dispatch(fetchPracticeExams(params)),
    updateSearchParams: (params) =>
      dispatch(updatePracticeExamSearchParams(params)),
    updatePagination: (data) => dispatch(updatePracticeExamPagination(data)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(PracticeExamComponent);
