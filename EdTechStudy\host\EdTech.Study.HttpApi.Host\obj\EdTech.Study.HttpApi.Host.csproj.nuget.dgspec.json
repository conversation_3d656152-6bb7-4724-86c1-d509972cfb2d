{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\host\\EdTech.Study.HttpApi.Host\\EdTech.Study.HttpApi.Host.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\host\\EdTech.Study.Host.Shared\\EdTech.Study.Host.Shared.csproj": {"version": "0.1.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\host\\EdTech.Study.Host.Shared\\EdTech.Study.Host.Shared.csproj", "projectName": "EdTech.Study.Host.Shared", "projectPath": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\host\\EdTech.Study.Host.Shared\\EdTech.Study.Host.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\host\\EdTech.Study.Host.Shared\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0", "netstandard2.0", "netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.abp.io/22f283ef-73fc-4811-be2f-f87b17465859/v3/index.json": {}, "https://pkgs.dev.azure.com/bpn25106/_packaging/modules/nuget/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}, "netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {}}, "netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"ConfigureAwait.Fody": {"suppressParent": "All", "target": "Package", "version": "[3.3.1, )"}, "Fody": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[6.5.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100/PortableRuntimeIdentifierGraph.json"}, "netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"ConfigureAwait.Fody": {"suppressParent": "All", "target": "Package", "version": "[3.3.1, )"}, "Fody": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[6.5.3, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}, "netstandard2.1": {"targetAlias": "netstandard2.1", "dependencies": {"ConfigureAwait.Fody": {"suppressParent": "All", "target": "Package", "version": "[3.3.1, )"}, "Fody": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[6.5.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"NETStandard.Library": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\host\\EdTech.Study.HttpApi.Host\\EdTech.Study.HttpApi.Host.csproj": {"version": "0.1.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\host\\EdTech.Study.HttpApi.Host\\EdTech.Study.HttpApi.Host.csproj", "projectName": "EdTech.Study.HttpApi.Host", "projectPath": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\host\\EdTech.Study.HttpApi.Host\\EdTech.Study.HttpApi.Host.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\host\\EdTech.Study.HttpApi.Host\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.abp.io/22f283ef-73fc-4811-be2f-f87b17465859/v3/index.json": {}, "https://pkgs.dev.azure.com/bpn25106/_packaging/modules/nuget/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\host\\EdTech.Study.Host.Shared\\EdTech.Study.Host.Shared.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\host\\EdTech.Study.Host.Shared\\EdTech.Study.Host.Shared.csproj"}, "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Application\\EdTech.Study.Application.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Application\\EdTech.Study.Application.csproj"}, "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.EntityFrameworkCore\\EdTech.Study.EntityFrameworkCore.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.EntityFrameworkCore\\EdTech.Study.EntityFrameworkCore.csproj"}, "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.HttpApi\\EdTech.Study.HttpApi.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.HttpApi\\EdTech.Study.HttpApi.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"ConfigureAwait.Fody": {"suppressParent": "All", "target": "Package", "version": "[3.3.1, )"}, "Fody": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[6.5.3, )"}, "IdentityModel": {"target": "Package", "version": "[6.2.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.AspNetCore.DataProtection.StackExchangeRedis": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Sinks.Async": {"target": "Package", "version": "[1.5.0, )"}, "Volo.Abp.AspNetCore.Mvc.UI.MultiTenancy": {"target": "Package", "version": "[8.1.4, )"}, "Volo.Abp.AspNetCore.Serilog": {"target": "Package", "version": "[8.1.4, )"}, "Volo.Abp.AuditLogging.EntityFrameworkCore": {"target": "Package", "version": "[8.1.4, )"}, "Volo.Abp.Autofac": {"target": "Package", "version": "[8.1.4, )"}, "Volo.Abp.Caching.StackExchangeRedis": {"target": "Package", "version": "[8.1.4, )"}, "Volo.Abp.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.1.4, )"}, "Volo.Abp.PermissionManagement.EntityFrameworkCore": {"target": "Package", "version": "[8.1.4, )"}, "Volo.Abp.SettingManagement.EntityFrameworkCore": {"target": "Package", "version": "[8.1.4, )"}, "Volo.Abp.Swashbuckle": {"target": "Package", "version": "[8.1.4, )"}, "Volo.Abp.TenantManagement.EntityFrameworkCore": {"target": "Package", "version": "[8.1.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Application.Contracts\\EdTech.Study.Application.Contracts.csproj": {"version": "0.1.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Application.Contracts\\EdTech.Study.Application.Contracts.csproj", "projectName": "EdTech.Study.Application.Contracts", "projectPath": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Application.Contracts\\EdTech.Study.Application.Contracts.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Application.Contracts\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.abp.io/22f283ef-73fc-4811-be2f-f87b17465859/v3/index.json": {}, "https://pkgs.dev.azure.com/bpn25106/_packaging/modules/nuget/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Domain.Shared\\EdTech.Study.Domain.Shared.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Domain.Shared\\EdTech.Study.Domain.Shared.csproj"}, "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Domain\\EdTech.Study.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Domain\\EdTech.Study.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"ConfigureAwait.Fody": {"suppressParent": "All", "target": "Package", "version": "[3.3.1, )"}, "Fody": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[6.5.3, )"}, "Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared": {"target": "Package", "version": "[8.1.4, )"}, "Volo.Abp.Authorization": {"target": "Package", "version": "[8.1.4, )"}, "Volo.Abp.Ddd.Application.Contracts": {"target": "Package", "version": "[8.1.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Application\\EdTech.Study.Application.csproj": {"version": "0.1.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Application\\EdTech.Study.Application.csproj", "projectName": "EdTech.Study.Application", "projectPath": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Application\\EdTech.Study.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.abp.io/22f283ef-73fc-4811-be2f-f87b17465859/v3/index.json": {}, "https://pkgs.dev.azure.com/bpn25106/_packaging/modules/nuget/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Application.Contracts\\EdTech.Study.Application.Contracts.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Application.Contracts\\EdTech.Study.Application.Contracts.csproj"}, "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Domain\\EdTech.Study.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Domain\\EdTech.Study.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"ConfigureAwait.Fody": {"suppressParent": "All", "target": "Package", "version": "[3.3.1, )"}, "Fody": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[6.5.3, )"}, "Volo.Abp.AutoMapper": {"target": "Package", "version": "[8.1.4, )"}, "Volo.Abp.BackgroundJobs": {"target": "Package", "version": "[8.1.4, )"}, "Volo.Abp.Ddd.Application": {"target": "Package", "version": "[8.1.4, )"}, "Volo.Abp.VirtualFileSystem": {"target": "Package", "version": "[8.1.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Domain.Shared\\EdTech.Study.Domain.Shared.csproj": {"version": "0.1.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Domain.Shared\\EdTech.Study.Domain.Shared.csproj", "projectName": "EdTech.Study.Domain.Shared", "projectPath": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Domain.Shared\\EdTech.Study.Domain.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Domain.Shared\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0", "netstandard2.0", "netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.abp.io/22f283ef-73fc-4811-be2f-f87b17465859/v3/index.json": {}, "https://pkgs.dev.azure.com/bpn25106/_packaging/modules/nuget/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}, "netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {}}, "netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"ConfigureAwait.Fody": {"suppressParent": "All", "target": "Package", "version": "[3.3.1, )"}, "Fody": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[6.5.3, )"}, "Microsoft.Extensions.FileProviders.Embedded": {"target": "Package", "version": "[8.0.0, )"}, "Volo.Abp.Ddd.Domain.Shared": {"target": "Package", "version": "[8.1.4, )"}, "Volo.Abp.Validation": {"target": "Package", "version": "[8.1.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100/PortableRuntimeIdentifierGraph.json"}, "netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"ConfigureAwait.Fody": {"suppressParent": "All", "target": "Package", "version": "[3.3.1, )"}, "Fody": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[6.5.3, )"}, "Microsoft.Extensions.FileProviders.Embedded": {"target": "Package", "version": "[8.0.0, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "Volo.Abp.Ddd.Domain.Shared": {"target": "Package", "version": "[8.1.4, )"}, "Volo.Abp.Validation": {"target": "Package", "version": "[8.1.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}, "netstandard2.1": {"targetAlias": "netstandard2.1", "dependencies": {"ConfigureAwait.Fody": {"suppressParent": "All", "target": "Package", "version": "[3.3.1, )"}, "Fody": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[6.5.3, )"}, "Microsoft.Extensions.FileProviders.Embedded": {"target": "Package", "version": "[8.0.0, )"}, "Volo.Abp.Ddd.Domain.Shared": {"target": "Package", "version": "[8.1.4, )"}, "Volo.Abp.Validation": {"target": "Package", "version": "[8.1.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"NETStandard.Library": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Domain\\EdTech.Study.Domain.csproj": {"version": "0.1.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Domain\\EdTech.Study.Domain.csproj", "projectName": "EdTech.Study.Domain", "projectPath": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Domain\\EdTech.Study.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Domain\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0", "netstandard2.0", "netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.abp.io/22f283ef-73fc-4811-be2f-f87b17465859/v3/index.json": {}, "https://pkgs.dev.azure.com/bpn25106/_packaging/modules/nuget/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Domain.Shared\\EdTech.Study.Domain.Shared.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Domain.Shared\\EdTech.Study.Domain.Shared.csproj"}}}, "netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {"C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Domain.Shared\\EdTech.Study.Domain.Shared.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Domain.Shared\\EdTech.Study.Domain.Shared.csproj"}}}, "netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Domain.Shared\\EdTech.Study.Domain.Shared.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Domain.Shared\\EdTech.Study.Domain.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"ConfigureAwait.Fody": {"suppressParent": "All", "target": "Package", "version": "[3.3.1, )"}, "Fody": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[6.5.3, )"}, "Volo.Abp.BlobStoring": {"target": "Package", "version": "[8.1.4, )"}, "Volo.Abp.Ddd.Domain": {"target": "Package", "version": "[8.1.4, )"}, "Volo.Abp.Identity.Domain": {"target": "Package", "version": "[8.1.4, )"}, "Volo.Abp.VirtualFileSystem": {"target": "Package", "version": "[8.1.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100/PortableRuntimeIdentifierGraph.json"}, "netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"ConfigureAwait.Fody": {"suppressParent": "All", "target": "Package", "version": "[3.3.1, )"}, "Fody": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[6.5.3, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "Volo.Abp.BlobStoring": {"target": "Package", "version": "[8.1.4, )"}, "Volo.Abp.Ddd.Domain": {"target": "Package", "version": "[8.1.4, )"}, "Volo.Abp.VirtualFileSystem": {"target": "Package", "version": "[8.1.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}, "netstandard2.1": {"targetAlias": "netstandard2.1", "dependencies": {"ConfigureAwait.Fody": {"suppressParent": "All", "target": "Package", "version": "[3.3.1, )"}, "Fody": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[6.5.3, )"}, "Volo.Abp.BlobStoring": {"target": "Package", "version": "[8.1.4, )"}, "Volo.Abp.Ddd.Domain": {"target": "Package", "version": "[8.1.4, )"}, "Volo.Abp.Identity.Domain": {"target": "Package", "version": "[8.1.4, )"}, "Volo.Abp.VirtualFileSystem": {"target": "Package", "version": "[8.1.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"NETStandard.Library": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.EntityFrameworkCore\\EdTech.Study.EntityFrameworkCore.csproj": {"version": "0.1.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.EntityFrameworkCore\\EdTech.Study.EntityFrameworkCore.csproj", "projectName": "EdTech.Study.EntityFrameworkCore", "projectPath": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.EntityFrameworkCore\\EdTech.Study.EntityFrameworkCore.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.EntityFrameworkCore\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.abp.io/22f283ef-73fc-4811-be2f-f87b17465859/v3/index.json": {}, "https://pkgs.dev.azure.com/bpn25106/_packaging/modules/nuget/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Domain\\EdTech.Study.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Domain\\EdTech.Study.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"ConfigureAwait.Fody": {"suppressParent": "All", "target": "Package", "version": "[3.3.1, )"}, "Fody": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[6.5.3, )"}, "Tsp.Core.EF": {"target": "Package", "version": "[1.0.19, )"}, "Volo.Abp.EntityFrameworkCore": {"target": "Package", "version": "[8.1.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.HttpApi\\EdTech.Study.HttpApi.csproj": {"version": "0.1.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.HttpApi\\EdTech.Study.HttpApi.csproj", "projectName": "EdTech.Study.HttpApi", "projectPath": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.HttpApi\\EdTech.Study.HttpApi.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.HttpApi\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.abp.io/22f283ef-73fc-4811-be2f-f87b17465859/v3/index.json": {}, "https://pkgs.dev.azure.com/bpn25106/_packaging/modules/nuget/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Application.Contracts\\EdTech.Study.Application.Contracts.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Application.Contracts\\EdTech.Study.Application.Contracts.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"ConfigureAwait.Fody": {"suppressParent": "All", "target": "Package", "version": "[3.3.1, )"}, "Fody": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[6.5.3, )"}, "Microsoft.AspNetCore.OData": {"target": "Package", "version": "[8.2.5, )"}, "Volo.Abp.AspNetCore.Mvc": {"target": "Package", "version": "[8.1.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100/PortableRuntimeIdentifierGraph.json"}}}}}