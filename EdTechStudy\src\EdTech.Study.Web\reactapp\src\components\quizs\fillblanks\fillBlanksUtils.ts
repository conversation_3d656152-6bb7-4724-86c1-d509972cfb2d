import {
  BaseAnswer,
  BaseQuestion,
} from '../../../interfaces/quizs/questionBase';
import {
  FillBlanksQuestion,
  FillBlanksAnswer,
} from '../../../interfaces/quizs/fillblanks.interfaces';
/**
 * Check if the user's answers for fill blanks question are correct
 * @param question
 * @param userSelect
 * @returns
 */
export const fillBlanksCheckCorrectAnswer = (
  question: BaseQuestion,
  userSelect?: BaseAnswer | BaseAnswer[]
) => {
  if (!userSelect || Array.isArray(userSelect)) return null;

  const fillBlanksQuestion = question as FillBlanksQuestion;
  const userAnswers = userSelect as FillBlanksAnswer;

  // If there are no blanks or user answers, return null
  if (!fillBlanksQuestion.blanks || !userAnswers.answers) return null;

  // Check if all blanks have been answered
  const allAnswered = fillBlanksQuestion.blanks.every(
    (blank) =>
      userAnswers.answers[blank.clientId] &&
      userAnswers.answers[blank.clientId].trim() !== ''
  );

  if (!allAnswered) return null;

  // Check if all answers are correct
  const allCorrect = fillBlanksQuestion.blanks.every((blank) => {
    const userAnswer = userAnswers.answers[blank.clientId];
    const correctAnswer = blank.correctAnswer;
    const alternativeAnswers = blank.alternativeAnswers || [];

    // Check if case sensitive is enabled
    if (fillBlanksQuestion.caseSensitive) {
      return (
        userAnswer === correctAnswer ||
        alternativeAnswers.some((alt) => alt === userAnswer)
      );
    } else {
      return (
        userAnswer.toLowerCase() === correctAnswer.toLowerCase() ||
        alternativeAnswers.some(
          (alt) => alt.toLowerCase() === userAnswer.toLowerCase()
        )
      );
    }
  });

  return allCorrect;
};
