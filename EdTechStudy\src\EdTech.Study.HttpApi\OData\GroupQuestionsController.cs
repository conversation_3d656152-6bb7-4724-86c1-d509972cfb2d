﻿using EdTech.Study.GroupQuestions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace EdTech.Study.OData
{
    public class GroupQuestionsController : BaseOdataEntityController<GroupQuestionEntity>
    {
        public GroupQuestionsController(IRepository<GroupQuestionEntity> repository) : base(repository)
        {
        }
    }
}
