import { Button } from 'antd';
import { memo } from 'react';

const VisibilityDebugPanel = memo(
  ({
    visible,
    onForceCheck,
  }: {
    visible: number[];
    onForceCheck: () => void;
  }) => (
    <div
      style={{
        position: 'fixed',
        top: 0,
        right: 0,
        background: 'rgba(0,0,0,0.7)',
        color: 'white',
        padding: '10px',
        zIndex: 9999,
        fontSize: '12px',
        maxHeight: '300px',
        overflowY: 'auto',
        maxWidth: '200px',
      }}
    >
      <div style={{ marginBottom: '8px' }}>
        <strong>Visible Items: {visible.length}</strong>
        <Button
          onClick={onForceCheck}
          size="small"
          style={{ marginLeft: '10px', fontSize: '10px', padding: '0 5px' }}
        >
          Check
        </Button>
      </div>
      <div style={{ wordBreak: 'break-all' }}>
        {visible.length > 0 ? visible.join(', ') : 'None detected'}
      </div>
    </div>
  )
);

export default VisibilityDebugPanel;
