import {
  BaseAnswer,
  BaseQuestion,
} from '../../../interfaces/quizs/questionBase';
import { MultiSelectQuestion } from '../../../interfaces/quizs/multiSelectQuiz.interface';
/**
 * Check if the user's answers for multi-select question are correct
 * @param question
 * @param userSelect
 * @returns
 */
export const multiSelectCheckCorrectAnswer = (
  question: BaseQuestion,
  userSelect?: BaseAnswer | BaseAnswer[]
) => {
  if (!userSelect) return null;

  const multiSelectQuestion = question as MultiSelectQuestion;

  // If no answers in the question, return null
  if (!multiSelectQuestion.options || multiSelectQuestion.options.length === 0)
    return null;

  // Get all correct answer IDs from the question
  const correctAnswerIds = multiSelectQuestion.options
    .filter((answer) => answer.isCorrect)
    .map((answer) => answer.id);

  // If no correct answers defined, return null
  if (correctAnswerIds.length === 0) return null;

  // For multi-select, userSelect should be an array
  if (!Array.isArray(userSelect)) return false;

  // Get user selected answer IDs
  const userSelectedIds = userSelect.map((answer) => answer.id);

  // If user didn't select any answers, return null
  if (userSelectedIds.length === 0) return null;

  // Check if user selected all correct answers and only correct answers
  const allCorrectSelected = correctAnswerIds.every((id) =>
    userSelectedIds.includes(id)
  );
  const onlyCorrectSelected = userSelectedIds.every((id) =>
    correctAnswerIds.includes(id)
  );

  return allCorrectSelected && onlyCorrectSelected;
};
