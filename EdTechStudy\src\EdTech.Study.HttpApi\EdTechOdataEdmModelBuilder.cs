﻿using EdTech.Study.Grade;
using EdTech.Study.GroupQuestions;
using EdTech.Study.Lessons.Dtos;
using EdTech.Study.Questions;
using EdTech.Study.Questions.Dtos;
using EdTech.Study.Subject;
using EdTech.Study.Users;
using Microsoft.OData.ModelBuilder;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection.Emit;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Identity;

namespace EdTech.Study
{
    public static class EdTechOdataEdmModelBuilder
    {
        public static ODataConventionModelBuilder AddEdTech(this ODataConventionModelBuilder builder)
        {
            var gradeSet = builder.EntitySet<LessonGrade>("LessonGrade");
            gradeSet.EntityType.HasKey(x => x.Id);
            var subjectSet = builder.EntitySet<Subject.Subject>("Subject");
            subjectSet.EntityType.HasKey(x => x.Id);
            var questionDraftOptionSet = builder.EntitySet<QuestionOptionDraftEntity>("QuestionOptionDrafts");
            questionDraftOptionSet.EntityType.HasKey(x => x.Id);
            var questionDraftSet = builder.EntitySet<QuestionDraftEntity>("QuestionDrafts");
            questionDraftSet.EntityType.HasKey(x => x.Id);

            var questionSet = builder.EntitySet<QuestionEntity>("Questions");
            questionSet.EntityType.HasKey(x => x.Id);
            var matchingItemSet = builder.EntitySet<MatchingItemEntity>("MatchingItems");
            matchingItemSet.EntityType.HasKey(x => x.Id);
            var matchingAnswerSet = builder.EntitySet<MatchingAnswerEntity>("MatchingAnswers");
            matchingAnswerSet.EntityType.HasKey(x => x.Id);
            var optionSet = builder.EntitySet<QuestionOptionEntity>("Options");
            optionSet.EntityType.HasKey(x => x.Id);
            var fillInBlankAnswerSet = builder.EntitySet<FillInBlankAnswerEntity>("FillInBlankAnswers");
            fillInBlankAnswerSet.EntityType.HasKey(x => x.Id);
            var groupQuestionSet = builder.EntitySet<GroupQuestionEntity>("GroupQuestions");
            groupQuestionSet.EntityType.HasKey(x => x.Id);

            var identitySet = builder.EntitySet<IdentityUserDto>("Users");
            identitySet.EntityType.HasKey(x => x.Id);

            // Configure navigation properties
            builder.EntityType<QuestionDraftEntity>()
                .HasMany(q => q.Options);
            builder.EntityType<QuestionDraftEntity>()
                .HasOptional(q => q.Subject);

            return builder;
        }
    }
}