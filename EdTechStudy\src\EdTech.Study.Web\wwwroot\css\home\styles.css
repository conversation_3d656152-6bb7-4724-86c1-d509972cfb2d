:root {
    --primary-color: #ea4c89;
    --primary-hover: #d43c78;
    --text-primary: #262223;
    --text-secondary: #908588;
    --bg-white: #ffffff;
    --border-color: #f1f0f1;
    --math-bg: #fcedf1;
    --literature-bg: #cef5fd;
    --language-bg: #ebeaff;
    --science-bg: #dfffef;
    --social-bg: #fff1f0;
    --stats-bg: #d7d4fb;
    --stats-text: #5e4cea;
    --section-padding: 2.5rem 1rem;
    --border-radius: 0.625rem;
    --card-gap: 1.5rem;
    --gradient-bg: linear-gradient(
        180deg,
        rgba(252, 237, 241, 1) 0%,
        rgba(255, 255, 255, 1) 100%
    );
}

/* Reset & Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Roboto', sans-serif;
}

body {
    background-color: var(--bg-white);
}

/* Common Flex Layouts */
.header-content,
.logo,
.nav-menu,
.auth-section,
.hero,
.subjects-row,
.course-tags,
.course-stats,
.stat,
.stat-item,
.stat-content,
.stat-icon,
.arrow-button,
.social-widget,
.testimonial-grid,
.testimonial-card,
.exam-card,
.exam-card-header,
.exam-subject-tag,
.exam-question-count,
.exam-progress-info {
    display: flex;
    align-items: center;
}

.header-content,
.nav-item,
.tag,
.cta-button,
.course-button,
.exam-button {
    display: flex;
    align-items: center;
}

.header-content,
.statistics .container,
.exam-progress-info {
    justify-content: space-between;
}

.hero,
.subjects-row,
.courses-grid,
.articles-grid,
.testimonial-grid,
.course-tags,
.course-stats,
.cta-button,
.course-button,
.stat-icon,
.arrow-button {
    justify-content: center;
}

.hero,
.stat-content,
.social-widget,
.testimonial-card,
.exam-card,
.exam-progress-section {
    flex-direction: column;
}

/* Common Text Styles */
.subjects,
.courses,
.articles,
.testimonial,
.exams,
.hero {
    text-align: center;
}

.subjects h2,
.courses h2,
.articles h2,
.testimonial h2,
.exams h2 {
    color: var(--primary-color);
    font-size: 1.875rem;
    margin-bottom: 1.5rem;
}

.subjects p,
.courses p,
.articles p,
.testimonial p {
    max-width: 52.875rem;
    margin: 0 auto 2.5rem;
    color: var(--text-secondary);
}

/* Common Section Padding */
.subjects,
.courses,
.articles,
.testimonial,
.exams,
.statistics {
    padding: var(--section-padding);
}

/* Common Background */
.hero,
.subjects {
    background: var(--gradient-bg);
}

/* Common Button Styles */
.cta-button,
.course-button,
.exam-button {
    gap: 0.5rem;
    background-color: var(--primary-color);
    color: var(--bg-white);
    border: none;
    cursor: pointer;
    transition: background-color 0.3s;
}

.cta-button:hover,
.course-button:hover,
.exam-button:hover {
    background-color: var(--primary-hover);
}

/* Common Card Styles */
.course-card,
.article-card,
.exam-card {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--bg-white);
}

/* Common Grid Layouts */
.courses-grid,
.articles-grid,
.testimonial-grid {
    gap: var(--card-gap);
    margin: 0 auto;
}

.separator-line {
    width: 18.875rem;
    height: 1px;
    background: url('/line-8.svg') no-repeat center;
    margin: 1.5rem auto;
}

/* Header */
.header {
    position: sticky;
    top: 0;
    background: var(--bg-white);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 5rem;
    z-index: 50;
}

.header-content {
    max-width: 1440px;
    margin: 0 auto;
}

.logo {
    gap: 0.75rem;
}

.logo-img {
    width: 2.5rem;
    height: 2.5rem;
}

.logo-text {
    font-family: 'Prosto One';
    color: var(--primary-color);
    font-size: 2rem;
    font-weight: 400;
}

.icon {
    font-size: 1.5rem;
    transform: translateY(3px);
}

.nav-menu {
    gap: 2rem;
}

.nav-item {
    gap: 0.5rem;
    padding: 0.5rem;
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.875rem;
    transition: color 0.3s;
}

.nav-item img {
    width: 1.5rem;
    height: 1.5rem;
}

.nav-item.active {
    color: var(--primary-color);
}

.auth-section {
    gap: 0.5rem;
}

.auth-img {
    width: 2rem;
    height: 2rem;
    object-fit: cover;
}

.auth-link {
    text-decoration: none;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.separator {
    width: 1px;
    height: 17px;
    background-color: var(--border-color);
}

/* Hero Section */
.hero {
    gap: 1.5rem;
    padding: 3rem 1rem;
}

.hero h1 {
    font-size: 2.375rem;
    line-height: 2.875rem;
    color: var(--bg-white);
    max-width: 56rem;
}

.hero p {
    font-size: 1.25rem;
    line-height: 1.75rem;
    color: var(--bg-white);
    max-width: 52.875rem;
}

.cta-button {
    padding: 0.75rem 1.5rem;
    border-radius: 9999px;
    font-weight: 500;
}

/* Subject Cards */
.subjects-grid {
    max-width: 66.375rem;
    margin: 0 auto;
}

.subjects-row {
    gap: var(--card-gap);
    margin-bottom: 2.5rem;
}

.subject-card {
    flex: 1;
    padding: 1rem;
    border-radius: var(--border-radius);
    text-align: center;
    position: relative;
}

.subject-card img {
    width: 4rem;
    height: 4rem;
    margin-bottom: 0.5rem;
}

.subject-card h4 {
    color: var(--text-primary);
    margin-bottom: 2.5rem;
}

.arrow-button {
    position: absolute;
    bottom: -1.25rem;
    left: 50%;
    transform: translateX(-50%);
    width: 2.5rem;
    height: 2.5rem;
    background-color: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
}
    .arrow-button i {
        color: var(--bg-white);
        display: inline-block;
        height: 1.25rem;
        font-size: 1.25rem;
        transform: translateY(-2px);
    }
/* Subject Background Colors */
.subject-card.math {
    background-color: var(--math-bg);
}

.subject-card.literature {
    background-color: var(--literature-bg);
}

.subject-card.language {
    background-color: var(--language-bg);
}

.subject-card.science {
    background-color: var(--science-bg);
}

.subject-card.social {
    background-color: var(--social-bg);
}

/* Course Cards */
.courses-grid {
    flex-wrap: wrap;
    max-width: 80rem;
}

.course-card {
    width: 18.875rem;
    padding: 1rem;
}

.course-image {
    width: 100%;
    height: 10rem;
    object-fit: cover;
    margin-bottom: 0.5rem;
}

.course-tags {
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.tag {
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.subject-tag {
    background-color: var(--science-bg);
    color: #3cbe8c;
}

.grade-tag {
    background-color: var(--border-color);
    color: var(--text-primary);
}

.course-card h3 {
    font-size: 0.875rem;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    height: 2.75rem;
    overflow: hidden;
}

.course-stats {
    gap: 1rem;
    margin-bottom: 1rem;
}

.stat {
    gap: 0.25rem;
    font-size: 0.75rem;
    color: var(--text-primary);
}

.stat img {
    width: 1rem;
    height: 1rem;
}

.course-button {
    width: 100%;
    padding: 0.5rem;
    border-radius: 0.25rem;
}

/* Statistics Section */
.statistics {
    background-color: var(--stats-bg);
}

.statistics .container {
    max-width: 72rem;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 2rem;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
    flex: 1;
    min-width: 12rem;
}

.stat-icon {
    background-color: var(--bg-white);
    border-radius: 50%;
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stat-icon img {
    width: 4rem;
    height: 4rem;
    object-fit: cover;
}

.stat-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.stat-number,
.stat-label {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    color: var(--stats-text);
}

.stat-number {
    font-size: 3rem;
}

.stat-label {
    font-size: 1.5rem;
}

/* Exams Section */
.exams {
    padding: var(--section-padding);
    text-align: center;
    background-color: #f9fafb;
}

.exams h2 {
    color: var(--primary-color);
    font-size: 1.875rem;
    margin-bottom: 1.5rem;
}

.exams-grid {
    max-width: 80rem;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(18rem, 1fr));
    gap: var(--card-gap);
    padding: 2rem;
}

.exam-card {
    background-color: var(--bg-white);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.exam-card-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.exam-subject-tag {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
}

.exam-subject-tag.khtn {
    background-color: #ecfdf5;
    color: #059669;
}

.exam-subject-tag.other {
    background-color: #fdf2f8;
    color: #ec4899;
}

.exam-grade {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.exam-title {
    font-weight: 500;
    color: #111827;
    margin: 0;
}

.exam-question-count {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.exam-progress-section {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.exam-progress-info {
    display: flex;
    justify-content: space-between;
    font-size: 0.875rem;
}

.exam-progress-text {
    color: var(--text-secondary);
}

.exam-status {
    color: #ef4444;
}

.exam-progress-bar {
    height: 0.25rem;
    background-color: #e5e7eb;
    border-radius: 9999px;
    overflow: hidden;
}

.exam-progress-fill {
    height: 100%;
    background-color: var(--primary-color);
    border-radius: 9999px;
    transition: width 0.3s ease;
}

.exam-button {
    width: 100%;
    background-color: var(--primary-color);
    color: var(--bg-white);
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: background-color 0.3s;
}

.exam-button:hover {
    background-color: var(--primary-hover);
}

/* Testimonial Section - Unified Styles */
.testimonial,
.student-feedback {
    padding: var(--section-padding);
    text-align: center;
}

.testimonial h2,
.student-feedback h2 {
    color: var(--primary-color);
    font-size: 1.875rem;
    margin-bottom: 1.5rem;
}

.testimonial p,
.student-feedback p {
    max-width: 52.875rem;
    margin: 0 auto 2.5rem;
    color: var(--text-secondary);
}

/* Custom Testimonial Carousel */
.testimonial-carousel-container {
    position: relative;
    overflow: hidden;
    padding: 0 60px;
}

.testimonial-carousel {
    display: flex;
    transition: transform 0.5s ease;
}

.testimonial-item {
    flex: 0 0 33.333%;
    padding: 0 15px;
}

.testimonial-card {
    background: var(--bg-white);
    border-radius: var(--border-radius);
    padding: 2rem 1.5rem;
    text-align: center;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.testimonial-avatar {
    width: 5rem;
    height: 5rem;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 1.25rem;
    border: 3px solid var(--border-color);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.testimonial-name {
    color: var(--primary-color);
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.testimonial-text {
    color: #374151;
    line-height: 1.6;
    font-size: 0.875rem;
}

/* Student Info for Bootstrap Carousel */
.student-info {
    display: flex;
    align-items: center;
    margin-top: 1.5rem;
}

.student-image {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 15px;
    border: 3px solid var(--border-color);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.student-name {
    color: var(--primary-color);
    margin-bottom: 0;
    font-weight: 600;
    font-size: 1.1rem;
}

.student-class {
    margin-bottom: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Articles */
.articles-grid {
    max-width: 80rem;
    display: flex;
    flex-wrap: wrap;
}

.article-card {
    flex: 1;
    min-width: 18rem;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
}

.article-card img {
    width: 100%;
    height: 13.25rem;
    object-fit: cover;
    margin-bottom: 0.5rem;
    flex-shrink: 0;
}

.article-card h3 {
    font-size: 1rem;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    flex-shrink: 0;
}

.article-card p {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0;
    text-align: left;
    flex-grow: 1;
}

/* Custom Testimonial Arrow Buttons */
.testimonial-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: var(--bg-white);
    border: 2px solid var(--border-color);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    transition: all 0.3s ease;
    z-index: 10;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    cursor: pointer;
}

.testimonial-arrow:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--bg-white);
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 4px 20px rgba(220, 53, 69, 0.3);
}

.testimonial-arrow:active {
    transform: translateY(-50%) scale(0.95);
}

.testimonial-arrow:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.testimonial-arrow:disabled:hover {
    background: var(--bg-white);
    border-color: var(--border-color);
    color: var(--text-secondary);
    transform: translateY(-50%);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.testimonial-arrow-left {
    left: 10px;
}

.testimonial-arrow-right {
    right: 10px;
}

/* Bootstrap Carousel Controls */
.carousel-control-prev,
.carousel-control-next {
    width: 60px;
    height: 60px;
    background-color: var(--primary-color);
    border-radius: 50%;
    top: 50%;
    transform: translateY(-50%);
    opacity: 1;
}

.carousel-control-prev {
    left: -10px;
}

.carousel-control-next {
    right: -10px;
}

.carousel-arrow {
    color: var(--bg-white);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Carousel Animation */
.carousel-item {
    opacity: 0;
    transition: transform 0.5s ease-in-out, opacity 0.5s ease-in-out;
}

.carousel-item.active {
    opacity: 1;
}

/* Student Feedback Carousel - Using same structure as testimonial */
.student-feedback-carousel-container {
    position: relative;
    overflow: hidden;
    padding: 0 60px;
}

.student-feedback-carousel {
    display: flex;
    transition: transform 0.5s ease;
}

.student-feedback-item {
    flex: 0 0 25%;
    padding: 0 15px;
    box-sizing: border-box;
}

.student-feedback-card {
    background: var(--bg-white);
    border-radius: var(--border-radius);
    padding: 2rem 1.5rem;
    text-align: center;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    min-height: 300px;
}

.student-feedback-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.student-feedback-text {
    color: #374151;
    line-height: 1.6;
    font-size: 0.875rem;
}

/* Use exact same arrow styles as testimonial */
.student-feedback-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: var(--bg-white);
    border: 2px solid var(--border-color);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    transition: all 0.3s ease;
    z-index: 10;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    cursor: pointer;
}

.student-feedback-arrow:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--bg-white);
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 4px 20px rgba(220, 53, 69, 0.3);
}

.student-feedback-arrow:active {
    transform: translateY(-50%) scale(0.95);
}

.student-feedback-arrow:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.student-feedback-arrow:disabled:hover {
    background: var(--bg-white);
    border-color: var(--border-color);
    color: var(--text-secondary);
    transform: translateY(-50%);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.student-feedback-arrow-left {
    left: 10px;
}

.student-feedback-arrow-right {
    right: 10px;
}

/* Responsive Design for Student Feedback Carousel */
@media (max-width: 1200px) {
    .student-feedback-item {
        flex: 0 0 33.333%;
    }
}

@media (max-width: 768px) {
    .student-feedback-item {
        flex: 0 0 50%;
    }

    .student-feedback-carousel-container {
        padding: 0 40px;
    }
}

@media (max-width: 480px) {
    .student-feedback-item {
        flex: 0 0 100%;
    }

    .student-feedback-carousel-container {
        padding: 0 30px;
    }

    .student-feedback-arrow {
        width: 40px;
        height: 40px;
    }
}

/* Social Widget */
.social-widget {
    position: fixed;
    top: 524px;
    right: 0;
    gap: 1rem;
    padding: 0.75rem;
    background: var(--bg-white);
    border-radius: 10px 0 0 10px;
    box-shadow: 0 2px 40px rgba(0, 0, 0, 0.1);
    z-index: 10;
}

.social-widget img {
    width: 2rem;
    height: 2rem;
    cursor: pointer;
}
/* Footer Styles */
.footer {
    background-color: #f9f9f9;
    padding: 48px 0 0;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
        Helvetica, Arial, sans-serif;
}

.logo-container {
    background-color: #e94d89;
    border-radius: 8px;
    padding: 24px;
    color: white;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.logo {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 24px;
}

.logo h2 {
    margin-top: 16px;
    font-size: 28px;
    font-weight: 700;
    letter-spacing: 1px;
}

.social-icons {
    display: flex;
    justify-content: center;
    gap: 16px;
}

.social-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    transition: transform 0.3s ease, background-color 0.3s ease;
}

.social-icon:hover {
    background-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-3px);
}

.footer-heading {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 24px;
    color: #333;
    position: relative;
    padding-bottom: 8px;
}

.footer-heading::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 32px;
    height: 3px;
    background-color: #e94d89;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 12px;
}

.footer-links a {
    color: #555;
    text-decoration: none;
    transition: color 0.2s ease, padding-left 0.2s ease;
    display: inline-block;
}

.footer-links a:hover {
    color: #e94d89;
    padding-left: 4px;
}

.contact-info {
    list-style: none;
    padding: 0;
    margin: 0;
}

.contact-info li {
    margin-bottom: 16px;
    display: flex;
    flex-direction: column;
}

.contact-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.contact-text {
    color: #555;
    line-height: 1.5;
}

.footer-bottom {
    margin-top: 48px;
    padding: 16px 0;
    background-color: #f2f2f2;
    text-align: center;
    color: #666;
    font-size: 14px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .footer {
        padding: 32px 0 0;
    }

    .logo-container {
        margin-bottom: 24px;
    }

    .footer-heading {
        margin-top: 16px;
        margin-bottom: 16px;
    }
}
