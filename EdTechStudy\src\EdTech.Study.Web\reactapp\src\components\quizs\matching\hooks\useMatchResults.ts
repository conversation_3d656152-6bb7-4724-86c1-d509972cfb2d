import { useState, useCallback, useEffect } from 'react';
import { message } from 'antd';
import {
  MatchingItemType,
  MatchingQuestion,
  MatchingQuestionAnswer,
} from '../../../../interfaces/quizs/questionBase';
import { MatchResultsResult } from '../../../../interfaces/quizs/mapping.interface';

interface UseMatchResultsProps {
  id: string;
  question: MatchingQuestion;
  leftIds: string[];
  leftItems: MatchingItemType[];
  rightItems: MatchingItemType[];
  pairedItems: { [key: string]: { partnerId: string; color: string } };
  getPairs: () => { left: string; right: string }[];
  onComplete?: (correct: boolean) => void;
}

export const useMatchResults = ({
  id,
  question,
  leftIds,
  leftItems,
  rightItems,
  pairedItems,
  getPairs,
  onComplete,
}: UseMatchResultsProps): MatchResultsResult => {
  const [isChecking, setIsChecking] = useState(false);
  const [results, setResults] = useState<{ [key: string]: boolean }>({});
  const [completed, setCompleted] = useState(false);
  const [correctCount, setCorrectCount] = useState(0);

  // Check if all items are paired
  const areAllItemsPaired = useCallback(() => {
    // Count left items that have a pair
    const pairedLeftItems = leftItems.filter((item) =>
      Object.keys(pairedItems).includes(item.clientId)
    );

    // All items are paired if the number of paired left items equals total left items
    return pairedLeftItems.length === leftItems.length;
  }, [leftItems, pairedItems]);

  // Check answers based on paired items
  const checkAnswers = useCallback(() => {
    // If not all items are paired, show a message
    if (!areAllItemsPaired()) {
      message.warning('Vui lòng ghép tất cả các mục trước khi kiểm tra!');
      return;
    }

    setIsChecking(true);

    // Get current pairs
    const currentPairs = getPairs();
    const newResults: { [key: string]: boolean } = {};
    let correct = 0;

    // Check each pair against the correct matches from the question
    currentPairs.forEach((pair) => {
      const leftItem = leftItems.find((item) => item.clientId === pair.left);

      // Check if the pairing is correct
      const isCorrect = leftItem?.matchId === pair.right;

      // Store the result for both items in the pair
      newResults[`${pair.left}-${pair.right}`] = isCorrect;

      if (isCorrect) {
        correct++;
      }
    });

    setResults(newResults);
    setCorrectCount(correct);

    // Check if all matches are correct
    const allCorrect = correct === leftItems.length;

    if (allCorrect) {
      setCompleted(true);
      message.success('Tất cả các câu trả lời đều chính xác!');
    } else {
      message.warning(`Bạn đã ghép đúng ${correct}/${leftItems.length} cặp.`);
    }

    if (onComplete) {
      onComplete(allCorrect);
    }
  }, [leftItems, getPairs, pairedItems, areAllItemsPaired, onComplete]);

  // Get match result for an item
  const getMatchResult = useCallback(
    (id: string) => {
      if (!isChecking) return null;

      // Find the item's partner
      const partner = pairedItems[id]?.partnerId;

      if (!partner) return null;

      // Get the result for this pair
      return results[`${id}-${partner}`] || results[`${partner}-${id}`];
    },
    [isChecking, pairedItems, results]
  );

  // Reset results when items change
  useEffect(() => {
    if (isChecking) {
      setIsChecking(false);
      setResults({});
      setCompleted(false);
      setCorrectCount(0);
    }
  }, [leftItems, rightItems]);

  return {
    isChecking,
    results,
    completed,
    correctCount,
    checkAnswers,
    getMatchResult,
    setIsChecking,
    setCompleted,
  };
};
