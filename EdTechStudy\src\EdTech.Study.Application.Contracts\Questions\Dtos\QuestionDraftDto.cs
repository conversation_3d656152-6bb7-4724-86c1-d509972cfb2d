﻿using EdTech.Study.Enum;
using System;
using System.Collections.Generic;
using Volo.Abp.Application.Dtos;

namespace EdTech.Study.Questions.Dtos
{
    public class QuestionDraftDto : AuditedEntityDto<Guid>
    {
        public string Content { get; set; }
        public string ContentFormat { get; set; }
        public string Description { get; set; }
        public QuestionType Type { get; set; }
        public int DifficultyLevel { get; set; }
        /// <summary>
        /// Danh sách chủ đề(Cách nhau bởi đấu phẩy)
        /// </summary>
        public string? Topics { get; set; }

        /// <summary>
        /// Danh sách gắn nhãn (Cách nhau bởi đấu phẩy)
        /// </summary>
        public string? Tags { get; set; }
        public Guid? SubjectId { get; set; }
        public string SubjectName { get; set; }
        public IList<QuestionOptionDraftDto> Options { get; set; }
        public ExamStatus Status { get; set; }
        public string Comment { get; set; }
        public string Source { get; set; }
    }

    public class CreateQuestionDraftDto
    {
        public string Content { get; set; } = string.Empty;
        public string ContentFormat { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public QuestionType Type { get; set; } = QuestionType.SingleChoice;
        public int DifficultyLevel { get; set; } = 0;
        /// <summary>
        /// Danh sách chủ đề(Cách nhau bởi đấu phẩy)
        /// </summary>
        public string? Topics { get; set; }

        /// <summary>
        /// Danh sách gắn nhãn (Cách nhau bởi đấu phẩy)
        /// </summary>
        public string? Tags { get; set; }
        public Guid? SubjectId { get; set; }
        public IList<CreateUpdateQuestionOptionDraftDto> Options { get; set; } = [];
        public string Source { get; set; } = string.Empty;
    }

    public class UpdateQuestionDraftDto : CreateQuestionDraftDto
    {
        public string Comment { get; set; } = string.Empty;
    }

    public class UpdateQuestionDraftWithIdDto : UpdateQuestionDraftDto
    {
        public Guid Id { get; set; }
    }

    public class QuestionDraftPagedAndSortedResultRequestDto : PagedAndSortedResultRequestDto
    {
        public string Filter { get; set; }
        public QuestionType? Type { get; set; }
        public ExamStatus? Status { get; set; }
        public Guid? SubjectId { get; set; }
    }
}