﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Domain.Entities.Auditing;

namespace EdTech.Study.Exams
{
    /// <summary>
    /// Đại diện cho một phần thi trong đề thi.
    /// </summary>
    public class SectionEntity : AuditedEntity<Guid>
    {
        public SectionEntity()
        {

        }

        public SectionEntity(Guid id) : base(id)
        {

        }

        /// <summary>
        /// Liên kết đến đề thi cha.
        /// </summary>
        public Guid ExamId { get; set; }

        public ExamEntity Exam { get; set; }

        /// <summary>
        /// Tiêu đề của phần thi.
        /// </summary>
        [Required]
        [MaxLength(256)]
        public string Title { get; set; }

        /// <summary>
        /// Nội dung mô tả chi tiết của phần thi.
        /// </summary>
        public string? Content { get; set; }

        /// <summary>
        /// Định dạng nội dung (Text thuần hay HTML).
        /// </summary>
        public ContentFormatType? ContentFormat { get; set; }

        /// <summary>
        /// Thứ tự sắp xếp phần thi trong đề thi.
        /// </summary>
        public int OrderIndex { get; set; }

        /// <summary>
        /// Số điểm tối đa của phần thi này.
        /// </summary>
        public float? SectionScore { get; set; }

        /// <summary>
        /// Hướng dẫn làm bài cho phần thi này.
        /// </summary>
        public string? Instructions { get; set; }

        /// <summary>
        /// Phân loại nguồn gốc đề thi (Đề thi thường, Ngân hàng đề).
        /// </summary>
        public ExamSourceType SourceType { get; set; }

        /// <summary>
        /// Danh sách các câu hỏi liên kết với phần thi này.
        /// </summary>
        public IList<SectionQuestionEntity> Questions { get; set; }
        public IList<SectionGroupQuestionEntity> GroupQuestions { get; set; }

        /// <summary>
        /// Đảm bảo duy nhất mỗi request thêm
        /// </summary>
        [MaxLength(256)]
        public string? IdempotentKey { get; set; }

        /// <summary>
        /// Nguồn gốc
        /// </summary>
        public string? Source { get; set; }

    }
}