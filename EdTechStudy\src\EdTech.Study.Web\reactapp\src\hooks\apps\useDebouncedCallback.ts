import { debounce } from 'lodash';
import { useCallback } from 'react';

const useDebouncedCallback = (
  callback: (...values: any[]) => void,
  dependencies: any[]
) => {
  const delay = 500; // 500ms

  const debouncedCallback = useCallback(
    debounce((...values: any[]) => {
      callback(...values);
    }, delay),
    [...dependencies]
  );

  return debouncedCallback;
};

export default useDebouncedCallback;
