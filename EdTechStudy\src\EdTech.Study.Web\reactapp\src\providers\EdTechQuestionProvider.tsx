import { Suspense } from 'react';
import LoadingScreen from '../components/common/Loading/LoadingScreen';
import { IEdTechQuestionProviderProps } from './EdTechQuestionProvider.interfaces';
import QuestionRenderComponent from '../components/common/QuestionRenderComponent/QuestionRenderComponent';
import usePracticesLogic from '../components/quizs/practiceEngines/hooks/usePracticesLogic';
import QuestionProviderLayout from './Questions/QuestionProvider';

const EdTechQuestionProvider = (_: IEdTechQuestionProviderProps) => {
  usePracticesLogic({
    consumerId: 'edtech-question-provider',
  });
  return (
    <Suspense fallback={<LoadingScreen />}>
      <QuestionProviderLayout>
        <QuestionRenderComponent />
      </QuestionProviderLayout>
    </Suspense>
  );
};

export default EdTechQuestionProvider;
