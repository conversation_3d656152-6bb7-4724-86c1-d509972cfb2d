import { DragEndEvent, DragStartEvent } from '@dnd-kit/core';
import {
  MatchingItemType,
  MatchingQuestion,
  QuestionComponentBaseProps,
} from './questionBase';

// Base item properties
export interface ItemProps {
  item: MatchingItemType;
  isMatched?: boolean;
  isIncorrect?: boolean;
}

// Properties for the new clickable item
export interface ClickableItemProps extends ItemProps {
  id: string;
  isSelected: boolean;
  isPaired: boolean;
  pairColor?: string;
  onClick: (id: string) => void;
}

// Properties for matching controls
export interface MatchingControlsProps {
  onCheck: () => void;
  onReset: () => void;
  isCompleted: boolean;
  isChecking: boolean;
}

// Properties for feedback component
export interface MatchingFeedbackProps {
  isChecking: boolean;
  isCompleted: boolean;
  correctCount: number;
  totalCount: number;
  showFeedback: boolean;
}

// Result from useMatchingLogic hook
export interface MatchingLogicResult {
  leftItems: MatchingItemType[];
  rightItems: MatchingItemType[];
  leftIds: string[];
  rightIds: string[];
  connections: { [key: string]: string };
  activeId: string | null;
  activeItem: MatchingItemType | null;
  handleDragStart: (event: DragStartEvent) => void;
  handleDragEnd: (event: DragEndEvent) => void;
  resetExercise: () => void;
  // New properties for click-to-pair approach
  selectedItemId?: string | null;
  pairedItems: { [key: string]: { partnerId: string; color: string } };
  isItemPaired: (id: string) => boolean;
  getPairColor: (id: string) => string;
  handleItemClick: (id: string) => void;
  getPairs: () => { left: string; right: string }[];
}

// Result from useMatchResults hook
export interface MatchResultsResult {
  isChecking: boolean;
  results: { [key: string]: boolean };
  completed: boolean;
  correctCount: number;
  checkAnswers: () => void;
  getMatchResult: (id: string) => boolean | null;
  setIsChecking: (value: boolean) => void;
  setCompleted: (value: boolean) => void;
}

export interface MatchingComponentProps extends QuestionComponentBaseProps {
  question: MatchingQuestion;
  onComplete?: (questionId: string, userSelect: any) => void;
}
