using EdTech.Study.Enum;
using EdTech.Study.Exams;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;

namespace EdTech.Study.Questions.Dtos
{
    public class QuestionDto : AuditedEntityDto<Guid>
    {
        [Required]
        public string Title { get; set; }

        public string Content { get; set; }
        public ContentFormatType ContentFormat { get; set; }
        public QuestionType QuestionType { get; set; }
        public int Difficulty { get; set; }
        public ExamStatus Status { get; set; }
        public string Comment { get; set; }
        public Guid? SubjectId { get; set; }
        public Guid? GradeId { get; set; }
        public bool ShuffleOptions { get; set; }
        public string Explanation { get; set; }
        public ExamSourceType SourceType { get; set; }
        public string Topics { get; set; }
        public string Tags { get; set; }
        public List<QuestionOptionDto> Options { get; set; }
        public Guid? AssignedUserId { get; set; }
        public DateTime? AssignedDate { get; set; }
        public DateTime? DueDate { get; set; }
    }
}
