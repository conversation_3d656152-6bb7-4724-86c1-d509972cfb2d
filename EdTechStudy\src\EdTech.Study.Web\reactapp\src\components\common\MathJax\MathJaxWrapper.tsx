// MathJaxWrapper.js
import React, {
  useEffect,
  useRef,
  forwardRef,
  useImperativeHandle,
} from 'react';
import { MathJaxContext, MathJax } from 'better-react-mathjax';
import DOMPurify from 'dompurify';

export interface MathJaxProviderProps {
  children: React.ReactNode;
  config?: Record<string, any>;
}

// Tạo một wrapper component cho MathJaxContext để tránh findDOMNode warnings
interface MathJaxContextWrapperProps {
  children: React.ReactNode;
  config: Record<string, any>;
}

interface MathJaxContextWrapperRef {
  containerRef: React.RefObject<HTMLDivElement>;
}

const MathJaxContextWrapper = forwardRef<
  MathJaxContextWrapperRef,
  MathJaxContextWrapperProps
>((props, ref) => {
  const { children, config } = props;
  const containerRef = useRef<HTMLDivElement>(null);

  // Forward the ref to the parent component
  useImperativeHandle(ref, () => ({
    containerRef,
  }));

  return (
    <div ref={containerRef}>
      <MathJaxContext config={config}>{children}</MathJaxContext>
    </div>
  );
});

// Component bọc MathJaxContext để sử dụng ở mức ứng dụng
export const MathJaxProvider = (props: MathJaxProviderProps) => {
  const { children, config = {} } = props;
  const contextRef = useRef<MathJaxContextWrapperRef>(null);

  // Cấu hình mặc định cho MathJax
  const defaultConfig = {
    tex: {
      inlineMath: [
        ['$', '$'],
        ['\\(', '\\)'],
      ],
      displayMath: [
        ['$$', '$$'],
        ['\\[', '\\]'],
      ],
      processEscapes: true,
      processEnvironments: true,
    },
    svg: {
      fontCache: 'global',
    },
    options: {
      skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre', 'code'],
      processHtmlClass: 'tex2jax_process',
      ignoreHtmlClass: 'tex2jax_ignore',
    },
    startup: {
      typeset: false, // Tắt typeset tự động để tránh lỗi
    },
    // Kết hợp cấu hình mặc định với cấu hình tùy chỉnh từ props
    ...config,
  };

  return (
    <MathJaxContextWrapper ref={contextRef} config={defaultConfig}>
      {children}
    </MathJaxContextWrapper>
  );
};

// Tạo một wrapper component cho MathJax để tránh findDOMNode warnings
interface MathJaxWrapperProps {
  children?: React.ReactNode;
  text?: string;
  inline?: boolean;
  className?: string;
  renderMode?: 'pre' | 'post';
  typesettingOptions?: Record<string, any>;
  [key: string]: any;
}

interface MathJaxWrapperRef {
  containerRef: React.RefObject<HTMLDivElement>;
}

const MathJaxWrapper = forwardRef<MathJaxWrapperRef, MathJaxWrapperProps>(
  (props, ref) => {
    const { children, ...rest } = props;
    const containerRef = useRef<HTMLDivElement>(null);

    // Forward the ref to the parent component
    useImperativeHandle(ref, () => ({
      containerRef,
    }));

    return (
      <div ref={containerRef}>
        <MathJax {...rest}>{children}</MathJax>
      </div>
    );
  }
);

export interface MathFormulaProps {
  formula: string;
  inline?: boolean;
  className?: string;
  [key: string]: any; // For additional props passed to MathJax
}

// Component để hiển thị công thức toán học
export const MathFormula = ({
  formula,
  inline = false,
  className = '',
  ...props
}: MathFormulaProps) => {
  // Đảm bảo công thức không rỗng
  const safeFormula = formula?.trim() ? formula : '';

  return (
    <MathJaxWrapper
      inline={inline}
      className={className}
      {...props}
      renderMode="pre"
      text={safeFormula}
      typesettingOptions={{ fn: 'tex2chtml' }}
    />
  );
};

export interface InlineMathProps {
  formula: string;
  className?: string;
  [key: string]: any; // For additional props passed to MathFormula
}

// Component tiện ích cho việc hiển thị công thức inline
export const InlineMath = ({
  formula,
  className = '',
  ...props
}: InlineMathProps) => {
  // Đảm bảo công thức không rỗng
  const safeFormula = formula?.trim() ? formula : '';

  return (
    <MathFormula
      formula={`$${safeFormula}$`}
      inline={true}
      className={className}
      {...props}
    />
  );
};

export interface BlockMathProps {
  formula: string;
  className?: string;
  [key: string]: any; // For additional props passed to MathFormula
}

// Component tiện ích cho việc hiển thị công thức block
export const BlockMath = ({
  formula,
  className = '',
  ...props
}: BlockMathProps) => {
  // Đảm bảo công thức không rỗng
  const safeFormula = formula?.trim() ? formula : '';

  return (
    <MathFormula
      formula={`$$${safeFormula}$$`}
      inline={false}
      className={className}
      {...props}
    />
  );
};

export interface MathContentProps {
  html: string;
  className?: string;
  [key: string]: any; // For additional props passed to MathJax
}

// Component để hiển thị trực tiếp chuỗi HTML chứa công thức MathJax
export const MathContent = ({
  html,
  className = '',
  ...props
}: MathContentProps) => {
  // Đảm bảo HTML không rỗng
  const safeHtml = html?.trim() ? DOMPurify.sanitize(html) : '';

  return (
    <div className={className}>
      {safeHtml && (
        <MathJaxWrapper
          {...props}
          // Không sử dụng renderMode="pre" khi dùng dangerouslySetInnerHTML
          renderMode="post"
          typesettingOptions={{ fn: 'tex2chtml' }}
        >
          <div dangerouslySetInnerHTML={{ __html: safeHtml }} />
        </MathJaxWrapper>
      )}
    </div>
  );
};
