﻿using EdTech.Study.Enum;
using EdTech.Study.Grade;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;
using Volo.Abp.Domain.Entities.Auditing;

namespace EdTech.Study.Exams
{
    /// <summary>
    /// Đại diện cho thực thể đề thi.
    /// </summary>
    public class ExamEntity : AuditedAggregateRoot<Guid>
    {
        public ExamEntity()
        {
        }

        public ExamEntity(Guid id) : base(id)
        {
        }

        /// <summary>
        /// Tiêu đề của đề thi.
        /// </summary>
        [Required]
        [MaxLength(256)]
        public string Title { get; set; }

        /// <summary>
        /// Mô tả ngắn gọn về đề thi.
        /// </summary>
        [MaxLength(512)]
        public string? Description { get; set; }

        /// <summary>
        /// Mã số hoặc ký hiệu của đề thi.
        /// </summary>
        [MaxLength(128)]
        public string? ExamCode { get; set; }

        /// <summary>
        /// Loại đề thi (<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ỳ, <PERSON><PERSON> thử, ...).
        /// </summary>
        public ExamType ExamType { get; set; }

        /// <summary>
        /// Kỳ thi mà đề thi này thuộc về (ví dụ: Học kỳ 1, Học kỳ 2).
        /// </summary>
        [MaxLength(128)]
        public string? ExamPeriod { get; set; }

        /// <summary>
        /// Ngày tổ chức kỳ thi.
        /// </summary>
        public DateTime? ExamDate { get; set; }

        /// <summary>
        /// Thời gian làm bài (tính bằng phút).
        /// </summary>
        public int? Duration { get; set; }

        /// <summary>
        /// Tổng điểm tối đa của đề thi.
        /// </summary>
        public float? TotalScore { get; set; }

        /// <summary>
        /// Trạng thái hiện tại của đề thi.
        /// </summary>
        public ExamStatus Status { get; set; }

        /// <summary>
        /// Liên kết đến môn học tương ứng.
        /// </summary>
        public Guid? SubjectId { get; set; }

        public Subject.Subject? Subject { get; set; }

        /// <summary>
        /// Liên kết đến lớp học tương ứng (có thể null nếu không gắn với lớp cụ thể).
        /// </summary>
        public Guid? GradeId { get; set; }

        public LessonGrade? Grade { get; set; }

        /// <summary>
        /// Phân loại nguồn gốc đề thi (Đề thi thường, Ngân hàng đề).
        /// </summary>
        public ExamSourceType SourceType { get; set; }

        public IList<SectionEntity> Sections { get; set; }

        /// <summary>
        /// Đảm bảo duy nhất mỗi request thêm
        /// </summary>
        [MaxLength(256)]
        public string? IdempotentKey { get; set; }

        /// <summary>
        /// Nguồn gốc
        /// </summary>
        public string? Source { get; set; }
    }
}