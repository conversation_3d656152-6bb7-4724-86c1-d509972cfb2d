.search-space > .ant-space-item:first-child {
  width: 100%;
}

.custom-space-item-full .ant-space-item {
  width: 100%;
}

/* QuestionStoreManager.css */

/* General styles for the question store manager */
.question-store-manager .ant-table-wrapper {
  width: 100%;
}

.question-store-manager .custom-space-item-full {
  width: 100%;
}

/* Styles for the resizable columns */
.question-store-manager .react-resizable {
  position: relative;
  background-clip: padding-box;
}

.question-store-manager .react-resizable-handle {
  position: absolute;
  right: -5px;
  bottom: 0;
  z-index: 1;
  width: 10px;
  height: 100%;
  cursor: col-resize;
}

.question-store-manager .react-resizable-handle::after {
  content: '';
  position: absolute;
  right: 3px;
  top: 0;
  width: 2px;
  height: 100%;
  background-color: #ddd;
  opacity: 0;
  transition: opacity 0.3s;
}

/* Show resize handle on hover */
.question-store-manager .react-resizable:hover .react-resizable-handle::after {
  opacity: 1;
}

/* Active state when resizing */
.question-store-manager .react-resizable-handle.active::after {
  opacity: 1;
  background-color: #1890ff;
}

/* Fix for the table layout when resizing */
.question-store-manager .ant-table-thead > tr > th {
  position: relative;
  background-color: #fafafa;
  transition: background 0.3s ease;
}

.question-store-manager .ant-table-thead > tr > th:hover {
  background-color: #f0f0f0;
}

/* Ensure the table respects the column widths */
.question-store-manager .ant-table-cell {
  word-break: break-word;
}

/* Fix for row selection column */
.question-store-manager .ant-table-selection-column {
  min-width: 60px;
}

/* Fix for action column */
.question-store-manager th[data-key='action'] {
  min-width: 100px;
}

/* Assignment action buttons */
.assignment-action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s;
}

.assignment-action-btn.assign {
  background-color: var(--edtt-color-primary);
  border-color: var(--edtt-color-primary);
  color: var(--edtt-color-white);
}

.assignment-action-btn.assign:hover:not(:disabled) {
  background-color: var(--edtt-color-white);
  color: var(--edtt-color-primary);
  border-color: var(--edtt-color-primary);
}

.assignment-action-btn.unassign {
  background-color: var(--edtt-color-white);
  border-color: var(--edtt-color-status-warning);
  color: var(--edtt-color-status-warning);
}

.assignment-action-btn.unassign:hover:not(:disabled) {
  background-color: var(--edtt-color-status-warning);
  color: var(--edtt-color-white);
}

.assignment-action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
