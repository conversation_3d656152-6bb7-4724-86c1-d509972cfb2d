﻿using EdTech.Study.Application.Services;
using EdTech.Study.Localization;
using EdTech.Study.Permissions;
using Microsoft.Extensions.DependencyInjection;
using System.Net.WebSockets;
using System.Threading.Tasks;
using Volo.Abp.UI.Navigation;
using Volo.Abp.Users;

namespace EdTech.Study.Web.Menus;

public class StudyMenuContributor : IMenuContributor
{

    public async Task ConfigureMenuAsync(MenuConfigurationContext context)
    {
        if (context.Menu.Name == StandardMenus.Main)
        {
            await ConfigureMainMenuAsync(context);
        }
    }

    private Task ConfigureMainMenuAsync(MenuConfigurationContext context)
    {
        var localizer = context.GetLocalizer<StudyResource>();

        //Add main menu items.
        //context.Menu.AddItem(new ApplicationMenuItem(StudyMenus.Prefix, displayName: "Study", "~/Study", icon: "fa fa-globe"));
        var currentUser = context.ServiceProvider.GetRequiredService<ICurrentUser>();
        if (currentUser.IsInRole("admin"))
        {
            // context.Menu.AddItem(new ApplicationMenuItem("demolessonpage", displayName: "Demo bài giảng", "~/demolessonpage", icon: "fa fa-globe"));
            context.Menu.AddItem(new ApplicationMenuItem("jsonconfig", displayName: "Quản lý cấu hình JSON", "~/Study/JsonConfig", icon: "fa fa-cog"));
            context.Menu.AddItem(
            new ApplicationMenuItem(
            "Menus",
            localizer["Menus"],
            url: "/Menus",
            icon: "fas fa-list",
            requiredPermissionName: MenuPermissions.Menus.Default)
            );

        }
        context.Menu.AddItem(new ApplicationMenuItem(
            "QuestionsStore",
            localizer["QuestionsStore"],
            url: "/Questions",
            icon: "fas fa-database",
            requiredPermissionName: QuestionsPermissions.Questions.Default));

        context.Menu.AddItem(new ApplicationMenuItem(
            "ExamManagement",
            localizer["ExamManagement"],
            url: "/ExamManagement",
            icon: "fa-solid fa-bookmark",
            requiredPermissionName: ExamPermissions.Exams.Default));

        context.Menu.AddItem(new ApplicationMenuItem(
           "PracticeExam",
           localizer["PracticeExam"],
           url: "/PracticeExam",
           icon: "fa fa-graduation-cap",
           requiredPermissionName: ExamPermissions.Exams.Default));

        //// SolarSystem
        //context.Menu.Items.Insert(
        //    0,
        //    RegisterMenuPageService.Instance.GetMenuItem(localizer)
        //    );


        return Task.CompletedTask;
    }
}
