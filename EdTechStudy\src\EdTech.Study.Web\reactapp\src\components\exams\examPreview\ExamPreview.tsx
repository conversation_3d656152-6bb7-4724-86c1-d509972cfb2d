import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Typography,
  Divider,
  Tag,
  Spin,
  Empty,
  Button,
  message,
  Space,
  Modal,
  List,
  FloatButton,
  Tooltip,
  Progress,
} from 'antd';
import {
  ArrowLeftOutlined,
  CloseOutlined,
  ExpandOutlined,
  ExclamationCircleOutlined,
  LeftOutlined,
  PlayCircleOutlined,
  PrinterOutlined,
  RightOutlined,
  SettingFilled,
  UnorderedListOutlined,
  FullscreenExitOutlined,
  RedoOutlined,
} from '@ant-design/icons';
import { connect } from 'react-redux';
import {
  ExamBase,
  ExamQuestion,
  ExamSection,
} from '../../../interfaces/exams/examBase';
import { fetchExamById } from '../../../store/slices/ExamSlices/examDataManagerSlice';

import {
  getExamStatusColor,
  formatExamDuration,
  getExamStatusDisplayVN,
  calculateTotalPoints,
} from '../../../utils/examUtils';
import dayjs from 'dayjs';
import {
  BaseAnswer,
  isAnswerCorrect,
  getQuestionComponentRegistry,
} from '../../../interfaces/quizs/questionBase';
import HashHelper from '../../../utils/HashHelper';
import usePracticesLogic from '../../quizs/practiceEngines/hooks/usePracticesLogic';
import ExamTimer, { ExamTimerRef } from './ExamTimer';
import FullscreenContainer, {
  FullscreenContainerHandle,
} from '../../common/Fullscreen/FullscreenContainer';
import { getFullscreenElement } from '../../common/Fullscreen/fullscreenUtils';
import { useNavigate } from 'react-router-dom';
import { getExamStatusIcon } from '../examEditor/helpers/examEditorHelper';
import ExamSectionTitleComponent from '../components/ExamSectionTitleComponent';
import ExamSectionContentComponent from '../components/ExamSectionContentComponent';
import './ExamPreview.css';
import {
  calculateSectionQuestions,
  calculateTotalQuestions,
  findQuestionById,
  findQuestionPosition,
  getAllQuestionsInSection,
  getQuestionNumberInSection,
  sortGroupQuestionsByOrder,
  sortQuestionsByOrder,
} from './ExamPreviewUtils';
import QuestionSidebar from './QuestionSidebar';
import { CardAntdCustom } from '../../customs/antd/CardAntdCustom';
import { TruncatedTitle } from './TruncatedTitle';
import { ExamPreviewProps } from '../../../interfaces/exams/examPreview';

const { Title, Paragraph } = Typography;
const { confirm } = Modal;

const ExamPreview: React.FC<ExamPreviewProps> = ({
  examId,
  mode,
  backUrl,
  currentExam,
  fetchExamById,
}) => {
  const navigate = useNavigate();

  // State management
  const [loading, setLoading] = useState(true);
  const [exam, setExam] = useState<ExamBase | null>(null);
  const [currentSection, setCurrentSection] = useState(0);
  const [fullscreen, setFullscreen] = useState(false);
  const [inExamMode, setInExamMode] = useState(false);
  const [selectedAnswers, setSelectedAnswers] = useState<
    Record<string, BaseAnswer | BaseAnswer[]>
  >({});
  const [examSubmitted, setExamSubmitted] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [timerActive, setTimerActive] = useState(false);
  const [activeCollapseKeys, setActiveCollapseKeys] = useState<string[]>(['0']);
  const [resultActiveKeys, setResultActiveKeys] = useState<string[]>(['1']);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [sidebarVisible, setSidebarVisible] = useState(false);
  const [markedQuestions, setMarkedQuestions] = useState<string[]>([]);

  // Refs
  const fullscreenRef = useRef<FullscreenContainerHandle>(null);
  const timerRef = useRef<ExamTimerRef | null>(null);
  const questionListItemRefs = useRef<
    Record<string, React.RefObject<HTMLDivElement>>
  >({});

  const questionRegistry = getQuestionComponentRegistry();

  usePracticesLogic({
    consumerId: examId ?? '',
  });

  const toggleSidebar = () => {
    setSidebarVisible(!sidebarVisible);
  };

  const toggleMarkQuestion = (questionId: string) => {
    setMarkedQuestions((prev) => {
      if (prev.includes(questionId)) {
        return prev.filter((id) => id !== questionId);
      } else {
        return [...prev, questionId];
      }
    });
  };

  const scrollToQuestion = (questionId: string) => {
    if (!questionId) return;

    setTimeout(() => {
      location.hash = `#${questionId}`;
      history.pushState(
        '',
        document.title,
        window.location.pathname + window.location.search
      );
    }, 100);
  };

  const highlightElement = (element: HTMLElement) => {
    if (!element) return;

    element.style.transition = 'all 0.3s ease';
    element.style.backgroundColor = 'rgba(245, 192, 192, 0.3)';

    setTimeout(() => {
      element.style.backgroundColor = '';
    }, 2000);
  };

  const scrollToQuestionInList = (questionId: string) => {
    if (!questionId) return;

    setTimeout(() => {
      const questionRef = questionListItemRefs.current[questionId];

      if (questionRef?.current) {
        questionRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
        });
        highlightElement(questionRef.current);
      } else {
        const element = document.getElementById(questionId);
        if (element) {
          element.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
          });
          highlightElement(element);
        }
      }
    }, 100);
  };

  const startExam = () => {
    setInExamMode(true);
    setSelectedAnswers({});
    setExamSubmitted(false);
    setShowResults(false);
    setCurrentSection(0);
    setCurrentQuestionIndex(0);
    setTimerActive(true);
  };

  const exitExamMode = () => {
    confirm({
      title: 'Thoát chế độ làm bài?',
      icon: <ExclamationCircleOutlined />,
      content:
        'Tiến trình làm bài của bạn sẽ bị mất. Bạn có chắc chắn muốn thoát?',
      onOk() {
        setInExamMode(false);
        setSelectedAnswers({});
        setExamSubmitted(false);
        setShowResults(false);
        setTimerActive(false);
      },
      getContainer: getFullscreenElement as any,
    });
  };

  const handleSelectAnswer = (
    questionId: string,
    answer: BaseAnswer | BaseAnswer[]
  ) => {
    if (examSubmitted) return;

    if (!inExamMode) {
      console.log('Preview mode: Answer selection not saved', {
        questionId,
        answer,
      });
      return;
    }

    setSelectedAnswers((prev) => ({
      ...prev,
      [questionId]: answer,
    }));
  };

  const handleSubmitExam = () => {
    setTimerActive(false);
    setExamSubmitted(true);
    setShowResults(true);
    message.success('Bài thi đã được nộp thành công!');
  };

  const handleNextQuestion = () => {
    if (!exam?.sections?.[currentSection]) return;

    const section = exam.sections[currentSection];
    const allQuestions = getAllQuestionsInSection(section);

    if (currentQuestionIndex < allQuestions.length - 1) {
      const nextQuestionId = allQuestions[currentQuestionIndex + 1]?.clientId;
      setCurrentQuestionIndex(currentQuestionIndex + 1);

      if (nextQuestionId) {
        scrollToQuestion(nextQuestionId);
      }
    } else {
      // Chuyển sang section tiếp theo
      if (currentSection < (exam.sections?.length ?? 0) - 1) {
        const nextSection = exam.sections[currentSection + 1];
        const nextSectionQuestions = getAllQuestionsInSection(nextSection);
        const nextSectionFirstQuestionId = nextSectionQuestions[0]?.clientId;

        setCurrentSection(currentSection + 1);
        setCurrentQuestionIndex(0);

        if (nextSectionFirstQuestionId) {
          scrollToQuestion(nextSectionFirstQuestionId);
        }
      }
    }
  };

  const handlePrevQuestion = () => {
    if (!exam?.sections?.[currentSection]) return;

    const section = exam.sections[currentSection];
    const allQuestions = getAllQuestionsInSection(section);

    if (currentQuestionIndex > 0) {
      const prevQuestionId = allQuestions[currentQuestionIndex - 1]?.clientId;
      setCurrentQuestionIndex(currentQuestionIndex - 1);

      if (prevQuestionId) {
        scrollToQuestion(prevQuestionId);
      }
    } else {
      // Quay về section trước đó
      if (currentSection > 0) {
        const prevSection = exam.sections[currentSection - 1];
        const prevSectionQuestions = getAllQuestionsInSection(prevSection);
        const prevQuestionIndex = prevSectionQuestions.length - 1;
        const prevSectionLastQuestionId =
          prevSectionQuestions[prevQuestionIndex]?.clientId;

        setCurrentSection(currentSection - 1);
        setCurrentQuestionIndex(prevQuestionIndex);

        if (prevSectionLastQuestionId) {
          scrollToQuestion(prevSectionLastQuestionId);
        }
      }
    }
  };

  const calculateScore = () => {
    if (!exam) return { score: 0, total: 0, percentage: 0 };

    let correctAnswers = 0;
    let totalQuestions = 0;

    exam.sections?.forEach((section) => {
      // Tính điểm từ câu hỏi độc lập
      if (section.questions) {
        section.questions.forEach((question) => {
          totalQuestions++;
          const userAnswer = selectedAnswers[question.clientId];
          const isCorrect = isAnswerCorrect(question, userAnswer);
          if (isCorrect) {
            correctAnswers++;
          }
        });
      }

      // Tính điểm từ các nhóm câu hỏi
      if (section.groupQuestions) {
        section.groupQuestions.forEach((group) => {
          if (group.questions) {
            group.questions.forEach((question) => {
              totalQuestions++;
              const userAnswer = selectedAnswers[question.clientId];
              const isCorrect = isAnswerCorrect(question, userAnswer);
              if (isCorrect) {
                correctAnswers++;
              }
            });
          }
        });
      }
    });

    const score = correctAnswers;
    const total = totalQuestions;
    const percentage =
      total > 0 ? Math.round((correctAnswers / total) * 100) : 0;

    return { score, total, percentage };
  };

  const checkIsAnswerCorrect = (clientId: string): boolean | null => {
    if (!exam) return null;

    if (!selectedAnswers[clientId]) {
      return null;
    }

    const question = findQuestionById(exam.sections || [], clientId);
    if (!question) return null;

    const userAnswer = selectedAnswers[clientId];
    return isAnswerCorrect(question, userAnswer);
  };

  const goToQuestion = (sectionIndex: number, questionIndex: number) => {
    setCurrentSection(sectionIndex);
    setSidebarVisible(false);

    const section = exam?.sections?.[sectionIndex];
    if (!section) return;

    let questionId = section.questions?.[questionIndex]?.clientId;

    if (!questionId) return;
    const allQuestions = getAllQuestionsInSection(section);
    const sortedIndex = allQuestions.findIndex(
      (q) => q.clientId === questionId
    );
    if (sortedIndex !== -1) {
      setCurrentQuestionIndex(sortedIndex);
    }
    if (inExamMode) {
      scrollToQuestion(questionId);
    } else {
      scrollToQuestionInList(questionId);
    }
  };

  const toggleFullscreen = () => {
    const scrollPosition = window.scrollY;
    fullscreenRef.current?.toggleFullscreen();

    if (fullscreen) {
      setTimeout(() => {
        window.scrollTo(0, scrollPosition);
      }, 100);
    }
  };

  const renderQuestionWithComponent = (
    question: ExamQuestion,
    questionNumber: number,
    showResults?: boolean | null
  ) => {
    if (questionRegistry) {
      const questionComponent = questionRegistry.getComponent(question.type);
      if (!questionComponent) return null;

      const questionData = {
        ...question,
        userSelect: selectedAnswers[question.clientId],
        userAnswer: selectedAnswers[question.clientId],
      };

      const componentId = HashHelper.computeHash([questionData.clientId]);

      return (
        <div id={question.clientId}>
          <questionComponent.component
            id={componentId}
            question={questionData}
            questionIndex={questionNumber}
            showResult={showResults || false}
            onComplete={(questionId, answer) => {
              if (answer) handleSelectAnswer(questionId, answer);
            }}
            configMode={false}
            disabled={examSubmitted}
            isMarked={markedQuestions.includes(question.clientId)}
            onToggleMark={toggleMarkQuestion}
          />
        </div>
      );
    }

    return null;
  };

  const renderExamInfo = () => {
    return (
      <>
        <Card
          className="tailwind-m-6 tailwind-shadow-sm exam-header-card"
          style={{ padding: '16px!important' }}
        >
          <div className="tailwind-flex tailwind-flex-wrap tailwind-items-start tailwind-gap-2">
            <Button
              type="text"
              icon={<ArrowLeftOutlined />}
              onClick={() => navigate(backUrl || '/ExamManagement')}
              className="tailwind-flex-shrink-0 tailwind-mr-2"
            />

            <div className="tailwind-flex-1 tailwind-min-w-0 tailwind-mr-2">
              <TruncatedTitle title={exam?.title || ''} />
            </div>

            {exam?.status !== undefined && mode == 'admin' && (
              <Tag
                color={getExamStatusColor(exam.status)}
                style={{
                  height: '32px',
                  fontSize: '14px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
                className="tailwind-whitespace-nowrap tailwind-flex-shrink-0 tailwind-mr-2 tailwind-mt-1"
              >
                {getExamStatusIcon(exam.status)}
                &nbsp;
                {getExamStatusDisplayVN(exam.status)}
              </Tag>
            )}

            <Space className="tailwind-flex-shrink-0 tailwind-mt-1">
              {!inExamMode && mode === 'admin' && (
                <Tooltip title="Chỉnh sửa">
                  <Button
                    type="default"
                    icon={<SettingFilled />}
                    onClick={() => {
                      navigate(`/ExamManagement/edit/${examId}`);
                    }}
                    title="Chỉnh sửa"
                  ></Button>
                </Tooltip>
              )}

              <Tooltip
                title={fullscreen ? 'Thoát toàn màn hình' : 'Toàn màn hình'}
              >
                <Button
                  icon={
                    fullscreen ? <FullscreenExitOutlined /> : <ExpandOutlined />
                  }
                  onClick={toggleFullscreen}
                ></Button>
              </Tooltip>

              {!inExamMode && mode === 'admin' && (
                <Tooltip title="In đề thi">
                  <Button
                    icon={<PrinterOutlined />}
                    onClick={() => {
                      window.print();
                    }}
                  ></Button>
                </Tooltip>
              )}
              {!inExamMode ? (
                <>
                  <Tooltip title="Làm bài thi">
                    <Button
                      type="primary"
                      icon={<PlayCircleOutlined />}
                      onClick={startExam}
                    >
                      Làm bài
                    </Button>
                  </Tooltip>
                </>
              ) : (
                <>
                  {showResults ? (
                    <Button
                      type="primary"
                      onClick={startExam}
                      icon={<RedoOutlined />}
                    >
                      Làm lại bài
                    </Button>
                  ) : (
                    <>
                      <Button type="primary" onClick={handleSubmitExam}>
                        Nộp bài
                      </Button>
                      <Button
                        danger
                        onClick={exitExamMode}
                        icon={<CloseOutlined />}
                      >
                        Thoát
                      </Button>
                    </>
                  )}
                </>
              )}
            </Space>
          </div>
        </Card>

        {!showResults && (
          <div className=" tailwind-m-6">
            <Paragraph className="tailwind-text-gray-600 tailwind-mt-4">
              {exam?.description || 'Không có mô tả.'}
            </Paragraph>
            <div className="tailwind-w-full tailwind-overflow-x-auto">
              <table className="tailwind-w-full tailwind-border-collapse tailwind-text-sm">
                <thead>
                  <tr className="">
                    <th className="tailwind-py-2 tailwind-px-4 tailwind-border tailwind-border-gray-300 tailwind-text-center">
                      MÔN HỌC
                    </th>
                    <th className="tailwind-py-2 tailwind-px-4 tailwind-border tailwind-border-gray-300 tailwind-text-center">
                      LỚP
                    </th>
                    <th className="tailwind-py-2 tailwind-px-4 tailwind-border tailwind-border-gray-300 tailwind-text-center">
                      MÃ ĐỀ
                    </th>
                    <th className="tailwind-py-2 tailwind-px-4 tailwind-border tailwind-border-gray-300 tailwind-text-center">
                      NGÀY THI
                    </th>
                    <th className="tailwind-py-2 tailwind-px-4 tailwind-border tailwind-border-gray-300 tailwind-text-center">
                      THỜI GIAN LÀM BÀI
                    </th>
                    <th className="tailwind-py-2 tailwind-px-4 tailwind-border tailwind-border-gray-300 tailwind-text-center">
                      SỐ CÂU HỎI
                    </th>
                    <th className="tailwind-py-2 tailwind-px-4 tailwind-border tailwind-border-gray-300 tailwind-text-center">
                      TỔNG ĐIỂM
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="tailwind-font-normal">
                    <td className="tailwind-py-2 tailwind-px-4 tailwind-border tailwind-border-gray-300 tailwind-text-center">
                      {exam?.subject?.name || 'Chưa đặt'}
                    </td>
                    <td className="tailwind-py-2 tailwind-px-4 tailwind-border tailwind-border-gray-300 tailwind-text-center">
                      {exam?.grade?.name || 'Chưa đặt'}
                    </td>
                    <td className="tailwind-py-2 tailwind-px-4 tailwind-border tailwind-border-gray-300 tailwind-text-center">
                      {exam?.examCode || 'Chưa đặt'}
                    </td>
                    <td className="tailwind-py-2 tailwind-px-4 tailwind-border tailwind-border-gray-300 tailwind-text-center">
                      {exam?.examDate
                        ? dayjs(exam.examDate).format('DD/MM/YYYY')
                        : 'Chưa đặt'}
                    </td>
                    <td className="tailwind-py-2 tailwind-px-4 tailwind-border tailwind-border-gray-300 tailwind-text-center">
                      {exam?.duration
                        ? formatExamDuration(exam.duration)
                        : 'Chưa đặt'}
                    </td>
                    <td className="tailwind-py-2 tailwind-px-4 tailwind-border tailwind-border-gray-300 tailwind-text-center">
                      {exam ? calculateTotalQuestions(exam) : 'Chưa đặt'}
                    </td>
                    <td className="tailwind-py-2 tailwind-px-4 tailwind-border tailwind-border-gray-300 tailwind-text-center">
                      {exam?.totalScore || 'Chưa đặt'}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        )}
      </>
    );
  };

  const renderExamContent = () => {
    if (!exam || !exam.sections || exam.sections.length === 0) {
      return <Empty description="Không có phần thi nào" />;
    }

    // Chế độ làm bài
    if (inExamMode && !examSubmitted) {
      const section = exam.sections[currentSection];
      if (!section) return <Empty description="Không có phần thi nào" />;

      const allQuestions = getAllQuestionsInSection(section);
      const currentQuestion = allQuestions[currentQuestionIndex];
      const totalQuestions = allQuestions.length;

      if (!currentQuestion) {
        return <Empty description="Không có câu hỏi nào trong phần này" />;
      }

      // Tính số thứ tự câu hỏi trong section (reset về 1 mỗi section)
      const questionNumberInSection = getQuestionNumberInSection(
        section,
        currentQuestion.clientId
      );

      const questionPosition = findQuestionPosition(
        section,
        currentQuestion.clientId
      );
      let groupInfo = null;

      if (
        questionPosition?.type === 'group' &&
        questionPosition.groupIndex !== undefined
      ) {
        groupInfo = section.groupQuestions?.[questionPosition.groupIndex];
      }

      return (
        <div className="tailwind-m-6">
          <ExamSectionTitleComponent
            sectionIndex={currentSection}
            title={section.title}
            questionCount={totalQuestions}
            sectionScore={section.sectionScore}
            className="tailwind-mb-6"
          />

          <ExamSectionContentComponent
            instructions={section.instructions}
            content={section.content}
          />

          <CardAntdCustom className="tailwind-mb-4">
            {groupInfo && (
              <div className="tailwind-rounded-md tailwind-mb-2">
                {groupInfo.content && (
                  <div
                    className="tailwind-mt-2 tailwind-text-black-600"
                    dangerouslySetInnerHTML={{
                      __html: groupInfo.content,
                    }}
                  />
                )}
                {groupInfo.instructions && (
                  <div
                    className="tailwind-mt-2 tailwind-text-gray-600"
                    dangerouslySetInnerHTML={{
                      __html: groupInfo.instructions,
                    }}
                  />
                )}
              </div>
            )}

            <div className="tailwind-w-full">
              {renderQuestionWithComponent(
                currentQuestion,
                questionNumberInSection // Sử dụng số thứ tự trong section
              ) || (
                <>
                  <div
                    className="tailwind-mb-4"
                    dangerouslySetInnerHTML={{
                      __html: currentQuestion.content,
                    }}
                  />
                  <div className="tailwind-pl-6 tailwind-mb-4">
                    {/* Fallback rendering */}
                  </div>
                </>
              )}
            </div>
          </CardAntdCustom>

          <div className="tailwind-flex tailwind-justify-between tailwind-items-center">
            <Button
              type="primary"
              icon={<LeftOutlined />}
              onClick={handlePrevQuestion}
              disabled={currentSection === 0 && currentQuestionIndex === 0}
            >
              Câu trước
            </Button>

            <Button
              type="primary"
              onClick={handleNextQuestion}
              disabled={
                currentSection === (exam.sections?.length ?? 0) - 1 &&
                currentQuestionIndex === totalQuestions - 1
              }
            >
              Câu sau <RightOutlined />
            </Button>
          </div>
        </div>
      );
    }

    // Chế độ xem đề thi
    else {
      const sortedSections = [...exam.sections].sort(
        (a, b) => (a.orderIndex || 0) - (b.orderIndex || 0)
      );

      const handleCollapseChange = (keys: string | string[]) => {
        setActiveCollapseKeys(typeof keys === 'string' ? [keys] : keys);
      };

      return (
        <div className="tailwind-m-6">
          {sortedSections.map((section, sectionIndex) => {
            const isExpanded = activeCollapseKeys.includes(
              sectionIndex.toString()
            );
            const totalQuestions = calculateSectionQuestions(section);

            return (
              <div key={sectionIndex.toString()}>
                <div
                  className="exam-section-header tailwind-cursor-pointer"
                  onClick={() => {
                    const newActiveKeys = isExpanded
                      ? activeCollapseKeys.filter(
                          (key) => key !== sectionIndex.toString()
                        )
                      : [...activeCollapseKeys, sectionIndex.toString()];
                    handleCollapseChange(newActiveKeys);
                  }}
                >
                  <ExamSectionTitleComponent
                    sectionIndex={sectionIndex}
                    title={section.title}
                    questionCount={totalQuestions}
                    sectionScore={section.sectionScore}
                    className="tailwind-mb-6"
                  />
                </div>

                {isExpanded && (
                  <>
                    <ExamSectionContentComponent
                      instructions={section.instructions}
                      content={section.content}
                    />

                    {/* Câu hỏi độc lập */}
                    {section.questions && section.questions.length > 0 && (
                      <List
                        itemLayout="vertical"
                        dataSource={sortQuestionsByOrder(section.questions)}
                        renderItem={(question, _) => {
                          if (
                            !questionListItemRefs.current[question.clientId]
                          ) {
                            questionListItemRefs.current[question.clientId] =
                              React.createRef<HTMLDivElement>();
                          }

                          // Tính số thứ tự câu hỏi trong section
                          const questionNumberInSection =
                            getQuestionNumberInSection(
                              section,
                              question.clientId
                            );

                          return (
                            <List.Item style={{ border: 'none' }}>
                              <div
                                ref={
                                  questionListItemRefs.current[
                                    question.clientId
                                  ]
                                }
                                id={question.clientId}
                              >
                                <CardAntdCustom>
                                  {renderQuestionWithComponent(
                                    question,
                                    questionNumberInSection // Sử dụng số thứ tự trong section
                                  ) || (
                                    <div
                                      className="tailwind-mb-4"
                                      dangerouslySetInnerHTML={{
                                        __html: question.content,
                                      }}
                                    />
                                  )}
                                </CardAntdCustom>
                              </div>
                            </List.Item>
                          );
                        }}
                      />
                    )}

                    {/* Nhóm câu hỏi */}
                    {section.groupQuestions &&
                      section.groupQuestions.length > 0 && (
                        <>
                          {sortGroupQuestionsByOrder(
                            section.groupQuestions
                          ).map((group, _) => (
                            <CardAntdCustom
                              key={group.clientId}
                              className="tailwind-mb-6"
                            >
                              <div className="tailwind-rounded-md tailwind-mb-2">
                                {group.content && (
                                  <div
                                    className="tailwind-mt-2 tailwind-text-black-600"
                                    dangerouslySetInnerHTML={{
                                      __html: group.content,
                                    }}
                                  />
                                )}
                                {group.instructions && (
                                  <div
                                    className="tailwind-mt-2 tailwind-text-gray-600"
                                    dangerouslySetInnerHTML={{
                                      __html: group.instructions,
                                    }}
                                  />
                                )}
                              </div>

                              {group.questions &&
                                group.questions.length > 0 && (
                                  <List
                                    itemLayout="vertical"
                                    dataSource={sortQuestionsByOrder(
                                      group.questions
                                    )}
                                    renderItem={(question, _) => {
                                      if (
                                        !questionListItemRefs.current[
                                          question.clientId
                                        ]
                                      ) {
                                        questionListItemRefs.current[
                                          question.clientId
                                        ] = React.createRef<HTMLDivElement>();
                                      }

                                      // Tính số thứ tự câu hỏi trong section
                                      const questionNumberInSection =
                                        getQuestionNumberInSection(
                                          section,
                                          question.clientId
                                        );

                                      return (
                                        <List.Item style={{ border: 'none' }}>
                                          <div
                                            ref={
                                              questionListItemRefs.current[
                                                question.clientId
                                              ]
                                            }
                                            id={question.clientId}
                                          >
                                            {renderQuestionWithComponent(
                                              question,
                                              questionNumberInSection
                                            ) || (
                                              <div
                                                className="tailwind-mb-4"
                                                dangerouslySetInnerHTML={{
                                                  __html: question.content,
                                                }}
                                              />
                                            )}
                                          </div>
                                        </List.Item>
                                      );
                                    }}
                                  />
                                )}
                            </CardAntdCustom>
                          ))}
                        </>
                      )}
                  </>
                )}
              </div>
            );
          })}
        </div>
      );
    }
  };

  const renderExamResults = () => {
    if (!exam || !exam.sections || exam.sections.length === 0) {
      return <Empty description="Không có phần thi nào" />;
    }

    const { total, percentage } = calculateScore();
    const timeRemaining = timerRef.current?.getTimeRemaining() || 0;
    console.log(timeRemaining);
    const timeUsed = exam.duration ? exam.duration * 60 - timeRemaining : 0;
    const minutes = Math.floor(timeUsed / 60);
    const seconds = timeUsed % 60;
    const answeredQuestions = Object.keys(selectedAnswers).length;

    const actualScore =
      exam.sections?.reduce((totalScore, section) => {
        let sectionScore = 0;

        if (section.questions) {
          sectionScore += section.questions.reduce((score, question) => {
            const isCorrect = checkIsAnswerCorrect(question.clientId);
            return score + (isCorrect ? question.points || 1 : 0);
          }, 0);
        }

        if (section.groupQuestions) {
          sectionScore += section.groupQuestions.reduce((groupScore, group) => {
            return (
              groupScore +
              (group.questions?.reduce((questionScore, question) => {
                const isCorrect = checkIsAnswerCorrect(question.clientId);
                return questionScore + (isCorrect ? question.points || 1 : 0);
              }, 0) || 0)
            );
          }, 0);
        }

        return totalScore + sectionScore;
      }, 0) || 0;

    const sortedSections = [...exam.sections].sort(
      (a, b) => (a.orderIndex || 0) - (b.orderIndex || 0)
    );

    const handleResultCollapseChange = (keys: string | string[]) => {
      setResultActiveKeys(typeof keys === 'string' ? [keys] : keys);
    };

    return (
      <div className="tailwind-m-6">
        <div className="tailwind-flex tailwind-flex-col tailwind-items-center tailwind-justify-center tailwind-py-6">
          <Progress
            type="circle"
            percent={percentage}
            size={120}
            strokeColor={percentage >= 70 ? '#52c41a' : '#faad14'}
          />

          <div className="tailwind-mt-4 tailwind-mb-8">
            <Title
              level={2}
              style={{
                fontSize: '24px',
                color: 'var(--ant-primary-color)',
                margin: 0,
              }}
            >
              {actualScore} điểm
            </Title>
          </div>

          <div className="tailwind-w-full tailwind-overflow-x-auto">
            <table className="tailwind-w-full tailwind-border-collapse tailwind-text-sm">
              <thead>
                <tr className="">
                  <th className="tailwind-py-2 tailwind-px-4 tailwind-border tailwind-border-gray-300 tailwind-text-center">
                    Mã đề
                  </th>
                  <th className="tailwind-py-2 tailwind-px-4 tailwind-border tailwind-border-gray-300 tailwind-text-center">
                    Thời gian làm bài
                  </th>
                  <th className="tailwind-py-2 tailwind-px-4 tailwind-border tailwind-border-gray-300 tailwind-text-center">
                    Số câu hỏi đã làm
                  </th>
                  <th className="tailwind-py-2 tailwind-px-4 tailwind-border tailwind-border-gray-300 tailwind-text-center">
                    Tỷ lệ chính xác
                  </th>
                  <th className="tailwind-py-2 tailwind-px-4 tailwind-border tailwind-border-gray-300 tailwind-text-center">
                    Tổng điểm
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td className="tailwind-py-2 tailwind-px-4 tailwind-border tailwind-border-gray-300 tailwind-text-center">
                    {exam.examCode || 'N/A'}
                  </td>
                  <td className="tailwind-py-2 tailwind-px-4 tailwind-border tailwind-border-gray-300 tailwind-text-center">
                    {minutes} phút {seconds} giây
                  </td>
                  <td className="tailwind-py-2 tailwind-px-4 tailwind-border tailwind-border-gray-300 tailwind-text-center">
                    {answeredQuestions}/{total}
                  </td>
                  <td className="tailwind-py-2 tailwind-px-4 tailwind-border tailwind-border-gray-300 tailwind-text-center">
                    {percentage}%
                  </td>
                  <td className="tailwind-py-2 tailwind-px-4 tailwind-border tailwind-border-gray-300 tailwind-text-center">
                    {actualScore}/{exam ? calculateTotalPoints(exam) : 0}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <Divider orientation="center" size="middle">
          Kết quả chi tiết
        </Divider>

        <div className="tailwind-mb-4">
          {sortedSections.map((section, sectionIndex) => {
            const resultSectionKey = (sectionIndex + 1).toString();
            const isExpanded = resultActiveKeys.includes(resultSectionKey);
            const totalQuestions = calculateSectionQuestions(section);

            return (
              <div key={resultSectionKey} className="tailwind-mb-4">
                <div
                  className="exam-section-header tailwind-cursor-pointer"
                  onClick={() => {
                    const newActiveKeys = isExpanded
                      ? resultActiveKeys.filter(
                          (key) => key !== resultSectionKey
                        )
                      : [...resultActiveKeys, resultSectionKey];
                    handleResultCollapseChange(newActiveKeys);
                  }}
                >
                  <ExamSectionTitleComponent
                    sectionIndex={sectionIndex}
                    title={section.title}
                    questionCount={totalQuestions}
                    sectionScore={section.sectionScore}
                    className="tailwind-mb-6"
                  />
                </div>

                {isExpanded && (
                  <>
                    <ExamSectionContentComponent
                      instructions={section.instructions}
                      content={section.content}
                    />

                    {/* Kết quả câu hỏi độc lập */}
                    {section.questions && section.questions.length > 0 && (
                      <List
                        itemLayout="vertical"
                        dataSource={sortQuestionsByOrder(section.questions)}
                        renderItem={(question, _) => {
                          if (
                            !questionListItemRefs.current[question.clientId]
                          ) {
                            questionListItemRefs.current[question.clientId] =
                              React.createRef<HTMLDivElement>();
                          }

                          // Tính số thứ tự câu hỏi trong section
                          const questionNumberInSection =
                            getQuestionNumberInSection(
                              section,
                              question.clientId
                            );

                          return (
                            <List.Item>
                              <div
                                ref={
                                  questionListItemRefs.current[
                                    question.clientId
                                  ]
                                }
                              >
                                <CardAntdCustom className="tailwind-w-full tailwind-shadow-sm tailwind-border-gray-200">
                                  {renderQuestionWithComponent(
                                    question,
                                    questionNumberInSection,
                                    true
                                  )}
                                </CardAntdCustom>
                              </div>
                            </List.Item>
                          );
                        }}
                      />
                    )}

                    {/* Kết quả nhóm câu hỏi */}
                    {section.groupQuestions &&
                      section.groupQuestions.length > 0 && (
                        <>
                          {sortGroupQuestionsByOrder(
                            section.groupQuestions
                          ).map((group, _) => (
                            <CardAntdCustom
                              key={group.clientId}
                              className="tailwind-mb-6"
                            >
                              <div className="tailwind-rounded-md tailwind-mb-2 tailwind-flex tailwind-items-center">
                                {group.content && (
                                  <div
                                    className="tailwind-mt-2 tailwind-text-black-600"
                                    dangerouslySetInnerHTML={{
                                      __html: group.content,
                                    }}
                                  />
                                )}
                                {group.instructions && (
                                  <div
                                    className="tailwind-mt-2 tailwind-text-gray-600"
                                    dangerouslySetInnerHTML={{
                                      __html: group.instructions,
                                    }}
                                  />
                                )}
                              </div>

                              {group.questions &&
                                group.questions.length > 0 && (
                                  <List
                                    itemLayout="vertical"
                                    dataSource={sortQuestionsByOrder(
                                      group.questions
                                    )}
                                    renderItem={(question, _) => {
                                      if (
                                        !questionListItemRefs.current[
                                          question.clientId
                                        ]
                                      ) {
                                        questionListItemRefs.current[
                                          question.clientId
                                        ] = React.createRef<HTMLDivElement>();
                                      }

                                      // Tính số thứ tự câu hỏi trong section
                                      const questionNumberInSection =
                                        getQuestionNumberInSection(
                                          section,
                                          question.clientId
                                        );

                                      return (
                                        <List.Item>
                                          <div
                                            ref={
                                              questionListItemRefs.current[
                                                question.clientId
                                              ]
                                            }
                                          >
                                            <CardAntdCustom className="tailwind-w-full tailwind-shadow-sm tailwind-border-gray-200">
                                              {renderQuestionWithComponent(
                                                question,
                                                questionNumberInSection,
                                                true
                                              )}
                                            </CardAntdCustom>
                                          </div>
                                        </List.Item>
                                      );
                                    }}
                                  />
                                )}
                            </CardAntdCustom>
                          ))}
                        </>
                      )}
                  </>
                )}
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  useEffect(() => {
    if (examId) {
      fetchExamById(examId);
    }
    return () => {
      setExam(null);
    };
  }, [examId, fetchExamById]);

  useEffect(() => {
    if (currentExam) {
      setExam(currentExam);
      setLoading(false);
    }
  }, [currentExam]);

  useEffect(() => {
    if (exam?.sections && !inExamMode) {
      questionListItemRefs.current = {};

      exam.sections.forEach((section) => {
        if (section.questions) {
          section.questions.forEach((question) => {
            questionListItemRefs.current[question.clientId] =
              React.createRef<HTMLDivElement>();
          });
        }

        if (section.groupQuestions) {
          section.groupQuestions.forEach((group) => {
            if (group.questions) {
              group.questions.forEach((question) => {
                questionListItemRefs.current[question.clientId] =
                  React.createRef<HTMLDivElement>();
              });
            }
          });
        }
      });
    }
  }, [exam, inExamMode]);

  if (loading) {
    return (
      <div className="tailwind-flex tailwind-justify-center tailwind-items-center tailwind-h-64">
        <Spin size="large" />
      </div>
    );
  }

  if (!exam) {
    return (
      <Empty description="Không tìm thấy đề thi" className="tailwind-my-12" />
    );
  }

  return (
    <FullscreenContainer
      ref={fullscreenRef}
      onEnterFullscreen={() => {
        setFullscreen(true);
      }}
      onExitFullscreen={() => {
        setFullscreen(false);
      }}
    >
      <div
        className={`exam-preview-container tailwind-bg-white ${
          fullscreen
            ? 'tailwind-fixed tailwind-inset-0 tailwind-z-50 tailwind-p-8 tailwind-overflow-auto'
            : ''
        }`}
        id={'exam-preview-container-' + examId}
      >
        {renderExamInfo()}

        {showResults ? renderExamResults() : renderExamContent()}

        {!showResults && (
          <QuestionSidebar
            visible={sidebarVisible}
            onClose={toggleSidebar}
            sections={exam?.sections || []}
            currentSection={currentSection}
            currentQuestionIndex={currentQuestionIndex}
            selectedAnswers={selectedAnswers}
            onSelectQuestion={goToQuestion}
            inExamMode={inExamMode}
            markedQuestions={markedQuestions}
            containerId={'exam-preview-container-' + examId}
          />
        )}

        <FloatButton.BackTop style={{ right: 24, bottom: 24 + 56 }} />

        <FloatButton
          icon={<UnorderedListOutlined />}
          type="primary"
          onClick={toggleSidebar}
          tooltip="Danh sách câu hỏi"
          style={{ right: 24, bottom: 24 }}
        />
        <ExamTimer
          ref={timerRef}
          initialTime={exam.duration ? exam.duration * 60 : null}
          timerActive={timerActive}
          onTimeUp={handleSubmitExam}
        />
      </div>
    </FullscreenContainer>
  );
};

const mapStateToProps = (state: any) => ({
  currentExam: state.examDataManager.currentExam,
  loading: state.examDataManager.loading,
});

const mapDispatchToProps = {
  fetchExamById,
};

export default connect(mapStateToProps, mapDispatchToProps)(ExamPreview);
