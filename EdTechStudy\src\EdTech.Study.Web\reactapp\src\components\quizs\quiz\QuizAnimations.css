/* Quiz answer animations */
.quiz-answer-option {
  transition: all 0.3s ease;
}

/* Radio button styling to make label take full space */
.quiz-option {
  /* Ant Design Radio override */
}

.quiz-option .ant-radio-wrapper {
  display: flex !important;
  align-items: flex-start !important;
  width: 100% !important;
  height: 100% !important;
  margin: 0 !important;
}

.quiz-option .ant-radio {
  flex-shrink: 0;
  margin-top: 2px; /* Align with first line of text */
}

.quiz-option .ant-radio + span {
  flex: 1 !important;
  padding-left: 8px !important;
  width: calc(100% - 24px) !important;
  min-height: 1.5em;
  display: flex !important;
  align-items: flex-start !important;
}

/* Đá<PERSON> án đúng được chọn - background xanh + animation */
.quiz-answer-option.correct-selected-answer {
  animation: correctSelectedAnswer 0.5s ease;
}

/* Đáp án sai được chọn - background đỏ + animation */
.quiz-answer-option.incorrect-selected-answer {
  animation: incorrectSelectedAnswer 0.5s ease;
}

/* <PERSON><PERSON><PERSON> án đúng không được chọn - chỉ hiển thị tích xanh, không thay đổi background */
.quiz-answer-option.correct-unselected-answer {
  /* Không có animation đặc biệt, chỉ hiển thị tích xanh */
}

/* Animation cho đáp án đúng được chọn */
@keyframes correctSelectedAnswer {
  0% {
    transform: scale(1);
    background-color: white;
    border-color: #d9d9d9;
  }
  50% {
    transform: scale(1.05);
    background-color: #f6ffed;
    border-color: #b7eb8f;
  }
  100% {
    transform: scale(1);
    background-color: #f6ffed;
    border-color: #b7eb8f;
  }
}

/* Animation cho đáp án sai được chọn */
@keyframes incorrectSelectedAnswer {
  0% {
    transform: scale(1);
    background-color: white;
    border-color: #d9d9d9;
  }
  25% {
    transform: translateX(-5px);
    background-color: #fff2f0;
    border-color: #ffccc7;
  }
  50% {
    transform: translateX(5px);
    background-color: #fff2f0;
    border-color: #ffccc7;
  }
  75% {
    transform: translateX(-5px);
    background-color: #fff2f0;
    border-color: #ffccc7;
  }
  100% {
    transform: translateX(0);
    background-color: #fff2f0;
    border-color: #ffccc7;
  }
}

/* Đảm bảo các class cũ vẫn hoạt động cho tương thích ngược */
.quiz-answer-option.correct-answer,
.quiz-option.correct-selected-answer {
  animation: correctSelectedAnswer 0.5s ease;
}

.quiz-answer-option.incorrect-answer,
.quiz-option.incorrect-selected-answer {
  animation: incorrectSelectedAnswer 0.5s ease;
}

.quiz-option.correct-unselected-answer {
  /* Không có animation đặc biệt, chỉ hiển thị tích xanh */
}

/* Fireworks animation */
.firework-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 9999;
  overflow: hidden;
}

.firework {
  position: absolute;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  animation: firework-animation 1s ease-out;
  animation-fill-mode: forwards;
}

@keyframes firework-animation {
  0% {
    transform: translate(var(--x), var(--y)) scale(0);
    opacity: 1;
    box-shadow: 0 0 0 0px rgba(255, 255, 255, 0.8);
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translate(var(--x), var(--y)) scale(1);
    opacity: 0;
    box-shadow: 0 0 0 40px rgba(255, 255, 255, 0);
  }
}

/* Confetti animation */
.confetti-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 9998;
  overflow: hidden;
}

.confetti {
  position: absolute;
  width: 10px;
  height: 10px;
  animation: confetti-fall 3s linear forwards;
}

@keyframes confetti-fall {
  0% {
    transform: translateY(-100px) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(calc(100vh + 100px)) rotate(360deg);
    opacity: 0;
  }
}

/* Success message animation */
.success-message {
  animation: fadeInScale 0.5s ease;
  display: inline-block;
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Icon animations */
.quiz-icon.correct {
  animation: correctIcon 0.5s ease;
}

.quiz-icon.incorrect {
  animation: incorrectIcon 0.5s ease;
}

@keyframes correctIcon {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.5);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes incorrectIcon {
  0% {
    transform: scale(0) rotate(0deg);
  }
  25% {
    transform: scale(1.2) rotate(-15deg);
  }
  50% {
    transform: scale(1.2) rotate(10deg);
  }
  100% {
    transform: scale(1) rotate(0deg);
  }
}

.question-image-container {
  margin: 1rem 0;
  display: flex;
  justify-content: center;
  align-items: center;
  max-width: 100%;
  overflow: hidden;
  border-radius: 8px;
  background-color: #f5f5f5;
}

.question-image-container .ant-image {
  display: block;
  max-width: 100%;
}

.question-image-container .ant-upload-button {
  width: 100%;
  height: 120px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  transition: all 0.3s;
}

.question-image-container .ant-upload-button:hover {
  border-color: #1677ff;
}
