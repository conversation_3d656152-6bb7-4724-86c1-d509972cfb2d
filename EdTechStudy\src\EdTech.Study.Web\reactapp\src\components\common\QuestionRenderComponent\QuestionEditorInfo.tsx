import React from 'react';
import { LessonGrade } from '../../../interfaces/lessons/lessonGrade';
import { Subject } from '../../../interfaces/lessons/subject';
import QuestionEditor from '../../quizs/questionEditor/QuestionEditor';
import { BaseQuestion } from '../../../interfaces/quizs/questionBase';

interface QuestionEditorInfoProps {
  question: BaseQuestion;
  subjects: Subject[];
  grades: LessonGrade[];
}

const QuestionEditorInfo: React.FC<QuestionEditorInfoProps> = ({
  question,
  subjects,
  grades,
}) => {
  return (
    <div className="question-editor-info tailwind-p-4">
      <QuestionEditor
        visible={true}
        question={question}
        onCancel={() => {}}
        onSave={() => {}}
        subjects={subjects}
        grades={grades}
        onNext={() => {}}
        onPrev={() => {}}
        options={{
          hideStatusButton: true,
          hideDeleteButton: true,
          hideSaveButton: true,
          hideStatusTag: true,
          hideSourceTypeButton: false,
        }}
      />
    </div>
  );
};

export default QuestionEditorInfo;
