import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

interface SortableItemProps {
  id: string;
  renderItem: () => React.ReactNode;
}

export const SortableItem: React.FC<SortableItemProps> = ({
  id,
  renderItem,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    position: 'relative' as 'relative',
    zIndex: isDragging ? 1 : 0,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="sortable-item"
      data-is-dragging={isDragging}
      {...attributes}
    >
      <div className="dragHandle" {...listeners}>
        {renderItem()}
      </div>
    </div>
  );
};
