﻿using EdTech.Study.Exams;
using EdTech.Study.Grade;
using EdTech.Study.Questions;
using Microsoft.EntityFrameworkCore;
using Volo.Abp;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace EdTech.Study.EntityFrameworkCore
{
    public static class StudyCategoryDbContextModelCreatingExtensions
    {
        public static void ConfigureStudyCategory(
            this ModelBuilder builder)
        {
            Check.NotNull(builder, nameof(builder));

            // Subject
            builder.Entity<Subject.Subject>(b =>
            {
                //Configure table & schema name
                b.ToTable(StudyDbProperties.DbTablePrefix + "Subjects", StudyDbProperties.DbSchema);
                b.ConfigureByConvention();
            });

            // LessonGrade
            builder.Entity<LessonGrade>(b =>
            {
                //Configure table & schema name
                b.ToTable(StudyDbProperties.DbTablePrefix + "LessonGrades", StudyDbProperties.DbSchema);
                b.ConfigureByConvention();
            });
        }
    }
}