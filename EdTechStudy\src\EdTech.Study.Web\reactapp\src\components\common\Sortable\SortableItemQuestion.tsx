import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Button, Space, Tag } from 'antd';
import { DragOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { ExamQuestion } from '../../../interfaces/exams/examBase';
import { QuestionType } from '../../../interfaces/quizs/questionBase';

interface SortableItemQuestionProps {
  id: string;
  index: number;
  question: ExamQuestion;
  onEdit: () => void;
  onDelete: () => void;
}

export const SortableItemQuestion: React.FC<SortableItemQuestionProps> = ({
  id,
  index,
  question,
  onEdit,
  onDelete,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 1 : 0,
  };

  // Helper function to get question type display text
  const getQuestionTypeText = (type: QuestionType): string => {
    switch (type) {
      case QuestionType.SingleChoice:
        return 'Single Choice';
      case QuestionType.MultipleChoice:
        return 'Multiple Choice';
      case QuestionType.Essay:
        return 'Essay';
      case QuestionType.Matching:
        return 'Matching';
      case QuestionType.FillInBlank:
        return 'Fill in the Blank';
      case QuestionType.TrueFalse:
        return 'True/False';
      case QuestionType.Ordering:
        return 'Ordering';
      // case QuestionType.ShortAnswer:
      //   return 'Short Answer';
      default:
        return 'Unknown';
    }
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="tailwind-p-2 tailwind-mb-2 tailwind-border tailwind-border-gray-200 tailwind-rounded-md tailwind-bg-gray-50 tailwind-hover:tailwind-bg-white tailwind-transition-colors"
      {...attributes}
    >
      <div className="tailwind-flex tailwind-justify-between tailwind-items-center">
        <div className="tailwind-flex tailwind-items-center">
          <div
            className="tailwind-mr-2 tailwind-cursor-move tailwind-text-gray-400 tailwind-hover:tailwind-text-gray-700"
            {...listeners}
          >
            <DragOutlined />
          </div>
          <div className="tailwind-mr-2 tailwind-font-medium">
            Q{index + 1}:
          </div>
          <div className="tailwind-truncate tailwind-max-w-md">
            {question.content || 'New Question'}
          </div>
        </div>
        <Space>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              onEdit();
            }}
          />
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }}
          />
        </Space>
      </div>
      <div className="tailwind-mt-2 tailwind-flex tailwind-flex-wrap tailwind-gap-2">
        <Tag color="purple">{getQuestionTypeText(question.questionType)}</Tag>
        {question.points && <Tag color="green">{question.points} points</Tag>}
        {question.shuffleOptions && <Tag color="orange">Shuffle Options</Tag>}
        {question.difficulty !== undefined && (
          <Tag color="blue">Difficulty: {question.difficulty}</Tag>
        )}
      </div>
    </div>
  );
};
