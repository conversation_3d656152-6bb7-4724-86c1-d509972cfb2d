import React from 'react';
import { MathContent } from '../../common/MathJax/MathJaxWrapper';

export interface ExamSectionContentProps {
  /**
   * Section instructions HTML content (optional)
   */
  instructions?: string;
  
  /**
   * Section content HTML content (optional)
   */
  content?: string;
  
  /**
   * Custom class name for instructions content
   * Default: 'tailwind-mb-6 tailwind-text-black'
   */
  instructionsClassName?: string;
  
  /**
   * Custom class name for section content
   * Default: 'tailwind-mb-6 tailwind-text-black'
   */
  contentClassName?: string;
}

/**
 * A component for displaying exam section instructions and content
 */
const ExamSectionContentComponent: React.FC<ExamSectionContentProps> = ({
  instructions,
  content,
  instructionsClassName = 'tailwind-mb-6 tailwind-text-black',
  contentClassName = 'tailwind-mb-6 tailwind-text-black',
}) => {
  return (
    <>
      {/* Section Instructions */}
      {instructions && (
        <MathContent
          html={instructions}
          className={instructionsClassName}
        />
      )}

      {/* Section Content */}
      {content && (
        <MathContent
          html={content}
          className={contentClassName}
        />
      )}
    </>
  );
};

export default ExamSectionContentComponent;
