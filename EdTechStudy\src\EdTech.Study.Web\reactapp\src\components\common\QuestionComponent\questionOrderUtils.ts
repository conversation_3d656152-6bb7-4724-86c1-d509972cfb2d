import { useState, useEffect } from 'react';
import { Question } from './QuestionBankConfig';

/**
 * Updates the order property of questions based on their positions in an array
 * @param questions Array of questions to update with order values
 * @returns A new array with updated order values
 */
export const updateQuestionsOrder = (questions: Question[]): Question[] => {
  return questions.map((question, index) => ({
    ...question,
    order: index + 1,
  }));
};

/**
 * <PERSON>les reordering questions based on a reordered subset of questions
 * This is useful when working with filtered lists where only some questions are visible and being reordered
 *
 * @param allQuestions The complete list of questions
 * @param reorderedQuestions A subset of questions that have been reordered
 * @returns A new array with all questions ordered according to the reordering
 */
export const handleQuestionReordering = (
  allQuestions: Question[],
  reorderedQuestions: Question[]
): Question[] => {
  // First, ensure all questions have an order property
  const questionsWithOrder = allQuestions.map((q, idx) => ({
    ...q,
    order: q.order !== undefined ? q.order : idx + 1,
  }));

  // Create a mapping of id -> new order based on the reordered questions
  const orderMap = new Map<string, number>();
  reorderedQuestions.forEach((q, idx) => {
    if (q.id) orderMap.set(q.id, idx + 1); // Always use the index + 1 as the new order
  });

  // Apply the new order to all questions, preserving original order for questions not in the reordered subset
  const updatedQuestions = questionsWithOrder.map((q) => {
    if (q.id && orderMap.has(q.id)) {
      // If the question was in the reordered set, update its order
      const newOrder = orderMap.get(q.id);
      return { ...q, order: newOrder };
    }
    // Otherwise keep it unchanged
    return q;
  });

  // Sort all questions by their order
  updatedQuestions.sort((a, b) => {
    const orderA = a.order !== undefined ? a.order : 9999;
    const orderB = b.order !== undefined ? b.order : 9999;
    return orderA - orderB;
  });

  return updatedQuestions;
};

/**
 * Creates a custom hook for managing question order
 * @param questions Initial array of questions
 * @param onOrderChange Optional callback when order changes
 * @returns Object with methods and state for managing question order
 */
export const useQuestionOrdering = (
  initialQuestions: Question[],
  onOrderChange?: (questions: Question[]) => void
) => {
  const [questions, setQuestions] = useState<Question[]>(
    updateQuestionsOrder(initialQuestions)
  );

  // Move a question from one position to another
  const moveQuestion = (fromIndex: number, toIndex: number) => {
    if (fromIndex === toIndex) return;

    const newQuestions = [...questions];
    const [movedItem] = newQuestions.splice(fromIndex, 1);
    newQuestions.splice(toIndex, 0, movedItem);

    const updatedQuestions = updateQuestionsOrder(newQuestions);
    setQuestions(updatedQuestions);

    if (onOrderChange) {
      onOrderChange(updatedQuestions);
    }
  };

  // Move a question up one position
  const moveQuestionUp = (index: number) => {
    if (index <= 0) return;
    moveQuestion(index, index - 1);
  };

  // Move a question down one position
  const moveQuestionDown = (index: number) => {
    if (index >= questions.length - 1) return;
    moveQuestion(index, index + 1);
  };

  // Handle reordering from a subset of questions (like when filtered)
  const handleReorder = (reorderedQuestions: Question[]) => {
    const updatedQuestions = handleQuestionReordering(
      questions,
      reorderedQuestions
    );
    setQuestions(updatedQuestions);

    if (onOrderChange) {
      onOrderChange(updatedQuestions);
    }
  };

  // Update when initial questions change
  useEffect(() => {
    setQuestions(updateQuestionsOrder(initialQuestions));
  }, [initialQuestions]);

  return {
    questions,
    moveQuestion,
    moveQuestionUp,
    moveQuestionDown,
    handleReorder,
  };
};
