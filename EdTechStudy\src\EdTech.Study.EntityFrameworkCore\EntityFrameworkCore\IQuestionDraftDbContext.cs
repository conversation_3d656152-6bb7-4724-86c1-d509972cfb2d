﻿using EdTech.Study.Exams;
using EdTech.Study.GroupQuestions;
using EdTech.Study.Questions;
using Microsoft.EntityFrameworkCore;

namespace EdTech.Study.EntityFrameworkCore
{
    public interface IQuestionDraftDbContext : IStudyCategoryDbContext
    {
        DbSet<QuestionDraftEntity> QuestionDrafts { get; }

        DbSet<QuestionOptionDraftEntity> OptionDrafts { get; }

        DbSet<ExamEntity> Exams { get; }

        DbSet<SectionEntity> Sections { get; }

        DbSet<SectionQuestionEntity> SectionQuestions { get; }

        DbSet<QuestionEntity> Questions { get; }

        DbSet<QuestionOptionEntity> QuestionOptions { get; }

        DbSet<FillInBlankAnswerEntity> FillInBlankAnswers { get; }

        DbSet<MatchingAnswerEntity> MatchingAnswers { get; }

        DbSet<MatchingItemEntity> MatchingItems { get; }

        DbSet<GroupQuestionEntity> GroupQuestions { get; }
        DbSet<SectionGroupQuestionEntity> SectionGroupQuestions { get; }
    }
}