import React, { useCallback, useMemo, useRef } from 'react';
import {
  EEngineInteractionMode,
  ETypeEdTechComponent,
} from '../../../enums/AppEnums';
import '../styles/component-highlight.css';
import { getImmediateParentPath } from '../utils/pathUtils';
import EngineHeader from './EngineHeader';
import { IEdTechRenderProps } from '../../../interfaces/AppComponents';

interface EngineContentProps {
  props: IEdTechRenderProps;
  children: React.ReactNode;
}

/**
 * Component hiển thị nội dung của engine với các tính năng highlight và interactive
 */
const EngineContent: React.FC<EngineContentProps> = ({ props, children }) => {
  const { type, path, engineState, updateEngineState } = props;
  const refWrapper = useRef<HTMLDivElement>(null);

  const interactionMode = engineState?.interactionMode;
  // <PERSON><PERSON><PERSON> đ<PERSON>nh các trạng thái
  const isEngineEditable = useMemo(() => {
    return (
      interactionMode === EEngineInteractionMode.CONFIGURATION &&
      type !== ETypeEdTechComponent.STRUCTURE
    );
  }, [interactionMode, type]);

  const isActive = engineState?.pathEngineActive === path && isEngineEditable;
  const isHovered = engineState?.pathEngineHover === path && isEngineEditable;

  // Kiểm tra xem component hiện tại có phải là cha của component đang hover hoặc active không
  const isParentOfHoveredOrActiveElement = useMemo(() => {
    if (!engineState?.pathEngineHover || !path) return false;
    const hoveredParentPath = getImmediateParentPath(
      engineState.pathEngineHover
    );
    const activeParentPath = getImmediateParentPath(
      engineState.pathEngineActive || ''
    );
    return hoveredParentPath === path || activeParentPath === path;
  }, [engineState, path]);

  // Xử lý các sự kiện
  const handleClick = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();

      if (type === ETypeEdTechComponent.STRUCTURE) {
        return;
      }

      // Chỉ xử lý click khi ở chế độ CONFIGURATION và component có thể chỉnh sửa
      if (!isEngineEditable) return;

      // Cập nhật pathEngineActive trong store
      updateEngineState({
        pathEngineActive: path,
      });
    },
    [path, updateEngineState, isEngineEditable, type]
  );

  const handleMouseEnter = useCallback(() => {
    // Chỉ xử lý hover khi ở chế độ CONFIGURATION và component có thể chỉnh sửa
    if (!isEngineEditable) return;

    // Cập nhật pathEngineHover trực tiếp
    updateEngineState({ pathEngineHover: path });
  }, [updateEngineState, path, isEngineEditable]);

  // Xác định css classes dựa trên trạng thái
  const containerClass = useMemo(() => {
    if (isActive && isEngineEditable) {
      return 'engine-active-content';
    }
    if (isHovered && isEngineEditable) {
      return 'engine-highlight-hover';
    }

    if (isParentOfHoveredOrActiveElement && isEngineEditable) {
      return 'engine-highlight-parent';
    }

    return '';
  }, [isActive, isHovered, isParentOfHoveredOrActiveElement, isEngineEditable]);

  // Xác định khi nào hiển thị label và toolbar
  const shouldShowUI = isActive;

  return (
    <div
      ref={refWrapper}
      onClick={handleClick}
      onMouseEnter={handleMouseEnter}
      style={{
        minHeight: '20px',
        cursor: isEngineEditable ? 'pointer' : 'default',
        position: 'relative',
      }}
      data-path={path}
      data-type={type}
    >
      {/* {shouldShowUI && <EngineHeader props={props} />} */}
      <div className={containerClass}>{children}</div>
    </div>
  );
};

export default React.memo(EngineContent);
