﻿using EdTech.Study.Questions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace EdTech.Study.OData
{
    public class MatchingItemsController : BaseOdataEntityController<MatchingItemEntity>
    {
        public MatchingItemsController(IRepository<MatchingItemEntity, Guid> repository) : base(repository)
        {
        }
    }
}
