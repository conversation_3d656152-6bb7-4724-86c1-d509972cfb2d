import React, { useEffect, useState, useMemo } from 'react';
import { QuizCardGameConfig } from './QuizCardGameConfig';
import {
  Question,
  QuestionType,
} from '../../common/QuestionComponent/QuestionBankConfig';
import {
  QuestionListComponent,
  QuestionFormModal,
} from '../../common/QuestionComponent';
import {
  Tabs,
  Form,
  Input,
  Button,
  InputNumber,
  Select,
  Checkbox,
  Typography,
  message,
} from 'antd';
import {
  InfoCircleOutlined,
  QuestionCircleOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { ModalAntdCustom } from '../../customs/antd/ModalAntdCustom';
import { handleQuestionReordering } from '../../common/QuestionComponent/questionOrderUtils';

const { TextArea } = Input;
const { Option } = Select;
const { Paragraph } = Typography;

interface QuizCardGameModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (config: QuizCardGameConfig) => void;
  defaultConfig: QuizCardGameConfig;
}

type TabType = 'basic' | 'questions' | 'advanced';

const QuizCardGameModal: React.FC<QuizCardGameModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  defaultConfig,
}) => {
  // State for active tab
  const [activeTab, setActiveTab] = useState<TabType>('basic');

  // State for game configuration
  const [config, setConfig] = useState<QuizCardGameConfig>(defaultConfig);

  // Updated state management for question editing
  const [showQuestionForm, setShowQuestionForm] = useState(false);
  const [editingQuestion, setEditingQuestion] = useState<Question | undefined>(
    undefined
  );
  const [editingQuestionIndex, setEditingQuestionIndex] = useState<number>(-1);

  // State cho loại câu hỏi đang chọn để thêm mới
  const [selectedQuestionType, setSelectedQuestionType] =
    useState<QuestionType>('truefalse');

  // Add state for search and filtering
  const questionSearch = '';
  const questionFilter = 'all';

  // Lọc câu hỏi theo loại
  const filteredQuestions = useMemo(() => {
    return config.questions.filter((question) => {
      // Filter by type if filter is set
      if (questionFilter !== 'all' && question.type !== questionFilter) {
        return false;
      }

      // Filter by search term if any
      if (questionSearch.trim() !== '') {
        const search = questionSearch.toLowerCase();
        return (
          question.questionText?.toLowerCase().includes(search) ||
          question.explanation?.toLowerCase().includes(search)
        );
      }

      return true;
    });
  }, [config.questions, questionSearch, questionFilter]);

  // Add handler for reordering questions
  const handleReorderQuestions = (reorderedQuestions: Question[]) => {
    // Use the utility function to handle reordering properly with filtered questions
    const updatedQuestions = handleQuestionReordering(
      config.questions,
      reorderedQuestions
    );

    // Update the config with the new question order
    setConfig((prev) => ({
      ...prev,
      questions: updatedQuestions,
    }));
  };

  // Handle input changes cho các cấu hình cơ bản
  const handleConfigChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value, type, checked } = e.target as HTMLInputElement;

    // Xử lý các trường hợp đặc biệt trước
    if (name === 'animationSpeed') {
      // Xử lý trường hợp thuộc tính con trong theme
      setConfig((prev) => ({
        ...prev,
        theme: {
          ...(prev.theme || {}),
          animationSpeed: value as 'slow' | 'normal' | 'fast',
        },
      }));
    } else {
      // Xử lý các trường thông thường
      setConfig((prev) => ({
        ...prev,
        [name]:
          type === 'checkbox'
            ? checked
            : type === 'number'
            ? Number(value)
            : value,
      }));
    }
  };

  // Handle showing the add question form
  const handleShowAddQuestionForm = () => {
    // Reset everything to ensure a clean slate
    setEditingQuestion(undefined);
    setEditingQuestionIndex(-1);
    setSelectedQuestionType('multiplechoice');
    setShowQuestionForm(true);
  };

  // Handle editing a question
  const handleEditQuestion = (question: Question, index: number) => {
    setEditingQuestion(question);
    setEditingQuestionIndex(index);
    setShowQuestionForm(true);
  };

  // Handle deleting a question
  const handleDeleteQuestion = (questionIds: string[]) => {
    const updatedQuestions = config.questions.filter(
      (question) => !questionIds.includes(question.id || '')
    );
    setConfig({
      ...config,
      questions: updatedQuestions,
    });
  };

  // Handle saving a question (new or edited)
  const handleSaveQuestion = (question: Question) => {
    const updatedQuestions = [...config.questions];

    if (editingQuestionIndex >= 0) {
      // Update existing question
      updatedQuestions[editingQuestionIndex] = question;
    } else {
      // Add new question
      updatedQuestions.push(question);
    }

    setConfig({
      ...config,
      questions: updatedQuestions,
    });

    // Reset form state
    handleCloseQuestionForm();
  };

  // Handle closing the question form
  const handleCloseQuestionForm = () => {
    setShowQuestionForm(false);
    setEditingQuestion(undefined);
    setEditingQuestionIndex(-1);
  };

  // Handle submit form
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!config.titleProps.text || !config.description) {
      message.error('Vui lòng điền đầy đủ thông tin cơ bản');
      setActiveTab('basic');
      return;
    }

    onSubmit(config);
  };

  // Render the questions tab
  const renderQuestionsTab = () => {
    return (
      <div className="tailwind-mt-1">
        <QuestionListComponent
          allowManualReordering={true}
          questions={filteredQuestions}
          onEdit={handleEditQuestion}
          onDelete={handleDeleteQuestion}
          onAdd={handleShowAddQuestionForm}
          onReorder={handleReorderQuestions}
          tableHeight="300px"
          showFilters={true}
        />
      </div>
    );
  };

  // Render basic tab
  const renderBasicTab = () => {
    return (
      <div className="tailwind-space-y-6">
        <Paragraph type="secondary">
          Cấu hình cơ bản cho trò chơi Lật thẻ câu hỏi.
        </Paragraph>

        <Form
          layout="vertical"
          className="tailwind-grid tailwind-grid-cols-1 tailwind-gap-y-4 tailwind-gap-x-4 sm:tailwind-grid-cols-2"
        >
          <Form.Item label="Tiêu đề" className="sm:tailwind-col-span-2">
            <Input
              name="title"
              id="title"
              value={config.titleProps.text}
              onChange={(e) => handleConfigChange(e as any)}
              placeholder="VD: Lật thẻ câu hỏi"
            />
          </Form.Item>

          <Form.Item label="Mô tả" className="sm:tailwind-col-span-2">
            <TextArea
              name="description"
              id="description"
              rows={3}
              value={config.description}
              onChange={(e) => handleConfigChange(e as any)}
              placeholder="Mô tả ngắn về trò chơi"
            />
          </Form.Item>

          <Form.Item label="Thời gian trả lời (giây)">
            <InputNumber
              name="timeLimit"
              id="timeLimit"
              min={5}
              max={300}
              value={config.timeLimit}
              onChange={(value) => {
                if (value !== null) {
                  setConfig((prev) => ({ ...prev, timeLimit: value }));
                }
              }}
              className="tailwind-w-full"
            />
          </Form.Item>

          <Form.Item label="Điểm phạt trả lời sai">
            <InputNumber
              name="incorrectAnswerPenalty"
              id="incorrectAnswerPenalty"
              min={0}
              value={config.incorrectAnswerPenalty}
              onChange={(value) => {
                if (value !== null) {
                  setConfig((prev) => ({
                    ...prev,
                    incorrectAnswerPenalty: value,
                  }));
                }
              }}
              className="tailwind-w-full"
            />
          </Form.Item>
        </Form>
      </div>
    );
  };

  // Render advanced tab
  const renderAdvancedTab = () => {
    return (
      <div className="tailwind-space-y-6">
        <Paragraph type="secondary">
          Cấu hình nâng cao cho trò chơi Lật thẻ câu hỏi.
        </Paragraph>

        <Form
          layout="vertical"
          className="tailwind-grid tailwind-grid-cols-1 tailwind-gap-y-4 tailwind-gap-x-4 sm:tailwind-grid-cols-3"
        >
          <Form.Item>
            <Checkbox
              id="shuffleQuestions"
              checked={config.shuffleQuestions}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  shuffleQuestions: e.target.checked,
                }))
              }
            >
              Xáo trộn câu hỏi
            </Checkbox>
          </Form.Item>

          <Form.Item>
            <Checkbox
              id="allowSkip"
              checked={config.allowSkip}
              onChange={(e) =>
                setConfig((prev) => ({ ...prev, allowSkip: e.target.checked }))
              }
            >
              Cho phép bỏ qua câu hỏi
            </Checkbox>
          </Form.Item>

          <Form.Item>
            <Checkbox
              id="requireConfirmation"
              checked={config.requireConfirmation}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  requireConfirmation: e.target.checked,
                }))
              }
            >
              Yêu cầu xác nhận đáp án
            </Checkbox>
          </Form.Item>

          <Form.Item>
            <Checkbox
              id="showExplanation"
              checked={config.showExplanation}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  showExplanation: e.target.checked,
                }))
              }
            >
              Hiển thị giải thích sau khi trả lời
            </Checkbox>
          </Form.Item>
        </Form>

        <Paragraph type="secondary" className="tailwind-mt-6">
          Cấu hình giao diện thẻ
        </Paragraph>

        <Form
          layout="vertical"
          className="tailwind-grid tailwind-grid-cols-1 tailwind-gap-y-4 tailwind-gap-x-4 sm:tailwind-grid-cols-2"
        >
          <Form.Item label="Kích cỡ thẻ">
            <Select
              id="cardSize"
              value={config.theme?.cardSize || 'medium'}
              onChange={(value) =>
                setConfig((prev) => ({
                  ...prev,
                  theme: {
                    ...(prev.theme || {}),
                    cardSize: value as 'small' | 'medium' | 'large',
                  },
                }))
              }
            >
              <Option value="small">Nhỏ (120px)</Option>
              <Option value="medium">Vừa phải (160px)</Option>
              <Option value="large">Lớn (200px)</Option>
            </Select>
            <Paragraph type="secondary" className="tailwind-mt-1">
              Điều chỉnh chiều rộng cơ bản của thẻ
            </Paragraph>
          </Form.Item>

          <Form.Item label="Tỷ lệ khung hình thẻ">
            <Select
              id="cardAspectRatio"
              value={config.theme?.cardAspectRatio || '2/3'}
              onChange={(value) =>
                setConfig((prev) => ({
                  ...prev,
                  theme: {
                    ...(prev.theme || {}),
                    cardAspectRatio: value as string,
                  },
                }))
              }
            >
              <Option value="1/1">Vuông (1:1)</Option>
              <Option value="2/3">Dọc (2:3)</Option>
              <Option value="3/4">Dọc hẹp (3:4)</Option>
              <Option value="4/3">Ngang rộng (4:3)</Option>
              <Option value="3/2">Ngang (3:2)</Option>
            </Select>
            <Paragraph type="secondary" className="tailwind-mt-1">
              Xác định tỷ lệ chiều cao so với chiều rộng của thẻ
            </Paragraph>
          </Form.Item>

          <Form.Item label="Chế độ hiển thị">
            <Select
              id="displayMode"
              value={config.theme?.displayMode || 'highlight'}
              onChange={(value) =>
                setConfig((prev) => ({
                  ...prev,
                  theme: {
                    ...(prev.theme || {}),
                    displayMode: value as 'simple' | 'highlight',
                  },
                }))
              }
            >
              <Option value="simple">Đơn giản</Option>
              <Option value="highlight">Nổi bật</Option>
            </Select>
            <Paragraph type="secondary" className="tailwind-mt-1">
              Chế độ đơn giản sử dụng màu đơn sắc, chế độ nổi bật sử dụng nhiều
              màu sắc
            </Paragraph>
          </Form.Item>

          <Form.Item label="Số cột hiển thị">
            <InputNumber
              min={1}
              max={10}
              value={config.theme?.gridColumns || 5}
              onChange={(value) => {
                if (value !== null) {
                  setConfig((prev) => ({
                    ...prev,
                    theme: {
                      ...(prev.theme || {}),
                      gridColumns: value,
                    },
                  }));
                }
              }}
              className="tailwind-w-full"
            />
            <Paragraph type="secondary" className="tailwind-mt-1">
              Số cột hiển thị thẻ câu hỏi (số hàng sẽ tự động tính theo số câu
              hỏi)
            </Paragraph>
          </Form.Item>
        </Form>
      </div>
    );
  };

  // Update config when default changes
  useEffect(() => {
    setConfig((prev) => ({
      ...prev,
      ...defaultConfig,
    }));
  }, [defaultConfig]);

  // Tab items configuration
  const items = [
    {
      key: 'basic',
      label: (
        <span>
          <InfoCircleOutlined /> Cấu hình cơ bản
        </span>
      ),
      children: renderBasicTab(),
    },
    {
      key: 'questions',
      label: (
        <span>
          <QuestionCircleOutlined /> Câu hỏi
        </span>
      ),
      children: renderQuestionsTab(),
    },
    {
      key: 'advanced',
      label: (
        <span>
          <SettingOutlined /> Nâng cao
        </span>
      ),
      children: renderAdvancedTab(),
    },
  ];

  return (
    <>
      <ModalAntdCustom
        title={`Cấu hình trò chơi`}
        open={isOpen}
        onCancel={onClose}
        width={1000}
        style={{
          zIndex: 9999, // Extremely high z-index to appear above everything
        }}
        destroyOnHidden={true}
        keyboard={true}
        centered={false}
        footer={[
          <Button key="cancel" onClick={onClose}>
            Huỷ
          </Button>,
          <Button
            key="submit"
            className="tailwind-bg-blue-500"
            type="primary"
            onClick={handleSubmit}
          >
            Lưu cấu hình
          </Button>,
        ]}
      >
        <Tabs
          activeKey={activeTab}
          onChange={(key) => setActiveTab(key as TabType)}
          items={items}
          className="tailwind-px-0"
        />
      </ModalAntdCustom>

      <QuestionFormModal
        isOpen={showQuestionForm}
        onClose={handleCloseQuestionForm}
        onSave={handleSaveQuestion}
        question={editingQuestion}
        title={editingQuestion ? 'Chỉnh sửa câu hỏi' : 'Thêm câu hỏi mới'}
        initialQuestionType={
          !editingQuestion ? selectedQuestionType : undefined
        }
      />
    </>
  );
};

export default QuizCardGameModal;
