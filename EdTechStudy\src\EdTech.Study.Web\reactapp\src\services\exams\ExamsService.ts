// src/services/examService.ts
import { Dispatch } from 'redux';
import {
  ExamBase,
  ExamSection,
  ExamQuestion,
  ExamGrade,
  ExamFillInBlankAnswer,
  ExamMatchingAnswer,
  ExamMatchingItem,
  ExamQuestionOption,
  ExamGroupQuestion,
} from '../../interfaces/exams/examBase';
import {
  CreateQuestionOptionDto,
  CreateUpdateExamDto,
  CreateUpdateExamSectionDto,
  CreateUpdateFillInBlankAnswer,
  CreateUpdateMatchingAnswerDto,
  CreateUpdateMatchingItemDto,
  CreateUpdateQuestionDto,
  GetExamListDto,
  CreateUpdateGroupQuestionDto,
} from '../../interfaces/exams/examDtos';
import { Subject } from '../../interfaces/lessons/subject';

import {
  setExams,
  setLoading,
  setError,
  addExam,
  updateExam,
  removeExam as removeExamAction,
  setTotalCount, // Add this to your slice if not already there
  setCurrentPage,
  setCurrentExam, // Add this to your slice if not already there
} from '../../store/slices/ExamSlices/examDataManagerSlice';
import { axiosClient } from '../axiosClient';
import { Guid } from 'guid-typescript';
import { camelCaseKeys } from '../../utils/parsers';
import {
  ContentFormatType,
  ExamStatus,
} from '../../interfaces/exams/examEnums';

// API endpoints
export const API_ENDPOINTS = {
  EXAMS: '/api/app/exams',
  SUBJECTS: '/odata/Subject',
  GRADES: '/odata/LessonGrade',
};

// Fetch exams with filtering, sorting, and pagination
export const fetchExams =
  (params: GetExamListDto = {}) =>
  async (dispatch: Dispatch) => {
    dispatch(setLoading(true));
    try {
      // Set default values for pagination and sorting if not provided
      const requestParams: GetExamListDto = {
        skipCount: params.skipCount !== undefined ? params.skipCount : 0,
        maxResultCount:
          params.maxResultCount !== undefined ? params.maxResultCount : 10,
        sorting: params.sorting || undefined,
        filter: params.filter || undefined,
        examType: params.examType !== undefined ? params.examType : undefined,
        startDate: params.startDate || undefined,
        endDate: params.endDate || undefined,
        status: params.status !== undefined ? params.status : undefined,
        subjectId: params.subjectId || undefined,
        gradeId: params.gradeId || undefined,
      };

      // Convert the params object to URLSearchParams
      const queryParams = new URLSearchParams();

      // Add all parameters that are defined
      Object.entries(requestParams).forEach(([key, value]) => {
        if (value !== undefined) {
          // Handle date objects by converting to ISO string
          if (value instanceof Date) {
            queryParams.append(key, value.toISOString());
          } else {
            queryParams.append(key, String(value));
          }
        }
      });

      const response = await axiosClient.get(
        `${API_ENDPOINTS.EXAMS}/exam?${queryParams.toString()}`
      );
      const data = camelCaseKeys(response.data);

      // Store exams in Redux
      dispatch(setExams(data.items));

      // If your backend returns total count, update it in the store
      if (data.totalCount !== undefined) {
        dispatch(setTotalCount(data.totalCount));
      }

      // Update current page in store
      const skipCount = requestParams.skipCount ?? 0;
      const maxResultCount = requestParams.maxResultCount ?? 10;
      const currentPage = Math.floor(skipCount / maxResultCount) + 1;
      dispatch(setCurrentPage(currentPage));

      dispatch(setError(null));
      return data;
    } catch (error) {
      console.error('Error fetching exams:', error);
      dispatch(setError('Failed to load exams. Please try again.'));
      return null;
    } finally {
      dispatch(setLoading(false));
    }
  };

// Convenience methods for UI components
export const handlePageChange =
  (page: number, pageSize: number = 10) =>
  (dispatch: Dispatch) => {
    const skipCount = (page - 1) * pageSize;
    return fetchExams({ skipCount, maxResultCount: pageSize })(dispatch);
  };

export const handleFilterChange =
  (filters: Partial<GetExamListDto>) => (dispatch: Dispatch) => {
    // Chuyển đổi các giá trị Date thành chuỗi ISO nếu cần
    const formattedFilters = { ...filters };

    // if (formattedFilters.startDate instanceof Date) {
    //   formattedFilters.startDate = formattedFilters.startDate.toISOString();
    // }

    // if (formattedFilters.endDate instanceof Date) {
    //   formattedFilters.endDate = formattedFilters.endDate.toISOString();
    // }

    return fetchExams(formattedFilters)(dispatch);
  };

export const handleSortChange =
  (sorting: string) => (dispatch: Dispatch, getState: any) => {
    const state = getState();
    const {
      skipCount,
      maxResultCount,
      filter,
      examType,
      status,
      subjectId,
      gradeId,
      startDate,
      endDate,
    } = state.examDataManager;

    return fetchExams({
      skipCount,
      maxResultCount,
      sorting,
      filter,
      examType,
      status,
      subjectId,
      gradeId,
      startDate,
      endDate,
    })(dispatch);
  };

// Fetch a single exam by ID
export const fetchExamById = (examId: string) => async (dispatch: Dispatch) => {
  dispatch(setLoading(true));
  try {
    const response = await axiosClient.get(`${API_ENDPOINTS.EXAMS}/${examId}`);
    const data = camelCaseKeys(response.data) as ExamBase;

    return data;
  } catch (error) {
    console.error(`Error fetching exam with ID ${examId}:`, error);
    dispatch(
      setError(`Failed to load exam with ID ${examId}. Please try again.`)
    );
    return null;
  } finally {
    dispatch(setLoading(false));
  }
};

// Export direct accessor functions for components that don't need Redux integration
export const getSubjects = async (): Promise<Subject[]> => {
  try {
    let response = await axiosClient.get(API_ENDPOINTS.SUBJECTS);
    let data = camelCaseKeys(response.data);
    return data.value;
  } catch (error) {
    console.error('Error fetching subjects:', error);
    return [];
  }
};

export const getGrades = async (): Promise<ExamGrade[]> => {
  try {
    const response = await axiosClient.get(API_ENDPOINTS.GRADES);
    const data = camelCaseKeys(response.data);
    return data.value;
  } catch (error) {
    console.error('Error fetching grades:', error);
    return [];
  }
};

// [POST] /api/exams
// Create a new exam
export const createExam =
  (examData: Partial<ExamBase>) => async (dispatch: Dispatch) => {
    dispatch(setLoading(true));
    try {
      // Generate clientId if not provided
      const clientId = examData.clientId || Guid.create().toString();

      // Prepare the exam data
      const newExam: CreateUpdateExamDto = {
        title: examData.title || 'New Exam',
        description: examData.description || '',
        examCode: examData.examCode,
        examType: examData.examType || 0,
        examPeriod: examData.examPeriod,
        examDate: examData.examDate,
        duration: examData.duration,
        totalScore: examData.totalScore,
        status: examData.status || 0,
        subjectId: examData.subjectId,
        gradeId: examData.gradeId,
        sourceType: examData.sourceType || 0,
        sections: examData.sections?.map(mapSectionToDto) || [],
      };

      const response = await axiosClient.post(API_ENDPOINTS.EXAMS, newExam);

      // Add the new exam to the store
      dispatch(
        addExam({
          ...response.data,
          clientId: response.data.id || clientId,
        })
      );
      dispatch(
        setCurrentExam({
          ...response.data,
        })
      );

      dispatch(setError(null));
      return response.data.id;
    } catch (error) {
      console.error('Error creating exam:', error);
      dispatch(setError('Failed to create exam. Please try again.'));
      throw error;
    } finally {
      dispatch(setLoading(false));
    }
  };

// [PUT] /api/exams
// Update an existing exam
export const updateExamData =
  (examId: string, examData: Partial<ExamBase>) =>
  async (dispatch: Dispatch) => {
    dispatch(setLoading(true));
    try {
      // Prepare the exam data for update
      const examToUpdate: CreateUpdateExamDto = {
        id: examId,
        title: examData.title || 'Untitled Exam',
        description: examData.description || '',
        examCode: examData.examCode,
        examType: examData.examType || 0,
        examPeriod: examData.examPeriod,
        examDate: examData.examDate,
        duration: examData.duration,
        totalScore: examData.totalScore,
        status: examData.status || 0,
        subjectId: examData.subjectId,
        gradeId: examData.gradeId,
        sourceType: examData.sourceType || 0,
        sections: examData.sections?.map(mapSectionToDto) || [],
      };

      const response = await axiosClient.put(
        `${API_ENDPOINTS.EXAMS}/${examId}`,
        examToUpdate
      );

      // Update the exam in the store
      dispatch(
        updateExam({
          ...response.data,
          clientId: response.data.id,
        })
      );

      dispatch(setError(null));
      return response.data;
    } catch (error) {
      console.error(`Error updating exam with ID ${examId}:`, error);
      dispatch(setError(`Failed to update exam. Please try again.`));
      throw error;
    } finally {
      dispatch(setLoading(false));
    }
  };

// Delete an exam
export const deleteExam = (examId: string) => async (dispatch: Dispatch) => {
  dispatch(setLoading(true));
  try {
    await axiosClient.delete(`${API_ENDPOINTS.EXAMS}/${examId}`);

    // Remove the exam from the store
    dispatch(removeExamAction(examId));

    dispatch(setError(null));
    return true;
  } catch (error) {
    console.error(`Error deleting exam with ID ${examId}:`, error);
    dispatch(setError(`Failed to delete exam. Please try again.`));
    return false;
  } finally {
    dispatch(setLoading(false));
  }
};

// Duplicate an exam
export const duplicateExam =
  (examId: string, newTitle: string) => async (dispatch: Dispatch) => {
    dispatch(setLoading(true));
    try {
      const response = await axiosClient.post(
        `${API_ENDPOINTS.EXAMS}/${examId}/duplicate`,
        { newTitle }
      );

      // Add the duplicated exam to the store
      dispatch(
        addExam({
          ...response.data,
          clientId: response.data.id,
        })
      );

      dispatch(setError(null));
      return response.data.id;
    } catch (error) {
      console.error(`Error duplicating exam with ID ${examId}:`, error);
      dispatch(setError(`Failed to duplicate exam. Please try again.`));
      throw error;
    } finally {
      dispatch(setLoading(false));
    }
  };

// Map ExamGroupQuestion to CreateUpdateGroupQuestionDto
const mapGroupQuestionToDto = (
  groupQuestion: ExamGroupQuestion
): CreateUpdateGroupQuestionDto => {
  return {
    id: groupQuestion.id,
    clientId: groupQuestion.clientId,
    groupQuestionId: groupQuestion.groupQuestionId,
    content: groupQuestion.content || '',
    contentFormat: groupQuestion.contentFormat || ContentFormatType.Html,
    instructions: groupQuestion.instructions || '',
    questionIds: groupQuestion.questionIds || [],
    questions: groupQuestion.questions?.map(mapQuestionToDto) || [],
    idempotentKey: groupQuestion.idempotentKey || '',
    order: groupQuestion.order || 0,
  };
};

// Map ExamSection to CreateUpdateExamSectionDto
const mapSectionToDto = (section: ExamSection): CreateUpdateExamSectionDto => {
  return {
    id: section.id,
    clientId: section.clientId,
    title: section.title,
    content: section.content,
    contentFormat: section.contentFormat,
    orderIndex: section.orderIndex || 0,
    sectionScore: section.sectionScore,
    instructions: section.instructions,
    questions: section.questions.map(mapQuestionToDto),
    groupQuestions: section.groupQuestions?.map(mapGroupQuestionToDto) || [],
  };
};

// Map ExamQuestion to CreateUpdateQuestionDto
const mapQuestionToDto = (question: ExamQuestion): CreateUpdateQuestionDto => {
  return {
    id: question.id,
    clientId: question.clientId,
    title: question.title,
    content: question.content,
    contentFormat: question.contentFormat || ContentFormatType.Html,
    questionType: question.questionType,
    difficulty: question.difficulty || 1,
    comment: question.comment,
    subjectId: question.subjectId,
    gradeId: question.gradeId,
    shuffleOptions: question.shuffleOptions || false,
    explanation: question.explanation,
    sourceType: question.sourceType || 0,
    topics: Array.isArray(question.topics)
      ? question.topics.join(',')
      : question.topics,
    tags: Array.isArray(question.tags)
      ? question.tags.join(',')
      : question.tags,
    options: question.options ? mapOptionsToDto(question.options) : undefined,
    matchingItems: question.matchingItems
      ? mapMatchingItemsToDto(question.matchingItems)
      : undefined,
    matchingAnswers: question.matchingAnswers
      ? mapMatchingAnswersToDto(question.matchingAnswers)
      : undefined,
    fillInBlankAnswers: question.fillInBlankAnswers
      ? mapFillInBlankAnswersToDto(question.fillInBlankAnswers)
      : undefined,
    syncQuestion: question.syncQuestion,
    lastSyncQuestionId: question.lastSyncQuestionId,
    order: question.order || 0,
    score: question.score,
    questionId: question.questionId,
    status: question.statusEntity || ExamStatus.Draft,
    correctAnswer: question.correctAnswer,
  };
};

// Map ExamQuestionOption[] to CreateQuestionOptionDto[]
const mapOptionsToDto = (
  options: ExamQuestionOption[]
): CreateQuestionOptionDto[] => {
  return options.map((option) => ({
    id: option.id,
    clientId: option.clientId,
    content: option.content,
    contentFormat: option.contentFormat,
    isCorrect: option.isCorrect,
    order: option.order,
    explanation: option.explanation || '', // Ensure explanation is never undefined
    score: option.score,
  }));
};

// Map ExamMatchingItem[] to CreateUpdateMatchingItemDto[]
const mapMatchingItemsToDto = (
  items: ExamMatchingItem[]
): CreateUpdateMatchingItemDto[] => {
  return items.map((item) => ({
    id: item.id,
    clientId: item.clientId,
    type: item.type,
    content: item.content,
    contentFormat: item.contentFormat,
    order: item.order,
  }));
};

// Map ExamMatchingAnswer[] to CreateUpdateMatchingAnswerDto[]
const mapMatchingAnswersToDto = (
  answers: ExamMatchingAnswer[]
): CreateUpdateMatchingAnswerDto[] => {
  return answers.map((answer) => ({
    id: answer.id,
    clientId: answer.clientId,
    leftItemId: answer.premiseId,
    rightItemId: answer.responseId,
  }));
};

// Map ExamFillInBlankAnswer[] to CreateUpdateFillInBlankAnswer[]
const mapFillInBlankAnswersToDto = (
  answers: ExamFillInBlankAnswer[]
): CreateUpdateFillInBlankAnswer[] => {
  return answers.map((answer) => ({
    id: answer.id,
    clientId: answer.clientId,
    blankIndex: answer.blankIndex,
    correctAnswers: answer.correctAnswers,
    caseSensitive: answer.caseSensitive,
    feedback: answer.feedback || '', // Ensure feedback is never undefined
    score: answer.score,
  }));
};

// Add this new function to update exam status
export const updateExamStatus =
  (examId: string, status: ExamStatus) => async (dispatch: Dispatch) => {
    dispatch(setLoading(true));
    try {
      // Call the API with the correct endpoint and method
      await axiosClient.put(`${API_ENDPOINTS.EXAMS}/exam-status/${examId}`, {
        examId: examId,
        newStatus: status,
      });
      dispatch(setError(null));
      return {
        id: examId,
        status: status,
      };
    } catch (error) {
      console.error(`Error updating exam status with ID ${examId}:`, error);
      dispatch(setError(`Failed to update exam status. Please try again.`));
      throw error;
    } finally {
      dispatch(setLoading(false));
    }
  };

export default {
  fetchExams,
  fetchExamById,
  createExam,
  updateExamData,
  deleteExam,
  duplicateExam,
  handlePageChange,
  handleFilterChange,
  handleSortChange,
  updateExamStatus,
};
