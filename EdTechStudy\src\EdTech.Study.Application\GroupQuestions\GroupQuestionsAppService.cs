using System;
using System.Linq;
using System.Threading.Tasks;
using EdTech.Study.GroupQuestions.Services;
using EdTech.Study.Questions;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using System.Collections.Generic;
using Volo.Abp.Uow;

namespace EdTech.Study.GroupQuestions
{
    public class GroupQuestionsAppService : CrudAppService<GroupQuestionEntity, GroupQuestionDto, Guid, PagedAndSortedResultRequestDto, CreateUpdateGroupQuestionDto, CreateUpdateGroupQuestionDto>, IGroupQuestionsAppService
    {
        public GroupQuestionsAppService(IRepository<GroupQuestionEntity, Guid> repository) : base(repository)
        {
        }

        //[UnitOfWork]
        //public override async Task<GroupQuestionDto> UpdateAsync(Guid id, CreateUpdateGroupQuestionDto input)
        //{
        //    var groupQuestion = (await Repository
        //    .WithDetailsAsync(x => x.Questions ?? Enumerable.Empty<QuestionEntity>())
        //    ).FirstOrDefault(x => x.Id == id);

        //    if (groupQuestion == null)
        //    {
        //        throw new EntityNotFoundException(typeof(GroupQuestionEntity), id);
        //    }

        //    var questions = groupQuestion.Questions?.ToList() ?? new List<QuestionEntity>();
        //    var questionIds = questions.Select(x => x.Id).ToList();

        //    var deletedQuestions = questionIds.Where(x => !(input.Questions ?? Enumerable.Empty<EdTech.Study.Exams.Dtos.CreateUpdateExamQuestionDto>()).Any(y => y.QuestionId == x)).ToList();

        //    // Update basic properties of GroupQuestion
        //    groupQuestion.Content = input.Content;
        //    groupQuestion.ContentFormat = input.ContentFormat;
        //    groupQuestion.Instructions = input.Instructions;
        //    groupQuestion.IdempotentKey = input.IdempotentKey;

        //    // Remove questions that were deleted
        //    if (deletedQuestions.Any())
        //    {
        //        foreach (var questionId in deletedQuestions)
        //        {
        //            var questionToRemove = questions.FirstOrDefault(q => q.Id == questionId);
        //            if (questionToRemove != null)
        //            {
        //                questions.Remove(questionToRemove);
        //            }
        //        }
        //    }

        //    // Update existing questions and add new ones
        //    if (input.Questions != null)
        //    {
        //        foreach (var questionDto in input.Questions)
        //        {
        //            if (questionDto.QuestionId.HasValue && questionIds.Contains(questionDto.QuestionId.Value))
        //            {
        //                // Update existing question
        //                var existingQuestion = questions.FirstOrDefault(q => q.Id == questionDto.QuestionId.Value);
        //                if (existingQuestion != null)
        //                {
        //                    // Only update if not synced with template
        //                    if (!questionDto.SyncQuestion)
        //                    {
        //                        existingQuestion.Title = questionDto.Title;
        //                        existingQuestion.Content = questionDto.Content;
        //                        existingQuestion.ContentFormat = questionDto.ContentFormat;
        //                        existingQuestion.QuestionType = questionDto.QuestionType;
        //                        existingQuestion.Difficulty = questionDto.Difficulty;
        //                        existingQuestion.SubjectId = questionDto.SubjectId;
        //                        existingQuestion.GradeId = questionDto.GradeId;
        //                        existingQuestion.ShuffleOptions = questionDto.ShuffleOptions;
        //                        existingQuestion.Explanation = questionDto.Explanation;
        //                        existingQuestion.Topics = questionDto.Topics;
        //                        existingQuestion.Tags = questionDto.Tags;
        //                        // Options are not updated here as they would need their own repository
        //                    }
        //                }
        //            }
        //            else if (!questionDto.QuestionId.HasValue || !questionIds.Contains(questionDto.QuestionId.Value))
        //            {
        //                // Create new question
        //                var newQuestion = new QuestionEntity
        //                {
        //                    Title = questionDto.Title,
        //                    Content = questionDto.Content,
        //                    ContentFormat = questionDto.ContentFormat,
        //                    QuestionType = questionDto.QuestionType,
        //                    Difficulty = questionDto.Difficulty,
        //                    SubjectId = questionDto.SubjectId,
        //                    GradeId = questionDto.GradeId,
        //                    ShuffleOptions = questionDto.ShuffleOptions,
        //                    Explanation = questionDto.Explanation,
        //                    SourceType = questionDto.SourceType,
        //                    Topics = questionDto.Topics,
        //                    Tags = questionDto.Tags,
        //                    GroupQuestionId = groupQuestion.Id,
        //                    Status = Enum.ExamStatus.Draft,
        //                    // Options are not set here as they would need to be created separately
        //                };

        //                questions.Add(newQuestion);
        //            }
        //        }
        //    }
        //    return ObjectMapper.Map<GroupQuestionEntity, GroupQuestionDto>(await Repository.UpdateAsync(groupQuestion));
        //}
    }
}
