using System;
using System.Collections.Generic;

namespace EdTech.Study.Questions.Dtos
{
    public class BatchAssignQuestionsDto
    {
        public Guid UserId { get; set; }
        public DateTime? DueDate { get; set; }
        
        // Danh sách ID câu hỏi cần gán (nếu gán theo ID)
        public List<Guid>? QuestionIds { get; set; }
        
        // Điều kiện lọc
        public Guid? SubjectId { get; set; }
        public Guid? GradeId { get; set; }
        
        // Cờ điều kiện: true - gán tất cả, false - chỉ gán những câu hỏi chưa được gán
        public bool AssignAll { get; set; }
    }
}