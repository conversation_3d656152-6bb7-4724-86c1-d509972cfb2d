import { ExamSourceType, ExamStatus } from '../interfaces/exams/examEnums';
import { IQuestion } from '../interfaces/questions/question';
import { BaseQuestion, QuestionType } from '../interfaces/quizs/questionBase';
import { axiosClient } from '../services/axiosClient';

import { camelCaseKeys } from '../utils/parsers';

// Generic API response interface
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

// Helper functions for OData filtering
function getQuestionTypeFilter(type: QuestionType): string {
  const typeName = QuestionType[type];
  if (typeName === undefined) {
    return `1 eq 1`;
  }
  return `QuestionType eq cast('${typeName}', 'EdTech.Study.Questions.QuestionType')`;
}

// function getQuestionTypeName(type: QuestionType): string {
//   return QuestionType[type];
// }

function getQuestionStatusFilter(status: ExamStatus): string {
  const statusName = ExamStatus[status];
  if (statusName === undefined) {
    // console.error(`Invalid QuestionStatus value: ${status}`);
    // Return a filter that will match all items (effectively removing the filter)
    return `1 eq 1`;
  }
  return `Status eq cast('${statusName}', 'EdTech.Study.Enum.ExamStatus')`;
}

function getQuestionSourceFilter(source: ExamSourceType): string {
  const sourceName = ExamSourceType[source];
  if (sourceName === undefined) {
    // console.error(`Invalid ExamSourceType value: ${source}`);
    // Return a filter that will match all items (effectively removing the filter)
    return `1 eq 1`;
  }
  return `SourceType eq cast('${sourceName}', 'EdTech.Study.Exams.ExamSourceType')`;
}


// Helper function to format GUIDs for OData queries
function formatGuid(guid: string): string {
  // OData requires GUIDs to be formatted with single quotes
  return `${guid}`;
}

// Updated interface to match QuestionDraftEntity.cs
export interface QuestionDraft {
  id?: string;
  content: string;
  contentFormat: string;
  type: QuestionType;
  difficultyLevel: number;
  description?: string;
  topics?: string;
  tags?: string;
  status: ExamStatus;
  comment?: string;
  idempotentKey?: string;
  source?: string;
  subjectId?: string;
  subject?: {
    id: string;
    code: string;
    name: string;
  };
  options?: QuestionOptionDraft[];
  extraProperties?: Record<string, any>;
  concurrencyStamp?: string;
  creationTime?: string;
  creatorId?: string;
  lastModificationTime?: string;
  lastModifierId?: string;
}

// Interface for question options matching QuestionOptionDraftEntity.cs
export interface QuestionOptionDraft {
  id?: string;
  content: string;
  contentFormat: string;
  isCorrect: boolean;
  order: number;
  explanation?: string;
  questionDraftId?: string;
}

export interface QuestionDraftParams {
  page?: number;
  pageSize?: number;
  sortField?: string;
  sortOrder?: string;
  searchText?: string;
  questionType?: QuestionType | QuestionType[];
  status?: ExamStatus | ExamStatus[];
  subjectId?: string;
  subjectIds?: string[];
  lessonGradeIds?: string[];
  assignedUserIds?: string[];
  sourceTypes?: ExamSourceType[];
  [key: string]: any;
}

// Interface for updating questions
export interface UpdateQuestionsRequest {
  questions: QuestionDraft[];
}

export interface BatchAssignQuestionsDto {
  userId: string;
  dueDate?: string; // ISO string format, e.g., "2025-05-27T00:00:00Z"
  questionIds?: string[];
  subjectId?: string;
  gradeId?: string;
  assignAll: boolean;
}

export interface BatchAssignResultDto {
  userId: string;
  assignedCount: number;
  assignedQuestionIds: string[];
  dueDate?: string;
}

export interface BatchUnassignQuestionsDto {
  questionIds: string[];
}

export interface BatchUnassignResultDto {
  unassignedCount: number;
  unassignedQuestionIds: string[];
}

class QuestionDraftApi {
  // Map for translating frontend field names to backend OData entity property names
  private fieldMapping: Record<string, string> = {
    title: 'Content',
    points: 'DifficultyLevel',
    status: 'Status',
    questionTypes: 'QuestionType',
    // Add more mappings as needed
  };

  // Common OData parameters to include Subject in all requests
  private getCommonODataParams(): Record<string, string> {
    return {
      $expand: 'Subject,Grade',
      $count: 'true',
    };
  }

  public async getQuestions(params: QuestionDraftParams) {
    // Use standard OData parameters for pagination
    const skip =
      params.page && params.pageSize ? (params.page - 1) * params.pageSize : 0;
    const top = params.pageSize || 10;

    // Build sorting
    let orderby = '';
    if (params.sortField) {
      // Map frontend field name to backend property name if needed
      let backendFieldName = params.sortField;
      if (this.fieldMapping[backendFieldName]) {
        backendFieldName = this.fieldMapping[backendFieldName];
      }

      // Convert sortOrder from frontend format to OData format
      const sortDir = params.sortOrder || 'asc';
      orderby = `${backendFieldName} ${sortDir}`;

      // console.log(`Applying sort: ${orderby}`); // Debug log
    }

    // Build filtering
    const filters = [];

    // Add search text filter if provided
    if (params.searchText) {
      filters.push(
        `contains(Title,'${params.searchText}') or contains(Content,'${params.searchText}')`
      );
    }

    // Add type filter if provided
    if (params.questionTypes && params.questionTypes !== undefined) {
      if (Array.isArray(params.questionTypes)) {
        const typeFilters = params.questionTypes
          .map((t) => getQuestionTypeFilter(t))
          .join(' or ');
        filters.push(`(${typeFilters})`);
      } else {
        filters.push(getQuestionTypeFilter(params.questionTypes));
      }
    }

    // Add status filter if provided
    if (params.status && params.status !== undefined) {
      if (Array.isArray(params.status)) {
        const statusFilters = params.status
          .map((s) => getQuestionStatusFilter(s))
          .join(' or ');
        if (statusFilters && statusFilters != '') {
          filters.push(`(${statusFilters})`);
        }
      } else {
        filters.push(getQuestionStatusFilter(params.status));
      }
    }

    // Add source type filter if provided
    if (params.sourceTypes && params.sourceTypes !== undefined) {
      if (Array.isArray(params.sourceTypes)) {
        const sourceTypeFilters = params.sourceTypes
          .map((s) => getQuestionSourceFilter(s))
          .join(' or ');
        filters.push(`(${sourceTypeFilters})`);
      } else {
        filters.push(getQuestionSourceFilter(params.sourceTypes));
      }
    }


    // Add single subject filter if provided
    if (params.subjectId) {
      filters.push(`SubjectId eq ${formatGuid(params.subjectId)}`);
    }

    // Add multiple subjects filter if provided
    if (params.subjectIds && params.subjectIds.length > 0) {
      // Skip if we already have a single subject filter
      if (!params.subjectId) {
        if (params.subjectIds.length === 1) {
          // For a single subject ID, use simple equality
          filters.push(`SubjectId eq ${formatGuid(params.subjectIds[0])}`);
        } else {
          // For multiple subject IDs, use OR condition
          const subjectFilters = params.subjectIds
            .map((id) => `SubjectId eq ${formatGuid(id)}`)
            .join(' or ');
          filters.push(`(${subjectFilters})`);
        }
      }
    }

    // Add lesson grade filter if provided
    if (params.lessonGradeIds && params.lessonGradeIds.length > 0) {
      const gradeFilters = params.lessonGradeIds
        .map((id) => `GradeId eq ${formatGuid(id)}`)
        .join(' or ');
      filters.push(`(${gradeFilters})`);
    }

    // Add assigned user filter if provided
    if (params.assignedUserIds && params.assignedUserIds.length > 0) {
      const assignedUserFilters = params.assignedUserIds
        .map((id) => `AssignedUserId eq ${formatGuid(id)}`)
        .join(' or ');
      filters.push(`(${assignedUserFilters})`);
    }

    // Combine all filters with AND operator
    const filter = filters.length > 0 ? filters.join(' and ') : '';

    // Build the request parameters
    const requestParams: Record<string, any> = {
      ...this.getCommonODataParams(),
      $skip: skip,
      $top: top,
    };

    if (orderby) {
      requestParams['$orderby'] = orderby;
    }

    if (filter) {
      requestParams['$filter'] = filter;
    }

    try {
      // console.log('Sending OData request with params:', requestParams); // Debug log

      const response = await axiosClient({
        method: 'get',
        url: `/odata/questions`,
        params: requestParams,
      });

      // Extract total count and items from OData response
      const total = response.data['@odata.count'] || response.data.length || 0;
      const items = camelCaseKeys(
        response.data.value || response.data || []
      ) as BaseQuestion[];

      return {
        success: true,
        data: items,
        total: total,
      };
    } catch (error) {
      return {
        success: false,
        data: [],
        total: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  public async getQuestionById(id: string) {
    try {
      const response = await axiosClient({
        method: 'get',
        url: `/odata/questions/${id}`,
        params: this.getCommonODataParams(),
      });

      return {
        success: true,
        data: camelCaseKeys(response.data),
      };
    } catch (error) {
      console.error(`Error fetching question with id ${id}:`, error);
      return {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  public async getPaginationInfo() {
    const response = await axiosClient({
      method: 'get',
      url: `/odata/questions/$count`,
    });

    return {
      success: true,
      data: {
        totalCount: response.data,
      },
    };
  }

  /**
   * Cập nhật nhiều câu hỏi cùng lúc
   * @param questions Danh sách câu hỏi cần cập nhật
   * @returns Kết quả trả về từ API
   */
  public async updateQuestions(questions: QuestionDraft[]) {
    try {
      const response = await axiosClient({
        method: 'post',
        url: `/api/odata/questionDrafts/update-list-question`,
        data: {
          questions: questions,
        } as UpdateQuestionsRequest,
      });

      return {
        success: true,
        data: response.data.data,
      };
    } catch (error) {
      console.error('Error updating questions:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  // Thêm mới một câu hỏi
  /**
   * Thêm mới một câu hỏi
   * @param question Câu hỏi cần thêm
   * @returns Kết quả trả về từ API
   */
  public async createQuestion(question: IQuestion) {
    // url post: /api/app/question-app
    try {
      const response = await axiosClient({
        method: 'post',
        url: `/api/app/question-app`,
        data: question,
      });

      return {
        success: true,
        data: response.data,
      };
    } catch (error) {
      console.error('Error creating question:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Cập nhật một câu hỏi
   * @param question Câu hỏi cần cập nhật
   * @returns Kết quả trả về từ API
   */
  public async updateQuestion(question: IQuestion) {
    try {
      const response = await axiosClient({
        method: 'put',
        url: `/api/app/question-app/${question.id}`,
        data: question,
      });

      return {
        success: true,
        data: response.data,
      };
    } catch (error) {
      console.error('Error updating questions:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  public async deleteQuestions(questionIds: string[]) {
    try {
      const response = await axiosClient({
        method: 'delete',
        url: `/api/app/question-app/many`,
        data: questionIds,
      });

      return {
        success: true,
        data: response.data.data,
      };
    } catch (error) {
      console.error('Error updating questions:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Lấy thông tin của một câu hỏi
   * @param questionId Id của câu hỏi
   * @returns Thông tin của câu hỏi
   */
  public async getQuestionInfo(questionId: string) {
    try {
      const response = await axiosClient({
        method: 'get',
        url: `/odata/questions/${questionId}?$expand=Subject,Grade,Options,FillInBlankAnswers,MatchingAnswers,MatchingItems,GroupQuestion`,
      });

      return {
        success: true,
        data: response.data,
      };
    } catch (error) {
      console.error('Error getting question info:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Cập nhật trạng thái của câu hỏi
   * @param questionId Id của câu hỏi
   * @param status Trạng thái mới
   * @returns Kết quả trả về từ API
   */
  public async updateQuestionStatus(questionId: string, status: ExamStatus) {
    try {
      const response = await axiosClient({
        method: 'put',
        url: `/api/app/question-app/${questionId}/status`,
        data: {
          status: ExamStatus[status],
        },
      });

      return {
        success: true,
        data: response.data,
      };
    } catch (error) {
      console.error('Error updating question status:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Cập nhật trạng thái của nhiều câu hỏi cùng lúc
   * @param questionIds Danh sách ID của các câu hỏi
   * @param status Trạng thái mới
   * @returns Kết quả trả về từ API
   */
  public async updateQuestionsStatus(
    questionIds: string[],
    status: ExamStatus
  ) {
    try {
      const response = await axiosClient({
        method: 'put',
        url: `/api/app/question-app/batch-status`,
        data: {
          ids: questionIds,
          status: ExamStatus[status],
        },
      });

      return {
        success: true,
        data: response.data,
      };
    } catch (error) {
      console.error('Error updating questions status:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Lấy thông tin đề thi dựa vào questionId
   * @param questionId Id của câu hỏi
   * @returns Thông tin của đề thi chứa câu hỏi
   */
  public async getExamInfoByQuestionId(questionId: string) {
    try {
      const response = await axiosClient({
        method: 'get',
        url: `/api/app/question-app/${questionId}/exams-by-question-id`,
      });

      return {
        success: true,
        data: response.data,
      };
    } catch (error) {
      console.error('Error getting exam info for question:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Lấy thông tin nhóm câu hỏi dựa vào questionId
   * @param questionId Id của câu hỏi
   * @returns Thông tin của nhóm câu hỏi chứa câu hỏi
   */
  public async getGroupInfoByQuestionId(questionId: string) {
    try {
      const response = await axiosClient({
        method: 'get',
        url: `/api/app/question-app/group-question-by-question-id/${questionId}`,
      });

      return {
        success: true,
        data: response.data,
      };
    } catch (error) {
      console.error('Error getting group info for question:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Cập nhật thông tin nhóm câu hỏi
   * @param groupInfo Thông tin nhóm câu hỏi cần cập nhật
   * @returns Kết quả trả về từ API
   */
  public async updateGroupInfo(groupInfo: {
    id: string;
    content?: string;
    instructions?: string;
    order?: number;
  }) {
    try {
      const response = await axiosClient({
        method: 'put',
        url: `/api/app/question-app/group-question/${groupInfo.id}`,
        data: groupInfo,
      });

      return {
        success: true,
        data: response.data,
      };
    } catch (error) {
      console.error('Error updating group info:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Batch assigns questions to a specific user
   * @param assignData The assignment data including userId, questionIds, etc.
   * @returns API response with assignment results
   */
  public async batchAssignQuestionsToUser(
    assignData: BatchAssignQuestionsDto
  ): Promise<ApiResponse<BatchAssignResultDto>> {
    try {
      const response = await axiosClient({
        method: 'post',
        url: `/api/app/question-app/batch-assign-to-user`,
        data: assignData,
      });

      return {
        success: true,
        data: response.data,
      };
    } catch (error) {
      console.error('Error assigning questions to user:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Batch unassigns questions from users
   * @param unassignData The data containing question IDs to unassign
   * @returns API response with unassignment results
   */
  public async batchUnassignQuestions(
    questionIds: string[]
  ): Promise<ApiResponse<BatchUnassignResultDto>> {
    try {
      const response = await axiosClient({
        method: 'post',
        url: `/api/app/question-app/batch-unassign-questions`,
        data: questionIds,
      });

      return {
        success: true,
        data: response.data,
      };
    } catch (error) {
      console.error('Error unassigning questions:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
}

export default new QuestionDraftApi();
