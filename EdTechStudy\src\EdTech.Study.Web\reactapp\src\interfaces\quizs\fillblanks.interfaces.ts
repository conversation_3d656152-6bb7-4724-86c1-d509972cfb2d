import {
  QuestionComponentBaseP<PERSON>,
  BaseQuestion,
  BaseAnswer,
} from './questionBase';

// Interface for FillBlanks questions
export interface FillBlanksQuestion extends BaseQuestion {
  type: 'fillblanks';
  content: string;
  blanks: FillBlankItem[];
  caseSensitive?: boolean;
  showHints?: boolean;
  explanation?: string;
  points?: number;
  options?: FillBlanksAnswer[];
  userSelect?: FillBlanksAnswer;
}

// Interface for each blank in the question
export interface FillBlankItem {
  clientId: string;
  id?: string;
  correctAnswer: string;
  alternativeAnswers?: string[];
  hint?: string;
  userAnswer?: string; // User's input for this blank
  isCorrect?: boolean; // Whether the user's answer is correct
  order?: number;
}

// Interface for tracking user selections/inputs
export interface FillBlanksAnswer extends BaseAnswer {
  answers: { [blankId: string]: string };
}

// Props for the FillBlanks component
export interface FillBlanksComponentProps extends QuestionComponentBaseProps {
  question: FillBlanksQuestion;
  allowManyTimes?: boolean;
  disabled?: boolean;
}
