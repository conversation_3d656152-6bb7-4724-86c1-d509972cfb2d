import {
  BaseAnswer,
  BaseQuestion,
  MatchingQuestion,
  MatchingQuestionAnswer,
} from '../../../interfaces/quizs/questionBase';

/**
 * Check if the user's answers for matching question are correct
 * @param question
 * @param userSelect
 * @returns
 */
export const matchingCheckCorrectAnswer = (
  question: BaseQuestion,
  userSelect?: BaseAnswer | BaseAnswer[]
) => {
  if (!userSelect || !Array.isArray(userSelect) || userSelect.length === 0)
    return null;

  const matchingQuestion = question as MatchingQuestion;
  const userAnswers = userSelect as MatchingQuestionAnswer[];

  // If there are no left items or right items, return null
  if (!matchingQuestion.leftItems || !matchingQuestion.rightItems) return null;

  // Check if all user answers are correct
  const allCorrect = userAnswers.every((answer) => {
    const leftItem = matchingQuestion.leftItems?.find(
      (item) => item.id === answer.left
    );
    return leftItem?.matchId === answer.right;
  });

  // Check if all left items have been matched
  const allMatched = matchingQuestion.leftItems.every((leftItem) =>
    userAnswers.some((answer) => answer.left === leftItem.id)
  );

  // Return true only if all answers are correct and all items are matched
  return allCorrect && allMatched;
};
