﻿using EdTech.Study.Grade;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.EntityFrameworkCore;

namespace EdTech.Study.EntityFrameworkCore
{
    public interface IStudyCategoryDbContext : IEfCoreDbContext
    {
        DbSet<Subject.Subject> Subjects { get; }

        DbSet<LessonGrade> LessonGrades { get; }
    }
}
