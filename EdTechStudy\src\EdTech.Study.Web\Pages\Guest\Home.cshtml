@page "/home"
@model EdTech.Study.Web.Pages.Guest.HomeModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <title>EdTech Platform</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link href="~/css/home/<USER>" rel="stylesheet" />
    <link href="~/libs/bootstrap/css/bootstrap.css" rel="stylesheet" />
    <link href="~/libs/tsp/jslibs/fluentui/fluentui-system-icons/fonts/FluentSystemIcons-Regular.css" rel="stylesheet" />
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <img class="logo-img" alt="Mask group" src="./images/home/<USER>" />
                <div class="logo-text">EDTECH</div>
            </div>

            <nav class="nav-menu">
                <a href="#" class="nav-item active">
                    <i class="icon icon-ic_fluent_home_20_regular"></i>
                    <span>TRANG CHỦ</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="icon icon-ic_fluent_book_24_regular"></i>
                    <span>BÀI GIẢNG</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="icon icon-ic_fluent_notepad_edit_16_regular"></i>
                    <span>BÀI TẬP</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="icon icon-ic_fluent_news_16_regular"></i>
                    <span>BÀI VIẾT</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="icon icon-ic_fluent_call_48_regular"></i>
                    <span>LIÊN HỆ</span>
                </a>
            </nav>

            <div class="auth-section">
                <div class="image-container">
                    <img class="auth-img" alt="User" src="./images/home/<USER>" />
                </div>
                <a href="#" class="auth-link">Đăng nhập</a>
                <div class="separator"></div>
                <a href="#" class="auth-link">Đăng ký</a>
            </div>
        </div>
    </header>

    <main>
        <section class="hero">
            <h1>Hệ thống giáo dục thông minh<br>Đa dạng bài giảng, mô phỏng sinh động, giảng dạy hiệu quả</h1>
            <p>Hệ thống mang đến kho bài giảng đa dạng, mô phỏng sinh động và công cụ hỗ trợ giảng dạy hiện đại, giúp học sinh tiếp thu kiến thức dễ dàng và giáo viên nâng cao hiệu quả giảng dạy.</p>
            <button class="cta-button">
                THỬ NGAY
                <img src="/icon-9.svg" alt="Arrow" />
            </button>
        </section>

        <section class="subjects">
            <h2>Đa Dạng Bài Giảng Của Tất Cả Môn Học</h2>
            <div class="separator-line"></div>
            <p>Chúng tôi cung cấp kho bài giảng phong phú, đa dạng với đầy đủ tất cả các môn học, giúp dễ dàng tiếp cận kiến thức, nâng cao hiệu quả học tập và chinh phục mọi mục tiêu.</p>

            <div class="subjects-grid">
                <div class="subjects-row">
                    <div class="subject-card math">
                        <img src="./images/home/<USER>/math.png" alt="Toán Học" />
                        <h4>Toán Học</h4>
                        <div class="arrow-button">
                            <i class="icon icon-ic_fluent_chevron_down_12_regular"></i>
                        </div>
                    </div>
                    <div class="subject-card literature">
                        <img src="./images/home/<USER>/literature.png" alt="Ngữ Văn" />
                        <h4>Ngữ Văn</h4>
                        <div class="arrow-button">
                            <i class="icon icon-ic_fluent_chevron_down_12_regular"></i>
                        </div>
                    </div>
                    <div class="subject-card language">
                        <img src="./images/home/<USER>/language.png" alt="Ngoại Ngữ" />
                        <h4>Ngoại Ngữ</h4>
                        <div class="arrow-button">
                            <i class="icon icon-ic_fluent_chevron_down_12_regular"></i>

                        </div>
                    </div>
                </div>
                <div class="subjects-row">
                    <div class="subject-card science">
                        <img src="./images/home/<USER>/naturalscience.png" alt="Khoa Học Tự Nhiên" />
                        <h4>Khoa Học Tự Nhiên</h4>
                        <div class="arrow-button">
                            <i class="icon icon-ic_fluent_chevron_down_12_regular"></i>
                        </div>
                    </div>
                    <div class="subject-card social">
                        <img src="./images/home/<USER>/socialscience.png" alt="Khoa Học Xã Hội" />
                        <h4>Khoa Học Xã Hội</h4>
                        <div class="arrow-button">
                            <i class="icon icon-ic_fluent_chevron_down_12_regular"></i>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="courses">
            <h2>Bài Giảng Dễ Hiểu</h2>
            <div class="separator-line"></div>
            <p>Hệ thống mô phỏng bài giảng được thiết kế sinh động, trực quan với giao diện thân thiện, giúp người học dễ dàng tiếp cận và hiểu sâu kiến thức một cách nhanh chóng, hiệu quả, biến việc học trở nên thú vị và dễ dàng hơn bao giờ hết.</p>

            <div class="courses-grid">
                <div class="course-card">
                    <img src="/image-12.png" alt="Course thumbnail" class="course-image" />
                    <div class="course-tags">
                        <span class="tag subject-tag">
                            <img src="/icon-7.svg" alt="Subject icon" />
                            KHTN
                        </span>
                        <span class="tag grade-tag">LỚP 6</span>
                    </div>
                    <h3>Bài 4: Sơ lược về bảng tuần hoàn các nguyên tố hóa học</h3>
                    <div class="course-stats">
                        <div class="stat">
                            <img src="/icon-1.svg" alt="Game icon" />
                            <span>1 trò chơi</span>
                        </div>
                        <div class="stat">
                            <img src="/icon-4.svg" alt="Exercise icon" />
                            <span>3 bài tập</span>
                        </div>
                    </div>
                    <button class="course-button">
                        <span>Vào học</span>
                        <img src="/icon-9.svg" alt="Arrow icon" />
                    </button>
                </div>
                <!-- Repeat course-card div 3 more times with different content -->
            </div>
        </section>

        <section class="statistics">
            <div class="container">
                <div class="stat-item">
                    <div class="stat-icon">
                        <img src="/image-8-1.png" alt="Students" />
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">1500+</div>
                        <div class="stat-label">Học Sinh</div>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon">
                        <img src="/image-14.png" alt="Teachers" />
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">150+</div>
                        <div class="stat-label">Giáo Viên</div>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon">
                        <img src="/image-15.png" alt="Lessons" />
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">500+</div>
                        <div class="stat-label">Bài Giảng</div>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon">
                        <img src="/image-16.png" alt="Exercises" />
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">1000+</div>
                        <div class="stat-label">Bài Luyện Tập</div>
                    </div>
                </div>
            </div>
        </section>

        <section class="exams">
            <h2>Bài tập</h2>
            <div class="separator-line"></div>
            <div class="p-4 bg-light">
                <div class="container-xl mx-auto">
                    <div class="row g-4">
                        <div class="col-12 col-md-6 col-lg-3">
                            <div class="bg-white rounded p-4 shadow-sm border border-light d-flex flex-column h-100">
                                <div class="d-flex align-items-center gap-2 mb-3">
                                    <div class="px-2 py-1 rounded small d-flex align-items-center gap-1 bg-success bg-opacity-10 text-success">
                                        <img src="/icon-7.svg" alt="Subject icon" width="16" height="16" />
                                        <span>KHTN</span>
                                    </div>
                                    <div class="small text-muted">Lớp 6</div>
                                </div>
                                <h5 class="fw-medium text-dark mb-3">Bài tập về nguyên tố hóa học</h5>
                                <div class="d-flex align-items-center gap-2 small text-muted mb-3">
                                    <span class="d-flex align-items-center gap-1">
                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                            <path d="M8 1v14M1 8h14" stroke="currentColor" stroke-width="2" />
                                        </svg>
                                        10 câu
                                    </span>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between small mb-2">
                                        <span class="text-muted">Tiến độ: 0%</span>
                                        <span class="text-danger">Chưa làm</span>
                                    </div>
                                    <div class="progress" style="height: 4px;">
                                        <div class="progress-bar bg-danger" role="progressbar" style="width: 0%"></div>
                                    </div>
                                </div>
                                <button class="btn btn-danger w-100 mt-auto">
                                    Làm bài
                                </button>
                            </div>
                        </div>
                        <div class="col-12 col-md-6 col-lg-3">
                            <div class="bg-white rounded p-4 shadow-sm border border-light d-flex flex-column h-100">
                                <div class="d-flex align-items-center gap-2 mb-3">
                                    <div class="px-2 py-1 rounded small d-flex align-items-center gap-1 bg-danger bg-opacity-10 text-danger">
                                        <img src="/icon-7.svg" alt="Subject icon" width="16" height="16" />
                                        <span>Toán</span>
                                    </div>
                                    <div class="small text-muted">Lớp 7</div>
                                </div>
                                <h5 class="fw-medium text-dark mb-3">Bài tập về phân số</h5>
                                <div class="d-flex align-items-center gap-2 small text-muted mb-3">
                                    <span class="d-flex align-items-center gap-1">
                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                            <path d="M8 1v14M1 8h14" stroke="currentColor" stroke-width="2" />
                                        </svg>
                                        15 câu
                                    </span>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between small mb-2">
                                        <span class="text-muted">Tiến độ: 30%</span>
                                        <span class="text-warning">Đang làm</span>
                                    </div>
                                    <div class="progress" style="height: 4px;">
                                        <div class="progress-bar bg-danger" role="progressbar" style="width: 30%"></div>
                                    </div>
                                </div>
                                <button class="btn btn-danger w-100 mt-auto">
                                    Làm bài
                                </button>
                            </div>
                        </div>
                        <div class="col-12 col-md-6 col-lg-3">
                            <div class="bg-white rounded p-4 shadow-sm border border-light d-flex flex-column h-100">
                                <div class="d-flex align-items-center gap-2 mb-3">
                                    <div class="px-2 py-1 rounded small d-flex align-items-center gap-1 bg-primary bg-opacity-10 text-primary">
                                        <img src="/icon-7.svg" alt="Subject icon" width="16" height="16" />
                                        <span>Văn</span>
                                    </div>
                                    <div class="small text-muted">Lớp 8</div>
                                </div>
                                <h5 class="fw-medium text-dark mb-3">Bài tập văn học</h5>
                                <div class="d-flex align-items-center gap-2 small text-muted mb-3">
                                    <span class="d-flex align-items-center gap-1">
                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                            <path d="M8 1v14M1 8h14" stroke="currentColor" stroke-width="2" />
                                        </svg>
                                        8 câu
                                    </span>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between small mb-2">
                                        <span class="text-muted">Tiến độ: 100%</span>
                                        <span class="text-success">Hoàn thành</span>
                                    </div>
                                    <div class="progress" style="height: 4px;">
                                        <div class="progress-bar bg-success" role="progressbar" style="width: 100%"></div>
                                    </div>
                                </div>
                                <button class="btn btn-danger w-100 mt-auto">
                                    Xem lại
                                </button>
                            </div>
                        </div>
                        <div class="col-12 col-md-6 col-lg-3">
                            <div class="bg-white rounded p-4 shadow-sm border border-light d-flex flex-column h-100">
                                <div class="d-flex align-items-center gap-2 mb-3">
                                    <div class="px-2 py-1 rounded small d-flex align-items-center gap-1 bg-warning bg-opacity-10 text-warning">
                                        <img src="/icon-7.svg" alt="Subject icon" width="16" height="16" />
                                        <span>Anh</span>
                                    </div>
                                    <div class="small text-muted">Lớp 9</div>
                                </div>
                                <h5 class="fw-medium text-dark mb-3">Bài tập ngữ pháp</h5>
                                <div class="d-flex align-items-center gap-2 small text-muted mb-3">
                                    <span class="d-flex align-items-center gap-1">
                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                            <path d="M8 1v14M1 8h14" stroke="currentColor" stroke-width="2" />
                                        </svg>
                                        12 câu
                                    </span>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between small mb-2">
                                        <span class="text-muted">Tiến độ: 60%</span>
                                        <span class="text-warning">Đang làm</span>
                                    </div>
                                    <div class="progress" style="height: 4px;">
                                        <div class="progress-bar bg-warning" role="progressbar" style="width: 60%"></div>
                                    </div>
                                </div>
                                <button class="btn btn-danger w-100 mt-auto">
                                    Tiếp tục
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Custom Testimonial Carousel Section -->
        <section class="testimonial">
            <h2>Công cụ hỗ trợ được giáo viên tin dùng</h2>
            <div class="separator-line"></div>
            <p>Học sinh chia sẻ cảm nhận về hệ thống giáo dục thông minh.</p>

            <div class="testimonial-carousel-container">
                <div class="testimonial-carousel" id="testimonialCarousel">
                    <!-- Testimonial Item 1 -->
                    <div class="testimonial-item">
                        <div class="testimonial-card">
                            <img src="/image-testimonial-1.png" alt="Học sinh 1" class="testimonial-avatar" />
                            <div class="testimonial-name">Nguyễn Minh An</div>
                            <div class="testimonial-text">
                                "Hệ thống học tập này thật sự tuyệt vời! Các bài giảng rất sinh động và dễ hiểu."
                            </div>
                        </div>
                    </div>

                    <!-- Testimonial Item 2 -->
                    <div class="testimonial-item">
                        <div class="testimonial-card">
                            <img src="/image-testimonial-2.png" alt="Giáo viên 1" class="testimonial-avatar" />
                            <div class="testimonial-name">Cô Phạm Thu Hà</div>
                            <div class="testimonial-text">
                                "Công cụ hỗ trợ giảng dạy rất hữu ích, giúp tôi tạo ra những bài học thú vị."
                            </div>
                        </div>
                    </div>

                    <!-- Testimonial Item 3 -->
                    <div class="testimonial-item">
                        <div class="testimonial-card">
                            <img src="/image-testimonial-3.png" alt="Phụ huynh 1" class="testimonial-avatar" />
                            <div class="testimonial-name">Chị Trần Thị Lan</div>
                            <div class="testimonial-text">
                                "Con tôi rất thích học trên hệ thống này. Các bài tập đa dạng và phù hợp."
                            </div>
                        </div>
                    </div>

                    <!-- Testimonial Item 4 -->
                    <div class="testimonial-item">
                        <div class="testimonial-card">
                            <img src="/image-testimonial-1.png" alt="Học sinh 2" class="testimonial-avatar" />
                            <div class="testimonial-name">Trần Văn Hưng</div>
                            <div class="testimonial-text">
                                "Giao diện thân thiện và các bài tập được phân loại rõ ràng theo từng cấp độ."
                            </div>
                        </div>
                    </div>

                    <!-- Testimonial Item 5 -->
                    <div class="testimonial-item">
                        <div class="testimonial-card">
                            <img src="/image-testimonial-2.png" alt="Giáo viên 2" class="testimonial-avatar" />
                            <div class="testimonial-name">Thầy Nguyễn Đức</div>
                            <div class="testimonial-text">
                                "Hệ thống theo dõi tiến độ học tập giúp tôi nắm bắt tình hình học sinh."
                            </div>
                        </div>
                    </div>

                    <!-- Testimonial Item 6 -->
                    <div class="testimonial-item">
                        <div class="testimonial-card">
                            <img src="/image-testimonial-3.png" alt="Phụ huynh 2" class="testimonial-avatar" />
                            <div class="testimonial-name">Chị Lê Thị Mai</div>
                            <div class="testimonial-text">
                                "Tôi yên tâm khi con học online với hệ thống này vì có nhiều tính năng hỗ trợ."
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Custom Arrow Buttons -->
                <button class="testimonial-arrow testimonial-arrow-left" id="prevBtn" aria-label="Previous testimonial">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                </button>
                <button class="testimonial-arrow testimonial-arrow-right" id="nextBtn" aria-label="Next testimonial">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                </button>
            </div>
        </section>
        <!-- Đánh giá của học sinh -->
        <section class="student-feedback">
            <!-- Header Section -->
            <h2>Đánh Giá Của Học Sinh</h2>
            <div class="separator-line"></div>
            <p>
                Hệ thống giúp học sinh tiếp thu kiến thức dễ dàng hơn với bài giảng sinh động, mô phỏng trực quan và công cụ hỗ trợ học tập hiệu quả,
                biến việc học trở nên thú vị và chủ động hơn.
            </p>

            <!-- Student Feedback Carousel -->
            <div class="student-feedback-carousel-container">
                <div class="student-feedback-carousel" id="studentFeedbackCarousel">
                    <!-- Student Feedback Item 1 -->
                    <div class="student-feedback-item">
                        <div class="student-feedback-card">
                            <img src="https://images.pexels.com/photos/762020/pexels-photo-762020.jpeg?auto=compress&cs=tinysrgb&w=150" alt="Học sinh 1" class="testimonial-avatar" />
                            <div class="testimonial-name">Nguyễn Minh An</div>
                            <div class="student-feedback-text">
                                "Từ khi sử dụng hệ thống này, em cảm thấy việc học trở nên dễ dàng và thú vị hơn rất nhiều. Các bài giảng không chỉ đầy đủ mà còn được trình bày một cách sinh động."
                            </div>
                        </div>
                    </div>

                    <!-- Student Feedback Item 2 -->
                    <div class="student-feedback-item">
                        <div class="student-feedback-card">
                            <img src="https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=150" alt="Học sinh 2" class="testimonial-avatar" />
                            <div class="testimonial-name">Trần Thị Hương</div>
                            <div class="student-feedback-text">
                                "Hệ thống này thực sự giúp em thay đổi cách học một cách tích cực. Các bài tập luyện tập và kiểm tra ngay sau mỗi bài học giúp em củng cố kiến thức kịp thời."
                            </div>
                        </div>
                    </div>

                    <!-- Student Feedback Item 3 -->
                    <div class="student-feedback-item">
                        <div class="student-feedback-card">
                            <img src="https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=150" alt="Học sinh 3" class="testimonial-avatar" />
                            <div class="testimonial-name">Lê Văn Đức</div>
                            <div class="student-feedback-text">
                                "Giao diện thân thiện và các bài tập được phân loại rõ ràng theo từng cấp độ. Em có thể học theo tốc độ của mình và không bị áp lực."
                            </div>
                        </div>
                    </div>

                    <!-- Student Feedback Item 4 -->
                    <div class="student-feedback-item">
                        <div class="student-feedback-card">
                            <img src="https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=150" alt="Học sinh 4" class="testimonial-avatar" />
                            <div class="testimonial-name">Phạm Thị Mai</div>
                            <div class="student-feedback-text">
                                "Các video mô phỏng giúp em hiểu rõ hơn những khái niệm khó. Đặc biệt là môn Hóa học, em có thể thấy được phản ứng diễn ra như thế nào."
                            </div>
                        </div>
                    </div>

                    <!-- Student Feedback Item 5 -->
                    <div class="student-feedback-item">
                        <div class="student-feedback-card">
                            <img src="https://images.pexels.com/photos/1300402/pexels-photo-1300402.jpeg?auto=compress&cs=tinysrgb&w=150" alt="Học sinh 5" class="testimonial-avatar" />
                            <div class="testimonial-name">Hoàng Văn Nam</div>
                            <div class="student-feedback-text">
                                "Hệ thống theo dõi tiến độ học tập giúp em biết được điểm mạnh và điểm yếu của mình. Từ đó em có thể tập trung cải thiện những phần còn thiếu."
                            </div>
                        </div>
                    </div>

                    <!-- Student Feedback Item 6 -->
                    <div class="student-feedback-item">
                        <div class="student-feedback-card">
                            <img src="https://images.pexels.com/photos/1516680/pexels-photo-1516680.jpeg?auto=compress&cs=tinysrgb&w=150" alt="Học sinh 6" class="testimonial-avatar" />
                            <div class="testimonial-name">Vũ Thị Lan</div>
                            <div class="student-feedback-text">
                                "Em thích nhất là có thể học mọi lúc mọi nơi. Chỉ cần có điện thoại hoặc máy tính là em có thể tiếp tục bài học của mình."
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Custom Arrow Buttons -->
                <button class="student-feedback-arrow student-feedback-arrow-left" id="studentPrevBtn" aria-label="Previous student feedback">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                </button>
                <button class="student-feedback-arrow student-feedback-arrow-right" id="studentNextBtn" aria-label="Next student feedback">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                </button>
            </div>
        </section>
        <section class="articles">
            <h2>Bài Viết Mới</h2>
            <div class="separator-line"></div>
            <p>Tổng hợp các bài viết chia sẻ thông tin và kinh nghiệm học tập từ các giáo viên và học sinh.</p>

            <div class="articles-grid">
                <article class="article-card">
                    <img src="/image-3-4.png" alt="Article thumbnail" />
                    <h3>Khoá học mùa hè bắt đầu từ 01/06</h3>
                    <p>Morbi accumsan ipsum velit. Nam nec tellus a odio tincidunt auctor a ornare odio. Sed non mauris itae erat conuat.</p>
                </article>
                <article class="article-card">
                    <img src="/image-3-4.png" alt="Article thumbnail" />
                    <h3>Khoá học mùa hè bắt đầu từ 01/06</h3>
                    <p>Morbi accumsan ipsum velit. Nam nec tellus a odio tincidunt auctor a ornare odio. Sed non mauris itae erat conuat.</p>
                </article>
                <article class="article-card">
                    <img src="/image-3-4.png" alt="Article thumbnail" />
                    <h3>Khoá học mùa hè bắt đầu từ 01/06</h3>
                    <p>Morbi accumsan ipsum velit. Nam nec tellus a odio tincidunt auctor a ornare odio. Sed non mauris itae erat conuat.</p>
                </article>
                <article class="article-card">
                    <img src="/image-3-4.png" alt="Article thumbnail" />
                    <h3>Khoá học mùa hè bắt đầu từ 01/06</h3>
                    <p>Morbi accumsan ipsum velit. Nam nec tellus a odio tincidunt auctor a ornare odio. Sed non mauris itae erat conuat.</p>
                </article>
            </div>
        </section>
        <section class ="footer">
            <div class="container py-5">
                <div class="row">
                    <!-- Logo Section -->
                    <div class="col-md-3 mb-4 mb-md-0">
                        <div class="logo-container">
                            <div class="logo">
                                <svg class="graduation-cap" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M22 10v6M2 10l10-5 10 5-10 5z"/>
                                    <path d="M6 12v5c0 2 2 3 6 3s6-1 6-3v-5"/>
                                </svg>
                                <h2>EDTECH</h2>
                            </div>
                            <div class="social-icons">
                                <a href="#" class="social-icon" aria-label="Facebook">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"/>
                                    </svg>
                                </a>
                                <a href="#" class="social-icon" aria-label="Twitter">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"/>
                                    </svg>
                                </a>
                                <a href="#" class="social-icon" aria-label="Youtube">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M22.54 6.42a2.78 2.78 0 0 0-1.94-2C18.88 4 12 4 12 4s-6.88 0-8.6.46a2.78 2.78 0 0 0-1.94 2A29 29 0 0 0 1 11.75a29 29 0 0 0 .46 5.33A2.78 2.78 0 0 0 3.4 19c1.72.46 8.6.46 8.6.46s6.88 0 8.6-.46a2.78 2.78 0 0 0 1.94-2 29 29 0 0 0 .46-5.25 29 29 0 0 0-.46-5.33z"/>
                                        <polygon points="9.75 15.02 15.5 11.75 9.75 8.48 9.75 15.02"/>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Subjects Section -->
                    <div class="col-md-3 mb-4 mb-md-0">
                        <h3 class="footer-heading">MÔN HỌC</h3>
                        <ul class="footer-links">
                            <li><a href="#">Toán học</a></li>
                            <li><a href="#">Ngữ văn</a></li>
                            <li><a href="#">Ngoại ngữ</a></li>
                            <li><a href="#">Khoa học tự nhiên</a></li>
                            <li><a href="#">Khoa học xã hội</a></li>
                        </ul>
                    </div>

                    <!-- Features Section -->
                    <div class="col-md-3 mb-4 mb-md-0">
                        <h3 class="footer-heading">TÍNH NĂNG</h3>
                        <ul class="footer-links">
                            <li><a href="#">Bài giảng</a></li>
                            <li><a href="#">Bài tập</a></li>
                            <li><a href="#">Liên hệ</a></li>
                        </ul>
                    </div>

                    <!-- Contact Section -->
                    <div class="col-md-3">
                        <h3 class="footer-heading">THÔNG TIN LIÊN HỆ</h3>
                        <ul class="contact-info">
                            <li>
                                <span class="contact-label">Địa chỉ:</span>
                                <span class="contact-text">
                                    Số 12 đường Hồ Tùng Mậu, Phú Diễn, Bắc Từ Liêm, Hà Nội
                                </span>
                            </li>
                            <li>
                                <span class="contact-label">Điện thoại:</span>
                                <span class="contact-text">0912345678</span>
                            </li>
                            <li>
                                <span class="contact-label">Email:</span>
                                <span class="contact-text"><EMAIL></span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="container">
                    <p class="mb-0">&copy; <span id="currentYear"></span> EdTech. All rights reserved.</p>
                </div>
            </div>
        </section>
    </main>

    <div class="social-widget">
        <img src="./images/home/<USER>/zalo.png" alt="Social 1" />
        <img src="./images/home/<USER>/messenger.png" alt="Social 2" />
        <img src="./images/home/<USER>/support.png" alt="Social 3" />
    </div>
    <script src="~/libs/bootstrap/js/bootstrap.bundle.js"></script>
    <script src="~/js/home/<USER>"></script>
</body>
</html>