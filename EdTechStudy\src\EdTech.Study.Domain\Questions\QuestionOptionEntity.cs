﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;
using Volo.Abp.Domain.Entities;
using Volo.Abp;
using EdTech.Study.Exams;

namespace EdTech.Study.Questions
{
    public class QuestionOptionEntity : Entity<Guid>
    {
        protected QuestionOptionEntity()
        {
            // Required by EF Core
        }

        public QuestionOptionEntity(Guid id) : base(id)
        {
        }

        /// <summary>
        /// Nội dung lựa chọn
        /// </summary>
        [Required]
        public string Content { get; set; }

        /// <summary>
        /// Định dạng nội dung [TEXT, HTML]
        /// </summary>
        public ContentFormatType ContentFormat { get; set; }

        /// <summary>
        /// Xác định lựa chọn đúng hay sai
        /// </summary>
        public bool IsCorrect { get; set; }

        /// <summary>
        /// Thứ tự sắp xếp lựa chọn
        /// </summary>
        public int Order { get; set; }

        /// <summary>
        /// Phản hồi khi người dùng chọn đáp án này
        /// </summary>
        public string? Explanation { get; set; }

        /// <summary>
        /// Điểm số cho ô trống này.
        /// </summary>
        public float? Score { get; set; }

        /// <summary>
        /// Đảm bảo duy nhất mỗi request thêm
        /// </summary>
        [MaxLength(256)]
        public string? IdempotentKey { get; set; }

        public Guid QuestionId { get; set; }

        public virtual QuestionEntity Question { get; set; }
        
    }
}
