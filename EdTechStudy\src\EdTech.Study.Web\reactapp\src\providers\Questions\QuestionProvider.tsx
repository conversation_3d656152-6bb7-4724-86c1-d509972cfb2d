import { createContext, useContext, useEffect, useMemo } from 'react';
import { connect } from 'react-redux';
import {
  IQuestionContext,
  IQuestionProviderDispatchProps,
  IQuestionProviderProps,
} from './QuestionProvider.interface';
import {
  fetchSubjects,
  SubjectState,
} from '../../store/slices/SubjectSlices/subjectSlice';
import {
  fetchLessonGrades,
  LessonGradeState,
} from '../../store/slices/LessonGradeSlices/lessonGradeSlice';
import { fetchUsers, UserState } from '../../store/slices/UserSlices/userSlice';

const QuestionContext = createContext<IQuestionContext>({});

export const useQuestionContext = () => useContext(QuestionContext);

const QuestionProvider = (props: IQuestionProviderProps) => {
  const {
    children,
    lessonGrades,
    subjects,
    users,
    getSubjects,
    getLessonGrades,
    getUsers,
  } = props;
  useEffect(() => {
    if (!subjects) {
      getSubjects();
    }
    if (!lessonGrades) {
      getLessonGrades();
    }
    if (!users) {
      getUsers();
    }
  }, []);

  const value = useMemo(() => {
    return {
      subjects,
      lessonGrades,
      users,
    };
  }, [subjects, lessonGrades, users]);

  return (
    <QuestionContext.Provider value={value}>
      {children}
    </QuestionContext.Provider>
  );
};

const mapStateToProps = ({
  subjectManager,
  lessonGradeManager,
  userDataManager,
}: {
  subjectManager: SubjectState;
  lessonGradeManager: LessonGradeState;
  userDataManager: UserState;
}) => {
  return {
    subjects: subjectManager.subjects,
    lessonGrades: lessonGradeManager.lessonGrades,
    users: userDataManager.users,
  };
};

const mapDispatchToProps = (dispatch: any): IQuestionProviderDispatchProps => {
  return {
    getSubjects: () => dispatch(fetchSubjects({ pageSize: 500 })),
    getLessonGrades: () => dispatch(fetchLessonGrades({ pageSize: 500 })),
    getUsers: () => dispatch(fetchUsers({ pageSize: 500 })),
  };
};

const QuestionProviderLayout = connect(
  mapStateToProps,
  mapDispatchToProps
)(QuestionProvider);

export default QuestionProviderLayout;
