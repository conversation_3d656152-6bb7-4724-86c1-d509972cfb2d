import { PlusOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { memo } from 'react';

const AddQuestionButton = memo(
  ({
    position,
    onAddQuestion,
  }: {
    position: number;
    onAddQuestion: (position: number) => void;
  }) => (
    <div className="add-question-button-container">
      <Button
        type="dashed"
        icon={<PlusOutlined />}
        onClick={(e) => {
          e.stopPropagation();
          onAddQuestion(position);
        }}
        block
      >
        Thêm câu hỏi mới
      </Button>
    </div>
  )
);

export default AddQuestionButton;
