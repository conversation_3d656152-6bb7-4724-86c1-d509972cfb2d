.tailwind-custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.tailwind-custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.tailwind-custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.tailwind-custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* Firefox */
.tailwind-custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

/* Marker item styles */
.marker-item {
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.marker-item:hover {
  border-left-color: var(--edtt-color-primary);
  background-color: rgba(234, 76, 137, 0.05);
}

.marker-item.active {
  border-left-color: var(--edtt-color-primary);
  background-color: rgba(234, 76, 137, 0.1);
}

/* Checkbox styles */
/* .ant-checkbox-checked .ant-checkbox-inner {
  background-color: var(--edtt-color-primary) !important;
  border-color: var(--edtt-color-primary) !important;
}

.ant-checkbox-wrapper:hover .ant-checkbox-inner,
.ant-checkbox:hover .ant-checkbox-inner,
.ant-checkbox-input:focus+.ant-checkbox-inner {
  border-color: var(--edtt-color-primary) !important;
} */