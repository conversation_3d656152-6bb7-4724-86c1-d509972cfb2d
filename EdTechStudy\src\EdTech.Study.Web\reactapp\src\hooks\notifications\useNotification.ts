import { useEffect } from 'react';
import { message } from 'antd';

const useNotification = ({
  messages,
}: {
  messages: {
    type: 'error' | 'info' | 'success';
    content: string;
  }[];
}) => {
  useEffect(() => {
    messages.forEach((m) => {
      switch (m.type) {
        case 'error':
          message.error(m.content);
          break;
        case 'info':
          message.info(m.content);
          break;
        case 'success':
          message.success(m.content);
          break;
      }
    });
  }, [messages]);
  return {};
};

export default useNotification;
