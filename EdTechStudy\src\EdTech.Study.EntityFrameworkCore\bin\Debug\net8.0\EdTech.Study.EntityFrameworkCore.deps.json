{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"EdTech.Study.EntityFrameworkCore/0.1.0": {"dependencies": {"ConfigureAwait.Fody": "3.3.1", "EdTech.Study.Domain": "0.1.0", "Fody": "6.5.3", "Tsp.Core.EF": "1.0.19", "Volo.Abp.EntityFrameworkCore": "8.1.4"}, "runtime": {"EdTech.Study.EntityFrameworkCore.dll": {}}}, "AsyncKeyedLock/6.3.4": {"runtime": {"lib/net8.0/AsyncKeyedLock.dll": {"assemblyVersion": "6.3.4.0", "fileVersion": "6.3.4.0"}}}, "AutoMapper/12.0.1": {"dependencies": {"Microsoft.CSharp": "4.7.0"}, "runtime": {"lib/netstandard2.1/AutoMapper.dll": {"assemblyVersion": "12.0.0.0", "fileVersion": "12.0.1.0"}}}, "ConfigureAwait.Fody/3.3.1": {"dependencies": {"Fody": "6.5.3"}, "runtime": {"lib/netstandard2.0/ConfigureAwait.dll": {"assemblyVersion": "3.3.1.0", "fileVersion": "3.3.1.0"}}}, "Fody/6.5.3": {}, "JetBrains.Annotations/2023.3.0": {"runtime": {"lib/netstandard2.0/JetBrains.Annotations.dll": {"assemblyVersion": "4242.42.42.42", "fileVersion": "2023.3.0.0"}}}, "Microsoft.AspNetCore.Authorization/8.0.0": {"dependencies": {"Microsoft.AspNetCore.Metadata": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.AspNetCore.Cryptography.Internal/8.0.0": {"runtime": {"lib/net8.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/8.0.0": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.AspNetCore.Metadata/8.0.0": {"runtime": {"lib/net8.0/Microsoft.AspNetCore.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.EntityFrameworkCore/8.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "8.0.0", "Microsoft.EntityFrameworkCore.Analyzers": "8.0.0", "Microsoft.Extensions.Caching.Memory": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.0": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.0": {}, "Microsoft.EntityFrameworkCore.Relational/8.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "System.Text.Json": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileProviders.Composite/8.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Composite.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileProviders.Embedded/8.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Embedded.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Identity.Core/8.0.0": {"dependencies": {"Microsoft.AspNetCore.Cryptography.KeyDerivation": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Identity.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.Extensions.Localization/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Localization.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Localization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.Extensions.Localization.Abstractions/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.Localization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Options/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Primitives/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Nito.AsyncEx.Context/5.1.2": {"dependencies": {"Nito.AsyncEx.Tasks": "5.1.2"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Context.dll": {"assemblyVersion": "5.1.2.0", "fileVersion": "5.1.2.0"}}}, "Nito.AsyncEx.Tasks/5.1.2": {"dependencies": {"Nito.Disposables": "2.2.1"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Tasks.dll": {"assemblyVersion": "5.1.2.0", "fileVersion": "5.1.2.0"}}}, "Nito.Disposables/2.2.1": {"dependencies": {"System.Collections.Immutable": "8.0.0"}, "runtime": {"lib/netstandard2.1/Nito.Disposables.dll": {"assemblyVersion": "2.2.1.0", "fileVersion": "2.2.1.0"}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Immutable/8.0.0": {}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/8.0.0": {}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Dynamic.Core/1.3.5": {"runtime": {"lib/net7.0/System.Linq.Dynamic.Core.dll": {"assemblyVersion": "1.3.5.0", "fileVersion": "1.3.5.0"}}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Linq.Queryable/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Loader/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encodings.Web/8.0.0": {}, "System.Text.Json/8.0.0": {"dependencies": {"System.Text.Encodings.Web": "8.0.0"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "TimeZoneConverter/6.1.0": {"runtime": {"lib/net6.0/TimeZoneConverter.dll": {"assemblyVersion": "6.1.0.0", "fileVersion": "6.1.0.0"}}}, "Tsp.Core.EF/1.0.19": {"dependencies": {"Tsp.Core.EF.Domain": "1.0.19", "Volo.Abp.EntityFrameworkCore": "8.1.4"}, "runtime": {"lib/net8.0/Core.EF.dll": {"assemblyVersion": "1.0.19.0", "fileVersion": "1.0.19.0"}}}, "Tsp.Core.EF.Domain/1.0.19": {"dependencies": {"Volo.Abp.Ddd.Domain": "8.1.4"}, "runtime": {"lib/net8.0/Core.EF.Domain.dll": {"assemblyVersion": "1.0.19.0", "fileVersion": "1.0.19.0"}}}, "Volo.Abp.Auditing/8.1.4": {"dependencies": {"Volo.Abp.Auditing.Contracts": "8.1.4", "Volo.Abp.Data": "8.1.4", "Volo.Abp.Json": "8.1.4", "Volo.Abp.MultiTenancy": "8.1.4", "Volo.Abp.Security": "8.1.4", "Volo.Abp.Threading": "8.1.4", "Volo.Abp.Timing": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Auditing.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Auditing.Contracts/8.1.4": {"dependencies": {"Volo.Abp.Core": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Auditing.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Authorization.Abstractions/8.1.4": {"dependencies": {"Microsoft.AspNetCore.Authorization": "8.0.0", "Volo.Abp.MultiTenancy.Abstractions": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Authorization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.AutoMapper/8.1.4": {"dependencies": {"AutoMapper": "12.0.1", "Volo.Abp.Auditing": "8.1.4", "Volo.Abp.ObjectExtending": "8.1.4", "Volo.Abp.ObjectMapping": "8.1.4"}, "runtime": {"lib/netstandard2.1/Volo.Abp.AutoMapper.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.BackgroundWorkers/8.1.4": {"dependencies": {"Volo.Abp.Threading": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.BackgroundWorkers.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.BlobStoring/8.1.4": {"dependencies": {"Volo.Abp.Core": "8.1.4", "Volo.Abp.MultiTenancy": "8.1.4", "Volo.Abp.Threading": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.BlobStoring.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Caching/8.1.4": {"dependencies": {"Microsoft.Extensions.Caching.Memory": "8.0.0", "Volo.Abp.Json": "8.1.4", "Volo.Abp.MultiTenancy": "8.1.4", "Volo.Abp.Serialization": "8.1.4", "Volo.Abp.Threading": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Core/8.1.4": {"dependencies": {"JetBrains.Annotations": "2023.3.0", "Microsoft.Extensions.Configuration.CommandLine": "8.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "8.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Localization": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0", "Nito.AsyncEx.Context": "5.1.2", "System.Collections.Immutable": "8.0.0", "System.Linq.Dynamic.Core": "1.3.5", "System.Linq.Queryable": "4.3.0", "System.Runtime.Loader": "4.3.0", "System.Text.Encodings.Web": "8.0.0"}, "runtime": {"lib/net8.0/Volo.Abp.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Data/8.1.4": {"dependencies": {"Volo.Abp.EventBus.Abstractions": "8.1.4", "Volo.Abp.ObjectExtending": "8.1.4", "Volo.Abp.Uow": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Data.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Ddd.Domain/8.1.4": {"dependencies": {"Volo.Abp.Auditing": "8.1.4", "Volo.Abp.Caching": "8.1.4", "Volo.Abp.Data": "8.1.4", "Volo.Abp.Ddd.Domain.Shared": "8.1.4", "Volo.Abp.EventBus": "8.1.4", "Volo.Abp.ExceptionHandling": "8.1.4", "Volo.Abp.Guids": "8.1.4", "Volo.Abp.ObjectMapping": "8.1.4", "Volo.Abp.Specifications": "8.1.4", "Volo.Abp.Timing": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Ddd.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Ddd.Domain.Shared/8.1.4": {"dependencies": {"Volo.Abp.EventBus.Abstractions": "8.1.4", "Volo.Abp.MultiTenancy.Abstractions": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Ddd.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.DistributedLocking.Abstractions/8.1.4": {"dependencies": {"AsyncKeyedLock": "6.3.4", "Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Volo.Abp.Core": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.DistributedLocking.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.EntityFrameworkCore/8.1.4": {"dependencies": {"Microsoft.EntityFrameworkCore": "8.0.0", "Microsoft.EntityFrameworkCore.Relational": "8.0.0", "Volo.Abp.Ddd.Domain": "8.1.4", "Volo.Abp.Json": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.EventBus/8.1.4": {"dependencies": {"Volo.Abp.BackgroundWorkers": "8.1.4", "Volo.Abp.DistributedLocking.Abstractions": "8.1.4", "Volo.Abp.EventBus.Abstractions": "8.1.4", "Volo.Abp.Guids": "8.1.4", "Volo.Abp.Json": "8.1.4", "Volo.Abp.MultiTenancy": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.EventBus.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.EventBus.Abstractions/8.1.4": {"dependencies": {"Volo.Abp.ObjectExtending": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.EventBus.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.ExceptionHandling/8.1.4": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "8.0.0", "Volo.Abp.Data": "8.1.4", "Volo.Abp.Localization": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.ExceptionHandling.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Features/8.1.4": {"dependencies": {"Volo.Abp.Authorization.Abstractions": "8.1.4", "Volo.Abp.Localization": "8.1.4", "Volo.Abp.MultiTenancy": "8.1.4", "Volo.Abp.Validation": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Features.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Guids/8.1.4": {"dependencies": {"Volo.Abp.Core": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Guids.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Identity.Domain/8.1.4": {"dependencies": {"Microsoft.Extensions.Identity.Core": "8.0.0", "Volo.Abp.AutoMapper": "8.1.4", "Volo.Abp.Ddd.Domain": "8.1.4", "Volo.Abp.Identity.Domain.Shared": "8.1.4", "Volo.Abp.Users.Domain": "8.1.4"}, "runtime": {"lib/netstandard2.1/Volo.Abp.Identity.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Identity.Domain.Shared/8.1.4": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "8.0.0", "Volo.Abp.Auditing.Contracts": "8.1.4", "Volo.Abp.Features": "8.1.4", "Volo.Abp.Users.Domain.Shared": "8.1.4", "Volo.Abp.Validation": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Identity.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Json/8.1.4": {"dependencies": {"Volo.Abp.Json.SystemTextJson": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Json.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Json.Abstractions/8.1.4": {"dependencies": {"Volo.Abp.Core": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Json.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Json.SystemTextJson/8.1.4": {"dependencies": {"System.Text.Json": "8.0.0", "Volo.Abp.Data": "8.1.4", "Volo.Abp.Json.Abstractions": "8.1.4", "Volo.Abp.Timing": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Json.SystemTextJson.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Localization/8.1.4": {"dependencies": {"Volo.Abp.Localization.Abstractions": "8.1.4", "Volo.Abp.Settings": "8.1.4", "Volo.Abp.Threading": "8.1.4", "Volo.Abp.VirtualFileSystem": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Localization.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Localization.Abstractions/8.1.4": {"dependencies": {"Volo.Abp.Core": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Localization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.MultiTenancy/8.1.4": {"dependencies": {"Volo.Abp.Data": "8.1.4", "Volo.Abp.EventBus.Abstractions": "8.1.4", "Volo.Abp.MultiTenancy.Abstractions": "8.1.4", "Volo.Abp.Security": "8.1.4", "Volo.Abp.Settings": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.MultiTenancy.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.MultiTenancy.Abstractions/8.1.4": {"dependencies": {"Volo.Abp.Localization": "8.1.4", "Volo.Abp.VirtualFileSystem": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.MultiTenancy.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.ObjectExtending/8.1.4": {"dependencies": {"Volo.Abp.Localization.Abstractions": "8.1.4", "Volo.Abp.Validation.Abstractions": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.ObjectExtending.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.ObjectMapping/8.1.4": {"dependencies": {"Volo.Abp.Core": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.ObjectMapping.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Security/8.1.4": {"dependencies": {"Volo.Abp.Core": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Security.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Serialization/8.1.4": {"dependencies": {"Volo.Abp.Core": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Settings/8.1.4": {"dependencies": {"Volo.Abp.Data": "8.1.4", "Volo.Abp.Localization.Abstractions": "8.1.4", "Volo.Abp.Security": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Settings.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Specifications/8.1.4": {"dependencies": {"Volo.Abp.Core": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Specifications.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Threading/8.1.4": {"dependencies": {"Volo.Abp.Core": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Timing/8.1.4": {"dependencies": {"TimeZoneConverter": "6.1.0", "Volo.Abp.Localization": "8.1.4", "Volo.Abp.Settings": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Timing.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Uow/8.1.4": {"dependencies": {"Volo.Abp.Core": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Uow.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Users.Abstractions/8.1.4": {"dependencies": {"Volo.Abp.EventBus": "8.1.4", "Volo.Abp.MultiTenancy": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Users.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Users.Domain/8.1.4": {"dependencies": {"Volo.Abp.Ddd.Domain": "8.1.4", "Volo.Abp.Users.Abstractions": "8.1.4", "Volo.Abp.Users.Domain.Shared": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Users.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Users.Domain.Shared/8.1.4": {"dependencies": {"Volo.Abp.Core": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Users.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Validation/8.1.4": {"dependencies": {"Volo.Abp.Localization": "8.1.4", "Volo.Abp.Validation.Abstractions": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Validation.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Validation.Abstractions/8.1.4": {"dependencies": {"Volo.Abp.Core": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Validation.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.VirtualFileSystem/8.1.4": {"dependencies": {"Microsoft.Extensions.FileProviders.Composite": "8.0.0", "Microsoft.Extensions.FileProviders.Embedded": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Volo.Abp.Core": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.VirtualFileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "EdTech.Study.Domain/0.1.0": {"dependencies": {"EdTech.Study.Domain.Shared": "0.1.0", "Volo.Abp.BlobStoring": "8.1.4", "Volo.Abp.Ddd.Domain": "8.1.4", "Volo.Abp.Identity.Domain": "8.1.4", "Volo.Abp.VirtualFileSystem": "8.1.4"}, "runtime": {"EdTech.Study.Domain.dll": {"assemblyVersion": "0.1.0", "fileVersion": "0.1.0.0"}}}, "EdTech.Study.Domain.Shared/0.1.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "8.0.0", "Volo.Abp.Ddd.Domain.Shared": "8.1.4", "Volo.Abp.Validation": "8.1.4"}, "runtime": {"EdTech.Study.Domain.Shared.dll": {"assemblyVersion": "0.1.0", "fileVersion": "0.1.0.0"}}}}}, "libraries": {"EdTech.Study.EntityFrameworkCore/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "AsyncKeyedLock/6.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-+0YPXvamnopzHSgBpv0vg+9/NJH+6jm1ZODuMMs7p43yH7hA/NxNi+/U7T7eazZqW+V+0ZsbreYruhu6i7N4PA==", "path": "asynckeyedlock/6.3.4", "hashPath": "asynckeyedlock.6.3.4.nupkg.sha512"}, "AutoMapper/12.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-hvV62vl6Hp/WfQ24yzo3Co9+OPl8wH8hApwVtgWpiAynVJkUcs7xvehnSftawL8Pe8FrPffBRM3hwzLQqWDNjA==", "path": "automapper/12.0.1", "hashPath": "automapper.12.0.1.nupkg.sha512"}, "ConfigureAwait.Fody/3.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-R9PQYf0AT4RBZcUXm22xWkCpSmNHdTzQ0dOyLIsxIK6dwXH4S9pY/rZdXU/63i8vZvSzZ99sB1kP7xer8MCe6w==", "path": "configureawait.fody/3.3.1", "hashPath": "configureawait.fody.3.3.1.nupkg.sha512"}, "Fody/6.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-sRkrGVPJWG5vVKF/3kExAwZhFMUzK/Zksgcv113ehyuYuTDMuqBC4lr6y0qqZ6ga5nT1uueebDzrsRZsNIrqLg==", "path": "fody/6.5.3", "hashPath": "fody.6.5.3.nupkg.sha512"}, "JetBrains.Annotations/2023.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PHfnvdBUdGaTVG9bR/GEfxgTwWM0Z97Y6X3710wiljELBISipSfF5okn/vz+C2gfO+ihoEyVPjaJwn8ZalVukA==", "path": "jetbrains.annotations/2023.3.0", "hashPath": "jetbrains.annotations.2023.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OGIGJMnlWvQgcweHcv1Mq/P24Zx/brUHeEdD05NzqkSXmQSnFomTvVyCuBtCXT4JPfv2m70y1RSocmd9bIbJRg==", "path": "microsoft.aspnetcore.authorization/8.0.0", "hashPath": "microsoft.aspnetcore.authorization.8.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.Internal/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-buuMMCTxFcVkOkEftb2OafYxrveNGre9KJF4Oi1DkR4rxIj6oLam7Wq3g0Fp9hNVpJteKEPiupsxYnPrD/oUGA==", "path": "microsoft.aspnetcore.cryptography.internal/8.0.0", "hashPath": "microsoft.aspnetcore.cryptography.internal.8.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-65w93R5wqUUs35R9wjHHDf75GqAbxJsNByKZo5TbQOWSXcUbLWrDUWBQHv78iXIT0PL1pXNqKQz7OHiHMvo0/A==", "path": "microsoft.aspnetcore.cryptography.keyderivation/8.0.0", "hashPath": "microsoft.aspnetcore.cryptography.keyderivation.8.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Metadata/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OmuSztiZMitRTYlbMNDkBk3BinSsVcOApSNBAsrw+KYNJh6ALarPhWLlKdtvMgrKzpyCY06xtLAjTmQLURHSlQ==", "path": "microsoft.aspnetcore.metadata/8.0.0", "hashPath": "microsoft.aspnetcore.metadata.8.0.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3WA9q9yVqJp222P3x1wYIGDAkpjAku0TMUaaQV22g6L67AI0LdOIrVS7Ht2vJfLHGSPVuqN94vIr15qn+HEkHw==", "path": "microsoft.bcl.asyncinterfaces/8.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-SoODat83pGQUpWB9xULdMX6tuKpq/RTXDuJ2WeC1ldUKcKzLkaFJD1n+I0nOLY58odez/e7z8b6zdp235G/kyg==", "path": "microsoft.entityframeworkcore/8.0.0", "hashPath": "microsoft.entityframeworkcore.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VR22s3+zoqlVI7xauFKn1znSIFHO8xuILT+noSwS8bZCKcHz0ydkTDQMuaxSa5WBaQrZmwtTz9rmRvJ7X8mSPQ==", "path": "microsoft.entityframeworkcore.abstractions/8.0.0", "hashPath": "microsoft.entityframeworkcore.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZXxEeLs2zoZ1TA+QoMMcw4f3Tirf8PzgdDax8RoWo0dxI2KmqiEGWYjhm2B/XyWfglc6+mNRyB8rZiQSmxCpeg==", "path": "microsoft.entityframeworkcore.analyzers/8.0.0", "hashPath": "microsoft.entityframeworkcore.analyzers.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fFKkr24cYc7Zw5T6DC4tEyOEPgPbq23BBmym1r9kn4ET9F3HKaetpOeQtV2RryYyUxEeNkJuxgfiZHTisqZc+A==", "path": "microsoft.entityframeworkcore.relational/8.0.0", "hashPath": "microsoft.entityframeworkcore.relational.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "path": "microsoft.extensions.caching.abstractions/8.0.0", "hashPath": "microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7pqivmrZDzo1ADPkRwjy+8jtRKWRCPag9qPI+p7sgu7Q4QreWhcvbiWXsbhP+yY8XSiDvZpu2/LWdBv7PnmOpQ==", "path": "microsoft.extensions.caching.memory/8.0.0", "hashPath": "microsoft.extensions.caching.memory.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "path": "microsoft.extensions.configuration/8.0.0", "hashPath": "microsoft.extensions.configuration.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-mBMoXLsr5s1y2zOHWmKsE9veDcx8h1x/c3rz4baEdQKTeDcmQAPNbB54Pi/lhFO3K431eEq6PFbMgLaa6PHFfA==", "path": "microsoft.extensions.configuration.binder/8.0.0", "hashPath": "microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NZuZMz3Q8Z780nKX3ifV1fE7lS+6pynDHK71OfU4OZ1ItgvDOhyOC7E6z+JMZrAj63zRpwbdldYFk499t3+1dQ==", "path": "microsoft.extensions.configuration.commandline/8.0.0", "hashPath": "microsoft.extensions.configuration.commandline.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-plvZ0ZIpq+97gdPNNvhwvrEZ92kNml9hd1pe3idMA7svR0PztdzVLkoWLcRFgySYXUJc3kSM3Xw3mNFMo/bxRA==", "path": "microsoft.extensions.configuration.environmentvariables/8.0.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-McP+Lz/EKwvtCv48z0YImw+L1gi1gy5rHhNaNIY2CrjloV+XY8gydT8DjMR6zWeL13AFK+DioVpppwAuO1Gi1w==", "path": "microsoft.extensions.configuration.fileextensions/8.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-C2wqUoh9OmRL1akaCcKSTmRU8z0kckfImG7zLNI8uyi47Lp+zd5LWAD17waPQEqCz3ioWOCrFUo+JJuoeZLOBw==", "path": "microsoft.extensions.configuration.json/8.0.0", "hashPath": "microsoft.extensions.configuration.json.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ihDHu2dJYQird9pl2CbdwuNDfvCZdOS0S7SPlNfhPt0B81UTT+yyZKz2pimFZGUp3AfuBRnqUCxB2SjsZKHVUw==", "path": "microsoft.extensions.configuration.usersecrets/8.0.0", "hashPath": "microsoft.extensions.configuration.usersecrets.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "path": "microsoft.extensions.dependencyinjection/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JHYCQG7HmugNYUhOl368g+NMxYE/N/AiclCYRNlgCY9eVyiBkOHMwK4x60RYMxv9EL3+rmj1mqHvdCiPpC+D4Q==", "path": "microsoft.extensions.diagnostics.abstractions/8.0.0", "hashPath": "microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Composite/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ynMjdZ5B3Fd3A9GxJaNhIcTrjLY1bXDQltyVIMVOxbT0ssTOCpFYWc977bVBAocB62fYWu/RN6/1HLnX/HjVuQ==", "path": "microsoft.extensions.fileproviders.composite/8.0.0", "hashPath": "microsoft.extensions.fileproviders.composite.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Embedded/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-TuRh62KcoOvaSDCbtHT8K0WYptZysYQHPRRNfOgqF7ZUtUL4O0WMV8RdxbtDFJDsg3jv9bgHwXbrgwTeI9+5uQ==", "path": "microsoft.extensions.fileproviders.embedded/8.0.0", "hashPath": "microsoft.extensions.fileproviders.embedded.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "path": "microsoft.extensions.fileproviders.physical/8.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AG7HWwVRdCHlaA++1oKDxLsXIBxmDpMPb3VoyOoAghEWnkUvEAdYQUwnV4jJbAaa/nMYNiEh5ByoLauZBEiovg==", "path": "microsoft.extensions.hosting.abstractions/8.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Identity.Core/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hnXHyIQc+uc2uNMcIbr43+oNBAPEhMpW6lE8ux3MOegRz50WBna4AItlZDY7Y+Id1LLBbf73osUqeTw7CQ371w==", "path": "microsoft.extensions.identity.core/8.0.0", "hashPath": "microsoft.extensions.identity.core.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Localization/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-I4xyK9RaXyJBkU5+jQpkZITR/54tXGbdt5CsBmogf8Irrz+RXXsp1zJgrmQzxEOpxj5VMAA/GQ4SGZMeM5nVSg==", "path": "microsoft.extensions.localization/8.0.0", "hashPath": "microsoft.extensions.localization.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Localization.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LIMzXjjzv7o3tx3XXvQVHNcrMNPfSoUp4R/YJucdyBixJGRp5p8v2rF1CnRbo+wwtzB4Se/gJYyUpNN8F/TMGw==", "path": "microsoft.extensions.localization.abstractions/8.0.0", "hashPath": "microsoft.extensions.localization.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "path": "microsoft.extensions.logging/8.0.0", "hashPath": "microsoft.extensions.logging.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "path": "microsoft.extensions.logging.abstractions/8.0.0", "hashPath": "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JOVOfqpnqlVLUzINQ2fox8evY2SKLYJ3BV8QDe/Jyp21u1T7r45x/R/5QdteURMR5r01GxeJSBBUOCOyaNXA3g==", "path": "microsoft.extensions.options/8.0.0", "hashPath": "microsoft.extensions.options.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "path": "microsoft.extensions.options.configurationextensions/8.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Nito.AsyncEx.Context/5.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-rMwL7Nj3oNyvFu/jxUzQ/YBobEkM2RQHe+5mpCDRyq6mfD7vCj7Z3rjB6XgpM6Mqcx1CA2xGv0ascU/2Xk8IIg==", "path": "nito.asyncex.context/5.1.2", "hashPath": "nito.asyncex.context.5.1.2.nupkg.sha512"}, "Nito.AsyncEx.Tasks/5.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-jEkCfR2/M26OK/U4G7SEN063EU/F4LiVA06TtpZILMdX/quIHCg+wn31Zerl2LC+u1cyFancjTY3cNAr2/89PA==", "path": "nito.asyncex.tasks/5.1.2", "hashPath": "nito.asyncex.tasks.5.1.2.nupkg.sha512"}, "Nito.Disposables/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-6sZ5uynQeAE9dPWBQGKebNmxbY4xsvcc5VplB5WkYEESUS7oy4AwnFp0FhqxTSKm/PaFrFqLrYr696CYN8cugg==", "path": "nito.disposables/2.2.1", "hashPath": "nito.disposables.2.2.1.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AurL6Y5BA1WotzlEvVaIDpqzpIPvYnnldxru8oXJU2yFxFUy3+pNXjXd1ymO+RA0rq0+590Q8gaz2l3Sr7fmqg==", "path": "system.collections.immutable/8.0.0", "hashPath": "system.collections.immutable.8.0.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-c9xLpVz6PL9lp/djOWtk5KPDZq3cSYpmXoJQY524EOtuFl5z9ZtsotpsyrDW40U1DRnQSYvcPKEUV0X//u6gkQ==", "path": "system.diagnostics.diagnosticsource/8.0.0", "hashPath": "system.diagnostics.diagnosticsource.8.0.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Dynamic.Core/1.3.5": {"type": "package", "serviceable": true, "sha512": "sha512-G8aRAVHSQ/Jmt6dekX+z4K3L6ecX3CizaIYSQoDUolPRIPBYmBCabT+/pq5fMAFM57aOchFn8PUufuVfLjvEZQ==", "path": "system.linq.dynamic.core/1.3.5", "hashPath": "system.linq.dynamic.core.1.3.5.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Linq.Queryable/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-In1Bmmvl/j52yPu3xgakQSI0YIckPUr870w4K5+Lak3JCCa8hl+my65lABOuKfYs4ugmZy25ScFerC4nz8+b6g==", "path": "system.linq.queryable/4.3.0", "hashPath": "system.linq.queryable.4.3.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "path": "system.reflection.emit/4.3.0", "hashPath": "system.reflection.emit.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Loader/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHMaRn8D8YCK2GG2pw+UzNxn/OHVfaWx7OTLBD/hPegHZZgcZh3H6seWegrC4BYwsfuGrywIuT+MQs+rPqRLTQ==", "path": "system.runtime.loader/4.3.0", "hashPath": "system.runtime.loader.4.3.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "path": "system.text.encodings.web/8.0.0", "hashPath": "system.text.encodings.web.8.0.0.nupkg.sha512"}, "System.Text.Json/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON>drZ<PERSON>2WjkiEG6ajEFRABTRCi/wuXQPxeV6g8xvUJqdxMvvuCCEk86zPla8UiIQJz3durtUEbNyY/3lIhS0yZvQ==", "path": "system.text.json/8.0.0", "hashPath": "system.text.json.8.0.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "TimeZoneConverter/6.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-UGdtyKWJqXXinyvGB9X6NVoIYbTAidoZYmn3aXzxeEYC9+OL8vF36eDt1qjb6RqBkWDl4v7iE84ecI+dFhA80A==", "path": "timezoneconverter/6.1.0", "hashPath": "timezoneconverter.6.1.0.nupkg.sha512"}, "Tsp.Core.EF/1.0.19": {"type": "package", "serviceable": true, "sha512": "sha512-w1mRD9qCl6qM9dhDLyRS0+ygHq3ytacro7d6MCs8mp9s/sjI3kKLnw9TyQ2gZ3WzsqNut0UdG5wzdGysfKeIiw==", "path": "tsp.core.ef/1.0.19", "hashPath": "tsp.core.ef.1.0.19.nupkg.sha512"}, "Tsp.Core.EF.Domain/1.0.19": {"type": "package", "serviceable": true, "sha512": "sha512-5NF40Ov4y6fsql5qxgz19nn5sm06mf78ezhWIAYbxa3jdLIZq2pke27biJn0eksf38FDe2j9UVVOCDkTUcaz1A==", "path": "tsp.core.ef.domain/1.0.19", "hashPath": "tsp.core.ef.domain.1.0.19.nupkg.sha512"}, "Volo.Abp.Auditing/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-D+gHxVii18t2yXQfoSCFWvPIV+LcYXGP21O5kOShybr+7Ler8J6CXP8MDHuoBs7Tyzf0C9jPwIYXakkLVvit1Q==", "path": "volo.abp.auditing/8.1.4", "hashPath": "volo.abp.auditing.8.1.4.nupkg.sha512"}, "Volo.Abp.Auditing.Contracts/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-bL2dXDgGK4aQRWkaMVBgkpyLbfdN+8plofW7pqfMZag+fgIwrnBD0jBZI2aMU9RhsvkbYf0pPArp2PpkxxkQeQ==", "path": "volo.abp.auditing.contracts/8.1.4", "hashPath": "volo.abp.auditing.contracts.8.1.4.nupkg.sha512"}, "Volo.Abp.Authorization.Abstractions/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-zwhJm6ZZx7tU6145e98qUTSSNYYMefn2gfib26CKdXbCtr7r8GjbjUVrsn+nRhJ1kU8Va76/B7NvKk+3SPyTaA==", "path": "volo.abp.authorization.abstractions/8.1.4", "hashPath": "volo.abp.authorization.abstractions.8.1.4.nupkg.sha512"}, "Volo.Abp.AutoMapper/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-wl58RP3TyVG7e9u7NZzMRKu5XQH33z2TwIh8Cmn6SCSFQNYrMepWIdx+p40pYHSMIWUfNnjftL8AOprZo/3DTg==", "path": "volo.abp.automapper/8.1.4", "hashPath": "volo.abp.automapper.8.1.4.nupkg.sha512"}, "Volo.Abp.BackgroundWorkers/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-uoRpquLWtqf6HpKI4CJ49O6LWstvlqJNYU1pUNB3bsUBaW59imJyIy1t4GjG0lDfrBMKysejqwHlTakSE/o0Bg==", "path": "volo.abp.backgroundworkers/8.1.4", "hashPath": "volo.abp.backgroundworkers.8.1.4.nupkg.sha512"}, "Volo.Abp.BlobStoring/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-X5iKQqfAhtdZbDe7DUWqyR4auOWisJ3tjL+CiHgro6+7IiWDMlSmBMTuPaQpuCXG68Q6Afifu5h5DFnlOwOxMg==", "path": "volo.abp.blobstoring/8.1.4", "hashPath": "volo.abp.blobstoring.8.1.4.nupkg.sha512"}, "Volo.Abp.Caching/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-HHlzPtw2TqJfQyxEMqaiSgTovtBhcW+9MeboUuXTDWNz4YgczTRGHi/VX+/JIKoP2IQJnW+SAQRfzAu/8VtyJA==", "path": "volo.abp.caching/8.1.4", "hashPath": "volo.abp.caching.8.1.4.nupkg.sha512"}, "Volo.Abp.Core/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-Shswqpv1ngZ/OyJEXEXVZWmNcaLoHTDELcngeDnUovMzrFn6QqNkZzV0+liKk4jt5LSDLuQJY31H72HxzLA6Pw==", "path": "volo.abp.core/8.1.4", "hashPath": "volo.abp.core.8.1.4.nupkg.sha512"}, "Volo.Abp.Data/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-v6j4JaNSq70hvxe1T1+MC5e4YnsAeanjlxfiZLDo4NI1V0KINaS9hJuZunC1K3gLEXNAoa7pmQDPcdNOskK1Qg==", "path": "volo.abp.data/8.1.4", "hashPath": "volo.abp.data.8.1.4.nupkg.sha512"}, "Volo.Abp.Ddd.Domain/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-XgC3RaZIVumyKBDlFmHPSab6Rl+H87dDz4ltK8dT6THc8YVzZWidb0xfrSRVesOK/TZAXahvupzQc+5f+rFN3w==", "path": "volo.abp.ddd.domain/8.1.4", "hashPath": "volo.abp.ddd.domain.8.1.4.nupkg.sha512"}, "Volo.Abp.Ddd.Domain.Shared/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-n+hmsXtv84JMQow5eYRE9fVLCZq1bolvy/XPod4QeurSx9GiJSm8N05SPwaLDg5NFhyNMEGwNuUckXlNbTNKOw==", "path": "volo.abp.ddd.domain.shared/8.1.4", "hashPath": "volo.abp.ddd.domain.shared.8.1.4.nupkg.sha512"}, "Volo.Abp.DistributedLocking.Abstractions/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-lLU64EBd8vRCZ9fDC3uE2mnVQxvoqidXzMRqfkgxmIuKAy6euNQF10hbfPqkAMT6BTtjyol1dldj4IDkOwyLZQ==", "path": "volo.abp.distributedlocking.abstractions/8.1.4", "hashPath": "volo.abp.distributedlocking.abstractions.8.1.4.nupkg.sha512"}, "Volo.Abp.EntityFrameworkCore/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-9O/njxx+6SEpzRGHuw8VKvb2xwOifmJ/pwE3vIA8NPKHRgO90WHydas2+PIbDw6fyIe9RjLWkih/9g6tNdktjA==", "path": "volo.abp.entityframeworkcore/8.1.4", "hashPath": "volo.abp.entityframeworkcore.8.1.4.nupkg.sha512"}, "Volo.Abp.EventBus/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-/tdUlQaCJWZ/9+dG0AWdwyJ0EidR3FOAow/Aduw2+Zrxj034m42A+/F8OcMx/QvqIkc8TQw+El1npUaCEw69Jg==", "path": "volo.abp.eventbus/8.1.4", "hashPath": "volo.abp.eventbus.8.1.4.nupkg.sha512"}, "Volo.Abp.EventBus.Abstractions/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-U6Ttfxi5GqvsL6HR9kbrCMABg3UzT3FaUETjsUYoz+wv+4WdbdpY3LbXD90UBnDXEaI9toN6ScGE4vEXdQDGXw==", "path": "volo.abp.eventbus.abstractions/8.1.4", "hashPath": "volo.abp.eventbus.abstractions.8.1.4.nupkg.sha512"}, "Volo.Abp.ExceptionHandling/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-0jflyVlbiIzSzA2j2DJwGKQ7imbwnZ3A8DCB/IS0gA7Wwl35COLOlEV9b+lxHOdKIswgwDXDP3xrMGyYRkOCAQ==", "path": "volo.abp.exceptionhandling/8.1.4", "hashPath": "volo.abp.exceptionhandling.8.1.4.nupkg.sha512"}, "Volo.Abp.Features/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-FVR3ACMbV9I/JzRfsCYYGQQ2UrWbpK18weluBcMFOVWTu0oENJwX7JMj49oeZJnwDfGqNvHWZrkF+nLE7T5DkQ==", "path": "volo.abp.features/8.1.4", "hashPath": "volo.abp.features.8.1.4.nupkg.sha512"}, "Volo.Abp.Guids/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-v79TQ7kt84h8F9wWi/RQbJUl03Ka1aU6yozpN/D+xC8ATzn06UOennDQw48aESWtMbPnlC99Yb+11tfY8QJwcQ==", "path": "volo.abp.guids/8.1.4", "hashPath": "volo.abp.guids.8.1.4.nupkg.sha512"}, "Volo.Abp.Identity.Domain/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-dgPSfasv22KiznCUivBcfPkY6QYvMRhIt0x1UK17q1C6dunp53XLxP8GdyME1az080Cvv8Tht0eLxiEywHLhLw==", "path": "volo.abp.identity.domain/8.1.4", "hashPath": "volo.abp.identity.domain.8.1.4.nupkg.sha512"}, "Volo.Abp.Identity.Domain.Shared/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-PtAnufkyHM0RWEzWel2IOZ+oMQ1YB6qW99XXhVREOr3I9P5eSmgVQt2R35URg5WDZ4ixci13zyG5S7wHdCHeug==", "path": "volo.abp.identity.domain.shared/8.1.4", "hashPath": "volo.abp.identity.domain.shared.8.1.4.nupkg.sha512"}, "Volo.Abp.Json/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-YNM6P+kPVD5pymRiAfPZTw1PCK1/vCDwHDNXYoJIEv9z0r3Hvjx89Y8o0CyaMn8dlIHGporl4laFcGlhvk4m/w==", "path": "volo.abp.json/8.1.4", "hashPath": "volo.abp.json.8.1.4.nupkg.sha512"}, "Volo.Abp.Json.Abstractions/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-CnJZlden/3DcWHZG4QJM4CnlAH6WGCaAkQYIYabZRcYtPfgGShgVq48zb/YzJHB2hWe1/q14zOT+i1PreTgTxg==", "path": "volo.abp.json.abstractions/8.1.4", "hashPath": "volo.abp.json.abstractions.8.1.4.nupkg.sha512"}, "Volo.Abp.Json.SystemTextJson/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-c5vjP86Yw7e5Uvl54n0AChKj5McWuEVfJxJQJRCDvv70lDZALeMMhMx2E0Lg76xDRPoIPCjaC5o0Sbbw152skg==", "path": "volo.abp.json.systemtextjson/8.1.4", "hashPath": "volo.abp.json.systemtextjson.8.1.4.nupkg.sha512"}, "Volo.Abp.Localization/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-Ht2BCTFsiaFgJ8/fR1xXbpppUXeRYFyYFSkOD4/AkLpCFRotFvlxdFDJKyGzUHCqH5NJJ3WM3umAdWf/Mh+S/Q==", "path": "volo.abp.localization/8.1.4", "hashPath": "volo.abp.localization.8.1.4.nupkg.sha512"}, "Volo.Abp.Localization.Abstractions/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-JBsD9bIPodbuWKPWMCz1kSPZwyvSULMw9xzeXRi0yytOcniDTISs48PwCZL7zhurcxAcztNdDfvuhwOEETuf5g==", "path": "volo.abp.localization.abstractions/8.1.4", "hashPath": "volo.abp.localization.abstractions.8.1.4.nupkg.sha512"}, "Volo.Abp.MultiTenancy/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-EemIM2l2moTDFoEV/AJSaHFqWTE2LiXmPlupznky5nPChsJpdTuhljbmo7I/0wRR35xtuTKK79mnNBAk69Ux1w==", "path": "volo.abp.multitenancy/8.1.4", "hashPath": "volo.abp.multitenancy.8.1.4.nupkg.sha512"}, "Volo.Abp.MultiTenancy.Abstractions/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-TalCX6aBVbtne7rAc4MssuoyVSHgZL5fIeS55ZmnwVfm1XXHbeps2RPiKflyP7WfFnUFzRdGu0V+or8H6Oeydg==", "path": "volo.abp.multitenancy.abstractions/8.1.4", "hashPath": "volo.abp.multitenancy.abstractions.8.1.4.nupkg.sha512"}, "Volo.Abp.ObjectExtending/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-cnYkPQBi3WQgP+881wwJH0sMtfpBYPkLlt/sjZmgngS/HuQgUINWiTznY+8UJDxw5BOmuxSWEsIRfoOYHkg78w==", "path": "volo.abp.objectextending/8.1.4", "hashPath": "volo.abp.objectextending.8.1.4.nupkg.sha512"}, "Volo.Abp.ObjectMapping/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-eXgVvXGv0rli7e+s73dYBZd1dQZ2J1x649V3fkR1HAx8TIwxCR++9FszJmq3ZchiSqQ7m/7d3+ZyloStU4RZng==", "path": "volo.abp.objectmapping/8.1.4", "hashPath": "volo.abp.objectmapping.8.1.4.nupkg.sha512"}, "Volo.Abp.Security/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-/FVWhbDjnHWvpquMiDpVSnnDFqxNXWuAtxuS8Iq177oMN0UQhUIERruepqXjk8X3i/EYm0Nsj6rpgcTqiipS2Q==", "path": "volo.abp.security/8.1.4", "hashPath": "volo.abp.security.8.1.4.nupkg.sha512"}, "Volo.Abp.Serialization/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-DsxE6T54Zuudoc5N0TFTgrsrg07UffX48WqP+dPHEJkY3KaD9b0zxeFwmCIJMupaqi+IQyJ/qr+uiv+eGzKMrQ==", "path": "volo.abp.serialization/8.1.4", "hashPath": "volo.abp.serialization.8.1.4.nupkg.sha512"}, "Volo.Abp.Settings/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-LhibNHGZpPKiLMhGSeDp9E2RGuWrw3GxeglF6Jws83jB4hnWMzyO+4xM7ZGQQc54ANCA1of+ARlZUNQWK39grw==", "path": "volo.abp.settings/8.1.4", "hashPath": "volo.abp.settings.8.1.4.nupkg.sha512"}, "Volo.Abp.Specifications/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-WehbhDYwisIK4dqL8HEqcuyjUfXdj1Ps/5FJa6CLdO77tZXKxaw9+Um7jrECrSNOcL0r70bWiSudW0TH3C8ySQ==", "path": "volo.abp.specifications/8.1.4", "hashPath": "volo.abp.specifications.8.1.4.nupkg.sha512"}, "Volo.Abp.Threading/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-aOxSG+kpyE0cpPO1av1sFVNbVjvOHpgEENPP55jAvsKWuw1NZB6j6vS4U5+fkGrjZFFe7ILjJjA7yLLcwp15kg==", "path": "volo.abp.threading/8.1.4", "hashPath": "volo.abp.threading.8.1.4.nupkg.sha512"}, "Volo.Abp.Timing/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-nqX7H/MKfMd1fcs5mj3LGmX12A/DXyj8qXVBw7tRcu9b0Px/Bh5i3lqa9YiAb7bES1ezTLX011D46aHySrnmXw==", "path": "volo.abp.timing/8.1.4", "hashPath": "volo.abp.timing.8.1.4.nupkg.sha512"}, "Volo.Abp.Uow/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-9UjVi0qRK2X6Fw3W5Dfjz+dZ1wycey+C0jIPM4PSNKXC3sDLIh+4erQqnP2vtcKHOAUSI7LYdbu5pTTKehotuA==", "path": "volo.abp.uow/8.1.4", "hashPath": "volo.abp.uow.8.1.4.nupkg.sha512"}, "Volo.Abp.Users.Abstractions/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-iBxpFFtNoSaB0XUN39ijn93W/IbMFBGLKwlrysLQc/WspdLj35+dEszm4YAMLUlIF+qdFggc849S1FN50GI1jg==", "path": "volo.abp.users.abstractions/8.1.4", "hashPath": "volo.abp.users.abstractions.8.1.4.nupkg.sha512"}, "Volo.Abp.Users.Domain/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-6s7ukjCYkERMsTGh4sIwmrpYWfS4BQl7RPkIN6pDFFk6KYrGwxHN2aoSYsYErHT95qi3lOXni/bb2NA2J+0BOA==", "path": "volo.abp.users.domain/8.1.4", "hashPath": "volo.abp.users.domain.8.1.4.nupkg.sha512"}, "Volo.Abp.Users.Domain.Shared/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-dZs1e8tl8I3DcAsULs40YIeH8NOvsuUENovrhIGJkpTBrbB3sbBglFR0eFN/do78hdeD8aolq6ieicaA/9uRZw==", "path": "volo.abp.users.domain.shared/8.1.4", "hashPath": "volo.abp.users.domain.shared.8.1.4.nupkg.sha512"}, "Volo.Abp.Validation/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-R<PERSON>KUhTUsLkdzDv3g2Oaywm1VnmdR0KqL8MYzY5p80/NNk6SVw9UVJcfxFGcbfPD26xqvDbn7tu0O2csDa1KVQw==", "path": "volo.abp.validation/8.1.4", "hashPath": "volo.abp.validation.8.1.4.nupkg.sha512"}, "Volo.Abp.Validation.Abstractions/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-X9vjFMGbHVlZ6s9lcJAqnwtHoc0DsdHj36j0xj/d7Ra45dIPEesg66/t0NN6O0T8g0MDlhwNzShFmh9X5yeE+A==", "path": "volo.abp.validation.abstractions/8.1.4", "hashPath": "volo.abp.validation.abstractions.8.1.4.nupkg.sha512"}, "Volo.Abp.VirtualFileSystem/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-KvbiYsrAZYRS2Vm+IsNOvDm0EUoY3RE5fx7PgMOilD11DrkTVziJT2MhQoukWrOAXW7qoN3HAET1YHYyqs9tNw==", "path": "volo.abp.virtualfilesystem/8.1.4", "hashPath": "volo.abp.virtualfilesystem.8.1.4.nupkg.sha512"}, "EdTech.Study.Domain/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "EdTech.Study.Domain.Shared/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}}}