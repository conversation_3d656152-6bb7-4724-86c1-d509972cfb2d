using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EdTech.Study.Exams;
using EdTech.Study.Exams.Dtos;
using EdTech.Study.GroupQuestions;
using EdTech.Study.Questions.Dtos;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using static EdTech.Study.Permissions.QuestionsPermissions;

namespace EdTech.Study.Questions
{
    public class QuestionAppService :
        CrudAppService<
            QuestionEntity,
            QuestionDto,
            Guid,
            QuestionPagedAndSortedResultRequestDto,
            CreateUpdateQuestionDto>,
        IQuestionAppService
    {
        private readonly IRepository<QuestionEntity, Guid> _repository;
        private readonly IRepository<QuestionOptionEntity, Guid> _optionRepository;
        private readonly IRepository<MatchingItemEntity, Guid> _matchingItemRepository;
        private readonly IRepository<MatchingAnswerEntity, Guid> _matchingAnswerRepository;
        private readonly IRepository<FillInBlankAnswerEntity, Guid> _fillInBlankAnswerRepository;
        private readonly IRepository<ExamEntity, Guid> _examRepository;
        private readonly IRepository<SectionQuestionEntity, Guid> _sectionQuestionRepository;
        private readonly IRepository<SectionEntity, Guid> _sectionRepository;
        private readonly IRepository<GroupQuestionEntity, Guid> _groupQuestionRepository;

        public QuestionAppService(
            IRepository<QuestionEntity, Guid> repository,
            IRepository<QuestionOptionEntity, Guid> optionRepository,
            IRepository<MatchingItemEntity, Guid> matchingItemRepository,
            IRepository<MatchingAnswerEntity, Guid> matchingAnswerRepository,
            IRepository<FillInBlankAnswerEntity, Guid> fillInBlankAnswerRepository,
            IRepository<ExamEntity, Guid> examRepository,
            IRepository<SectionQuestionEntity, Guid> sectionQuestionRepository,
            IRepository<SectionEntity, Guid> sectionRepository,
            IRepository<GroupQuestionEntity, Guid> groupQuestionRepository)
            : base(repository)
        {
            _repository = repository;
            _optionRepository = optionRepository;
            _matchingItemRepository = matchingItemRepository;
            _matchingAnswerRepository = matchingAnswerRepository;
            _fillInBlankAnswerRepository = fillInBlankAnswerRepository;
            _examRepository = examRepository;
            _sectionQuestionRepository = sectionQuestionRepository;
            _sectionRepository = sectionRepository;
            _groupQuestionRepository = groupQuestionRepository;
        }

        protected override async Task<IQueryable<QuestionEntity>> CreateFilteredQueryAsync(QuestionPagedAndSortedResultRequestDto input)
        {
            var query = await base.CreateFilteredQueryAsync(input);

            return query
                .WhereIf(!input.Filter.IsNullOrWhiteSpace(), x =>
                    x.Content.Contains(input.Filter) ||
                    x.Topics.Contains(input.Filter) ||
                    x.Tags.Contains(input.Filter))
                .WhereIf(input.QuestionType.HasValue, x => x.QuestionType == input.QuestionType.Value)
                .WhereIf(input.Status.HasValue, x => x.Status == input.Status.Value)
                .WhereIf(input.SubjectId.HasValue, x => x.SubjectId == input.SubjectId.Value)
                .WhereIf(input.GradeId.HasValue, x => x.GradeId == input.GradeId.Value)
                .WhereIf(!input.Topic.IsNullOrWhiteSpace(), x => x.Topics.Contains(input.Topic))
                .WhereIf(!input.Tag.IsNullOrWhiteSpace(), x => x.Tags.Contains(input.Tag))
                .WhereIf(input.MinDifficulty.HasValue, x => x.Difficulty >= input.MinDifficulty.Value)
                .WhereIf(input.MaxDifficulty.HasValue, x => x.Difficulty <= input.MaxDifficulty.Value);
        }

        public override async Task<QuestionDto> CreateAsync(CreateUpdateQuestionDto input)
        {
            var optionInputs = input.Options ?? new List<CreateQuestionOptionDto>();
            var matchingItemInputs = input.MatchingItems ?? new List<CreateUpdateMatchingItemDto>();
            var matchingAnswerInputs = input.MatchingAnswers ?? new List<CreateUpdateMatchingAnswerDto>();
            var fillInBlankAnswerInputs = input.FillInBlankAnswers ?? new List<CreateUpdateFillInBlankAnswer>();
            input.Options = null; // Clear the options from the input to avoid duplication
            input.MatchingItems = null; // Clear the matching items from the input to avoid duplication
            input.MatchingAnswers = null; // Clear the matching answers from the input to avoid duplication
            input.FillInBlankAnswers = null; // Clear the fill-in-blank answers from the input to avoid duplication
            if (input.AssignedUserId.HasValue)
            {
                input.AssignedDate = DateTime.Now;
            }
            var question = await base.CreateAsync(input);

            if (optionInputs.Any())
            {
                var options = optionInputs.Select(o => new QuestionOptionEntity(Guid.NewGuid())
                {
                    QuestionId = question.Id,
                    Content = o.Content,
                    ContentFormat = o.ContentFormat,
                    IsCorrect = o.IsCorrect,
                    Order = o.Order,
                    Explanation = o.Explanation,
                    Score = o.Score
                }).ToList();

                await _optionRepository.InsertManyAsync(options);
            }

            Dictionary<Guid, Guid> itemIdToQuestionIdMap = new Dictionary<Guid, Guid>();

            if (matchingItemInputs.Any())
            {
                var matchingItems = new List<MatchingItemEntity>();
                foreach (var item in matchingItemInputs)
                {
                    var matchingItem = new MatchingItemEntity(Guid.NewGuid())
                    {
                        QuestionId = question.Id,
                        Content = item.Content,
                        ContentFormat = item.ContentFormat,
                        Type = item.Type,
                        Order = item.Order
                    };

                    itemIdToQuestionIdMap.Add(item.ClientId, matchingItem.Id);

                    matchingItems.Add(matchingItem);
                }

                await _matchingItemRepository.InsertManyAsync(matchingItems);
            }

            if (matchingAnswerInputs.Any())
            {
                // Map the client-side IDs to server-side IDs
                var matchingAnswers = new List<MatchingAnswerEntity>();
                foreach (var answer in matchingAnswerInputs)
                {
                    if (itemIdToQuestionIdMap.TryGetValue(answer.PremiseId, out var premiseId) && itemIdToQuestionIdMap.TryGetValue(answer.ResponseId, out var responseId))
                    {
                        var matchingAnswer = new MatchingAnswerEntity(Guid.NewGuid())
                        {
                            QuestionId = question.Id,
                            PremiseId = premiseId,
                            ResponseId = responseId,
                            Score = answer.Score,
                            Feedback = answer.Feedback,
                        };
                        matchingAnswers.Add(matchingAnswer);

                    }
                }

                await _matchingAnswerRepository.InsertManyAsync(matchingAnswers);


            }

            if (fillInBlankAnswerInputs.Any())
            {
                var fillInBlankAnswers = fillInBlankAnswerInputs.Select(f => new FillInBlankAnswerEntity(Guid.NewGuid())
                {
                    QuestionId = question.Id,
                    BlankIndex = f.BlankIndex,
                    CorrectAnswers = f.CorrectAnswers,
                    CaseSensitive = f.CaseSensitive,
                    Feedback = f.Feedback,
                    Score = f.Score
                }).ToList();

                await _fillInBlankAnswerRepository.InsertManyAsync(fillInBlankAnswers);
            }

            return question;
        }

        public override async Task<QuestionDto> UpdateAsync(Guid id, CreateUpdateQuestionDto input)
        {
            // Lấy entity câu hỏi hiện tại
            var questionEntity = await _repository.GetAsync(id);

            // Cập nhật thông tin cơ bản của câu hỏi
            questionEntity.Title = input.Title;
            questionEntity.Content = input.Content;
            questionEntity.ContentFormat = input.ContentFormat;
            questionEntity.QuestionType = input.QuestionType;
            questionEntity.Difficulty = input.Difficulty;
            questionEntity.Explanation = input.Explanation;
            questionEntity.ShuffleOptions = input.ShuffleOptions;
            questionEntity.SubjectId = input.SubjectId;
            questionEntity.GradeId = input.GradeId;
            questionEntity.SourceType = input.SourceType;
            questionEntity.Topics = input.Topics;
            questionEntity.Tags = input.Tags;

            questionEntity.AssignedUserId = input.AssignedUserId;
            questionEntity.DueDate = input.DueDate;
            if (questionEntity.AssignedUserId.HasValue && questionEntity.AssignedDate == null)
            {
                questionEntity.AssignedDate = DateTime.Now;
            }

            if (!questionEntity.AssignedUserId.HasValue)
            {
                questionEntity.AssignedDate = null;
            }

            if (input.Status.HasValue)
            {
                questionEntity.Status = input.Status.Value;
            }

            // Cập nhật entity câu hỏi
            await _repository.UpdateAsync(questionEntity);

            // Create a dictionary to map item IDs if needed for matching answers
            Dictionary<Guid, Guid> itemIdToQuestionIdMap = new Dictionary<Guid, Guid>();

            if (input.MatchingItems != null)
            {
                // Get existing matching items
                var existingItems = await _matchingItemRepository.GetListAsync(m => m.QuestionId == id);

                // Identify items to update, insert, and delete
                var itemsToUpdate = input.MatchingItems.Where(m => m.Id.HasValue).ToList();
                var itemsToInsert = input.MatchingItems.Where(m => !m.Id.HasValue).ToList();
                var itemsToDelete = existingItems.Where(m => !input.MatchingItems.Any(nm => nm.Id == m.Id)).ToList();

                // Delete items not in the input
                if (itemsToDelete.Any())
                {
                    await _matchingItemRepository.DeleteManyAsync(itemsToDelete);
                }

                // Update existing items
                foreach (var itemDto in itemsToUpdate)
                {
                    var existingItem = existingItems.FirstOrDefault(m => m.Id == itemDto.Id);
                    if (existingItem != null)
                    {
                        existingItem.Content = itemDto.Content;
                        existingItem.ContentFormat = itemDto.ContentFormat;
                        existingItem.Type = itemDto.Type;
                        existingItem.Order = itemDto.Order;

                        await _matchingItemRepository.UpdateAsync(existingItem);

                        // Map existing item ID for matching answers
                        if (itemDto.ClientId != Guid.Empty)
                        {
                            itemIdToQuestionIdMap[itemDto.ClientId] = existingItem.Id;
                        }
                    }
                }

                // Insert new items
                if (itemsToInsert.Any())
                {
                    var newItems = new List<MatchingItemEntity>();
                    foreach (var itemDto in itemsToInsert)
                    {
                        var newItem = new MatchingItemEntity(Guid.NewGuid())
                        {
                            QuestionId = id,
                            Content = itemDto.Content,
                            ContentFormat = itemDto.ContentFormat,
                            Type = itemDto.Type,
                            Order = itemDto.Order
                        };

                        // Map new item ID for matching answers
                        if (itemDto.ClientId != Guid.Empty)
                        {
                            itemIdToQuestionIdMap[itemDto.ClientId] = newItem.Id;
                        }

                        newItems.Add(newItem);
                    }

                    await _matchingItemRepository.InsertManyAsync(newItems);
                }
            }

            if (input.MatchingAnswers != null)
            {
                // Get existing matching answers
                var existingAnswers = await _matchingAnswerRepository.GetListAsync(m => m.QuestionId == id);

                // Identify answers to update, insert, and delete
                var answersToUpdate = input.MatchingAnswers.Where(m => m.Id.HasValue).ToList();
                var answersToInsert = input.MatchingAnswers.Where(m => !m.Id.HasValue).ToList();
                var answersToDelete = existingAnswers.Where(m => !input.MatchingAnswers.Any(nm => nm.Id == m.Id)).ToList();

                // Delete answers not in the input
                if (answersToDelete.Any())
                {
                    await _matchingAnswerRepository.DeleteManyAsync(answersToDelete);
                }

                // Update existing answers
                foreach (var answerDto in answersToUpdate)
                {
                    var existingAnswer = existingAnswers.FirstOrDefault(m => m.Id == answerDto.Id);
                    if (existingAnswer != null)
                    {
                        // Map IDs if needed
                        Guid premiseId = answerDto.PremiseId;
                        Guid responseId = answerDto.ResponseId;

                        if (itemIdToQuestionIdMap.TryGetValue(answerDto.PremiseId, out var mappedPremiseId))
                        {
                            premiseId = mappedPremiseId;
                        }

                        if (itemIdToQuestionIdMap.TryGetValue(answerDto.ResponseId, out var mappedResponseId))
                        {
                            responseId = mappedResponseId;
                        }

                        existingAnswer.PremiseId = premiseId;
                        existingAnswer.ResponseId = responseId;
                        existingAnswer.Score = answerDto.Score;
                        existingAnswer.Feedback = answerDto.Feedback;

                        await _matchingAnswerRepository.UpdateAsync(existingAnswer);
                    }
                }

                // Insert new answers
                if (answersToInsert.Any())
                {
                    var newAnswers = new List<MatchingAnswerEntity>();
                    foreach (var answerDto in answersToInsert)
                    {
                        Guid premiseId = answerDto.PremiseId;
                        Guid responseId = answerDto.ResponseId;

                        // Map client IDs to server IDs if needed
                        if (itemIdToQuestionIdMap.TryGetValue(answerDto.PremiseId, out var mappedPremiseId))
                        {
                            premiseId = mappedPremiseId;
                        }

                        if (itemIdToQuestionIdMap.TryGetValue(answerDto.ResponseId, out var mappedResponseId))
                        {
                            responseId = mappedResponseId;
                        }

                        var newAnswer = new MatchingAnswerEntity(Guid.NewGuid())
                        {
                            QuestionId = id,
                            PremiseId = premiseId,
                            ResponseId = responseId,
                            Score = answerDto.Score,
                            Feedback = answerDto.Feedback
                        };

                        newAnswers.Add(newAnswer);
                    }

                    await _matchingAnswerRepository.InsertManyAsync(newAnswers);
                }
            }

            if (input.Options != null)
            {
                // Get existing options
                var existingOptions = await _optionRepository.GetListAsync(o => o.QuestionId == id);

                // Identify options to update, insert, and delete
                var optionsToUpdate = input.Options.Where(o => o.Id.HasValue).ToList();
                var optionsToInsert = input.Options.Where(o => !o.Id.HasValue).ToList();
                var optionsToDelete = existingOptions.Where(o => !input.Options.Any(no => no.Id == o.Id)).ToList();

                // Delete options not in the input
                if (optionsToDelete.Any())
                {
                    await _optionRepository.DeleteManyAsync(optionsToDelete);
                }

                // Update existing options
                foreach (var optionDto in optionsToUpdate)
                {
                    var existingOption = existingOptions.FirstOrDefault(o => o.Id == optionDto.Id);
                    if (existingOption != null)
                    {
                        existingOption.Content = optionDto.Content;
                        existingOption.ContentFormat = optionDto.ContentFormat;
                        existingOption.IsCorrect = optionDto.IsCorrect;
                        existingOption.Explanation = optionDto.Explanation;
                        existingOption.Score = optionDto.Score;
                        existingOption.Order = optionDto.Order;

                        await _optionRepository.UpdateAsync(existingOption);
                    }
                }

                // Insert new options
                if (optionsToInsert.Any())
                {
                    var newOptions = optionsToInsert.Select(o => new QuestionOptionEntity(Guid.NewGuid())
                    {
                        QuestionId = id,
                        Content = o.Content,
                        ContentFormat = o.ContentFormat,
                        IsCorrect = o.IsCorrect,
                        Order = o.Order,
                        Explanation = o.Explanation,
                        Score = o.Score
                    }).ToList();

                    await _optionRepository.InsertManyAsync(newOptions);
                }
            }

            if (input.FillInBlankAnswers != null)
            {
                // Get existing fill-in-blank answers
                var existingAnswers = await _fillInBlankAnswerRepository.GetListAsync(f => f.QuestionId == id);

                // Identify answers to update, insert, and delete
                var answersToUpdate = input.FillInBlankAnswers.Where(f => f.Id.HasValue).ToList();
                var answersToInsert = input.FillInBlankAnswers.Where(f => !f.Id.HasValue).ToList();
                var answersToDelete = existingAnswers.Where(f => !input.FillInBlankAnswers.Any(nf => nf.Id == f.Id)).ToList();

                // Delete answers not in the input
                if (answersToDelete.Any())
                {
                    await _fillInBlankAnswerRepository.DeleteManyAsync(answersToDelete);
                }

                // Update existing answers
                foreach (var answerDto in answersToUpdate)
                {
                    var existingAnswer = existingAnswers.FirstOrDefault(f => f.Id == answerDto.Id);
                    if (existingAnswer != null)
                    {
                        existingAnswer.BlankIndex = answerDto.BlankIndex;
                        existingAnswer.CorrectAnswers = answerDto.CorrectAnswers;
                        existingAnswer.CaseSensitive = answerDto.CaseSensitive;
                        existingAnswer.Feedback = answerDto.Feedback;
                        existingAnswer.Score = answerDto.Score;

                        await _fillInBlankAnswerRepository.UpdateAsync(existingAnswer);
                    }
                }

                // Insert new answers
                if (answersToInsert.Any())
                {
                    var newAnswers = answersToInsert.Select(f => new FillInBlankAnswerEntity(Guid.NewGuid())
                    {
                        QuestionId = id,
                        BlankIndex = f.BlankIndex,
                        CorrectAnswers = f.CorrectAnswers,
                        CaseSensitive = f.CaseSensitive,
                        Feedback = f.Feedback,
                        Score = f.Score
                    }).ToList();

                    await _fillInBlankAnswerRepository.InsertManyAsync(newAnswers);
                }
            }

            return ObjectMapper.Map<QuestionEntity, QuestionDto>(questionEntity);
        }

        public async Task<QuestionDto> PublishAsync(Guid id)
        {
            var question = await _repository.GetAsync(id);
            question.Status = Enum.ExamStatus.Published;
            await _repository.UpdateAsync(question);
            return ObjectMapper.Map<QuestionEntity, QuestionDto>(question);
        }

        public async Task<QuestionDto> UnpublishAsync(Guid id)
        {
            var question = await _repository.GetAsync(id);
            question.Status = Enum.ExamStatus.Draft;
            await _repository.UpdateAsync(question);
            return ObjectMapper.Map<QuestionEntity, QuestionDto>(question);
        }

        public async Task<List<QuestionDto>> GetByIdsAsync(List<Guid> ids)
        {
            var questions = await _repository.GetListAsync(x => ids.Contains(x.Id));
            return ObjectMapper.Map<List<QuestionEntity>, List<QuestionDto>>(questions);
        }

        public async Task<PagedResultDto<QuestionDto>> GetQuestionsBySubjectAsync(
            Guid subjectId,
            QuestionPagedAndSortedResultRequestDto input)
        {
            input.SubjectId = subjectId;
            return await GetListAsync(input);
        }

        public async Task<QuestionDto> UpdateStatusAsync(Guid id, UpdateQuestionStatusRequest status)
        {
            var question = await _repository.GetAsync(id);
            question.Status = status.Status;
            await _repository.UpdateAsync(question);
            return ObjectMapper.Map<QuestionEntity, QuestionDto>(question);
        }


        public async Task<List<Guid>> DeleteManyAsync(List<Guid> ids)
        {
            foreach (var id in ids)
            {
                // Delete related entities first
                await _optionRepository.DeleteAsync(x => x.QuestionId == id);
                await _matchingItemRepository.DeleteAsync(x => x.QuestionId == id);
                await _matchingAnswerRepository.DeleteAsync(x => x.QuestionId == id);
                await _fillInBlankAnswerRepository.DeleteAsync(x => x.QuestionId == id);
            }

            // Delete the questions
            await _repository.DeleteManyAsync(ids);

            return ids;
        }

        public async Task<BatchUpdateStatusResultDto> UpdateBatchStatusAsync(BatchUpdateStatusRequestDto request)
        {
            if (request.Ids == null || !request.Ids.Any())
            {
                throw new UserFriendlyException("No question IDs provided");
            }

            var questions = await _repository.GetListAsync(q => request.Ids.Contains(q.Id));

            if (!questions.Any())
            {
                throw new UserFriendlyException("No questions found with the provided IDs");
            }

            // Update status for all found questions
            foreach (var question in questions)
            {
                question.Status = request.Status;
            }

            // Update all questions in a single operation
            await _repository.UpdateManyAsync(questions);

            // Return the IDs of the updated questions
            return new BatchUpdateStatusResultDto
            {
                Ids = questions.Select(q => q.Id).ToList(),
                Status = request.Status
            };
        }

        public async Task<List<ExamDto>> GetExamsByQuestionIdAsync(Guid questionId)
        {
            // Find all section questions that reference this question
            var sectionQuestions = await _sectionQuestionRepository.GetListAsync(sq => sq.QuestionId == questionId);

            if (!sectionQuestions.Any())
            {
                return new List<ExamDto>();
            }

            // Get the section IDs
            var sectionIds = sectionQuestions.Select(sq => sq.SectionId).Distinct().ToList();

            // Find all sections
            var sections = await _sectionRepository.GetListAsync(s => sectionIds.Contains(s.Id));

            // Get the exam IDs
            var examIds = sections.Select(s => s.ExamId).Distinct().ToList();

            // Find all exams
            var exams = await _examRepository.GetListAsync(e => examIds.Contains(e.Id));

            // Map to DTOs
            return ObjectMapper.Map<List<ExamEntity>, List<ExamDto>>(exams);
        }

        public async Task<GroupQuestionDto> GetGroupQuestionByQuestionIdAsync(Guid questionId)
        {
            // Get the question to find its GroupQuestionId
            var question = await _repository.GetAsync(questionId);

            if (question.GroupQuestionId == null || question.GroupQuestionId == Guid.Empty)
            {
                throw new UserFriendlyException("This question is not part of any group");
            }

            // Get the group question
            var groupQuestion = (await _groupQuestionRepository.WithDetailsAsync(x => x.Questions)).FirstOrDefault(x => x.Id.Equals(question.GroupQuestionId.Value));

            if (groupQuestion == null)
            {
                throw new UserFriendlyException("Group question not found");
            }

            // Map to DTO and return
            return ObjectMapper.Map<GroupQuestionEntity, GroupQuestionDto>(groupQuestion);
        }

        public async Task<GroupQuestionDto> UpdateGroupQuestionAsync(Guid groupQuestionId, UpdateGroupInfoDto input)
        {
            // Get the group question entity
            var groupQuestion = await _groupQuestionRepository.GetAsync(groupQuestionId);

            if (groupQuestion == null)
            {
                throw new UserFriendlyException("Group question not found");
            }

            // Update the group question properties
            groupQuestion.Content = input.Content;
            groupQuestion.ContentFormat = input.ContentFormat;
            groupQuestion.Instructions = input.Instructions;

            // Save the changes
            await _groupQuestionRepository.UpdateAsync(groupQuestion);


            // Include
            var questions = await Repository.GetListAsync(x => x.GroupQuestionId.HasValue && x.GroupQuestionId == groupQuestionId);

            // Return the updated group question
            return ObjectMapper.Map<GroupQuestionEntity, GroupQuestionDto>(groupQuestion);
        }

        public async Task<QuestionDto> AssignToUserAsync(Guid id, AssignQuestionDto input)
        {
            var question = await _repository.GetAsync(id);

            question.AssignedUserId = input.UserId;
            question.AssignedDate = DateTime.Now;
            question.DueDate = input.DueDate;

            await _repository.UpdateAsync(question);

            return ObjectMapper.Map<QuestionEntity, QuestionDto>(question);
        }

        public async Task<QuestionDto> UnassignAsync(Guid id)
        {
            var question = await _repository.GetAsync(id);

            question.AssignedUserId = null;
            question.AssignedDate = null;
            question.DueDate = null;

            await _repository.UpdateAsync(question);

            return ObjectMapper.Map<QuestionEntity, QuestionDto>(question);
        }

        public async Task<List<QuestionDto>> GetQuestionsByAssignedUserAsync(Guid userId)
        {
            var questions = await _repository.GetListAsync(q => q.AssignedUserId == userId);
            return ObjectMapper.Map<List<QuestionEntity>, List<QuestionDto>>(questions);
        }

        public async Task<BatchAssignResultDto> BatchAssignToUserAsync(BatchAssignQuestionsDto input)
        {
            var result = new BatchAssignResultDto
            {
                UserId = input.UserId,
                DueDate = input.DueDate
            };

            // Kiểm tra input QuestionIds, SubjectId, GradeId phải có it nhất một giá trị
            if (input.QuestionIds == null || !input.QuestionIds.Any() &&
                !input.SubjectId.HasValue && !input.GradeId.HasValue)
            {
                throw new UserFriendlyException("At least one of QuestionIds, SubjectId, GradeId or AssignAll must be provided.");
            }

            // Xây dựng query dựa trên điều kiện
            var query = await Repository.GetQueryableAsync();

            // Lọc theo ID nếu có
            if (input.QuestionIds != null && input.QuestionIds.Any())
            {
                query = query.Where(q => input.QuestionIds.Contains(q.Id));
            }

            // Lọc theo môn học
            if (input.SubjectId.HasValue)
            {
                query = query.Where(q => q.SubjectId == input.SubjectId.Value);
            }

            // Lọc theo lớp
            if (input.GradeId.HasValue)
            {
                query = query.Where(q => q.GradeId == input.GradeId.Value);
            }

            // Lọc theo trạng thái gán
            if (!input.AssignAll)
            {
                query = query.Where(q => q.AssignedUserId == null);
            }

            // Lấy danh sách câu hỏi cần gán
            var questions = await AsyncExecuter.ToListAsync(query);

            // Cập nhật thông tin gán cho từng câu hỏi
            foreach (var question in questions)
            {
                question.AssignedUserId = input.UserId;
                question.AssignedDate = DateTime.Now;
                question.DueDate = input.DueDate;

                result.AssignedQuestionIds.Add(question.Id);
            }

            // Cập nhật hàng loạt
            if (questions.Any())
            {
                await _repository.UpdateManyAsync(questions);
            }

            result.AssignedCount = questions.Count;

            return result;
        }

        public async Task<BatchUnassignResultDto> BatchUnassignQuestionsAsync(List<Guid> questionIds)
        {
            if (questionIds == null || !questionIds.Any())
            {
                throw new UserFriendlyException("No question IDs provided");
            }

            // Get questions that need to be unassigned
            var questions = await _repository.GetListAsync(q => questionIds.Contains(q.Id) && q.AssignedUserId != null);

            if (!questions.Any())
            {
                return new BatchUnassignResultDto
                {
                    UnassignedQuestionIds = new List<Guid>()
                };
            }

            // Update assignment information for all questions
            foreach (var question in questions)
            {
                question.AssignedUserId = null;
                question.AssignedDate = null;
                question.DueDate = null;
            }

            // Update all questions in a single operation
            await _repository.UpdateManyAsync(questions);

            // Return the IDs of the unassigned questions
            return new BatchUnassignResultDto
            {
                UnassignedQuestionIds = questions.Select(q => q.Id).ToList()
            };
        }
    }
}
