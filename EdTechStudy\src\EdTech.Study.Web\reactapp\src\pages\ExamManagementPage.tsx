import ReactDOM from 'react-dom/client';
import { lazy, Suspense } from 'react';
import { Provider } from 'react-redux';
import LoadingScreen from '../components/common/Loading/LoadingScreen';
import { questionStore } from '../store/questionStore';
import '../index.css';
import '../styles/modal-fixes.css';
import { CombinedThemeProvider } from '../providers/theme';
import { MathJaxProvider } from '../components/common/MathJax/MathJaxWrapper';
import QuestionProviderLayout from '../providers/Questions/QuestionProvider';
import { registerLicenseSyncfusionBase } from '../utils/syncfusion-license';
import { useDevLicenseSyncfusion } from './syncfusion-license-custom';
const ExamManagementRouter = lazy(
  () =>
    import(
      /* webpackChunkName: "exam-management-router" */ '../routing/ExamManagementRouting'
    )
);

registerLicenseSyncfusionBase();
useDevLicenseSyncfusion();

export const ExamManagementPage = () => {
  return (
    <Provider store={questionStore}>
      <CombinedThemeProvider>
        <Suspense fallback={<LoadingScreen />}>
          {/* better-react-mathjax */}
          <MathJaxProvider>
            <QuestionProviderLayout>
              <ExamManagementRouter />
            </QuestionProviderLayout>
          </MathJaxProvider>
        </Suspense>
      </CombinedThemeProvider>
    </Provider>
  );
};

const initApp = () => {
  const rootElement = document.getElementById('ExamManagementPage');

  if (rootElement) {
    const root = ReactDOM.createRoot(rootElement);
    root.render(<ExamManagementPage />);
  } else {
    console.error('Cannot find root element with id "ExamManagementPage"');
  }
};

initApp();
