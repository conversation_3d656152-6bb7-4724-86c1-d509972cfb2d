/* Drag and drop styling */
.sortable-item {
  transition: all 0.3s ease;
}

.sortable-item[data-is-dragging='true'] {
  cursor: grabbing;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  background: #fff;
  z-index: 10;
}

.sortable-item:not([data-is-dragging='true']):hover {
  background-color: #f9f9f9;
}

.drag-handle {
  cursor: grab;
  color: #aaa;
}

.drag-handle:hover {
  color: #1890ff;
}

.exam-section-card {
  border-radius: 6px;
  transition: all 0.2s ease;
  border-color: transparent;
}

.exam-section-card:hover {
  border-color: #d9d9d9;
}

.exam-section-card .ant-card-head {
  background-color: var(--background-default);
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}

/* Animation for drag overlay */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(24, 144, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
  }
}

.ant-drag-overlay {
  animation: pulse 2s infinite;
}

/* Transition for section editing */
.section-edit-form {
  transition: max-height 0.3s ease-in-out, opacity 0.3s ease-in-out;
  max-height: 0;
  opacity: 0;
  overflow: hidden;
}

.section-edit-form.active {
  max-height: 1000px;
  opacity: 1;
}

/* Question editor modal customization */
.question-editor-modal .ant-modal-content {
  border-radius: 8px;
  overflow: hidden;
}

.question-editor-modal .ant-modal-header {
  background-color: #f8f9fa;
}

.question-editor-modal .ant-tabs-nav {
  margin-bottom: 24px;
}

/* Improve form spacing */
.exam-editor-form .ant-form-item {
  margin-bottom: 16px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .sortable-item {
    margin-bottom: 12px;
  }

  .question-actions {
    flex-direction: column;
  }

  .ant-form-item {
    margin-bottom: 12px;
  }
}

.width-transition-animation {
  transition: all 0.3s ease-in-out;
}

.exam-editor-toolbar {
  border-radius: 0.5rem 0;
}

.exam-editor-toolbar.active-sticky {
  border-radius: 0 0.5rem;
}

.carousel-btn {
  border-radius: 50%;
  width: 3rem !important;
  height: 3rem !important;
  background-color: var(--edtt-color-primary);
  color: var(--edtt-color-white);
  position: fixed;
  z-index: 1000;
  left: 50%;
  transform: translateX(-50%);
}

.carousel-btn:hover {
  background-color: var(--background-primary-hover);
}

.carousel-btn-top {
  top: 2rem;
}

.carousel-btn-bottom {
  bottom: 2rem;
}

.section-question-editor-wrapper
  .single-question-container
  > .ant-card.ant-card-bordered {
  border: none !important;
}
