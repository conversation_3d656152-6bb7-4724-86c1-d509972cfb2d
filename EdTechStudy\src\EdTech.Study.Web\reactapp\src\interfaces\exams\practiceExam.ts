// src/interfaces/exams/practiceExam.ts
import { ExamBase } from './examBase';

/**
 * Practice exam status
 */
export enum PracticeExamStatus {
  NOT_STARTED = 'NOT_STARTED', // Chưa làm
  IN_PROGRESS = 'IN_PROGRESS', // Đang làm
  COMPLETED = 'COMPLETED', // Hoàn thành
}

export interface IPracticeExamComponentProps {
  onPreviewExam?: (exam: ExamBase) => void;
}
/**
 * Interface for exam attempts tracking user's progress
 */
export interface IExamAttempt {
  id: string;
  examId: string;
  userId: string;
  startTime: Date;
  endTime?: Date;
  progress: number; // Tiến độ (0-100)
  status: PracticeExamStatus;
  score?: number; // Điểm số (0-100)
  timeSpent: number; // Thời gian đã làm (phút)
  isCompleted: boolean;
  submittedAt?: Date;
}

/**
 * Practice exam data interface
 */
export interface IPracticeExamData extends ExamBase {
  // Core metadata
  questionCount: number; // Tổng số câu hỏi
  estimatedDuration: number; // Thời gian ước tính (phút)

  // User-specific data (populated when fetched for specific user)
  userProgress?: number; // Tiến độ hiện tại của user (0-100)
  userStatus?: PracticeExamStatus; // Trạng thái của user với đề này
  userBestScore?: number; // Điểm cao nhất đạt được
  userAttempt?: IExamAttempt; // Lần làm gần nhất của user
}

/**
 * Filter options for practice exam list
 */
export interface IPracticeExamFilters {
  subjects?: string[]; // Lọc theo môn học
  grades?: string[]; // Lọc theo lớp
  statuses?: PracticeExamStatus[]; // Lọc theo trạng thái làm bài
  searchQuery?: string; // Từ khóa tìm kiếm
}

/**
 * Pagination for practice exam list
 */
export interface IPracticeExamPagination {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalItems: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

/**
 * State interface for PracticeExamPage
 */
export interface IPracticeExamPageState {
  exams: IPracticeExamData[];
  loading: boolean;
  error: string | null;
  filters: IPracticeExamFilters;
  pagination: IPracticeExamPagination;
  viewMode: 'grid' | 'list';
}

/**
 * Props interface for PracticeExamPage component
 */
export interface IPracticeExamPageProps {
  userId?: string;
  className?: string;
}

/**
 * Metadata for filter options (from API)
 */
export interface IExamFilterMetadata {
  subjects: IExamFilterOption[];
  grades: IExamFilterOption[];
}

/**
 * Filter option structure
 */
export interface IExamFilterOption {
  value: string;
  label: string;
}

/**
 * Response structure from API
 */
export interface IPracticeExamApiResponse {
  exams: IPracticeExamData[];
  pagination: IPracticeExamPagination;
  totalCount: number;
}

/**
 * API request parameters
 */
export interface IPracticeExamApiParams {
  userId?: string;
  filters?: IPracticeExamFilters;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  page?: number;
  pageSize?: number;
}

/**
 * Exam start response from API
 */
export interface IExamStartResponse {
  attemptId: string;
  examId: string;
  startTime: Date;
  duration: number; // Thời gian làm bài (phút)
}

/**
 * Exam submit request
 */
export interface IExamSubmitRequest {
  examId: string;
  userId: string;
  attemptId: string;
  answers: any[];
  timeSpent: number; // Thời gian làm bài (phút)
}

/**
 * Exam result response
 */
export interface IExamResultResponse {
  attemptId: string;
  examId: string;
  score: number; // Điểm số (0-100)
  correctAnswers: number; // Số câu đúng
  totalQuestions: number; // Tổng số câu
  timeSpent: number; // Thời gian làm bài (phút)
  details: IQuestionResult[]; // Chi tiết từng câu
}

/**
 * Result for individual question
 */
export interface IQuestionResult {
  questionId: string;
  isCorrect: boolean;
  userAnswer: any;
  correctAnswer: any;
  score: number;
  maxScore: number;
}
