import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON>, Button } from 'antd';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

class LazyLoadErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    console.error('LazyLoad Error:', error, errorInfo);
  }

  render(): ReactNode {
    if (this.state.hasError) {
      return (
        this.props.fallback || (
          <Alert
            message="Something went wrong"
            description={
              <div>
                <p>There was an error loading this component.</p>
                <Button
                  type="primary"
                  onClick={() =>
                    this.setState({ hasError: false, error: null })
                  }
                >
                  Try again
                </Button>
              </div>
            }
            type="error"
            showIcon
          />
        )
      );
    }

    return this.props.children;
  }
}

export default LazyLoadErrorBoundary;
