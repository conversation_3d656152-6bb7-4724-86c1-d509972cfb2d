﻿using System;
using System.Collections.Generic;
using Volo.Abp.Domain.Entities.Auditing;
using EdTech.Study.Subject;
using System.ComponentModel.DataAnnotations;
using EdTech.Study.Enum;

namespace EdTech.Study.Questions
{
    /// <summary>
    /// Bộ câu hỏi soạn thảo
    /// </summary>
    public class QuestionDraftEntity : AuditedAggregateRoot<Guid>
    {
        public QuestionDraftEntity()
        {
        }

        public QuestionDraftEntity(Guid key) : base(key)
        {
        }

        /// <summary>
        /// Nội dung câu hỏi
        /// </summary>
        [Required]
        public string Content { get; set; }

        /// <summary>
        /// Định dạng nội dung câu hỏi: html, text
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string ContentFormat { get; set; }

        /// <summary>
        /// Loại câu hỏi
        /// </summary>
        public QuestionType Type { get; set; }

        /// <summary>
        /// <PERSON><PERSON> khó thang điểm từ 1 - 5
        /// </summary>
        public int DifficultyLevel { get; set; }
        /// <summary>
        /// Mô tả
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Danh sách chủ đề(Cách nhau bởi đấu phẩy)
        /// </summary>
        public string? Topics { get; set; }

        /// <summary>
        /// Danh sách gắn nhãn (Cách nhau bởi đấu phẩy)
        /// </summary>
        public string? Tags { get; set; }

        #region xử lý câu hỏi soạn thảo
        /// <summary>
        /// Trạng thái
        /// </summary>
        public ExamStatus Status { get; set; }

        /// <summary>
        /// Bình luận
        /// </summary>
        [MaxLength(512)]
        public string? Comment { get; set; }

        /// <summary>
        /// Đảm bảo duy nhất mỗi request thêm
        /// </summary>
        [MaxLength(256)]
        public string? IdempotentKey { get; set; }

        /// <summary>
        /// Nguồn gốc
        /// </summary>
        public string? Source { get; set; } 

        #endregion

        public Guid? SubjectId { get; set; }

        public virtual Subject.Subject Subject { get; set; }

        public virtual IList<QuestionOptionDraftEntity> Options { get; set; }
    }
}