import React, { Suspense, useCallback, useEffect, useState } from 'react';
import {
  BaseQuestion,
  getQuestionComponentRegistry,
  BaseAnswer,
  isAnswerCorrect,
} from '../../../interfaces/quizs/questionBase';
import { getQuestionTypeString } from '../../../utils/questionUtil';
import LoadingScreen from '../Loading/LoadingScreen';
import { Button, Card, Typography, Alert, Tag, Space } from 'antd';
import {
  CheckOutlined,
  CloseOutlined,
  EyeInvisibleOutlined,
  EyeOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import practiceLocalization from '../../quizs/localization';

interface QuestionPreviewProps {
  question: BaseQuestion;
}

const QuestionPreview: React.FC<QuestionPreviewProps> = ({ question }) => {
  console.log('🚀 ~ question:', question);
  const [showResult, setShowResult] = useState(false);

  const questionRegistry = getQuestionComponentRegistry();
  const QuestionRenderer = questionRegistry.getComponent(question.type);

  const [currentQuestion, setCurrentQuestion] = useState(question);

  useEffect(() => {
    setCurrentQuestion(question);
  }, [question]);

  if (!QuestionRenderer) {
    return (
      <div className="question-preview-error">
        Không tìm thấy component cho loại câu hỏi này
      </div>
    );
  }

  const handleComplete = (questionId: string, answer: BaseAnswer) => {
    console.log('🚀 ~ handleComplete ~ answer:', answer);
    setCurrentQuestion((prev) => {
      let output = { ...prev };

      switch (prev.type) {
        case practiceLocalization.quiz:
        case practiceLocalization.multiselect:
        case practiceLocalization.fillblanks:
        case practiceLocalization.matching:
          output.userSelect = answer;
          break;
        case practiceLocalization.essay:
          output.userAnswer = answer;
          break;
      }

      return output;
    });
  };

  const toggleShowAnswer = () => {
    setShowResult(!showResult);
  };

  return (
    <div id={currentQuestion.clientId} className="question-preview">
      <Suspense fallback={<LoadingScreen />}>
        <div className="tailwind-ml-4 tailwind-mr-4">
          <QuestionRenderer.component
            id={'preview'}
            questionIndex={1}
            question={currentQuestion}
            onComplete={handleComplete}
            options={{
              hideSaveButton: true,
              hideDeleteButton: true,
              hideFeedback: false,
            }}
          />
        </div>

        <div className="tailwind-mt-4 tailwind-flex tailwind-justify-center">
          <Button
            type="primary"
            onClick={toggleShowAnswer}
            icon={showResult ? <EyeInvisibleOutlined /> : <EyeOutlined />}
          >
            {showResult ? 'Ẩn kết quả' : 'Xem kết quả'}
          </Button>
        </div>

        {showResult && (
          <div className="tailwind-m-4">
            <QuestionRenderer.component
              question={currentQuestion}
              configMode={false}
              id="preview-result"
              questionIndex={1}
              showResult={true}
            />
          </div>
        )}
      </Suspense>
    </div>
  );
};

export default QuestionPreview;
