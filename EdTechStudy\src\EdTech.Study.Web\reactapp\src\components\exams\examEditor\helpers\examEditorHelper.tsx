import { ExamStatus } from '../../../../interfaces/exams/examEnums';
import {
  CheckOutlined,
  GlobalOutlined,
  EditOutlined,
  CloseOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons';

export const getExamStatusIcon = (status: ExamStatus): React.ReactNode => {
  switch (status) {
    case ExamStatus.Submitted:
      return <ClockCircleOutlined />;
    case ExamStatus.Approved:
      return <CheckOutlined />;
    case ExamStatus.Published:
      return <GlobalOutlined />;
    case ExamStatus.Rejected:
      return <CloseOutlined />;
    case ExamStatus.Draft:
    default:
      return <EditOutlined />;
  }
};
