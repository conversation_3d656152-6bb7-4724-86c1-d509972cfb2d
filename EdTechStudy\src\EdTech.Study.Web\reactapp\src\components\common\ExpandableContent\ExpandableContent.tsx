import { useState, useRef, PropsWithChildren, useEffect } from 'react';
import { Transition, TransitionStatus } from 'react-transition-group';

function ExpandableContent({ children }: PropsWithChildren) {
  const [expanded, setExpanded] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);

  // Get the natural height of the content
  const getHeight = () => {
    return contentRef.current ? contentRef.current.scrollHeight : 0;
  };

  // Define transition styles
  const transitionStyles: Record<TransitionStatus, React.CSSProperties> = {
    entering: { height: getHeight() + 'px', opacity: 1 },
    entered: { height: 'auto', opacity: 1 },
    exiting: { height: '0px', opacity: 0 },
    exited: { height: '0px', opacity: 0 },
    unmounted: { height: '0px', opacity: 0 },
  };

  const defaultStyle = {
    transition: 'height 300ms ease-in-out, opacity 300ms ease-in-out',
    overflow: 'hidden',
    opacity: 0,
    height: '0px',
  };

  const isInit = useRef<boolean>(false);
  useEffect(() => {
    if (!isInit.current) {
      setTimeout(() => {
        setExpanded(true);
      }, 500);
      isInit.current = true;
    }
  }, []);

  return (
    <Transition in={expanded} timeout={300} mountOnEnter unmountOnExit>
      {(state) => (
        <div
          ref={contentRef}
          style={{
            ...defaultStyle,
            ...transitionStyles[state],
          }}
        >
          {children}
        </div>
      )}
    </Transition>
  );
}

export default ExpandableContent;
