import { Suspense } from 'react';
import { B<PERSON>er<PERSON>outer, <PERSON> } from 'react-router-dom';
import { Routes } from 'react-router-dom';
import LoadingScreen from '../components/common/Loading/LoadingScreen';
import ReactDOM from 'react-dom/client';
import { Route } from 'react-router-dom';
import { ExamManagementPage } from './ExamManagementPage';
import { registerLicenseSyncfusionBase } from '../utils/syncfusion-license';

registerLicenseSyncfusionBase();
export const BasePage = () => {
  // console.log('BasePage');
  return (
    <BrowserRouter basename={'/'}>
      <Suspense fallback={<LoadingScreen />}>
        <Routes>
          <Route path="/ExamManagement/*" element={<ExamManagementPage />} />
          <Route
            path="/"
            element={
              <>
                <Link to="/ExamManagement">Danh sách đề thi</Link>
              </>
            }
          />
        </Routes>
      </Suspense>
    </BrowserRouter>
  );
};

const rootQuestionPageElement = document.getElementById('root') as HTMLElement;

const rootQuestionPage = ReactDOM.createRoot(rootQuestionPageElement);
rootQuestionPage.render(<BasePage />);
