using EdTech.Study.BlobContainers;
using EdTech.Study.Dtos.JsonConfig;
using EdTech.Study.IServices;
using EdTech.Study.Lessons;
using EdTech.Study.Lessons.Dtos;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.BlobStoring;
using Volo.Abp.VirtualFileSystem;

namespace EdTech.Study.Services
{
    public partial class OfflineExportService : IOfflineExportService
    {
        private const string TemplateFilePath = "/wwwroot/template/index.html";
        private readonly HashSet<string> _processedPaths = [];

        private readonly IJsonDataService _jsonDataService;
        private readonly IDefaultLessonAppService _defaultLessonAppService;
        private readonly ILogger<OfflineExportService> _logger;
        private readonly IConfiguration _configuration;
        private readonly IVirtualFileProvider _virtualFileProvider;
        private readonly IBlobContainer<TempFileContainer> _tempFileContainer;

        public OfflineExportService(
            IJsonDataService jsonDataService,
            IDefaultLessonAppService defaultLessonAppService,
            ILogger<OfflineExportService> logger,
            IConfiguration configuration,
            IVirtualFileProvider virtualFileProvider,
            IBlobContainer<TempFileContainer> tempFileContainer)
        {
            _jsonDataService = jsonDataService;
            _defaultLessonAppService = defaultLessonAppService;
            _logger = logger;
            _configuration = configuration;
            _virtualFileProvider = virtualFileProvider;
            _tempFileContainer = tempFileContainer;
        }

        public async Task<byte[]> ExportLessonForOfflineUseAsync(OfflineExportLessonRequest input)
        {
            try
            {
                // Clear the processed paths for a fresh export
                _processedPaths.Clear();

                // Get lesson data
                var lesson = await _defaultLessonAppService.GetLessonByKey(input.Key)
                    ?? throw new UserFriendlyException($"Lesson with key {input.Key} not found");

                // Generate the preview URL parts
                var parts = input.Key.Split('_');
                if (parts.Length != 3)
                {
                    throw new UserFriendlyException("Invalid lesson key format");
                }

                string grade = parts[0];
                string subject = parts[1];
                string lessonCode = parts[2];

                // Create the base URL for relative path resolution
                var baseUrl = _configuration["Application:SelfUrl"] ?? "https://localhost:44348";

                _logger.LogInformation("Creating offline export for lesson: {Key}", input.Key);

                // Create a memory stream to store the zip file
                using var memoryStream = new MemoryStream();
                using var archive = new ZipArchive(memoryStream, ZipArchiveMode.Create, true);

                // Read the template HTML file directly from the virtual file system
                var templateFile = _virtualFileProvider.GetFileInfo(TemplateFilePath);
                if (!templateFile.Exists)
                {
                    throw new UserFriendlyException($"Template file not found at {TemplateFilePath}");
                }

                string html;
                using (var stream = templateFile.CreateReadStream())
                using (var reader = new StreamReader(stream))
                {
                    html = await reader.ReadToEndAsync();
                }

                // Process the HTML to make it work offline
                var processedHtml = await ProcessHtmlForOfflineUse(html, archive, baseUrl);

                // Replace the JSON config placeholder with actual data
                var jsonData = input.JsonConfig.IsNullOrEmpty()? await _jsonDataService.GetJsonDataAsync(input.Key): input.JsonConfig;
                processedHtml = processedHtml.Replace("{{JSONCONFIG}}", jsonData);
                processedHtml = processedHtml.Replace("{{LESSONNAME}}", lessonCode);

                // Find all media files in the JSON configuration
                _logger.LogInformation("Finding media files in JSON configuration");
                var mediaItems = FindAllMediaFromJsonConfig(jsonData);
                _logger.LogInformation("Found {Count} media items in JSON configuration", mediaItems.Count);

                // Process each media file
                foreach (var mediaItem in mediaItems)
                {
                    try
                    {
                        // First check if we have a BlobKey and BlobContext to use
                        if (!string.IsNullOrWhiteSpace(mediaItem.BlobKey))
                        {
                            _logger.LogInformation("Processing media with BlobKey: {BlobKey}, BlobContext: {BlobContext}",
                                mediaItem.BlobKey, mediaItem.BlobContext);

                            await AddMediaFromBlobToArchive(mediaItem, archive);
                        }
                        // Fall back to MediaUrl if no BlobKey is available
                        else if (!string.IsNullOrWhiteSpace(mediaItem.MediaUrl))
                        {
                            // Skip data URIs
                            if (mediaItem.MediaUrl.StartsWith("data:"))
                            {
                                continue;
                            }

                            await DownloadAndAddToArchive(mediaItem.MediaUrl, archive, baseUrl);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing media item: {MediaUrl}", mediaItem.MediaUrl);
                    }
                }
                processedHtml = processedHtml.Replace("\"/TempFiles", "\"./TempFiles");
                // Save the processed HTML as index.html
                var htmlEntry = archive.CreateEntry("index.html");
                using (var entryStream = htmlEntry.Open())
                using (var writer = new StreamWriter(entryStream))
                {
                    await writer.WriteAsync(processedHtml);
                }

                // Complete the archive
                archive.Dispose();

                // Return the zip file as a byte array
                return memoryStream.ToArray();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting lesson for offline use");
                throw new UserFriendlyException("Failed to export lesson for offline use", ex.Message);
            }
        }

        private async Task<string> ProcessHtmlForOfflineUse(string html, ZipArchive archive, string baseUrl)
        {
            // Create required directories in the zip file
            CreateRequiredDirectories(archive);

            // Define resource mappings: server path -> local path in zip
            // The key is the path to find the file on the server
            // The value is the path where it should be saved in the zip (matching what the HTML expects)
            var resourceMappings = new Dictionary<string, string>
            {
                // CSS files - match the paths in the HTML template
                { "/EdTech/reactapp/assets/css/app.css", "EdTech/reactapp/assets/css/styles.css" },
                { "/EdTech/reactapp/assets/css/vendor.css", "EdTech/reactapp/assets/css/vendor-react.css" },

                // JavaScript files - match the paths in the HTML template
                { "/js/store/lesson-config-db-module.js", "js/store/lesson-config-db-module.js" },
                { "/EdTech/reactapp/PreviewLessonPage.js", "EdTech/reactapp/PreviewLessonPage.js" },
                { "/EdTech/reactapp/runtime.js", "EdTech/reactapp/runtime.js" },
                { "/js/common/lesson-init-module.js", "js/common/lesson-init-module.js" }
            };

            // Process each resource mapping
            foreach (var mapping in resourceMappings)
            {
                string serverPath = mapping.Key;
                string localPath = mapping.Value;

                await DownloadAndAddToArchiveWithMapping(serverPath, localPath, archive, baseUrl);
            }

            return html;
        }

        private async Task DownloadAndAddToArchive(string path, ZipArchive archive, string baseUrl)
        {
            try
            {
                // Skip data URIs
                if (path.StartsWith("data:"))
                {
                    return;
                }

                // Create the local path
                var localPath = GetLocalPath(path);

                // We can't use archive.GetEntry in Create mode, so track entries ourselves
                if (_processedPaths.Contains(localPath))
                {
                    _logger.LogInformation("File already exists in archive: {LocalPath}", localPath);
                    return;
                }

                _logger.LogInformation("Processing asset: {Path} -> {LocalPath}", path, localPath);

                // Ensure the directory exists in the archive
                EnsureDirectoryExists(archive, localPath);

                // Mark the path as processed before downloading to prevent concurrent processing
                _processedPaths.Add(localPath);

                try
                {
                    var virtualFilePath = "/" + path.TrimStart('/');
                    await AddFileToArchiveFromAnySource(virtualFilePath, localPath, archive, baseUrl, path);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing file {Path}: {ErrorMessage}", path, ex.Message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing file for archive: {Path}", path);
            }
        }

        private static string GetLocalPath(string path)
        {
            // Handle full URLs (convert to relative paths)
            if (path.StartsWith("http://") || path.StartsWith("https://"))
            {
                try {
                    var uri = new Uri(path);
                    path = uri.PathAndQuery;
                } catch {
                    // If URI parsing fails, create a safe local path
                    var hash = path.GetHashCode().ToString("X");
                    var extension = Path.GetExtension(path);
                    if (string.IsNullOrEmpty(extension))
                    {
                        extension = ".resource";
                    }
                    return $"external/{hash}{extension}";
                }
            }

            // Remove query string if any
            var queryIndex = path.IndexOf('?');
            if (queryIndex > 0)
            {
                path = path[..queryIndex];
            }

            // Remove leading slash if any
            if (path.StartsWith('/'))
            {
                path = path[1..];
            }

            return path;
        }

        [GeneratedRegex(@"url\(['""]?([^'"")\)]+)['""]?\)", RegexOptions.IgnoreCase)]
        private static partial Regex CssUrlRegex();

        /// <summary>
        /// Ensures that all directories in the path exist in the archive
        /// </summary>
        private static void EnsureDirectoryExists(ZipArchive archive, string filePath)
        {
            var directory = Path.GetDirectoryName(filePath);
            if (string.IsNullOrEmpty(directory) || directory.Equals("."))
                return;

            var dirs = directory.Split('/');
            var currentDir = "";
            foreach (var dir in dirs)
            {
                currentDir = string.IsNullOrEmpty(currentDir) ? dir : $"{currentDir}/{dir}";
                if (!string.IsNullOrEmpty(currentDir))
                {
                    try
                    {
                        archive.CreateEntry($"{currentDir}/");
                    }
                    catch (Exception)
                    {
                        // Directory might already exist, ignore
                    }
                }
            }
        }

        /// <summary>
        /// Creates all required directories in the archive
        /// </summary>
        private static void CreateRequiredDirectories(ZipArchive archive)
        {
            try
            {
                var directories = new[]
                {
                    "css/",
                    "js/",
                    "js/store/",
                    "js/common/",
                    "assets/",
                    "assets/fonts/",
                    "assets/images/",
                    "fonts/",
                    "images/",
                    "EdTech/",
                    "EdTech/reactapp/",
                    "EdTech/reactapp/assets/",
                    "EdTech/reactapp/assets/css/",
                    "EdTech/reactapp/assets/js/",
                    "EdTech/reactapp/assets/fonts/",
                    "EdTech/reactapp/assets/images/",
                    "external/",
                    "TempFiles/"
                };

                foreach (var dir in directories)
                {
                    try
                    {
                        archive.CreateEntry(dir);
                    }
                    catch
                    {
                        // Directory might already exist, ignore
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error but continue - this is not critical
                Console.Error.WriteLine($"Error creating directory structure: {ex.Message}");
            }
        }

        /// <summary>
        /// Adds a file to the archive from any available source (virtual file system, web root, etc.)
        /// </summary>
        private async Task AddFileToArchiveFromAnySource(string sourcePath, string targetPath, ZipArchive archive, string baseUrl, string logPath)
        {
            // First try to get the file from VirtualFileProvider
            var fileInfo = _virtualFileProvider.GetFileInfo(sourcePath);

            if (!fileInfo.Exists)
            {
                // Try with wwwroot prefix
                fileInfo = _virtualFileProvider.GetFileInfo($"wwwroot{sourcePath}");
            }

            if (fileInfo.Exists)
            {
                // Create a new entry in the zip file
                var entry = archive.CreateEntry(targetPath);

                // Write the file content to the zip entry
                using var entryStream = entry.Open();
                using var fileStream = fileInfo.CreateReadStream();
                await fileStream.CopyToAsync(entryStream);

                _logger.LogInformation("Added from virtual file system: {SourcePath} -> {TargetPath}", logPath, targetPath);

                // If this is a CSS file, parse it for additional references
                if (targetPath.EndsWith(".css", StringComparison.OrdinalIgnoreCase))
                {
                    string cssContent;
                    using (var stream = fileInfo.CreateReadStream())
                    using (var reader = new StreamReader(stream))
                    {
                        cssContent = await reader.ReadToEndAsync();
                    }
                    await ProcessCssForAdditionalAssets(cssContent, archive, baseUrl);
                }
            }
            else
            {
                // If not found in virtual file system, try web root
                var filePath = Path.Combine("wwwroot", sourcePath.TrimStart('/').Replace('/', Path.DirectorySeparatorChar));

                if (File.Exists(filePath))
                {
                    // Create a new entry in the zip file
                    var entry = archive.CreateEntry(targetPath);

                    // Write the file content to the zip entry
                    using var entryStream = entry.Open();
                    using var fileStream = File.OpenRead(filePath);
                    await fileStream.CopyToAsync(entryStream);

                    _logger.LogInformation("Added from web root: {SourcePath} -> {TargetPath}", logPath, targetPath);

                    // Process CSS files from web root for additional assets
                    if (targetPath.EndsWith(".css", StringComparison.OrdinalIgnoreCase))
                    {
                        var cssContent = await File.ReadAllTextAsync(filePath);
                        await ProcessCssForAdditionalAssets(cssContent, archive, baseUrl);
                    }
                }
                else
                {
                    _logger.LogWarning("Resource file not found: {SourcePath}", logPath);
                }
            }
        }

        private async Task ProcessCssForAdditionalAssets(string cssContent, ZipArchive archive, string baseUrl)
        {
            try
            {
                // Find all url() references in the CSS
                var matches = CssUrlRegex().Matches(cssContent);

                foreach (Match match in matches)
                {
                    var assetUrl = match.Groups[1].Value;
                    if (!assetUrl.StartsWith("data:")) // Skip data URLs
                    {
                        var localPath = GetLocalPath(assetUrl);
                        // Check global tracking to avoid duplicates
                        if (!_processedPaths.Contains(localPath))
                        {
                            await DownloadAndAddToArchive(assetUrl, archive, baseUrl);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing CSS for additional assets");
            }
        }

        private async Task DownloadAndAddToArchiveWithMapping(string serverPath, string localPath, ZipArchive archive, string baseUrl)
        {
            try
            {
                // Skip data URIs
                if (serverPath.StartsWith("data:"))
                {
                    return;
                }

                // We can't use archive.GetEntry in Create mode, so track entries ourselves
                if (_processedPaths.Contains(localPath))
                {
                    _logger.LogInformation("File already exists in archive: {LocalPath}", localPath);
                    return;
                }

                _logger.LogInformation("Processing asset: {ServerPath} -> {LocalPath}", serverPath, localPath);

                // Ensure the directory exists in the archive
                EnsureDirectoryExists(archive, localPath);

                // Mark the path as processed before downloading to prevent concurrent processing
                _processedPaths.Add(localPath);

                try
                {
                    await AddFileToArchiveFromAnySource(serverPath, localPath, archive, baseUrl, serverPath);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing file {ServerPath}: {ErrorMessage}", serverPath, ex.Message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing file for archive: {ServerPath}", serverPath);
            }
        }

        /// <summary>
        /// Find all media items from the lesson JSON configuration
        /// </summary>
        /// <param name="jsonConfig">JSON configuration string</param>
        /// <returns>List of MediaItem objects found in the configuration</returns>
        public List<MediaItem> FindAllMediaFromJsonConfig(string jsonConfig)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(jsonConfig))
                {
                    return [];
                }

                List<MediaItem> allMediaItems = [];

                // Parse JSON
                using (JsonDocument document = JsonDocument.Parse(jsonConfig))
                {
                    JsonNode? rootNode = JsonNode.Parse(jsonConfig);
                    if (rootNode == null)
                    {
                        return allMediaItems;
                    }

                    // Find all "medias" arrays in the JSON
                    List<JsonNode> mediaArrays = FindAllMediaArrays(rootNode);

                    // Process each media array
                    foreach (var mediaArray in mediaArrays)
                    {
                        if (mediaArray is JsonArray array)
                        {
                            foreach (var mediaNode in array)
                            {
                                if (mediaNode is JsonObject mediaObj)
                                {
                                    // Create MediaItem from JsonObject
                                    MediaItem item = new()
                                    {
                                        // Handle different property names for different media types (video, image, audio, 3D)
                                        MediaUrl = GetJsonPropertyValue(mediaObj, "mediaUrl", "modelUrl", "imageUrl"),
                                        MediaType = GetJsonPropertyValue(mediaObj, "mediaType", "modelType", "type"),
                                        Name = GetJsonPropertyValue(mediaObj, "name"),
                                        BlobKey = GetJsonPropertyValue(mediaObj, "blobKey"),
                                        BlobContext = GetJsonPropertyValue(mediaObj, "blobContext")
                                    };

                                    // Only add to the list if it has a URL
                                    if (!string.IsNullOrWhiteSpace(item.MediaUrl))
                                    {
                                        allMediaItems.Add(item);
                                    }
                                }
                            }
                        }
                    }
                }

                return allMediaItems;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error finding media from JSON config");
                return [];
            }
        }

        /// <summary>
        /// Add media from blob storage to the archive using BlobKey and BlobContext
        /// </summary>
        private async Task AddMediaFromBlobToArchive(MediaItem mediaItem, ZipArchive archive)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(mediaItem.BlobKey))
                {
                    _logger.LogWarning("Cannot add media to archive: BlobKey is empty");
                    return;
                }

                // Determine the local path in the archive
                string localPath;

                // If MediaUrl is available, use it to determine the path
                if (!string.IsNullOrWhiteSpace(mediaItem.MediaUrl))
                {
                    localPath = GetLocalPath(mediaItem.MediaUrl);
                }
                else
                {
                    // Create a path based on BlobKey and file extension
                    string extension = Path.GetExtension(mediaItem.Name ?? "");
                    if (string.IsNullOrEmpty(extension))
                    {
                        // Try to determine extension from MediaType
                        extension = DetermineExtensionFromMediaType(mediaItem.MediaType ?? "");
                    }

                    // Use TempFiles directory with BlobKey as filename
                    localPath = $"TempFiles/{mediaItem.BlobKey}{extension}";
                }

                // Skip if already processed
                if (_processedPaths.Contains(localPath))
                {
                    _logger.LogInformation("File already exists in archive: {LocalPath}", localPath);
                    return;
                }

                // Ensure directory exists
                EnsureDirectoryExists(archive, localPath);

                // Mark as processed
                _processedPaths.Add(localPath);

                // Get content from blob storage
                byte[] content;

                // Use TempFileContainer for files with BlobKey
                try
                {
                    var filePath = Path.GetExtension(mediaItem.MediaUrl);
                    

                    content = await _tempFileContainer.GetAllBytesAsync($"{mediaItem.BlobContext}{filePath}");

                    // Verify content is not null or empty
                    if (content == null || content.Length == 0)
                    {
                        _logger.LogWarning("Retrieved empty content for BlobKey: {BlobKey}", mediaItem.BlobKey);
                        throw new InvalidOperationException($"Empty content retrieved for BlobKey: {mediaItem.BlobKey}");
                    }

                    _logger.LogInformation("Retrieved {Length} bytes for BlobKey: {BlobKey}",
                        content.Length, mediaItem.BlobKey);

                    // Create entry in the archive with appropriate compression
                    var entry = archive.CreateEntry(localPath, CompressionLevel.Optimal);

                    // Set the last write time to current time to ensure freshness
                    entry.LastWriteTime = DateTimeOffset.Now;

                    // Write content to the entry
                    using var entryStream = entry.Open();
                    await entryStream.WriteAsync(content.AsMemory(), default);
                    await entryStream.FlushAsync();

                    _logger.LogInformation("Added media from blob storage: {BlobKey} -> {LocalPath}",
                        mediaItem.BlobKey, localPath);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error retrieving content from blob storage for BlobKey: {BlobKey}",
                        mediaItem.BlobKey);

                    // If we have a MediaUrl, try to fall back to it
                    if (!string.IsNullOrWhiteSpace(mediaItem.MediaUrl) && !mediaItem.MediaUrl.StartsWith("data:"))
                    {
                        _logger.LogInformation("Falling back to MediaUrl for BlobKey: {BlobKey}", mediaItem.BlobKey);

                        try
                        {
                            // Remove the entry from processed paths so it can be processed again
                            _processedPaths.Remove(localPath);

                            // Try to download using the MediaUrl instead
                            await DownloadAndAddToArchive(mediaItem.MediaUrl, archive,
                                _configuration["Application:SelfUrl"] ?? "https://localhost:44348");
                        }
                        catch (Exception fallbackEx)
                        {
                            _logger.LogError(fallbackEx, "Fallback to MediaUrl also failed for BlobKey: {BlobKey}",
                                mediaItem.BlobKey);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding media from blob to archive: {BlobKey}", mediaItem.BlobKey);
            }
        }

        /// <summary>
        /// Determine file extension from media type
        /// </summary>
        private static string DetermineExtensionFromMediaType(string mediaType)
        {
            return mediaType?.ToLowerInvariant() switch
            {
                "video" => ".mp4",
                "audio" => ".mp3",
                "image" => ".jpg",
                "3d" => ".glb",
                _ => ".bin"
            };
        }

        /// <summary>
        /// Get property value from JsonObject, trying multiple property names
        /// </summary>
        private static string GetJsonPropertyValue(JsonObject obj, params string[] propertyNames)
        {
            foreach (var propName in propertyNames)
            {
                if (obj.TryGetPropertyValue(propName, out JsonNode? value) && value != null)
                {
                    return value.GetValue<string>();
                }
            }
            return string.Empty;
        }

        /// <summary>
        /// Find all "medias" arrays in the JSON structure
        /// </summary>
        private static List<JsonNode> FindAllMediaArrays(JsonNode? node)
        {
            List<JsonNode> results = [];

            if (node is JsonObject obj)
            {
                foreach (var property in obj)
                {
                    if (property.Key == "medias" && property.Value is JsonArray)
                    {
                        results.Add(property.Value);
                    }
                    results.AddRange(FindAllMediaArrays(property.Value));
                }
            }
            else if (node is JsonArray array)
            {
                foreach (var item in array)
                {
                    results.AddRange(FindAllMediaArrays(item));
                }
            }

            return results;
        }
    }
}