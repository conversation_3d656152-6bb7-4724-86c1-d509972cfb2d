import { registerLicense } from '@syncfusion/ej2-base';

/**
 * Registers the Syncfusion license key for the application
 * This ensures that all Syncfusion components work properly without license warnings
 *
 * @param LicenseKey - The Syncfusion license key (defaults to the provided key)
 */
export const registerLicenseSyncfusionBase = (
  LicenseKey = 'Ngo9BigBOggjHTQxAR8/V1NDaF5cWWtCf1FpRmJGdld5fUVHYVZUTXxaS00DNHVRdkdnWH5cdXZXRWdZVER/W0E='
) => {
  registerLicense(LicenseKey);
};

// Export a default function for direct import and execution
export default function initializeSyncfusion() {
  registerLicenseSyncfusionBase();
}
