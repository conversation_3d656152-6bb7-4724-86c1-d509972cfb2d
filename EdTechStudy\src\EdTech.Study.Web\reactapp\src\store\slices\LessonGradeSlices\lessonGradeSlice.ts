import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import lessonGradeApi, { LessonGradeParams } from '../../../api/lessonGradeApi';
import { LessonGrade } from '../../../interfaces/lessons/lessonGrade';

// Define the state interface
export interface LessonGradeState {
  lessonGrades?: LessonGrade[];
  currentLessonGrade: LessonGrade | null;
  loading: boolean;
  error: string | null;
  totalCount: number;
  currentPage: number;
  pageSize: number;
}

// Initial state
const initialState: LessonGradeState = {
  currentLessonGrade: null,
  loading: false,
  error: null,
  totalCount: 0,
  currentPage: 1,
  pageSize: 1000,
};

// Create thunk actions
export const fetchLessonGrades = createAsyncThunk(
  'lessonGrade/fetchLessonGrades',
  async (params: LessonGradeParams = {}, { rejectWithValue }) => {
    try {
      const response = await lessonGradeApi.getLessonGrades(params);
      if (response.success) {
        return {
          data: response.data,
          total: response.total,
        };
      }
      return rejectWithValue(response.error || 'Failed to fetch lesson grades');
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Unknown error'
      );
    }
  }
);

export const fetchLessonGradeById = createAsyncThunk(
  'lessonGrade/fetchLessonGradeById',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await lessonGradeApi.getLessonGradeById(id);
      if (response.success) {
        return response.data;
      }
      return rejectWithValue(response.error || 'Failed to fetch lesson grade');
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Unknown error'
      );
    }
  }
);

// Create the slice
const lessonGradeSlice = createSlice({
  name: 'lessonGrade',
  initialState,
  reducers: {
    setCurrentPage: (state, action: PayloadAction<number>) => {
      state.currentPage = action.payload;
    },
    setPageSize: (state, action: PayloadAction<number>) => {
      state.pageSize = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // fetchLessonGrades
      .addCase(fetchLessonGrades.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchLessonGrades.fulfilled, (state, action) => {
        state.loading = false;
        state.lessonGrades = action.payload.data;
        state.totalCount = action.payload.total;
      })
      .addCase(fetchLessonGrades.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // fetchLessonGradeById
      .addCase(fetchLessonGradeById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchLessonGradeById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentLessonGrade = action.payload as LessonGrade;
      })
      .addCase(fetchLessonGradeById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

// Export actions and reducer
export const { setCurrentPage, setPageSize, clearError } =
  lessonGradeSlice.actions;
export default lessonGradeSlice.reducer;
