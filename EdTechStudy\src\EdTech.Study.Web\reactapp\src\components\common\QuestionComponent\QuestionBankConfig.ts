import { BaseQuestion as BaseQuestion2 } from '../../../interfaces/quizs/questionBase';

// <PERSON><PERSON><PERSON> nghĩa các loại câu hỏi
export type QuestionType =
  | 'truefalse'
  | 'multiplechoice'
  | 'fillin'
  | 'ordering'
  | 'matching';

// Đ<PERSON>nh nghĩa độ khó
export type DifficultyLevel = 'easy' | 'medium' | 'hard' | 'expert';

// Định nghĩa câu hỏi cơ bản
export interface BaseQuestion {
  id: string;
  type: QuestionType;
  questionText: string;
  explanation?: string;
  difficulty: DifficultyLevel;
  points: number;
  tags?: string[];
  category?: string;
  imageUrl?: string;
  hint?: string;
  order?: number;
}

// Đ<PERSON>nh nghĩa các loại câu hỏi cụ thể
export interface TrueFalseQuestion extends BaseQuestion {
  type: 'truefalse';
  isCorrect: boolean;
}

export interface MultipleChoiceQuestion extends BaseQuestion {
  type: 'multiplechoice';
  options: string[];
  correctOptionIndex: number;
}

export interface FillInQuestion extends BaseQuestion {
  type: 'fillin';
  correctAnswer: string;
  caseSensitive: boolean;
}

export interface OrderingQuestion extends BaseQuestion {
  type: 'ordering';
  items: string[];
  correctOrder: number[];
}

export interface MatchingQuestion extends BaseQuestion2 {
  type: 'matching';
  leftItems: string[];
  rightItems: string[];
  correctLeftToRight: number[][]; // Array of [leftIndex, rightIndex] pairs, allowing multiple right indices per left index
  correctRightToLeft: number[][]; // Array of [rightIndex, leftIndex] pairs, allowing multiple left indices per right index
}

// Union type cho tất cả loại câu hỏi
export type Question =
  | TrueFalseQuestion
  | MultipleChoiceQuestion
  | FillInQuestion
  | OrderingQuestion
  | MatchingQuestion;

// Kiểu dữ liệu cho câu trả lời của người dùng
export type QuestionAnswer =
  | boolean // For TrueFalse questions
  | number // For MultipleChoice questions
  | string // For FillIn questions
  | number[] // For Ordering questions
  | number[][] // For Matching questions
  | null; // No answer yet

// Kiểu dữ liệu cho kết quả của mỗi câu hỏi
export interface QuestionResult {
  question: Question;
  userAnswer: QuestionAnswer;
  isCorrect: boolean;
  timeSpent?: number;
  points: number;
  lifelinesUsed?: string[];
}

// Danh sách tất cả câu hỏi
export const allQuestions: Question[] = [
  // Câu hỏi Toán học
  {
    id: 'math_1',
    type: 'multiplechoice',
    questionText: 'Tính giá trị của biểu thức: 2x + 3y khi x = 5 và y = 2',
    options: ['10', '13', '16', '19'],
    correctOptionIndex: 2,
    explanation: '2(5) + 3(2) = 10 + 6 = 16',
    difficulty: 'easy',
    points: 10,
    category: 'math',
  },
  {
    id: 'math_2',
    type: 'truefalse',
    questionText: 'Số Pi (π) là một số vô tỷ',
    isCorrect: true,
    explanation:
      'Số Pi là một số vô tỷ, có nghĩa là nó không thể được biểu diễn dưới dạng phân số của hai số nguyên',
    difficulty: 'easy',
    points: 10,
    category: 'math',
  },
  {
    id: 'math_3',
    type: 'fillin',
    questionText: 'Tổng các góc trong một tam giác là bao nhiêu độ?',
    correctAnswer: '180',
    caseSensitive: false,
    explanation: 'Tổng các góc trong một tam giác luôn bằng 180 độ',
    difficulty: 'easy',
    points: 10,
    category: 'math',
  },
  {
    id: 'math_4',
    type: 'multiplechoice',
    questionText: 'Diện tích hình tròn được tính bằng công thức nào?',
    options: ['πr²', '2πr', 'πr', '2πr²'],
    correctOptionIndex: 0,
    explanation:
      'Diện tích hình tròn được tính bằng công thức S = πr², trong đó r là bán kính',
    difficulty: 'medium',
    points: 15,
    category: 'math',
  },

  // Câu hỏi Vật lý
  {
    id: 'physics_1',
    type: 'multiplechoice',
    questionText: 'Lực hấp dẫn tỷ lệ với khoảng cách giữa hai vật như thế nào?',
    options: [
      'Tỷ lệ thuận với khoảng cách',
      'Tỷ lệ nghịch với khoảng cách',
      'Tỷ lệ nghịch với bình phương khoảng cách',
      'Không phụ thuộc vào khoảng cách',
    ],
    correctOptionIndex: 2,
    explanation:
      'Theo định luật vạn vật hấp dẫn của Newton, lực hấp dẫn tỷ lệ nghịch với bình phương khoảng cách giữa hai vật',
    difficulty: 'medium',
    points: 15,
    category: 'physics',
  },

  // Câu hỏi Địa lý
  {
    id: 'geography_1',
    type: 'multiplechoice',
    questionText: 'Đâu là sông dài nhất thế giới?',
    options: ['Sông Amazon', 'Sông Nile', 'Sông Mississippi', 'Sông Dương Tử'],
    correctOptionIndex: 1,
    explanation: 'Sông Nile là sông dài nhất thế giới, dài khoảng 6,650 km',
    difficulty: 'easy',
    points: 10,
    category: 'geography',
  },

  // Câu hỏi Kiến thức chung
  {
    id: 'general_1',
    type: 'multiplechoice',
    questionText: 'Thủ đô của Việt Nam là gì?',
    options: ['Hà Nội', 'Hồ Chí Minh', 'Đà Nẵng', 'Huế'],
    correctOptionIndex: 0,
    explanation: 'Hà Nội là thủ đô của Việt Nam.',
    difficulty: 'easy',
    points: 10,
    tags: ['round_warmup'],
    category: 'general_knowledge',
  },
  {
    id: 'general_2',
    type: 'truefalse',
    questionText: 'Mặt trời mọc ở hướng Đông.',
    isCorrect: true,
    explanation: 'Mặt trời luôn mọc ở hướng Đông.',
    difficulty: 'easy',
    points: 10,
    tags: ['round_warmup'],
    category: 'general_knowledge',
  },
  {
    id: 'general_3',
    type: 'multiplechoice',
    questionText: 'Quốc gia nào có diện tích lớn nhất thế giới?',
    options: ['Nga', 'Trung Quốc', 'Mỹ', 'Canada'],
    correctOptionIndex: 0,
    explanation: 'Nga là quốc gia có diện tích lớn nhất thế giới.',
    difficulty: 'medium',
    points: 15,
    tags: ['round_obstacle'],
    category: 'general_knowledge',
  },

  // Thêm câu hỏi trắc nghiệm chủ đề lịch sử
  {
    id: 'mc_history_1',
    type: 'multiplechoice',
    questionText: 'Ai là vị vua cuối cùng của triều đại nhà Nguyễn?',
    options: ['Bảo Đại', 'Khải Định', 'Hàm Nghi', 'Duy Tân'],
    correctOptionIndex: 0,
    explanation:
      'Bảo Đại là vị vua cuối cùng của triều đại phong kiến Việt Nam, trị vì từ 1926-1945.',
    difficulty: 'medium',
    points: 15,
    category: 'history',
  },
  {
    id: 'mc_history_2',
    type: 'multiplechoice',
    questionText:
      'Năm nào đánh dấu sự kết thúc của Chiến tranh thế giới thứ hai?',
    options: ['1944', '1945', '1946', '1947'],
    correctOptionIndex: 1,
    explanation:
      'Chiến tranh thế giới thứ hai kết thúc vào năm 1945 với sự đầu hàng của Nhật Bản.',
    difficulty: 'easy',
    points: 10,
    category: 'history',
  },
  {
    id: 'mc_history_3',
    type: 'multiplechoice',
    questionText: 'Ai là người đã phát minh ra bóng đèn điện?',
    options: [
      'Albert Einstein',
      'Thomas Edison',
      'Nikola Tesla',
      'Alexander Graham Bell',
    ],
    correctOptionIndex: 1,
    explanation:
      'Thomas Edison được công nhận là người phát minh ra bóng đèn điện thực dụng đầu tiên vào năm 1879.',
    difficulty: 'easy',
    points: 10,
    category: 'history',
  },

  // Thêm câu hỏi trắc nghiệm chủ đề khoa học
  {
    id: 'mc_science_1',
    type: 'multiplechoice',
    questionText: 'Nguyên tố nào chiếm tỷ lệ lớn nhất trong không khí?',
    options: ['Oxy', 'Nitơ', 'Argon', 'Carbon dioxide'],
    correctOptionIndex: 1,
    explanation:
      'Nitơ chiếm khoảng 78% thành phần không khí, trong khi oxy chiếm khoảng 21%.',
    difficulty: 'medium',
    points: 15,
    category: 'science',
  },
  {
    id: 'mc_science_2',
    type: 'multiplechoice',
    questionText: 'Đâu là hành tinh lớn nhất trong hệ mặt trời?',
    options: [
      'Trái Đất',
      'Sao Mộc (Jupiter)',
      'Sao Thổ (Saturn)',
      'Sao Hỏa (Mars)',
    ],
    correctOptionIndex: 1,
    explanation:
      'Sao Mộc (Jupiter) là hành tinh lớn nhất trong hệ mặt trời, với đường kính khoảng 11 lần Trái Đất.',
    difficulty: 'easy',
    points: 10,
    category: 'science',
  },
  {
    id: 'mc_science_3',
    type: 'multiplechoice',
    questionText: 'Bộ phận nào của cơ thể con người sản xuất insulin?',
    options: ['Gan', 'Thận', 'Tụy', 'Lá lách'],
    correctOptionIndex: 2,
    explanation:
      'Tụy (pancreas) sản xuất insulin, một hormone điều hòa lượng đường trong máu.',
    difficulty: 'medium',
    points: 15,
    category: 'science',
  },

  // Thêm câu hỏi trắc nghiệm chủ đề địa lý
  {
    id: 'mc_geography_1',
    type: 'multiplechoice',
    questionText: 'Quốc gia nào có diện tích lớn thứ hai thế giới?',
    options: ['Mỹ', 'Trung Quốc', 'Canada', 'Brazil'],
    correctOptionIndex: 2,
    explanation:
      'Canada là quốc gia có diện tích lớn thứ hai thế giới, sau Nga.',
    difficulty: 'medium',
    points: 15,
    category: 'geography',
  },
  {
    id: 'mc_geography_2',
    type: 'multiplechoice',
    questionText: 'Sông nào dài nhất Việt Nam?',
    options: ['Sông Hồng', 'Sông Mekong', 'Sông Đồng Nai', 'Sông Đà'],
    correctOptionIndex: 1,
    explanation:
      'Phần sông Mekong chảy qua Việt Nam dài khoảng 220km và là con sông dài nhất Việt Nam.',
    difficulty: 'medium',
    points: 15,
    category: 'geography',
  },
  {
    id: 'mc_geography_3',
    type: 'multiplechoice',
    questionText: 'Thành phố nào đông dân nhất thế giới?',
    options: ['Tokyo', 'Delhi', 'Shanghai', 'Mexico City'],
    correctOptionIndex: 0,
    explanation:
      'Tokyo, Nhật Bản là thành phố đông dân nhất thế giới với hơn 37 triệu người.',
    difficulty: 'medium',
    points: 15,
    category: 'geography',
  },

  // Thêm câu hỏi trắc nghiệm chủ đề văn học
  {
    id: 'mc_literature_1',
    type: 'multiplechoice',
    questionText: 'Tác giả của tác phẩm "Truyện Kiều" là ai?',
    options: ['Nguyễn Du', 'Hồ Xuân Hương', 'Nguyễn Trãi', 'Nguyễn Đình Chiểu'],
    correctOptionIndex: 0,
    explanation:
      'Nguyễn Du (1766-1820) là tác giả của Truyện Kiều, một kiệt tác văn học Việt Nam.',
    difficulty: 'easy',
    points: 10,
    category: 'literature',
  },
  {
    id: 'mc_literature_2',
    type: 'multiplechoice',
    questionText: 'Ai là tác giả của "Romeo và Juliet"?',
    options: [
      'Charles Dickens',
      'William Shakespeare',
      'Jane Austen',
      'Mark Twain',
    ],
    correctOptionIndex: 1,
    explanation: 'William Shakespeare viết Romeo và Juliet vào những năm 1590.',
    difficulty: 'easy',
    points: 10,
    category: 'literature',
  },

  // Thêm câu hỏi trắc nghiệm chủ đề toán học
  {
    id: 'mc_math_1',
    type: 'multiplechoice',
    questionText: 'Nếu x + y = 10 và x - y = 4, thì x = ?',
    options: ['3', '5', '7', '8'],
    correctOptionIndex: 2,
    explanation:
      'Từ hệ phương trình: x + y = 10 và x - y = 4, ta có 2x = 14, vậy x = 7.',
    difficulty: 'medium',
    points: 15,
    category: 'math',
  },
  {
    id: 'mc_math_2',
    type: 'multiplechoice',
    questionText: 'Diện tích của hình tròn có bán kính r = 5cm là bao nhiêu?',
    options: ['25π cm²', '10π cm²', '5π cm²', '50π cm²'],
    correctOptionIndex: 0,
    explanation:
      'Diện tích hình tròn = πr². Với r = 5cm, ta có S = π×5² = 25π cm².',
    difficulty: 'easy',
    points: 10,
    category: 'math',
  },

  // Thêm câu hỏi trắc nghiệm chủ đề thể thao
  {
    id: 'mc_sports_1',
    type: 'multiplechoice',
    questionText: 'Môn thể thao nào được gọi là "Vua của các môn thể thao"?',
    options: ['Bóng đá', 'Quần vợt', 'Điền kinh', 'Cờ vua'],
    correctOptionIndex: 0,
    explanation:
      'Bóng đá thường được gọi là "Vua của các môn thể thao" vì tính phổ biến và số lượng người hâm mộ trên toàn thế giới.',
    difficulty: 'easy',
    points: 10,
    category: 'sports',
  },
  {
    id: 'mc_sports_2',
    type: 'multiplechoice',
    questionText: 'Olympic được tổ chức mấy năm một lần?',
    options: ['2 năm', '3 năm', '4 năm', '5 năm'],
    correctOptionIndex: 2,
    explanation:
      'Olympic được tổ chức 4 năm một lần kể từ năm 1896 (trừ thời gian chiến tranh thế giới).',
    difficulty: 'easy',
    points: 10,
    category: 'sports',
  },

  // Thêm câu hỏi trắc nghiệm chủ đề âm nhạc
  {
    id: 'mc_music_1',
    type: 'multiplechoice',
    questionText: 'Nhạc cụ nào thuộc nhóm dây gảy?',
    options: ['Sáo', 'Đàn piano', 'Đàn guitar', 'Trống'],
    correctOptionIndex: 2,
    explanation:
      'Đàn guitar là nhạc cụ thuộc nhóm dây gảy, âm thanh tạo ra bằng cách gảy dây đàn.',
    difficulty: 'easy',
    points: 10,
    category: 'music',
  },
  {
    id: 'mc_music_2',
    type: 'multiplechoice',
    questionText: 'Ai được mệnh danh là "Vua nhạc Pop"?',
    options: [
      'Elvis Presley',
      'Michael Jackson',
      'Justin Bieber',
      'Bruno Mars',
    ],
    correctOptionIndex: 1,
    explanation:
      'Michael Jackson thường được gọi là "Vua nhạc Pop" vì những đóng góp quan trọng cho ngành công nghiệp âm nhạc.',
    difficulty: 'easy',
    points: 10,
    category: 'music',
  },

  // Thêm câu hỏi đúng/sai
  {
    id: 'tf_science_1',
    type: 'truefalse',
    questionText: 'Ánh sáng di chuyển nhanh hơn âm thanh.',
    isCorrect: true,
    explanation:
      'Ánh sáng di chuyển với vận tốc khoảng 300,000 km/s, nhanh hơn rất nhiều so với âm thanh (khoảng 343 m/s trong không khí).',
    difficulty: 'easy',
    points: 10,
    category: 'science',
  },
  {
    id: 'tf_history_1',
    type: 'truefalse',
    questionText: 'Chiến tranh thế giới thứ nhất bắt đầu vào năm 1914.',
    isCorrect: true,
    explanation:
      'Chiến tranh thế giới thứ nhất bắt đầu vào ngày 28 tháng 7 năm 1914 và kết thúc vào năm 1918.',
    difficulty: 'easy',
    points: 10,
    category: 'history',
  },
  {
    id: 'tf_geography_1',
    type: 'truefalse',
    questionText: 'Australia là quốc gia lớn nhất ở Nam bán cầu.',
    isCorrect: false,
    explanation:
      'Brazil là quốc gia lớn nhất ở Nam bán cầu, không phải Australia.',
    difficulty: 'medium',
    points: 15,
    category: 'geography',
  },
  {
    id: 'tf_math_1',
    type: 'truefalse',
    questionText: 'Số Pi (π) có giá trị chính xác là 22/7.',
    isCorrect: false,
    explanation:
      '22/7 là một giá trị gần đúng của π. Số Pi là một số vô tỷ, không thể biểu diễn chính xác dưới dạng phân số.',
    difficulty: 'medium',
    points: 15,
    category: 'math',
  },
  {
    id: 'tf_sports_1',
    type: 'truefalse',
    questionText: 'Một trận đấu bóng đá chuẩn có thời gian thi đấu là 90 phút.',
    isCorrect: true,
    explanation:
      'Một trận đấu bóng đá chuẩn có thời gian thi đấu chính thức là 90 phút, chia làm hai hiệp, mỗi hiệp 45 phút.',
    difficulty: 'easy',
    points: 10,
    category: 'sports',
  },
  {
    id: 'tf_literature_1',
    type: 'truefalse',
    questionText: 'Tác phẩm "Truyện Kiều" có cấu trúc 3.254 câu thơ lục bát.',
    isCorrect: true,
    explanation:
      'Truyện Kiều của Nguyễn Du gồm 3.254 câu thơ lục bát, là tác phẩm văn học nổi tiếng của Việt Nam.',
    difficulty: 'medium',
    points: 15,
    category: 'literature',
  },

  // Thêm câu hỏi điền vào chỗ trống
  {
    id: 'fill_math_1',
    type: 'fillin',
    questionText: 'Diện tích hình chữ nhật được tính bằng công thức: S = ?',
    correctAnswer: 'dài * rộng',
    caseSensitive: false,
    explanation:
      'Diện tích hình chữ nhật bằng tích của chiều dài và chiều rộng.',
    difficulty: 'easy',
    points: 10,
    category: 'math',
  },
  {
    id: 'fill_math_2',
    type: 'fillin',
    questionText: 'Chu vi hình tròn được tính bằng công thức: C = ?',
    correctAnswer: '2πr',
    caseSensitive: false,
    explanation: 'Chu vi hình tròn C = 2πr, trong đó r là bán kính.',
    difficulty: 'medium',
    points: 15,
    category: 'math',
  },
  {
    id: 'fill_science_1',
    type: 'fillin',
    questionText: 'Đơn vị cơ bản đo độ dài trong hệ SI là?',
    correctAnswer: 'mét',
    caseSensitive: false,
    explanation:
      'Mét (m) là đơn vị cơ bản đo độ dài trong hệ đo lường quốc tế (SI).',
    difficulty: 'easy',
    points: 10,
    category: 'science',
  },
  {
    id: 'fill_science_2',
    type: 'fillin',
    questionText: 'Công thức hóa học của nước là?',
    correctAnswer: 'H2O',
    caseSensitive: false,
    explanation:
      'Nước có công thức hóa học là H2O, gồm 2 nguyên tử hydro và 1 nguyên tử oxy.',
    difficulty: 'easy',
    points: 10,
    category: 'science',
  },
  {
    id: 'fill_history_1',
    type: 'fillin',
    questionText: 'Chiến thắng Điện Biên Phủ của Việt Nam diễn ra vào năm?',
    correctAnswer: '1954',
    caseSensitive: false,
    explanation:
      'Chiến thắng Điện Biên Phủ diễn ra vào ngày 7/5/1954, chấm dứt sự thống trị của thực dân Pháp tại Việt Nam.',
    difficulty: 'medium',
    points: 15,
    category: 'history',
  },
  {
    id: 'fill_geography_1',
    type: 'fillin',
    questionText: 'Sông lớn nhất thế giới theo lưu lượng nước là sông?',
    correctAnswer: 'Amazon',
    caseSensitive: false,
    explanation:
      'Sông Amazon ở Nam Mỹ là con sông lớn nhất thế giới về lưu lượng nước, chiếm khoảng 20% tổng lượng nước ngọt đổ ra biển trên toàn cầu.',
    difficulty: 'medium',
    points: 15,
    category: 'geography',
  },
  {
    id: 'fill_literature_1',
    type: 'fillin',
    questionText: 'Tác giả của tiểu thuyết "Số Đỏ" là?',
    correctAnswer: 'Vũ Trọng Phụng',
    caseSensitive: false,
    explanation:
      'Vũ Trọng Phụng (1912-1939) là tác giả của tiểu thuyết "Số Đỏ", một tác phẩm văn học phê phán xã hội nổi tiếng.',
    difficulty: 'medium',
    points: 15,
    category: 'literature',
  },
  {
    id: 'fill_sports_1',
    type: 'fillin',
    questionText:
      'Môn thể thao Olympic cổ đại được tổ chức tại thành phố nào của Hy Lạp?',
    correctAnswer: 'Olympia',
    caseSensitive: false,
    explanation:
      'Thế vận hội Olympic cổ đại được tổ chức tại thành phố Olympia (Olympia) của Hy Lạp.',
    difficulty: 'medium',
    points: 15,
    category: 'sports',
  },

  // Thêm câu hỏi sắp xếp thứ tự
  {
    id: 'order_history_1',
    type: 'ordering',
    questionText:
      'Sắp xếp các sự kiện lịch sử Việt Nam theo thứ tự thời gian từ xa đến gần:',
    items: [
      'Chiến thắng Điện Biên Phủ',
      'Khởi nghĩa Hai Bà Trưng',
      'Chiến thắng Bạch Đằng của Ngô Quyền',
      'Cách mạng Tháng 8',
    ],
    correctOrder: [1, 2, 0, 3],
    explanation:
      'Thứ tự đúng là: Khởi nghĩa Hai Bà Trưng (năm 40), Chiến thắng Bạch Đằng (năm 938), Chiến thắng Điện Biên Phủ (năm 1954), Cách mạng Tháng 8 (năm 1945).',
    difficulty: 'hard',
    points: 20,
    category: 'history',
  },
  {
    id: 'order_science_1',
    type: 'ordering',
    questionText:
      'Sắp xếp các hành tinh sau theo thứ tự khoảng cách từ gần đến xa Mặt Trời:',
    items: ['Trái Đất', 'Sao Thủy', 'Sao Mộc', 'Sao Kim'],
    correctOrder: [1, 3, 0, 2],
    explanation: 'Thứ tự đúng là: Sao Thủy, Sao Kim, Trái Đất, Sao Mộc.',
    difficulty: 'medium',
    points: 15,
    category: 'science',
  },
  {
    id: 'order_math_1',
    type: 'ordering',
    questionText: 'Sắp xếp các phân số sau theo thứ tự từ nhỏ đến lớn:',
    items: ['3/4', '2/3', '5/6', '1/2'],
    correctOrder: [3, 1, 0, 2],
    explanation:
      'Thứ tự đúng là: 1/2 (0.5), 2/3 (0.67), 3/4 (0.75), 5/6 (0.83).',
    difficulty: 'medium',
    points: 15,
    category: 'math',
  },
  {
    id: 'order_geography_1',
    type: 'ordering',
    questionText: 'Xếp các quốc gia sau theo thứ tự diện tích từ lớn đến bé:',
    items: ['Mỹ', 'Trung Quốc', 'Nga', 'Canada'],
    correctOrder: [2, 3, 1, 0],
    explanation: 'Thứ tự đúng là: Nga, Canada, Trung Quốc, Mỹ.',
    difficulty: 'medium',
    points: 15,
    category: 'geography',
  },
  {
    id: 'order_literature_1',
    type: 'ordering',
    questionText:
      'Sắp xếp các tác phẩm văn học Việt Nam theo thời gian ra đời (từ sớm đến muộn):',
    items: ['Truyện Kiều', 'Nhật ký trong tù', 'Lục Vân Tiên', 'Số đỏ'],
    correctOrder: [0, 2, 3, 1],
    explanation:
      'Thứ tự đúng là: Truyện Kiều (1820), Lục Vân Tiên (1850), Số đỏ (1936), Nhật ký trong tù (1943).',
    difficulty: 'hard',
    points: 20,
    category: 'literature',
  },

  // // Thêm câu hỏi nối cặp
  // {
  //   id: 'match_countries_1',
  //   type: 'matching',
  //   questionText: 'Nối các quốc gia với thủ đô tương ứng:',
  //   leftItems: ['Nhật Bản', 'Đức', 'Úc', 'Brazil'],
  //   rightItems: ['Tokyo', 'Berlin', 'Canberra', 'Brasilia'],
  //   correctLeftToRight: [
  //     [0, 0],
  //     [1, 1],
  //     [2, 2],
  //     [3, 3],
  //   ],
  //   correctRightToLeft: [
  //     [0, 0],
  //     [1, 1],
  //     [2, 2],
  //     [3, 3],
  //   ],
  //   explanation:
  //     'Các cặp đúng là: Nhật Bản - Tokyo, Đức - Berlin, Úc - Canberra, Brazil - Brasilia.',
  //   difficulty: 'medium',
  //   points: 15,
  //   category: 'geography',
  // },
  // {
  //   id: 'match_science_1',
  //   type: 'matching',
  //   questionText: 'Nối các nguyên tố hóa học với ký hiệu tương ứng:',
  //   leftItems: ['Vàng', 'Bạc', 'Sắt', 'Đồng'],
  //   rightItems: ['Au', 'Ag', 'Fe', 'Cu'],
  //   correctLeftToRight: [
  //     [0, 0],
  //     [1, 1],
  //     [2, 2],
  //     [3, 3],
  //   ],
  //   correctRightToLeft: [
  //     [0, 0],
  //     [1, 1],
  //     [2, 2],
  //     [3, 3],
  //   ],
  //   explanation:
  //     'Các cặp đúng là: Vàng - Au (Aurum), Bạc - Ag (Argentum), Sắt - Fe (Ferrum), Đồng - Cu (Cuprum).',
  //   difficulty: 'medium',
  //   points: 15,
  //   category: 'science',
  // },
  // {
  //   id: 'match_math_1',
  //   type: 'matching',
  //   questionText: 'Nối các hình với công thức tính diện tích tương ứng:',
  //   leftItems: ['Hình vuông', 'Hình tròn', 'Hình tam giác', 'Hình chữ nhật'],
  //   rightItems: ['a²', 'πr²', '(a×h)/2', 'a×b'],
  //   correctLeftToRight: [
  //     [0, 0],
  //     [1, 1],
  //     [2, 2],
  //     [3, 3],
  //   ],
  //   correctRightToLeft: [
  //     [0, 0],
  //     [1, 1],
  //     [2, 2],
  //     [3, 3],
  //   ],
  //   explanation:
  //     'Các cặp đúng là: Hình vuông - a², Hình tròn - πr², Hình tam giác - (a×h)/2, Hình chữ nhật - a×b.',
  //   difficulty: 'medium',
  //   points: 15,
  //   category: 'math',
  // },
  // {
  //   id: 'match_history_1',
  //   type: 'matching',
  //   questionText: 'Nối các nhân vật lịch sử với thành tựu của họ:',
  //   leftItems: [
  //     'Albert Einstein',
  //     'Marie Curie',
  //     'Alexander Graham Bell',
  //     'Thomas Edison',
  //   ],
  //   rightItems: [
  //     'Thuyết tương đối',
  //     'Phát hiện ra phóng xạ',
  //     'Điện thoại',
  //     'Bóng đèn điện',
  //   ],
  //   correctLeftToRight: [
  //     [0, 0],
  //     [1, 1],
  //     [2, 2],
  //     [3, 3],
  //   ],
  //   correctRightToLeft: [
  //     [0, 0],
  //     [1, 1],
  //     [2, 2],
  //     [3, 3],
  //   ],
  //   explanation:
  //     'Các cặp đúng là: Albert Einstein - Thuyết tương đối, Marie Curie - Phát hiện ra phóng xạ, Alexander Graham Bell - Điện thoại, Thomas Edison - Bóng đèn điện.',
  //   difficulty: 'medium',
  //   points: 15,
  //   category: 'history',
  // },
  // {
  //   id: 'match_literature_1',
  //   type: 'matching',
  //   questionText: 'Nối các tác phẩm văn học với tác giả tương ứng:',
  //   leftItems: ['Truyện Kiều', 'Số đỏ', 'Tắt đèn', 'Chí Phèo'],
  //   rightItems: ['Nguyễn Du', 'Vũ Trọng Phụng', 'Ngô Tất Tố', 'Nam Cao'],
  //   correctLeftToRight: [
  //     [0, 0],
  //     [1, 1],
  //     [2, 2],
  //     [3, 3],
  //   ],
  //   correctRightToLeft: [
  //     [0, 0],
  //     [1, 1],
  //     [2, 2],
  //     [3, 3],
  //   ],
  //   explanation:
  //     'Các cặp đúng là: Truyện Kiều - Nguyễn Du, Số đỏ - Vũ Trọng Phụng, Tắt đèn - Ngô Tất Tố, Chí Phèo - Nam Cao.',
  //   difficulty: 'medium',
  //   points: 15,
  //   category: 'literature',
  // },
  // {
  //   id: 'match_music_1',
  //   type: 'matching',
  //   questionText: 'Nối các loại nhạc cụ với nhóm tương ứng:',
  //   leftItems: ['Violin', 'Saxophone', 'Piano', 'Trống'],
  //   rightItems: ['Dây kéo', 'Hơi', 'Phím', 'Gõ'],
  //   correctLeftToRight: [
  //     [0, 0],
  //     [1, 1],
  //     [2, 2],
  //     [3, 3],
  //   ],
  //   correctRightToLeft: [
  //     [0, 0],
  //     [1, 1],
  //     [2, 2],
  //     [3, 3],
  //   ],
  //   explanation:
  //     'Các cặp đúng là: Violin - Dây kéo, Saxophone - Hơi, Piano - Phím, Trống - Gõ.',
  //   difficulty: 'medium',
  //   points: 15,
  //   category: 'music',
  // },
  // {
  //   id: 'match_geography_2',
  //   type: 'matching',
  //   questionText: 'Nối các thành phố với quốc gia tương ứng:',
  //   leftItems: ['New York', 'Sydney', 'Paris', 'Rome'],
  //   rightItems: ['Mỹ', 'Úc', 'Pháp', 'Ý'],
  //   correctLeftToRight: [
  //     [0, 0],
  //     [1, 1],
  //     [2, 2],
  //     [3, 3],
  //   ],
  //   correctRightToLeft: [
  //     [0, 0],
  //     [1, 1],
  //     [2, 2],
  //     [3, 3],
  //   ],
  //   explanation:
  //     'Các cặp đúng là: New York - Mỹ, Sydney - Úc, Paris - Pháp, Rome - Ý.',
  //   difficulty: 'easy',
  //   points: 10,
  //   category: 'geography',
  // },
  // {
  //   id: 'match_science_multi_1',
  //   type: 'matching',
  //   questionText: 'Nối các nhóm hoạt chất với ứng dụng của chúng:',
  //   leftItems: ['Aspirin', 'Paracetamol', 'Ibuprofen', 'Morphine', 'Codeine'],
  //   rightItems: ['Giảm đau', 'Hạ sốt', 'Chống viêm'],
  //   correctLeftToRight: [
  //     [0, 0], // Aspirin - Giảm đau
  //     [0, 1], // Aspirin - Hạ sốt
  //     [0, 2], // Aspirin - Chống viêm
  //     [1, 0], // Paracetamol - Giảm đau
  //     [1, 1], // Paracetamol - Hạ sốt
  //     [2, 0], // Ibuprofen - Giảm đau
  //     [2, 1], // Ibuprofen - Hạ sốt
  //     [2, 2], // Ibuprofen - Chống viêm
  //     [3, 0], // Morphine - Giảm đau
  //     [4, 0], // Codeine - Giảm đau
  //   ],
  //   correctRightToLeft: [
  //     [0, 0], // Giảm đau - Aspirin
  //     [0, 1], // Giảm đau - Paracetamol
  //     [0, 2], // Giảm đau - Ibuprofen
  //     [0, 3], // Giảm đau - Morphine
  //     [0, 4], // Giảm đau - Codeine
  //     [1, 0], // Hạ sốt - Aspirin
  //     [1, 1], // Hạ sốt - Paracetamol
  //     [1, 2], // Hạ sốt - Ibuprofen
  //     [2, 0], // Chống viêm - Aspirin
  //     [2, 2], // Chống viêm - Ibuprofen
  //   ],
  //   explanation:
  //     'Aspirin có cả ba tác dụng. Paracetamol giảm đau và hạ sốt nhưng không chống viêm. Ibuprofen có cả ba tác dụng. Morphine và Codeine chỉ có tác dụng giảm đau.',
  //   difficulty: 'medium',
  //   points: 15,
  //   category: 'science',
  // },
];

// Hàm lấy câu hỏi ngẫu nhiên từ ngân hàng câu hỏi
export function getQuestions(
  count: number,
  difficulty?: DifficultyLevel[],
  questionTypes?: QuestionType[],
  tags?: string[]
): Question[] {
  let questions = [...allQuestions];

  // Lọc theo độ khó nếu được chỉ định
  if (difficulty && difficulty.length > 0) {
    // questions = questions.filter((q) => difficulty.includes(q.difficulty));
  }

  // Lọc theo loại câu hỏi nếu được chỉ định
  if (questionTypes && questionTypes.length > 0) {
    questions = questions.filter((q) => questionTypes.includes(q.type));
  }

  // Lọc theo tags nếu được chỉ định
  if (tags && tags.length > 0) {
    questions = questions.filter(
      (q) => q.tags && tags.some((tag) => q.tags?.includes(tag))
    );
  }

  // Xáo trộn câu hỏi
  for (let i = questions.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [questions[i], questions[j]] = [questions[j], questions[i]];
  }

  return questions.slice(0, count);
}

// Hàm lấy câu hỏi theo loại game
export function getQuestionsForGame(
  count: number,
  difficulty?: DifficultyLevel[],
  questionTypes?: QuestionType[],
  tags?: string[]
): Question[] {
  return getQuestions(count, difficulty, questionTypes, tags);
}

// Các hàm thêm để hỗ trợ việc lọc câu hỏi
export function getQuestionsByDifficulty(
  difficulty: DifficultyLevel
): Question[] {
  return allQuestions.filter((q) => q.difficulty === difficulty);
}

export function getQuestionsByType(type: QuestionType): Question[] {
  return allQuestions.filter((q) => q.type === type);
}

export function getQuestionsByCategory(category: string): Question[] {
  return allQuestions.filter((q) => q.category === category);
}

export function getQuestionsByTags(tags: string[]): Question[] {
  return allQuestions.filter(
    (q) => q.tags && tags.some((tag) => q.tags?.includes(tag))
  );
}

// Trả về một câu hỏi theo ID
export function getQuestionById(id: string): Question | undefined {
  return allQuestions.find((q) => q.id === id);
}
