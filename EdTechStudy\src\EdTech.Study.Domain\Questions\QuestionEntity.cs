﻿using EdTech.Study.Enum;
using EdTech.Study.Exams;
using EdTech.Study.Grade;
using EdTech.Study.GroupQuestions;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;
using Volo.Abp;
using Volo.Abp.Domain.Entities.Auditing;

namespace EdTech.Study.Questions
{
    /// <summary>
    /// Đại diện cho một câu hỏi trong phần thi.
    /// </summary>
    public class QuestionEntity : FullAuditedAggregateRoot<Guid>, ISoftDelete
    {
        public QuestionEntity()
        {

        }

        public QuestionEntity(Guid id) : base(id)
        {

        }

        [Required]
        [MaxLength(512)]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Nội dung câu hỏi
        /// </summary>
        [Required]
        public string Content { get; set; }

        /// <summary>
        /// Định dạng nội dung câu hỏi (Text thuần hoặc HTML).
        /// </summary>
        public ContentFormatType ContentFormat { get; set; }

        /// <summary>
        /// Loại câu hỏi (Chọn 1 đáp án, nhi<PERSON>u đáp án, điền chỗ trống, nối cặp).
        /// </summary>
        public QuestionType QuestionType { get; set; }

        /// <summary>
        /// Mức độ khó của câu hỏi (1-5).
        /// </summary>
        public int Difficulty { get; set; }

        /// <summary>
        /// Trạng thái hiện tại của câu hỏi.
        /// </summary>
        public ExamStatus Status { get; set; }

        /// <summary>
        /// Ghi chú hoặc nhận xét cho câu hỏi.
        /// </summary>
        public string? Comment { get; set; }

        /// <summary>
        /// Liên kết đến môn học tương ứng.
        /// </summary>
        public Guid? SubjectId { get; set; }

        public Subject.Subject? Subject { get; set; }

        /// <summary>
        /// Liên kết đến lớp học tương ứng (có thể null nếu không gắn với lớp cụ thể).
        /// </summary>
        public Guid? GradeId { get; set; }

        public LessonGrade? Grade { get; set; }

        /// <summary>
        /// Có xáo trộn thứ tự các đáp án hay không.
        /// </summary>
        public bool ShuffleOptions { get; set; }

        /// <summary>
        /// Nội dung giải thích đáp án đúng của câu hỏi.
        /// </summary>
        public string? Explanation { get; set; }

        /// <summary>
        /// Phân loại nguồn gốc câu hỏi (Đề thi thường, Ngân hàng đề).
        /// </summary>
        public ExamSourceType SourceType { get; set; }

        public IList<QuestionOptionEntity>? Options { get; set; }
        public IList<FillInBlankAnswerEntity>? FillInBlankAnswers { get; set; }
        public IList<MatchingAnswerEntity>? MatchingAnswers { get; set; }
        public IList<MatchingItemEntity>? MatchingItems { get; set; }

        /// <summary>
        /// Đáp án đúng cho câu hỏi tự luận/ câu hỏi có câu trả lời ngắn
        /// ContentType: Plain Text
        /// </summary>
        public string? CorrectAnswer { get; set; }

        /// <summary>
        /// Danh sách chủ đề(Cách nhau bởi đấu phẩy)
        /// </summary>
        public string? Topics { get; set; }

        /// <summary>
        /// Danh sách gắn nhãn (Cách nhau bởi đấu phẩy)
        /// </summary>
        public string? Tags { get; set; }

        /// <summary>
        /// Danh sách các phần thi liên kết với câu hỏi này.
        /// </summary>
        public IList<SectionQuestionEntity> ExamSectionQuestions { get; set; }

        /// <summary>
        /// Đảm bảo duy nhất mỗi request thêm
        /// </summary>
        [MaxLength(256)]
        public string? IdempotentKey { get; set; }

        /// <summary>
        /// Nguồn gốc
        /// </summary>
        public string? Source { get; set; }

        /// <summary>
        /// ID người dùng được giao xử lý câu hỏi
        /// </summary>
        public Guid? AssignedUserId { get; set; }

        /// <summary>
        /// Ngày giao việc
        /// </summary>
        public DateTime? AssignedDate { get; set; }

        /// <summary>
        /// Hạn xử lý
        /// </summary>
        public DateTime? DueDate { get; set; }

        /// <summary>
        /// Liên kết đến câu hỏi nhóm (nếu có).
        /// </summary>
        public Guid? GroupQuestionId { get; set; }

        public virtual GroupQuestionEntity? GroupQuestion { get; set; }
    }
}
