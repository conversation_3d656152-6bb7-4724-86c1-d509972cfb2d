import { configureStore } from '@reduxjs/toolkit';
import questionDataManagerSlice from './slices/QuestionSlices/questionDataManagerSlice';
import examDataManagerSlice from './slices/ExamSlices/examDataManagerSlice';
import { AppConfigSlice } from './slices/AppConfig/AppConfigSlice';
import subjectSlice from './slices/SubjectSlices/subjectSlice';
import lessonGradeSlice from './slices/LessonGradeSlices/lessonGradeSlice';
import userSlice from './slices/UserSlices/userSlice';
import practiceExamSlice from './slices/ExamSlices/practiceExamSlice';
const questionRootReducer = {
  appConfig: AppConfigSlice.reducer,
  questionDataManager: questionDataManagerSlice.reducer,
  examDataManager: examDataManagerSlice,
  practiceExam: practiceExamSlice,
  subjectManager: subjectSlice,
  lessonGradeManager: lessonGradeSlice,
  userDataManager: userSlice.reducer,
};

export const questionStore = configureStore({
  reducer: questionRootReducer,
});
