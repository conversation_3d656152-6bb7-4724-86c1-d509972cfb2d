is_global = true
build_property.TargetFramework = net8.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = EdTech.Study.Blazor
build_property.RootNamespace = EdTech.Study.Blazor
build_property.ProjectDir = C:\Users\<USER>\source\EdTech\EdTechStudy\src\EdTech.Study.Blazor\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 8.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = true
build_property.MSBuildProjectDirectory = C:\Users\<USER>\source\EdTech\EdTechStudy\src\EdTech.Study.Blazor
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 8.0
build_property.EnableCodeStyleSeverity = 

[C:/Users/<USER>/source/EdTech/EdTechStudy/src/EdTech.Study.Blazor/Pages/Study/Index.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcU3R1ZHlcSW5kZXgucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/EdTech/EdTechStudy/src/EdTech.Study.Blazor/_Imports.razor]
build_metadata.AdditionalFiles.TargetPath = X0ltcG9ydHMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 
