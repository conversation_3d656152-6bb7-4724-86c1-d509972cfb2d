import React, { ReactNode } from 'react';
import { Button, Tooltip, Space, Tag } from 'antd';
import {
  QuestionOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
  FlagOutlined,
  FlagFilled,
  CheckOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import DOMPurify from 'dompurify';
import { MathContent } from '../../common/MathJax/MathJaxWrapper';
import { BaseQuestion } from '../../../interfaces/quizs/questionBase';
import './BaseQuestionPreview.css';
import { EssayQuestion } from '../../../interfaces/quizs/essay.interface';

export interface BaseQuestionPreviewProps {
  // Dữ liệu câu hỏi
  question: BaseQuestion; // Dữ liệu câu hỏi đầy đủ

  // Trạng thái câu hỏi
  hideFeedback?: boolean;
  isMarked?: boolean; // Trạng thái đánh dấu (flag) của câu hỏi

  // Chế độ hiển thị kết quả
  showResult?: boolean; // Hiển thị kết quả sau khi hoàn thành bài thi
  isAnswered?: boolean; // Người dùng đã trả lời câu hỏi này chưa
  isCorrect?: boolean;

  // Số thứ tự câu hỏi
  questionIndex?: number; // Thêm prop để hiển thị số thứ tự câu hỏi

  // Thời gian làm bài
  timeLimit?: number; // Thời gian làm bài tính bằng giây
  showTimer?: boolean; // Hiển thị thời gian hay không

  // Các tùy chọn hiển thị
  titleLevel?: 5 | 4 | 3 | 2 | 1;
  className?: string;
  cardClassName?: string;
  contentClassName?: string;
  explanationClassName?: string;

  // Các slot để truyền nội dung tùy chỉnh
  renderContent?: () => ReactNode; // Slot để render nội dung câu hỏi
  renderInteraction?: () => ReactNode; // Slot để render phần tương tác (input, checkbox, etc.)
  renderExplanation?: () => ReactNode; // Slot để render phần giải thích tùy chỉnh
  renderCorrectAnswer?: () => ReactNode; // Slot để render đáp án đúng tùy chỉnh
  renderExtraActions?: () => ReactNode; // Slot để render các action bổ sung

  // Hướng dẫn
  guidanceText?: string;
  showGuidance?: boolean;

  // Sự kiện
  onToggleMark?: (questionId: string) => void; // Sự kiện khi người dùng nhấn vào icon flag để đánh dấu câu hỏi

  // Các props khác
  children?: ReactNode;
}

// Helper function để format time
const formatTime = (seconds: number): string => {
  if (!seconds) return '00:00';
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins.toString().padStart(2, '0')}:${secs
    .toString()
    .padStart(2, '0')}`;
};

const BaseQuestionPreviewComponent: React.FC<BaseQuestionPreviewProps> = ({
  question,
  isCorrect = false,
  hideFeedback = false,
  isMarked = false,
  showResult = false,
  isAnswered = true,
  questionIndex,
  timeLimit,
  showTimer = false,
  contentClassName = 'question-content',
  explanationClassName = '',
  renderContent,
  renderInteraction,
  renderExplanation,
  renderCorrectAnswer,
  renderExtraActions,
  guidanceText = 'Hướng dẫn cho câu hỏi này',
  showGuidance = true,
  onToggleMark,
  children,
}) => {
  // Render icon trạng thái cho chế độ kết quả
  const renderStatusIcon = () => {
    if (!showResult) {
      // Chế độ thường - hiển thị flag
      return (
        <div
          onClick={
            onToggleMark ? () => onToggleMark(question.clientId) : undefined
          }
          className={onToggleMark ? 'tailwind-cursor-pointer' : ''}
          title={
            onToggleMark
              ? isMarked
                ? 'Bỏ đánh dấu câu hỏi'
                : 'Đánh dấu câu hỏi'
              : undefined
          }
        >
          {isMarked ? (
            <FlagFilled
              className="tailwind-text-primary"
              style={{ fontSize: '20px' }}
            />
          ) : (
            <FlagOutlined
              className="tailwind-text-primary"
              style={{ fontSize: '20px' }}
            />
          )}
        </div>
      );
    } else {
      // Chế độ kết quả - hiển thị trạng thái đúng/sai/không trả lời
      if (!isAnswered) {
        return (
          <Button
            className="tailwind-w-[28px] tailwind-h-[24px] tailwind-bg-yellow-50 tailwind-text-yellow-500"
            icon={<QuestionOutlined />}
          />
        );
      }

      return isCorrect ? (
        <Button
          className="tailwind-w-[28px] tailwind-h-[24px] tailwind-bg-green-50 tailwind-text-green-500"
          icon={<CheckOutlined />}
        />
      ) : (
        <Button
          className="tailwind-w-[28px] tailwind-h-[24px] tailwind-bg-red-50 tailwind-text-red-500"
          icon={<CloseOutlined />}
        />
      );
    }
  };

  // Render phần nội dung mặc định
  const renderDefaultContent = () => {
    if (!question.content) return null;

    return (
      <div className={contentClassName}>
        <MathContent html={question.content} />
      </div>
    );
  };

  // Render phần giải thích mặc định
  const renderDefaultExplanation = () => {
    if (!question.explanation || hideFeedback) return null;

    // Trong chế độ kết quả, luôn hiển thị giải thích nếu có
    if (!showResult) return null;

    return (
      <div
        className={`${explanationClassName} explanation tailwind-flex tailwind-gap-2`}
      >
        <div className="tailwind-flex-shrink-0 tailwind-font-medium">
          Giải thích:
        </div>
        <div
          className="tailwind-flex-shrink-0"
          dangerouslySetInnerHTML={{
            __html: DOMPurify.sanitize(question.explanation),
          }}
        />
      </div>
    );
  };

  // Render phần đáp án mặc định cho câu hỏi Essay
  const renderDefaultCorrectAnswer = () => {
    if (!showResult) return null;
    // Tìm đáp án mặc định cho câu hỏi Essay
    let correctAnswerContent = null;
    if ((question as EssayQuestion).correctAnswer) {
      correctAnswerContent = (question as EssayQuestion).correctAnswer;
    }

    if (!correctAnswerContent) return null;

    return (
      <div className="correct-answer-display">
        <div className="answer-content">
          <div style={{ fontStyle: 'italic' }}>
            <MathContent html={correctAnswerContent} />
          </div>
        </div>
      </div>
    );
  };
  // Render phần hướng dẫn mặc định (chỉ trong chế độ thường)
  const renderDefaultGuidance = () => {
    if (!showGuidance) return null;

    return (
      <Tooltip title={guidanceText}>
        <Button
          icon={<QuestionOutlined style={{ fontSize: '14px' }} />}
          type="primary"
          style={{
            width: '20px',
            height: '20px',
            padding: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: '20px',
          }}
        />
      </Tooltip>
    );
  };

  // Render các action bổ sung
  const extraActions = () => {
    if (renderExtraActions) {
      return renderExtraActions();
    }

    return <Space>{renderDefaultGuidance()}</Space>;
  };

  // Render phần hiển thị điểm số và thời gian
  const renderScoreAndTime = () => {
    const hasScore = question.points !== undefined;
    const hasTime = timeLimit && showTimer; // Không hiển thị timer trong chế độ kết quả

    if (!hasScore && !hasTime) return null;

    return (
      <div className="tailwind-flex tailwind-items-center tailwind-gap-2">
        {hasScore && (
          <Tag
            className="tailwind-flex tailwind-items-center tailwind-gap-1"
            style={{
              color: '#3CAEBD',
              backgroundColor: '#CEF5FD',
              borderColor: '#CEF5FD',
            }}
          >
            <TrophyOutlined /> {question.points} điểm
          </Tag>
        )}
        {hasTime && (
          <Tag
            className="tailwind-flex tailwind-items-center tailwind-gap-1"
            style={{
              color: '#FAAD14',
              backgroundColor: '#FFF8DD',
              borderColor: '#FFF8DD',
            }}
          >
            <ClockCircleOutlined /> {formatTime(timeLimit)}
          </Tag>
        )}
      </div>
    );
  };

  return (
    <div className={`base-question-preview`}>
      {/* Header section with score, time and actions */}
      <div className="tailwind-flex tailwind-justify-between tailwind-items-center">
        <div className="tailwind-flex tailwind-items-center tailwind-gap-2">
          {renderScoreAndTime()}
        </div>
        <div>{extraActions()}</div>
      </div>

      {/* Question content section with fixed icon and index position */}
      <div className="question-content-container">
        <div className="tailwind-flex tailwind-items-start tailwind-gap-2">
          {/* Fixed position for status/flag icon */}
          <div className="tailwind-flex-shrink-0 tailwind-flex tailwind-items-center">
            {renderStatusIcon()}
          </div>

          {/* Fixed position for question index */}
          {questionIndex !== undefined && (
            <div className="tailwind-flex-shrink-0">
              <span className="tailwind-text-gray-700 tailwind-font-medium">
                {questionIndex}.
              </span>
            </div>
          )}

          {/* Question content - takes remaining space */}
          <div className="tailwind-flex-1">
            {renderContent ? renderContent() : renderDefaultContent()}
          </div>
        </div>
      </div>

      {/* Phần tương tác câu hỏi */}
      {renderInteraction && renderInteraction()}

      {/* Phần đáp án đúng - chỉ hiển thị trong chế độ kết quả khi trả lời sai */}
      {showResult &&
        !isCorrect &&
        (renderCorrectAnswer
          ? renderCorrectAnswer()
          : renderDefaultCorrectAnswer())}

      {/* Phần giải thích */}
      {(renderExplanation ||
        (question.explanation && !hideFeedback && showResult)) &&
        (renderExplanation ? renderExplanation() : renderDefaultExplanation())}

      {/* Các phần tử con khác */}
      {children}
    </div>
  );
};

export default BaseQuestionPreviewComponent;
