﻿/**
 * Lesson Configuration
 * This file handles the lesson configuration management functionality
 * using ABP Framework components instead of Syncfusion
 */

// Global variables
let lessonData = [];
let currentFilters = {
    grade: '',
    subject: '',
};
let currentPage = 1;
const pageSize = 15;
let totalPages = 0;
let originalJson = '';
const defaultSeparator = '_';

// JSON editors
let jsonEditor;
let newJsonEditor;

// Initialize the application when the document is ready
$(function () {
    console.log('Lesson Configuration module initialized');

    // Initialize Ace editors
    initializeJsonEditors();

    // Load data for dropdowns and grid
    loadGrades();
    loadSubjects();
    loadLessonData();

    // Add event listeners
    setupEventListeners();
});

/**
 * Initialize Ace editors for JSON editing
 */
function initializeJsonEditors() {
    // Initialize editor for existing lessons
    jsonEditor = ace.edit('jsonEditor');
    jsonEditor.setTheme('ace/theme/chrome');
    jsonEditor.session.setMode('ace/mode/json');
    jsonEditor.setOptions({
        fontSize: '14px',
        showPrintMargin: false,
        showGutter: true,
        highlightActiveLine: true,
        wrap: true,
    });

    // Initialize editor for new lessons
    newJsonEditor = ace.edit('newJsonEditor');
    newJsonEditor.setTheme('ace/theme/chrome');
    newJsonEditor.session.setMode('ace/mode/json');
    newJsonEditor.setOptions({
        fontSize: '14px',
        showPrintMargin: false,
        showGutter: true,
        highlightActiveLine: true,
        wrap: true,
    });

    // Set default template for new editor
    const defaultTemplate = {
        title: 'Bài học mới',
        description: 'Mô tả bài học',
        content: [],
        questions: [],
    };
    newJsonEditor.setValue(
        defaultLessonConfig || JSON.stringify(defaultTemplate, null, 2),
        -1
    );
}

/**
 * Set up all event listeners for the page
 */
function setupEventListeners() {
    // Search form submission
    $('#searchForm').on('submit', function (e) {
        e.preventDefault();
        applyFilters();
    });

    // Search button click (backup in case form submit doesn't trigger)
    $('#searchBtn').on('click', function (e) {
        e.preventDefault();
        applyFilters();
    });

    // Clear filters button
    $('#clearFilterBtn').on('click', clearFilters);

    // JSON formatting and validation buttons
    $('#formatBtn').on('click', function () {
        formatJson(jsonEditor, $('#jsonError'));
    });

    $('#validateBtn').on('click', function () {
        validateJson(jsonEditor, $('#jsonError'));
    });

    $('#formatNewBtn').on('click', function () {
        formatJson(newJsonEditor, $('#newJsonError'));
    });

    $('#validateNewBtn').on('click', function () {
        validateJson(newJsonEditor, $('#newJsonError'));
    });

    // Reset JSON button
    $('#resetJsonBtn').on('click', resetJson);

    // Save JSON button
    $('#saveJsonBtn').on('click', saveJson);

    // Create new lesson button
    $('#createLessonBtn').on('click', createNewLesson);

    // Create default configuration button
    $('#createDefaultBtn').on('click', createDefaultConfig);

    // JSON file input change event
    $('#jsonFileInput').on('change', handleFileInputChange);

    // Import JSON button
    $('#importJsonBtn').on('click', importJsonConfig);

    // Browse file button
    $('#browseFileBtn').on('click', function () {
        $('#jsonFileInput').click();
    });

    // Open edit lesson modal
    $('#editLessonBtn').on('click', function () {
        const selectedRow = $('#lessonTableBody').find('tr.selected');
        if (selectedRow.length > 0) {
            const lesson = selectedRow.data('lesson');
            openEditLessonModal(lesson);
        }
    });

    // Save lesson button
    $('#saveLessonBtn').on('click', saveLesson);
}

/**
 * Load grades from OData API
 */
function loadGrades() {
    abp.ajax({
        url: '/odata/LessonGrade',
        type: 'GET',
        success: function (data) {
            if (data && data.value && Array.isArray(data.value)) {
                // Sort grades by Name
                const sortedGrades = [...data.value].sort((a, b) =>
                    (a.Name || '').localeCompare(b.Name || '')
                );

                // Populate grade dropdowns
                populateDropdown(
                    '#gradeSelect',
                    sortedGrades,
                    'Tất cả khối lớp'
                );
                populateDropdown(
                    '#newGradeSelect',
                    sortedGrades,
                    'Chọn khối lớp'
                );

                // Set initial value from URL if available
                const urlParams = new URLSearchParams(window.location.search);
                const gradeParam = urlParams.get('Grade');
                if (gradeParam) {
                    $('#gradeSelect').val(gradeParam);
                    currentFilters.grade = gradeParam;
                    updateFilterHeader();
                }
            }
        },
        error: function (error) {
            abp.notify.error('Không thể tải danh sách khối lớp');
            console.error('Error loading grades:', error);
        },
    });
}

/**
 * Load subjects from OData API
 */
function loadSubjects() {
    abp.ajax({
        url: '/odata/Subject',
        type: 'GET',
        success: function (data) {
            if (data && data.value && Array.isArray(data.value)) {
                // Sort subjects by Name
                const sortedSubjects = [...data.value].sort((a, b) =>
                    (a.Name || '').localeCompare(b.Name || '')
                );

                // Populate subject dropdowns
                populateDropdown(
                    '#subjectSelect',
                    sortedSubjects,
                    'Tất cả môn học'
                );
                populateDropdown(
                    '#newSubjectSelect',
                    sortedSubjects,
                    'Chọn môn học'
                );

                // Set initial value from URL if available
                const urlParams = new URLSearchParams(window.location.search);
                const subjectParam = urlParams.get('Subject');
                if (subjectParam) {
                    $('#subjectSelect').val(subjectParam);
                    currentFilters.subject = subjectParam;
                    updateFilterHeader();
                }
            }
        },
        error: function (error) {
            abp.notify.error('Không thể tải danh sách môn học');
            console.error('Error loading subjects:', error);
        },
    });
}

/**
 * Populates a dropdown with data
 * @param {string} selector - The selector for the dropdown
 * @param {Array} data - The data to populate with
 * @param {string} defaultText - The text for the default option
 */
function populateDropdown(selector, data, defaultText) {
    const dropdown = $(selector);
    dropdown.empty();

    // Add default option
    dropdown.append($('<option></option>').attr('value', '').text(defaultText));

    // Add options from data
    $.each(data, function (index, item) {
        if (item && item.Code !== undefined && item.Name) {
            dropdown.append(
                $('<option></option>').attr('value', item.Code).text(item.Name)
            );
        }
    });
}

/**
 * Load lesson data from the API
 */
function loadLessonData() {
    // Show loading indicator
    $('#lessonTableBody').html(
        '<tr><td colspan="6" class="text-center"><i class="fas fa-spinner fa-spin"></i> Đang tải dữ liệu...</td></tr>'
    );

    // Fetch data from API
    abp.ajax({
        url: '/api/odata/lessons',
        type: 'GET',
        success: function (data) {
            if (data && data.value && Array.isArray(data.value)) {
                // OData v4 format
                lessonData = data.value;
                renderLessonData();
            } else if (data && Array.isArray(data)) {
                // Direct array format
                lessonData = data;
                renderLessonData();
            } else {
                throw new Error('Invalid data format');
            }
        },
        error: function (error) {
            console.error('Error loading data:', error);
            $('#lessonTableBody').html(
                `<tr><td colspan="6" class="text-center text-danger">
                    <strong>Lỗi:</strong> Không thể tải dữ liệu từ API. Vui lòng thử lại sau.
                </td></tr>`
            );
        },
    });
}

/**
 * Render lesson data to the table
 */
function renderLessonData() {
    // Filter data based on current filters
    let filteredData = lessonData;
    if (currentFilters.grade) {
        filteredData = filteredData.filter((item) => {
            const gradeName = item.gradeName || item.GradeName || '';
            const gradeCode = item.gradeCode || item.GradeCode || '';
            return (
                gradeName
                    .toLowerCase()
                    .includes(currentFilters.grade.toLowerCase()) ||
                gradeCode.toLowerCase() === currentFilters.grade.toLowerCase()
            );
        });
    }

    if (currentFilters.subject) {
        filteredData = filteredData.filter((item) => {
            const subjectName = item.subjectName || item.SubjectName || '';
            const subjectCode = item.subjectCode || item.SubjectCode || '';
            return (
                subjectName
                    .toLowerCase()
                    .includes(currentFilters.subject.toLowerCase()) ||
                subjectCode.toLowerCase() ===
                    currentFilters.subject.toLowerCase()
            );
        });
    }

    // Update total count
    $('#totalRecords').text(filteredData.length);

    // Calculate pagination
    totalPages = Math.ceil(filteredData.length / pageSize);
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, filteredData.length);

    // Update pagination info
    $('#startRecord').text(filteredData.length > 0 ? startIndex + 1 : 0);
    $('#endRecord').text(endIndex);

    // Get the current page data
    const currentPageData = filteredData.slice(startIndex, endIndex);

    // Clear the table
    $('#lessonTableBody').empty();

    // If no data, show message
    if (currentPageData.length === 0) {
        $('#lessonTableBody').html(
            '<tr><td colspan="6" class="text-center">Không có dữ liệu</td></tr>'
        );
        return;
    }

    // Render the data rows
    $.each(currentPageData, function (index, item) {
        const row = $('<tr></tr>');

        // STT column
        $('<td></td>')
            .addClass('text-center')
            .text(startIndex + index + 1)
            .appendTo(row);

        // Grade column
        $('<td></td>')
            .text(item.gradeName || item.GradeName || '')
            .appendTo(row);

        // Subject column
        $('<td></td>')
            .text(item.subjectName || item.SubjectName || '')
            .appendTo(row);

        // Lesson name column
        $('<td></td>')
            .text(item.lessonName || item.LessonName || '')
            .appendTo(row);

        // Status column
        const statusCell = $('<td></td>').addClass('text-center');
        if (item.status === 1) {
            $('<span></span>')
                .addClass('badge bg-success')
                .text('Đã cấu hình')
                .appendTo(statusCell);
        } else {
            $('<span></span>')
                .addClass('badge bg-warning')
                .text('Chưa cấu hình')
                .appendTo(statusCell);
        }
        statusCell.appendTo(row);

        // Actions column
        const actionsCell = $('<td></td>').addClass('text-center');
        const btnGroup = $('<div></div>').addClass('btn-group');

        // Get lesson info
        const grade = item.gradeCode || item.GradeCode || '';
        const subject = item.subjectCode || item.SubjectCode || '';
        const lesson =
            item.lessonCode ||
            item.LessonCode ||
            item.lesson ||
            item.Lesson ||
            '';
        const lessonName = item.lessonName || item.LessonName || '';

        // Preview button
        $('<button></button>')
            .attr('type', 'button')
            .addClass('btn btn-sm btn-info btn-preview me-1')
            .html('<i class="fas fa-eye"></i> Xem')
            .on('click', function () {
                window.location.href = `/demolessonpage?lesson=${lesson}&grade=${grade}&subject=${subject}`;
            })
            .appendTo(btnGroup);

        // Import button
        $('<button></button>')
            .attr('type', 'button')
            .addClass('btn btn-sm btn-secondary btn-import me-1')
            .html('<i class="fas fa-file-import"></i> Import')
            .on('click', function () {
                openImportModal(grade, subject, lesson, lessonName);
            })
            .appendTo(btnGroup);

        // Configure button
        $('<button></button>')
            .attr('type', 'button')
            .addClass('btn btn-sm btn-primary btn-configure')
            .html('<i class="fas fa-edit"></i> Cấu hình')
            .on('click', function () {
                openJsonConfigModal(item);
            })
            .appendTo(btnGroup);

        // Delete button
        $('<button></button>')
            .attr('type', 'button')
            .addClass('btn btn-sm btn-danger btn-delete ms-1')
            .html('<i class="fas fa-trash"></i>')
            .on('click', function () {
                deleteLesson(item.id);
            })
            .appendTo(btnGroup);

        actionsCell.append(btnGroup);
        row.append(actionsCell);
        $('#lessonTableBody').append(row);
    });

    // Render pagination controls
    renderPagination();
}

/**
 * Render pagination controls
 */
function renderPagination() {
    const paginationControls = $('#paginationControls');
    paginationControls.empty();

    // Previous button
    const prevLi = $('<li></li>')
        .addClass('page-item')
        .toggleClass('disabled', currentPage === 1);

    $('<a></a>')
        .addClass('page-link')
        .attr('href', '#')
        .html('&laquo;')
        .on('click', function (e) {
            e.preventDefault();
            if (currentPage > 1) {
                currentPage--;
                renderLessonData();
            }
        })
        .appendTo(prevLi);

    paginationControls.append(prevLi);

    // Page numbers
    const maxPages = Math.min(totalPages, 5);
    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(startPage + maxPages - 1, totalPages);

    if (endPage - startPage + 1 < maxPages) {
        startPage = Math.max(1, endPage - maxPages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
        const pageLi = $('<li></li>')
            .addClass('page-item')
            .toggleClass('active', i === currentPage);

        $('<a></a>')
            .addClass('page-link')
            .attr('href', '#')
            .text(i)
            .on('click', function (e) {
                e.preventDefault();
                currentPage = i;
                renderLessonData();
            })
            .appendTo(pageLi);

        paginationControls.append(pageLi);
    }

    // Next button
    const nextLi = $('<li></li>')
        .addClass('page-item')
        .toggleClass('disabled', currentPage === totalPages);

    $('<a></a>')
        .addClass('page-link')
        .attr('href', '#')
        .html('&raquo;')
        .on('click', function (e) {
            e.preventDefault();
            if (currentPage < totalPages) {
                currentPage++;
                renderLessonData();
            }
        })
        .appendTo(nextLi);

    paginationControls.append(nextLi);
}

/**
 * Apply filters from the form
 */
function applyFilters() {
    // Get filter values
    currentFilters.grade = $('#gradeSelect').val() || '';
    currentFilters.subject = $('#subjectSelect').val() || '';

    // Update URL to reflect filters (for bookmarking/sharing)
    const url = new URL(window.location.href);

    // Only add parameters to URL if they have values
    if (currentFilters.grade) {
        url.searchParams.set('Grade', currentFilters.grade);
    } else {
        url.searchParams.delete('Grade');
    }

    if (currentFilters.subject) {
        url.searchParams.set('Subject', currentFilters.subject);
    } else {
        url.searchParams.delete('Subject');
    }

    window.history.pushState({}, '', url);

    // Reset to first page when applying filters
    currentPage = 1;

    // Render the data with new filters
    renderLessonData();

    // Update the header to show we're using filters
    updateFilterHeader();
}

/**
 * Clear all filters
 */
function clearFilters() {
    // Reset the filter values
    $('#gradeSelect').val('');
    $('#subjectSelect').val('');

    currentFilters.grade = '';
    currentFilters.subject = '';

    // Update URL
    const url = new URL(window.location.href);
    url.searchParams.delete('Grade');
    url.searchParams.delete('Subject');
    window.history.pushState({}, '', url);

    // Reset to first page
    currentPage = 1;

    // Render the data with cleared filters
    renderLessonData();

    // Update the header to show we're not using filters
    updateFilterHeader();
}

/**
 * Update the filter header text
 */
function updateFilterHeader() {
    const filterIndicator = $('#filterIndicator');

    if (currentFilters.grade || currentFilters.subject) {
        let filterText = 'Đang lọc: ';

        if (currentFilters.grade) {
            const gradeText = $('#gradeSelect option:selected').text();
            filterText +=
                'Khối ' +
                (gradeText !== 'Tất cả khối lớp'
                    ? gradeText
                    : currentFilters.grade);
        }

        if (currentFilters.subject) {
            if (currentFilters.grade) filterText += ', ';
            const subjectText = $('#subjectSelect option:selected').text();
            filterText +=
                'Môn ' +
                (subjectText !== 'Tất cả môn học'
                    ? subjectText
                    : currentFilters.subject);
        }

        filterIndicator.text(filterText).show();
    } else {
        filterIndicator.hide();
    }
}

/**
 * Format JSON in an Ace editor
 * @param {object} editor - The Ace editor instance
 * @param {jQuery} errorDiv - The jQuery selector for the error div
 */
function formatJson(editor, errorDiv) {
    try {
        const jsonContent = editor.getValue().trim();
        if (jsonContent) {
            const parsedJson = JSON.parse(jsonContent);
            const formattedJson = JSON.stringify(parsedJson, null, 2);
            editor.setValue(formattedJson, -1);
            errorDiv.text('').removeClass('text-danger text-success');
        }
    } catch (e) {
        errorDiv
            .text('Lỗi: ' + e.message)
            .addClass('text-danger')
            .removeClass('text-success');
    }
}

/**
 * Validate JSON in an Ace editor
 * @param {object} editor - The Ace editor instance
 * @param {jQuery} errorDiv - The jQuery selector for the error div
 * @returns {boolean} - Whether the JSON is valid
 */
function validateJson(editor, errorDiv) {
    try {
        const jsonContent = editor.getValue().trim();
        if (jsonContent) {
            JSON.parse(jsonContent);
            errorDiv
                .text('JSON hợp lệ!')
                .addClass('text-success')
                .removeClass('text-danger');
            return true;
        } else {
            errorDiv.text('').removeClass('text-danger text-success');
            return true;
        }
    } catch (e) {
        errorDiv
            .text('Lỗi: ' + e.message)
            .addClass('text-danger')
            .removeClass('text-success');
        return false;
    }
}

/**
 * Open the JSON configuration modal
 * @param {object} rowData - The entire row data containing lesson information
 */
function openJsonConfigModal(rowData) {
    // Extract necessary information from row data
    const grade = rowData.gradeCode || rowData.GradeCode || '';
    const subject = rowData.subjectCode || rowData.SubjectCode || '';
    const lesson =
        rowData.lessonCode ||
        rowData.LessonCode ||
        rowData.lesson ||
        rowData.Lesson ||
        '';
    const lessonText = rowData.lessonName || rowData.LessonName || '';
    const lessonId = rowData.Id || rowData.id;
    const status = rowData.Status || rowData.status || 0;

    // Validate parameters
    if (!grade || !subject || !lesson) {
        abp.notify.error('Lỗi: Thiếu thông tin cần thiết để mở cấu hình JSON');
        return;
    }

    try {
        // Set modal title
        $('#jsonConfigTitle').text(
            `${lessonText || 'Bài học'} (${grade}_${subject}_${lesson}.json)`
        );

        // Set hidden input values
        const jsonKey = `${grade}${defaultSeparator}${subject}${defaultSeparator}${lesson}`;
        $('#modalJsonKey').val(jsonKey);
        $('#modalLessonId').val(lessonId);

        // Set lesson information
        $('#modalLessonValue').val(lesson);
        $('#modalLessonText').val(lessonText);
        $('#modalLessonStatus').val(status);

        // Load grades and subjects for dropdowns
        abp.ajax({
            url: '/odata/LessonGrade',
            type: 'GET',
            success: function (data) {
                if (data && data.value && Array.isArray(data.value)) {
                    // Sort grades by Name
                    const sortedGrades = [...data.value].sort((a, b) =>
                        (a.Name || '').localeCompare(b.Name || '')
                    );

                    // Populate grade dropdown
                    populateDropdown(
                        '#modalGradeSelect',
                        sortedGrades,
                        'Chọn khối lớp'
                    );

                    // Set selected grade
                    $('#modalGradeSelect').val(grade);
                }
            },
            error: function (error) {
                abp.notify.error('Không thể tải danh sách khối lớp');
                console.error('Error loading grades:', error);
            },
        });

        abp.ajax({
            url: '/odata/Subject',
            type: 'GET',
            success: function (data) {
                if (data && data.value && Array.isArray(data.value)) {
                    // Sort subjects by Name
                    const sortedSubjects = [...data.value].sort((a, b) =>
                        (a.Name || '').localeCompare(b.Name || '')
                    );

                    // Populate subject dropdown
                    populateDropdown(
                        '#modalSubjectSelect',
                        sortedSubjects,
                        'Chọn môn học'
                    );

                    // Set selected subject
                    $('#modalSubjectSelect').val(subject);
                }
            },
            error: function (error) {
                abp.notify.error('Không thể tải danh sách môn học');
                console.error('Error loading subjects:', error);
            },
        });

        // Fetch JSON data using the API
        abp.ajax({
            url: `/api/default-lesson-config/${encodeURIComponent(jsonKey)}`,
            type: 'GET',
            success: function (data) {
                // Store original JSON for reset functionality
                originalJson = data || '{}';

                // Set editor content
                jsonEditor.setValue(originalJson, -1);
                $('#jsonError')
                    .text('')
                    .removeClass('text-danger text-success');

                // Show the modal
                $('#jsonConfigModal').modal('show');
            },
            error: function (error) {
                // Check if it's a 404 (not found) error
                if (error && error.status === 404) {
                    // Show create default modal
                    $('#defaultGrade').val(grade);
                    $('#defaultSubject').val(subject);
                    $('#defaultLesson').val(lesson);
                    $('#createDefaultModal').modal('show');
                } else {
                    abp.notify.error(
                        'Lỗi khi mở cấu hình JSON: ' +
                            (error.message || 'Không thể tải dữ liệu')
                    );
                }
            },
        });
    } catch (error) {
        abp.notify.error('Lỗi khi mở cấu hình JSON: ' + error.message);
    }
}

/**
 * Reset JSON editor to original value
 */
function resetJson() {
    abp.message.confirm(
        'Bạn có chắc muốn khôi phục JSON về trạng thái ban đầu?',
        'Xác nhận khôi phục',
        function (isConfirmed) {
            if (isConfirmed) {
                jsonEditor.setValue(originalJson, -1);
                $('#jsonError')
                    .text('')
                    .removeClass('text-danger text-success');
            }
        }
    );
}

/**
 * Save lesson information and JSON configuration
 */
function saveJson() {
    // Validate JSON before saving
    if (!validateJson(jsonEditor, $('#jsonError'))) {
        abp.notify.warn('JSON không hợp lệ. Vui lòng sửa lỗi trước khi lưu.');
        return;
    }

    // Get form values
    const lessonId = $('#modalLessonId').val();
    const grade = $('#modalGradeSelect').val();
    const subject = $('#modalSubjectSelect').val();
    const lessonCode = $('#modalLessonValue').val();
    const lessonName = $('#modalLessonText').val();
    const status = $('#modalLessonStatus').val();
    const jsonKey = $('#modalJsonKey').val();
    const jsonContent = jsonEditor.getValue();

    // Validate form
    if (!grade) {
        abp.notify.warn('Vui lòng chọn khối lớp');
        return;
    }

    if (!subject) {
        abp.notify.warn('Vui lòng chọn môn học');
        return;
    }

    if (!lessonCode) {
        abp.notify.warn('Vui lòng nhập mã bài học');
        return;
    }

    if (!lessonName) {
        abp.notify.warn('Vui lòng nhập tên bài học');
        return;
    }

    try {
        // Parse JSON to validate
        const jsonData = JSON.parse(jsonContent);

        // Show loading indicator
        abp.ui.setBusy($('#jsonConfigModal'));

        // First, update lesson information
        const lessonData = {
            Id: lessonId,
            GradeCode: grade,
            SubjectCode: subject,
            Code: lessonCode,
            Name: lessonName,
            Status: parseInt(status),
            JsonConfig: jsonContent,
        };

        // Update lesson information using PUT /api/lessons
        abp.ajax({
            url: `/api/lessons/update`,
            type: 'PUT',
            data: JSON.stringify(lessonData),
            contentType: 'application/json',
            success: function () {
                // Then, save JSON configuration
                // Update lesson with JSON config
                lessonData.JsonConfig = jsonContent;
                abp.notify.success('Đã lưu thay đổi thành công!');
                $('#jsonConfigModal').modal('hide');

                // Refresh the grid to show updated status
                loadLessonData();
                abp.ui.clearBusy($('#jsonConfigModal'));
            },
            error: function (error) {
                abp.notify.error(
                    'Lỗi khi cập nhật bài học: ' +
                        (error.message || 'Không thể cập nhật bài học')
                );
                abp.ui.clearBusy($('#jsonConfigModal'));
            },
        });
    } catch (error) {
        abp.notify.error('Lỗi khi phân tích JSON: ' + error.message);
        abp.ui.clearBusy($('#jsonConfigModal'));
    }
}

/**
 * Create default configuration for a lesson
 */
function createDefaultConfig() {
    const grade = $('#defaultGrade').val();
    const subject = $('#defaultSubject').val();
    const lesson = $('#defaultLesson').val();
    const jsonKey = `${grade}${defaultSeparator}${subject}${defaultSeparator}${lesson}`;

    abp.ui.setBusy($('#createDefaultModal'));

    // Create default template
    const defaultTemplate = {
        title: 'Bài học mới',
        description: 'Mô tả bài học',
        content: [],
        questions: [],
    };

    // Use the API to save the default configuration
    abp.ajax({
        url: `/api/default-lesson-config/save`,
        type: 'POST',
        data: JSON.stringify({
            key: jsonKey,
            data: JSON.stringify(defaultTemplate),
        }),
        contentType: 'application/json',
        success: function () {
            abp.notify.success('Đã tạo cấu hình mặc định thành công!');
            $('#createDefaultModal').modal('hide');

            // Refresh the grid to show updated status
            loadLessonData();
        },
        error: function (error) {
            abp.notify.error(
                'Lỗi khi tạo cấu hình mặc định: ' +
                    (error.message || 'Không thể tạo cấu hình')
            );
        },
        complete: function () {
            abp.ui.clearBusy($('#createDefaultModal'));
        },
    });
}

/**
 * Create a new lesson
 */
function createNewLesson() {
    // Get form values
    const grade = $('#newGradeSelect').val();
    const subject = $('#newSubjectSelect').val();
    const code = $('#newLessonValue').val();
    const name = $('#newLessonText').val();
    const status = $('#newLessonStatus').val();
    const jsonConfig = newJsonEditor.getValue();
    // Validate required fields
    if (!grade || !subject || !code || !name) {
        abp.notify.error('Vui lòng điền đầy đủ thông tin bài học');
        return;
    }

    abp.ui.setBusy($('#newLessonModal'));

    // Call API to create new lesson
    abp.ajax({
        url: '/api/lessons',
        type: 'POST',
        data: JSON.stringify({
            gradeCode: grade,
            subjectCode: subject,
            code: code,
            name: name,
            status: parseInt(status),
            jsonConfig: jsonConfig, // Send as string directly
        }),
        contentType: 'application/json',
        success: function () {
            abp.notify.success('Đã tạo bài học mới thành công!');
            $('#newLessonModal').modal('hide');

            // Reset form
            $('#newLessonForm')[0].reset();
            newJsonEditor.setValue(defaultLessonConfig || '{}', -1);

            // Refresh lesson list
            loadLessonData();
        },
        error: function (error) {
            abp.notify.error(
                'Lỗi khi tạo bài học: ' +
                    (error.message || 'Không thể tạo bài học')
            );
        },
        complete: function () {
            abp.ui.clearBusy($('#newLessonModal'));
        },
    });
}

/**
 * Delete a lesson
 * @param {number} lessonId - The ID of the lesson to delete
 */
function deleteLesson(lessonId) {
    abp.message.confirm(
        'Bạn có chắc muốn xóa bài học này?',
        'Xác nhận xóa',
        function (isConfirmed) {
            if (isConfirmed) {
                abp.ajax({
                    url: `/api/lessons/${lessonId}`,
                    type: 'DELETE',
                    success: function () {
                        abp.notify.success('Đã xóa bài học thành công');
                        loadLessonData();
                    },
                    error: function (error) {
                        abp.notify.error(
                            'Lỗi khi xóa bài học: ' +
                                (error.message || 'Không thể xóa bài học')
                        );
                    },
                });
            }
        }
    );
}

/**
 * Open the import JSON modal
 * @param {string} grade - The grade code
 * @param {string} subject - The subject code
 * @param {string} lesson - The lesson code
 * @param {string} lessonName - The lesson name
 */
function openImportModal(grade, subject, lesson, lessonName) {
    // Set modal values
    $('#importLessonName').text(lessonName || `${grade}_${subject}_${lesson}`);
    $('#importGrade').val(grade);
    $('#importSubject').val(subject);
    $('#importLesson').val(lesson);

    // Reset file input and error message
    $('#jsonFileInput').val('');
    $('#fileError').text('');
    $('#importJsonBtn').prop('disabled', true);
    $('#fileNameDisplay').val('');

    // Show the modal
    $('#importConfigModal').modal('show');
}

/**
 * Open the edit lesson modal
 * @param {object} lesson - The lesson object containing lesson information
 */
function openEditLessonModal(lesson) {
    // Set form values
    $('#editLessonId').val(lesson.id);
    $('#editGradeSelect').val(lesson.gradeCode || lesson.GradeCode);
    $('#editSubjectSelect').val(lesson.subjectCode || lesson.SubjectCode);
    $('#editLessonValue').val(
        lesson.lessonCode || lesson.LessonCode || lesson.lesson || lesson.Lesson
    );
    $('#editLessonText').val(lesson.lessonName || lesson.LessonName);
    $('#editLessonStatus').val(lesson.status || lesson.Status);

    // Show the modal
    $('#editLessonModal').modal('show');
}

/**
 * Save lesson information
 */
function saveLesson() {
    // Get form values
    const lessonId = $('#editLessonId').val();
    const grade = $('#editGradeSelect').val();
    const subject = $('#editSubjectSelect').val();
    const lessonCode = $('#editLessonValue').val();
    const lessonName = $('#editLessonText').val();
    const status = $('#editLessonStatus').val();

    // Validate form
    if (!grade) {
        abp.notify.warn('Vui lòng chọn khối lớp');
        return;
    }

    if (!subject) {
        abp.notify.warn('Vui lòng chọn môn học');
        return;
    }

    if (!lessonCode) {
        abp.notify.warn('Vui lòng nhập mã bài học');
        return;
    }

    if (!lessonName) {
        abp.notify.warn('Vui lòng nhập tên bài học');
        return;
    }

    // Prepare data for API call
    const data = {
        Id: lessonId,
        GradeCode: grade,
        SubjectCode: subject,
        Code: lessonCode,
        Name: lessonName,
        Status: parseInt(status),
    };

    // Show loading indicator
    abp.ui.setBusy($('#editLessonModal'));

    // Call API to update lesson
    abp.ajax({
        url: `/api/lessons/${lessonId}`,
        type: 'PUT',
        data: JSON.stringify(data),
        contentType: 'application/json',
        success: function () {
            abp.notify.success('Đã cập nhật thông tin bài học thành công!');
            $('#editLessonModal').modal('hide');

            // Refresh grid
            loadLessonData();
        },
        error: function (error) {
            abp.notify.error(
                'Lỗi khi cập nhật bài học: ' +
                    (error.message || 'Không thể cập nhật bài học')
            );
        },
        complete: function () {
            abp.ui.clearBusy($('#editLessonModal'));
        },
    });
}

/**
 * Handle file input change event
 */
function handleFileInputChange(e) {
    const file = e.target.files[0];
    if (!file) {
        $('#fileNameDisplay').val('');
        $('#importJsonBtn').prop('disabled', true);
        return;
    }

    // Check if file is JSON
    if (!file.name.endsWith('.json')) {
        $('#fileError').text('Vui lòng chọn file JSON');
        $('#importJsonBtn').prop('disabled', true);
        return;
    }

    // Read file content
    const reader = new FileReader();
    reader.onload = function (e) {
        try {
            // Validate JSON
            const jsonContent = e.target.result;
            JSON.parse(jsonContent);

            // Update UI
            $('#fileNameDisplay').val(file.name);
            $('#fileError').text('');
            $('#importJsonBtn').prop('disabled', false);
        } catch (error) {
            $('#fileError').text('File JSON không hợp lệ: ' + error.message);
            $('#importJsonBtn').prop('disabled', true);
        }
    };
    reader.onerror = function () {
        $('#fileError').text('Không thể đọc file');
        $('#importJsonBtn').prop('disabled', true);
    };
    reader.readAsText(file);
}

/**
 * Import JSON configuration from file
 */
function importJsonConfig() {
    const file = $('#jsonFileInput')[0].files[0];
    if (!file) {
        abp.notify.warn('Vui lòng chọn file JSON để import');
        return;
    }

    const grade = $('#importGrade').val();
    const subject = $('#importSubject').val();
    const lesson = $('#importLesson').val();

    if (!grade || !subject || !lesson) {
        abp.notify.error('Thiếu thông tin bài học');
        return;
    }

    const jsonKey = `${grade}${defaultSeparator}${subject}${defaultSeparator}${lesson}`;

    // Show loading indicator
    abp.ui.setBusy($('#importConfigModal'));

    // Read file content
    const reader = new FileReader();
    reader.onload = function (e) {
        try {
            const jsonContent = e.target.result;
            // Validate JSON
            JSON.parse(jsonContent);

            // Save JSON configuration
            abp.ajax({
                url: `/api/default-lesson-config/save`,
                type: 'POST',
                data: JSON.stringify({
                    key: jsonKey,
                    data: jsonContent,
                }),
                contentType: 'application/json',
                success: function () {
                    abp.notify.success('Đã import cấu hình JSON thành công!');
                    $('#importConfigModal').modal('hide');

                    // Refresh the grid to show updated status
                    loadLessonData();
                },
                error: function (error) {
                    abp.notify.error(
                        'Lỗi khi import JSON: ' +
                            (error.message || 'Không thể lưu dữ liệu')
                    );
                },
                complete: function () {
                    abp.ui.clearBusy($('#importConfigModal'));
                },
            });
        } catch (error) {
            abp.notify.error('File JSON không hợp lệ: ' + error.message);
            abp.ui.clearBusy($('#importConfigModal'));
        }
    };
    reader.onerror = function () {
        abp.notify.error('Không thể đọc file');
        abp.ui.clearBusy($('#importConfigModal'));
    };
    reader.readAsText(file);
}
