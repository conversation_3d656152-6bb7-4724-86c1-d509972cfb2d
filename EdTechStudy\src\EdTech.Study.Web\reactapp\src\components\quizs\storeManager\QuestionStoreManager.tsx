import {
  BookOutlined,
  CaretRightOutlined,
  CheckCircleOutlined,
  CheckOutlined,
  CloseCircleOutlined,
  CopyOutlined,
  DeleteFilled,
  EditOutlined,
  FilterOutlined,
  ProfileOutlined,
  QuestionCircleOutlined,
  ReadOutlined,
  ReloadOutlined,
  SettingOutlined,
  TagsOutlined,
  UserDeleteOutlined,
  UserOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import {
  Badge,
  Button,
  Card,
  Col,
  Collapse,
  CollapseProps,
  DatePicker,
  Input,
  Modal,
  Row,
  Select,
  Space,
  Table,
  Tag,
  Tooltip,
  Typography,
  message,
} from 'antd';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
const { Option } = Select;

import type {
  ColumnsType,
  FilterValue,
  SorterResult,
  TablePaginationConfig,
} from 'antd/es/table/interface';
import { BatchAssignQuestionsDto } from '../../../api/questionDraftApi';
import {
  ExamStatus,
  ExamSourceType,
} from '../../../interfaces/exams/examEnums';
import {
  BaseQuestion,
  QuestionType,
} from '../../../interfaces/quizs/questionBase';
import { useQuestionContext } from '../../../providers/Questions/QuestionProvider';
import { DeleteIcon } from '../../icons/IconIndex';
import './QuestionStoreManager.css';
import QuestionTableAdvancedSettings, {
  TableColumn,
} from './QuestionTableAdvancedSettings';
import { getExamStatusDisplayVN } from '../../../utils/examUtils';
import { SEARCH_PARAMS_STORAGE_KEY } from '../../common/QuestionRenderComponent/QuestionRenderComponent';

const { Title } = Typography;
const { Search } = Input;

export interface IQuestionStoreManagerProps {
  id: string;
  displayMode?: string;
  questions: BaseQuestion[];
  loading?: boolean;
  totalCount?: number;
  onEditQuestion?: (question: BaseQuestion) => void;
  onDeleteQuestions?: (questionIds: string[]) => void;
  onPreviewQuestion?: (questionId: string) => void;
  onExportQuestions?: () => void;
  onImportQuestions?: () => void;
  onTableChange?: (
    pagination: TablePaginationConfig,
    filters: Record<string, FilterValue | null>,
    sorter: SorterResult<BaseQuestion> | SorterResult<BaseQuestion>[]
  ) => void;
  onSearch?: (searchText: string) => void;
  onRefresh?: () => void;
  onFilterChange?: (filterType: string, value: any) => void;
  onStatusChangeQuestions?: (questionIds: string[], status: ExamStatus) => void;
  extraButtons?: React.ReactNode[];
  onAssignQuestions?: (input: BatchAssignQuestionsDto) => any;
  onUnassignQuestions?: (questionIds: string[]) => any;
}

// Map for question type display names use QuestionType
const questionTypeMap: Record<QuestionType, string> = {
  [QuestionType.SingleChoice]: 'Trắc nghiệm 1 đáp án',
  [QuestionType.MultipleChoice]: 'Trắc nghiệm nhiều đáp án',
  [QuestionType.Matching]: 'Câu hỏi nối',
  [QuestionType.Essay]: 'Câu hỏi tự luận',
  [QuestionType.FillInBlank]: 'Câu hỏi điền từ',
  [QuestionType.TrueFalse]: 'Câu hỏi đúng/Sai',
  [QuestionType.Ordering]: 'Câu hỏi sắp xếp',
};

// Map for type to color use QuestionType
const typeColorMap: Record<string, string> = {
  [QuestionType.SingleChoice]: 'blue',
  [QuestionType.MultipleChoice]: 'cyan',
  [QuestionType.Matching]: 'green',
  [QuestionType.Essay]: 'purple',
  [QuestionType.FillInBlank]: 'orange',
  [QuestionType.TrueFalse]: 'volcano',
  [QuestionType.Ordering]: 'geekblue',
};

// Map for source type display names use ExamSourceType
const sourceTypeMap: Record<number, string> = {
  [ExamSourceType.Default]: 'Mặc định',
  [ExamSourceType.Banked]: 'Ngân hàng câu hỏi',
};

// Define all possible columns for the table
export const allTableColumns: TableColumn[] = [
  { key: 'id', title: 'ID', visible: false, width: 100 },
  { key: 'questionType', title: 'Loại câu hỏi', visible: true, width: 150 },
  { key: 'title', title: 'Tiêu đề', visible: false, width: 150 },
  { key: 'preview', title: 'Nội dung', visible: true, width: 300 },
  { key: 'points', title: 'Độ khó', visible: false, width: 80 },
  { key: 'statusEntity', title: 'Trạng thái', visible: true, width: 120 },
  { key: 'assignedUser', title: 'Người xử lý', visible: true, width: 150 },
  { key: 'dueDate', title: 'Hạn xử lý', visible: false, width: 150 },
  { key: 'tags', title: 'Thẻ', visible: true, width: 150 },
  { key: 'topics', title: 'Chủ đề', visible: true, width: 150 },
  { key: 'sourceType', title: 'Nguồn gốc', visible: false, width: 120 },
  { key: 'creationTime', title: 'Ngày tạo', visible: true, width: 150 },
  {
    key: 'lastModificationTime',
    title: 'Ngày cập nhật',
    visible: false,
    width: 150,
  },
  { key: 'subject', title: 'Môn học', visible: false, width: 150 },
  { key: 'grade', title: 'Lớp', visible: false, width: 150 },
  { key: 'action', title: 'Thao tác', visible: true, width: 100 },
];

// Default key for localStorage
const SETTINGS_STORAGE_KEY = 'question-table-settings';

// Thêm interface cho filter tag
interface FilterTag {
  key: string;
  label: string;
  value: string | string[];
  onRemove: () => void;
}

// Add a new interface for the consolidated search state
interface SearchState {
  searchText: string;
  subjectIds: string[];
  lessonGradeIds: string[];
  status: ExamStatus[];
  questionTypes: QuestionType[];
  tagSearchText: string;
  topicSearchText: string;
  assignedUserIds: string[];
  sourceTypes: ExamSourceType[];
}

const QuestionStoreManager: React.FC<IQuestionStoreManagerProps> = (props) => {
  const {
    id,
    displayMode = 'table',
    questions,
    loading = false,
    totalCount,
    onEditQuestion,
    onDeleteQuestions,
    onPreviewQuestion,
    onExportQuestions,
    onImportQuestions,
    onTableChange,
    onSearch,
    onRefresh,
    onFilterChange,
    onStatusChangeQuestions,
    extraButtons,
    onAssignQuestions,
    onUnassignQuestions,
  } = props;

  const { subjects = [], lessonGrades = [], users = [] } = useQuestionContext();

  // State for table filter, sort, and search
  const [filteredInfo, setFilteredInfo] = useState<
    Record<string, FilterValue | null>
  >({});
  const [sortedInfo, setSortedInfo] = useState<SorterResult<BaseQuestion>>({});
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
    showSizeChanger: true,
    pageSizeOptions: ['10', '20', '50', '100'],
    showTotal: (total, range) => `${range[0]}-${range[1]} của ${total} câu hỏi`,
  });

  // State for table row selection
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // State for advanced settings
  const [settingsVisible, setSettingsVisible] = useState(false);
  const [tableColumns, setTableColumns] = useState<TableColumn[]>(() => {
    // Kiểm tra xem có cấu hình đã lưu trong localStorage không
    const savedColumns = localStorage.getItem(SETTINGS_STORAGE_KEY);
    if (savedColumns) {
      try {
        return JSON.parse(savedColumns);
      } catch (e) {
        console.error('Lỗi khi đọc cấu hình cột từ localStorage:', e);
        return allTableColumns;
      }
    }
    return allTableColumns;
  });

  const [searchState, setSearchState] = useState<SearchState>(() => {
    let initialSearchState: SearchState = {
      searchText: '',
      subjectIds: [],
      lessonGradeIds: [],
      status: [],
      questionTypes: [],
      tagSearchText: '',
      topicSearchText: '',
      assignedUserIds: [],
      sourceTypes: [], // Add this line
    };

    const savedParams = localStorage.getItem(SEARCH_PARAMS_STORAGE_KEY);
    if (savedParams) {
      try {
        let paramStorage = JSON.parse(savedParams);
        if (paramStorage) {
          initialSearchState = {
            ...initialSearchState,
            ...paramStorage,
          };
        }
      } catch (e) {
        console.error('Lỗi khi đọc cấu hình tìm kiếm từ localStorage:', e);
      }
    }
    return initialSearchState;
  });

  // Replace individual filter states with references to the consolidated state
  const selectedSubjectIds = searchState.subjectIds;
  const selectedLessonGradeIds = searchState.lessonGradeIds;
  const selectedStatus = searchState.status;
  const selectedQuestionTypes = searchState.questionTypes;
  const tagSearchText = searchState.tagSearchText;
  const topicSearchText = searchState.topicSearchText;
  const selectedAssignedUserIds = searchState.assignedUserIds;
  const selectedSourceTypes = searchState.sourceTypes; // Add this line

  // Consolidated handler for all filter changes
  const handleFilterChange = useCallback(
    (field: keyof SearchState, value: any) => {
      setSearchState((prev) => ({ ...prev, [field]: value }));

      // Call the generic filter change handler if provided
      if (onFilterChange) {
        onFilterChange(field, value);
        return;
      }

      // Otherwise, handle specific cases that need direct table updates
      switch (field) {
        case 'assignedUserIds':
          if (onTableChange) {
            onTableChange(
              pagination,
              { ...filteredInfo, assignedUserId: value },
              sortedInfo
            );
          }
          break;
      }
    },
    [onFilterChange, onTableChange, pagination, filteredInfo, sortedInfo]
  );

  // Update pagination total when questions or totalCount changes
  useEffect(() => {
    setPagination((prev) => ({
      ...prev,
      total: totalCount !== undefined ? totalCount : questions.length,
    }));
  }, [questions, totalCount]);

  // Reset filters and sorters
  const handleReset = useCallback(() => {
    setFilteredInfo({});
    setSortedInfo({});
    setSearchState({
      searchText: '',
      subjectIds: [],
      lessonGradeIds: [],
      status: [],
      questionTypes: [],
      tagSearchText: '',
      topicSearchText: '',
      assignedUserIds: [],
      sourceTypes: [], // Add this line
    });
    // Clear selections when filters are reset
    setSelectedRowKeys([]);

    // Notify parent components using the generic filter change handler if available
    if (onFilterChange) {
      onFilterChange('reset', null);
    } else {
      // If no filter change handler, at least handle search
      if (onSearch) onSearch('');
    }

    setPagination((prev) => ({
      ...prev,
      current: 1,
    }));
  }, [onFilterChange, onSearch]);

  // Refresh data
  const handleRefresh = useCallback(() => {
    if (onRefresh) {
      onRefresh();
    } else if (onTableChange) {
      onTableChange(pagination, filteredInfo, sortedInfo);
    }
  }, [onRefresh, onTableChange, pagination, filteredInfo, sortedInfo]);

  // Function to copy ID to clipboard
  const copyToClipboard = useCallback((text: string) => {
    navigator.clipboard.writeText(text).then(
      () => {
        message.success('ID đã được sao chép!');
      },
      (err) => {
        message.error('Không thể sao chép ID: ' + err);
      }
    );
  }, []);

  // Open and close advanced settings modal
  const showSettings = () => {
    setSettingsVisible(true);
  };

  const hideSettings = () => {
    setSettingsVisible(false);
  };

  // Handle change of visible columns
  const handleColumnsChange = (newColumns: TableColumn[]) => {
    setTableColumns(newColumns);
    localStorage.setItem(SETTINGS_STORAGE_KEY, JSON.stringify(newColumns));
  };

  // Hàm chuyển đổi từ loại filter sang trường trong SearchState
  const handleSpecificFilterChange = (filterType: string, value: any) => {
    switch (filterType) {
      case 'subject':
        handleFilterChange('subjectIds', value);
        break;
      case 'lessonGrade':
        handleFilterChange('lessonGradeIds', value);
        break;
      case 'questionType':
        handleFilterChange('questionTypes', value);
        break;
      case 'status':
        handleFilterChange('status', value);
        break;
      case 'assignedUser':
        handleFilterChange('assignedUserIds', value);
        break;
      case 'tag':
        handleFilterChange('tagSearchText', value);
        break;
      case 'topic':
        handleFilterChange('topicSearchText', value);
        break;
      case 'searchText':
        handleFilterChange('searchText', value);
        break;
      case 'sourceType':
        handleFilterChange('sourceTypes', value);
        break;
      default:
        console.warn(`Unknown filter type: ${filterType}`);
    }
  };

  // Handle page size change
  const handlePageSizeChange = (newSize: number) => {
    setPagination((prev) => ({
      ...prev,
      pageSize: newSize,
      current: 1, // Reset to first page when changing page size,
      showTotal: (total, range) =>
        `${range[0]}-${range[1]} của ${total} câu hỏi`,
    }));

    if (onTableChange) {
      onTableChange(
        { ...pagination, pageSize: newSize, current: 1 },
        filteredInfo,
        sortedInfo
      );
    }
  };

  // Generate question type filters
  const questionTypeFilters = [
    { text: 'Trắc nghiệm 1 đáp án', value: QuestionType.SingleChoice },
    { text: 'Trắc nghiệm nhiều đáp án', value: QuestionType.MultipleChoice },
    { text: 'Câu hỏi nối', value: QuestionType.Matching },
    { text: 'Câu hỏi Tự luận', value: QuestionType.Essay },
    { text: 'Câu hỏi điền từ', value: QuestionType.FillInBlank },
    { text: 'Câu hỏi đúng/Sai', value: QuestionType.TrueFalse },
    { text: 'Câu hỏi sắp xếp', value: QuestionType.Ordering },
  ];

  // Function to shorten ID for display
  const shortenId = (id: string): string => {
    if (id.length <= 8) return id;
    return id.substring(0, 6) + '...';
  };

  // Format date for display
  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const idFilterTimeout = useRef<any>(null);
  // Cập nhật các hàm xử lý tag và topic search để sử dụng handleSpecificFilterChange
  const handleTagSearch = (value: string) => {
    if (idFilterTimeout.current) {
      clearTimeout(idFilterTimeout.current);
    }
    idFilterTimeout.current = setTimeout(() => {
      handleSpecificFilterChange('tag', value);
    }, 500);
  };

  const handleTopicSearch = (value: string) => {
    if (idFilterTimeout.current) {
      clearTimeout(idFilterTimeout.current);
    }
    idFilterTimeout.current = setTimeout(() => {
      handleSpecificFilterChange('topic', value);
    }, 500);
  };

  const getColor = (index: number) => {
    const colors = [
      'purple',
      'geekblue',
      'blue',
      'cyan',
      'green',
      'lime',
      'gold',
      'orange',
      'volcano',
      'red',
      'magenta',
    ];
    return colors[index % colors.length];
  };

  // Filter questions based on tag and topic search
  const filteredQuestions = useMemo(() => {
    if (!tagSearchText && !topicSearchText) return questions;

    return questions.filter((question) => {
      // Tag filtering
      const questionTags = Array.isArray(question.tags)
        ? question.tags
        : question.tags
        ? question.tags.split(',').map((tag) => tag.trim())
        : [];

      const tagMatch =
        !tagSearchText ||
        questionTags.some((tag) =>
          tag.toLowerCase().includes(tagSearchText.toLowerCase())
        );

      // Topic filtering
      const questionTopics: string[] = Array.isArray(question.metadata?.topics)
        ? question.metadata.topics
        : question.topics
        ? (question.topics as string).split(',').map((topic) => topic.trim())
        : [];

      const topicMatch =
        !topicSearchText ||
        questionTopics.some((topic) =>
          topic.toLowerCase().includes(topicSearchText.toLowerCase())
        );

      return tagMatch && topicMatch;
    });
  }, [questions, tagSearchText, topicSearchText]);

  // Table columns definition with correct field names
  const getTableColumns = useMemo((): ColumnsType<BaseQuestion> => {
    const visibleColumns = tableColumns.filter((col) => col.visible);

    return visibleColumns.map((column) => {
      switch (column.key) {
        case 'id':
          return {
            title: column.title,
            dataIndex: 'id',
            key: 'id',
            width: column.width,
            ellipsis: true,
            render: (id: string) => (
              <Tooltip title={`${id} (Nhấp để sao chép)`} placement="topLeft">
                <Button
                  type="text"
                  size="small"
                  onClick={() => copyToClipboard(id)}
                  style={{ padding: '0', fontFamily: 'monospace' }}
                  icon={
                    <CopyOutlined
                      style={{ fontSize: '12px', marginRight: '4px' }}
                    />
                  }
                >
                  {shortenId(id)}
                </Button>
              </Tooltip>
            ),
          };
        case 'questionType':
          return {
            title: column.title,
            dataIndex: 'questionType',
            key: 'questionType',
            width: column.width,
            sorter: true,
            sortOrder:
              sortedInfo.columnKey === 'questionType' ? sortedInfo.order : null,
            // filters: questionTypeFilters,
            // filteredValue: filteredInfo.questionType || null,
            render: (type) => {
              return (
                <Tag color={typeColorMap[type] || 'default'}>
                  {questionTypeMap[type as QuestionType] || type}
                </Tag>
              );
            },
          };
        case 'title':
          return {
            title: column.title,
            dataIndex: 'title',
            key: 'title',
            width: column.width,
            sorter: true,
            sortOrder:
              sortedInfo.columnKey === 'title' ? sortedInfo.order : null,
            ellipsis: true,
            render: (_, record) => {
              const preview = record.title;
              return (
                <Tooltip title={preview}>
                  {preview.length > 200
                    ? `${preview.substring(0, 200)}...`
                    : preview}
                </Tooltip>
              );
            },
          };
        case 'preview':
          return {
            title: column.title,
            key: 'preview',
            width: column.width,
            render: (_, record) => {
              const preview = record.content || '';
              return (
                <Tooltip
                  title={
                    <span
                      dangerouslySetInnerHTML={{
                        __html: preview,
                      }}
                    ></span>
                  }
                >
                  <span
                    dangerouslySetInnerHTML={{
                      __html:
                        preview.length > 200
                          ? `${preview.substring(0, 200)}...`
                          : preview,
                    }}
                  ></span>
                </Tooltip>
              );
            },
            ellipsis: true,
          };
        case 'points':
          return {
            title: column.title,
            dataIndex: 'points',
            key: 'points',
            width: column.width,
            sorter: true,
            sortOrder:
              sortedInfo.columnKey === 'points' ? sortedInfo.order : null,
          };
        case 'statusEntity':
          return {
            title: column.title,
            dataIndex: 'statusEntity',
            key: 'statusEntity',
            width: column.width,
            sorter: true,
            sortOrder:
              sortedInfo.columnKey === 'statusEntity' ? sortedInfo.order : null,
            render: (status) => {
              let color = 'default';
              let text = 'Soạn thảo';

              switch (status) {
                case ExamStatus.Published:
                  color = 'success';
                  text = 'Công bố';
                  break;
                case ExamStatus.Approved:
                  color = 'processing';
                  text = 'Đã duyệt';
                  break;
                case ExamStatus.Submitted:
                  color = 'warning';
                  text = 'Đang chờ duyệt';
                  break;
                case ExamStatus.Draft:
                  color = 'default';
                  text = 'Soạn thảo';
                  break;
                case ExamStatus.Rejected:
                  color = 'error';
                  text = 'Đã từ chối';
                  break;
                default:
                  color = 'default';
                  text = 'Soạn thảo';
              }

              return <Tag color={color}>{text}</Tag>;
            },
          };
        case 'creationTime':
          return {
            title: column.title,
            dataIndex: 'creationTime',
            key: 'creationTime',
            width: column.width,
            sorter: true,
            sortOrder:
              sortedInfo.columnKey === 'creationTime' ? sortedInfo.order : null,
            render: (date) => {
              return formatDate(date);
            },
          };
        case 'lastModificationTime':
          return {
            title: column.title,
            dataIndex: 'lastModificationTime',
            key: 'lastModificationTime',
            width: column.width,
            sorter: true,
            sortOrder:
              sortedInfo.columnKey === 'lastModificationTime'
                ? sortedInfo.order
                : null,
            render: (date) => formatDate(date),
          };
        case 'subject':
          return {
            title: column.title,
            key: 'subject',
            width: column.width,
            render: (_, record) => {
              if (!record.subject) return '-';
              return (
                <Tooltip title={record.subject.code}>
                  <Tag color="blue">{record.subject.name}</Tag>
                </Tooltip>
              );
            },
          };
        case 'grade':
          return {
            title: column.title,
            key: 'grade',
            width: column.width,
            render: (_, record) => {
              if (!record.grade) return '-';
              return (
                <Tooltip title={record.grade.code}>
                  <Tag color="blue">{record.grade.name}</Tag>
                </Tooltip>
              );
            },
          };
        case 'action':
          return {
            title: column.title,
            key: 'action',
            width: column.width,
            fixed: 'right',
            render: (_, record) => (
              <Space size="small">
                <Tooltip title="Chỉnh sửa">
                  <Button
                    type="default"
                    color="primary"
                    icon={<EditOutlined />}
                    onClick={() => onEditQuestion?.(record)}
                  />
                </Tooltip>
                {/* <Tooltip title="Xem trước">
                  <Button
                    type="default"
                    color="primary"
                    icon={<EyeOutlined />}
                    onClick={() => onPreviewQuestion?.(record.clientId)}
                  />
                </Tooltip> */}
                <Tooltip title="Xóa">
                  <Button
                    type="default"
                    icon={<DeleteIcon />}
                    danger
                    onClick={() => onDeleteQuestions?.([record.id || ''])}
                  />
                </Tooltip>
              </Space>
            ),
          };
        case 'tags':
          return {
            title: column.title,
            key: 'tags',
            width: column.width,
            render: (_, record) => (
              <span>
                {Array.isArray(record.tags)
                  ? record.tags.map((t, index) => (
                      <Tag
                        className="tailwind-m-1"
                        color={getColor(index)}
                        key={t}
                      >
                        {t}
                      </Tag>
                    ))
                  : record.tags?.split(',').map((t, index) => (
                      <Tag
                        className="tailwind-m-1"
                        color={getColor(index)}
                        key={t.trim()}
                      >
                        {t.trim()}
                      </Tag>
                    ))}
              </span>
            ),
          };
        case 'topics':
          return {
            title: column.title,
            key: 'topics',
            width: column.width,
            render: (_, record) => (
              <span>
                {Array.isArray(record.topics)
                  ? record.topics.map((t, index) => (
                      <Tag
                        className="tailwind-m-1"
                        color={getColor(index)}
                        key={t}
                      >
                        {t}
                      </Tag>
                    ))
                  : record.topics
                  ? (record.topics as string).split(',').map((t, index) => (
                      <Tag
                        className="tailwind-m-1"
                        color={getColor(index)}
                        key={t.trim()}
                      >
                        {t.trim()}
                      </Tag>
                    ))
                  : []}
              </span>
            ),
          };
        case 'assignedUser':
          return {
            title: column.title,
            key: 'assignedUser',
            width: column.width,
            render: (_, record) => {
              if (!record.assignedUserId)
                return <Tag color="default">Chưa gán</Tag>;

              // Tìm thông tin người dùng từ danh sách users
              const assignedUser = users.find(
                (u) => u.id === record.assignedUserId
              );

              return (
                <Tooltip title={assignedUser?.email || record.assignedUserId}>
                  <Tag color="blue">
                    {assignedUser
                      ? assignedUser.name || assignedUser.userName
                      : 'Người dùng không xác định'}
                  </Tag>
                </Tooltip>
              );
            },
          };
        case 'dueDate':
          return {
            title: column.title,
            dataIndex: 'dueDate',
            key: 'dueDate',
            width: column.width,
            sorter: true,
            sortOrder:
              sortedInfo.columnKey === 'dueDate' ? sortedInfo.order : null,
            render: (date) => {
              if (!date) return '-';

              const dueDate = new Date(date);
              const now = new Date();
              const isOverdue = dueDate < now;

              return (
                <Tooltip title={isOverdue ? 'Đã quá hạn' : 'Còn hạn'}>
                  <Tag color={isOverdue ? 'red' : 'green'}>
                    {formatDate(date)}
                  </Tag>
                </Tooltip>
              );
            },
          };
        case 'sourceType':
          return {
            title: column.title,
            dataIndex: 'sourceType',
            key: 'sourceType',
            width: column.width,
            sorter: true,
            sortOrder:
              sortedInfo.columnKey === 'sourceType' ? sortedInfo.order : null,
            render: (sourceType) => {
              const sourceTypeValue = sourceType || ExamSourceType.Default;
              return (
                <Tag
                  color={
                    sourceTypeValue === ExamSourceType.Banked ? 'green' : 'blue'
                  }
                >
                  {sourceTypeMap[sourceTypeValue] || 'Mặc định'}
                </Tag>
              );
            },
          };
        default:
          return {
            title: column.title,
            dataIndex: column.key,
            key: column.key,
            width: column.width,
          };
      }
    });
  }, [
    tableColumns,
    filteredInfo,
    sortedInfo,
    copyToClipboard,
    onDeleteQuestions,
    onPreviewQuestion,
    questionTypeFilters,
    onEditQuestion,
  ]);

  // Trong phần getAdvanceFilteringItems, cập nhật các hàm xử lý sự kiện
  const getAdvanceFilteringItems: () => CollapseProps['items'] = () => [
    {
      key: '1',
      label: (
        <div className="tailwind-flex">
          <span className="tailwind-mr-2">Bộ lọc nâng cao</span>
          <FilterTags tags={getActiveFilterTags()} />
        </div>
      ),
      children: (
        <Row gutter={[16, 16]}>
          <Col span={6}>
            <div className="tailwind-flex tailwind-w-full">
              <CheckCircleOutlined
                style={{ color: '#1890ff' }}
                className="tailwind-mr-2"
              />
              <Select
                mode="multiple"
                style={{ width: '100%' }}
                placeholder="Lọc theo trạng thái"
                value={selectedStatus}
                onChange={(value) =>
                  handleSpecificFilterChange('status', value)
                }
                optionFilterProp="children"
                allowClear
              >
                <Option value={ExamStatus.Published}>Công bố</Option>
                <Option value={ExamStatus.Approved}>Đã duyệt</Option>
                <Option value={ExamStatus.Submitted}>Đang chờ duyệt</Option>
                <Option value={ExamStatus.Draft}>Soạn thảo</Option>
                <Option value={ExamStatus.Rejected}>Đã từ chối</Option>
              </Select>
            </div>
          </Col>
          <Col span={6}>
            <div className="tailwind-flex tailwind-w-full">
              <QuestionCircleOutlined
                style={{ color: '#1890ff' }}
                className="tailwind-mr-2"
              />
              <Select
                mode="multiple"
                style={{ width: '100%' }}
                placeholder="Lọc theo loại câu hỏi"
                value={selectedQuestionTypes}
                onChange={(value) =>
                  handleSpecificFilterChange('questionType', value)
                }
                optionFilterProp="children"
                allowClear
              >
                {questionTypeFilters.map((filter) => (
                  <Option key={filter.value} value={filter.value}>
                    {filter.text}
                  </Option>
                ))}
              </Select>
            </div>
          </Col>
          <Col span={6}>
            <div className="tailwind-flex tailwind-w-full">
              <BookOutlined
                style={{ color: '#1890ff' }}
                className="tailwind-mr-2"
              />
              <Select
                mode="multiple"
                style={{ width: '100%' }}
                placeholder="Lọc theo môn học"
                value={selectedSubjectIds}
                onChange={(value) =>
                  handleSpecificFilterChange('subject', value)
                }
                optionFilterProp="children"
                allowClear
              >
                {subjects?.map((subject) => (
                  <Option key={subject.id} value={subject.id}>
                    {subject.name} ({subject.code})
                  </Option>
                ))}
              </Select>
            </div>
          </Col>
          <Col span={6}>
            <div className="tailwind-flex tailwind-w-full">
              <ReadOutlined
                style={{ color: '#1890ff' }}
                className="tailwind-mr-2"
              />
              <Select
                mode="multiple"
                style={{ width: '100%' }}
                placeholder="Lọc theo lớp"
                value={selectedLessonGradeIds}
                onChange={(value) =>
                  handleSpecificFilterChange('lessonGrade', value)
                }
                optionFilterProp="children"
                allowClear
              >
                {lessonGrades?.map((lessonGrade) => (
                  <Option key={lessonGrade.id} value={lessonGrade.id}>
                    {lessonGrade.name} ({lessonGrade.code})
                  </Option>
                ))}
              </Select>
            </div>
          </Col>

          <Col span={6}>
            <div className="tailwind-flex tailwind-w-full">
              <TagsOutlined
                style={{ color: '#1890ff' }}
                className="tailwind-mr-2"
              />
              <Input
                placeholder="Lọc theo thẻ"
                allowClear
                onChange={(e) => handleTagSearch(e.target.value)}
                style={{ width: '100%' }}
              />
            </div>
          </Col>
          <Col span={6}>
            <div className="tailwind-flex tailwind-w-full">
              <ProfileOutlined
                style={{ color: '#1890ff' }}
                className="tailwind-mr-2"
              />
              <Input
                placeholder="Lọc theo chủ đề"
                allowClear
                onChange={(e) => handleTopicSearch(e.target.value)}
                style={{ width: '100%' }}
              />
            </div>
          </Col>
          <Col span={6}>
            <div className="tailwind-flex tailwind-w-full">
              <UserOutlined
                style={{ color: '#1890ff' }}
                className="tailwind-mr-2"
              />
              <Select
                mode="multiple"
                style={{ width: '100%' }}
                placeholder="Lọc theo người xử lý"
                value={selectedAssignedUserIds}
                onChange={(value) =>
                  handleSpecificFilterChange('assignedUser', value)
                }
                optionFilterProp="children"
                allowClear
                showSearch
              >
                {users.map((user) => (
                  <Option key={user.id} value={user.id}>
                    {user.name || user.userName} ({user.email})
                  </Option>
                ))}
              </Select>
            </div>
          </Col>
          {/* Add sourceType filter */}
          <Col span={6}>
            <div className="tailwind-flex tailwind-w-full">
              <ReadOutlined
                style={{ color: '#1890ff' }}
                className="tailwind-mr-2"
              />
              <Select
                mode="multiple"
                style={{ width: '100%' }}
                placeholder="Lọc theo nguồn gốc"
                value={selectedSourceTypes}
                onChange={(value) =>
                  handleSpecificFilterChange('sourceType', value)
                }
                optionFilterProp="children"
                allowClear
              >
                <Option value={ExamSourceType.Default}>Mặc định</Option>
                <Option value={ExamSourceType.Banked}>Ngân hàng câu hỏi</Option>
              </Select>
            </div>
          </Col>
        </Row>
      ),
    },
  ];

  // Handle table change (for sorting, filtering, and pagination)
  const handleTableChange = (
    paginationConfig: TablePaginationConfig,
    filters: Record<string, FilterValue | null>,
    sorter: SorterResult<BaseQuestion> | SorterResult<BaseQuestion>[]
  ) => {
    if (!paginationConfig.showTotal) {
      paginationConfig.showTotal = (total, range) =>
        `${range[0]}-${range[1]} của ${total} câu hỏi`;
    }
    // Store the filter and sort info for local state management
    setFilteredInfo(filters);

    // Ensure sorter is properly handled for both single and multiple column sorting
    if (Array.isArray(sorter)) {
      setSortedInfo(sorter.length > 0 ? sorter[0] : {});
    } else {
      setSortedInfo(sorter);
    }

    setPagination(paginationConfig);

    // Pass the updated parameters to the parent component
    if (onTableChange) {
      // fix statusEntity to status
      if (filters['statusEntity']) {
        filters['status'] = filters['statusEntity'];
        delete filters['statusEntity'];
      }

      onTableChange(paginationConfig, filters, sorter);
    }
  };

  // Determine if advanced filters are active
  const hasAdvancedFilters =
    selectedSubjectIds.length > 0 ||
    selectedLessonGradeIds.length > 0 ||
    selectedStatus !== undefined ||
    selectedQuestionTypes.length > 0 ||
    tableColumns.some((col) => col.key !== 'action' && !col.visible);

  // Handle row selection events
  const onSelectChange = (
    newSelectedRowKeys: React.Key[],
    selectedRows: BaseQuestion[]
  ) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  // Configure row selection options
  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
    fixed: true, // This will freeze the selection column
    selections: [
      Table.SELECTION_ALL,
      Table.SELECTION_INVERT,
      Table.SELECTION_NONE,
    ],
  };

  // Handle bulk delete action
  const handleBulkDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('Vui lòng chọn ít nhất một câu hỏi để xóa');
      return;
    }

    // Convert the selected keys to string array for the delete function
    const questionIds = selectedRowKeys.map((key) => String(key));

    if (onDeleteQuestions) {
      onDeleteQuestions(questionIds);
      // Clear selection after delete
      setSelectedRowKeys([]);
    } else {
      message.info('Chức năng xóa nhiều câu hỏi chưa được cài đặt');
    }
  };

  // Add this function to handle next page navigation
  const handleNextPage = useCallback(() => {
    if (pagination.current && pagination.total && pagination.pageSize) {
      const totalPages = Math.ceil(pagination.total / pagination.pageSize);
      if (pagination.current < totalPages) {
        const newPagination = {
          ...pagination,
          current: pagination.current + 1,
        };
        setPagination(newPagination);
        if (onTableChange) {
          onTableChange(newPagination, filteredInfo, sortedInfo);
        }
      }
    }
  }, [pagination, filteredInfo, sortedInfo, onTableChange]);

  // Add this handler function
  const handleStatusChange = (status: ExamStatus) => {
    if (selectedRowKeys.length === 0) {
      message.warning(
        'Vui lòng chọn ít nhất một câu hỏi để thay đổi trạng thái'
      );
      return;
    }

    // Convert the selected keys to string array
    const questionIds = selectedRowKeys.map((key) => String(key));

    if (onStatusChangeQuestions) {
      onStatusChangeQuestions(questionIds, status);
    } else {
      message.info('Chức năng thay đổi trạng thái chưa được cài đặt');
    }
  };

  // Thêm state cho modal gán người dùng
  const [assignModalVisible, setAssignModalVisible] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState<string | undefined>();
  const [assignDueDate, setAssignDueDate] = useState<Date | null>(null);

  // Thêm hàm xử lý gán người dùng
  const handleAssignQuestions = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('Vui lòng chọn ít nhất một câu hỏi để gán');
      return;
    }

    setAssignModalVisible(true);
  };

  const handleAssignSubmit = async () => {
    if (!selectedUserId) {
      message.warning('Vui lòng chọn người dùng để gán');
      return;
    }
    if (!onAssignQuestions) {
      // Chức năng chưa được cài đặt
      message.info('Chức năng hủy gán câu hỏi chưa được cài đặt');
      return;
    }

    const assignData: BatchAssignQuestionsDto = {
      userId: selectedUserId,
      questionIds: selectedRowKeys.map((key) => key.toString()),
      dueDate: assignDueDate ? assignDueDate.toISOString() : undefined,
      assignAll: false,
    };

    let response = await onAssignQuestions(assignData);

    if (response && response.payload && response.payload.assignedCount > 0) {
      message.success(
        `Đã gán ${response.payload.assignedCount} câu hỏi cho người dùng thành công`
      );
    } else {
      message.error('Gán câu hỏi thất bại');
    }

    setAssignModalVisible(false);
    setSelectedUserId(undefined);
  };

  const handleUnassignQuestions = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('Vui lòng chọn ít nhất một câu hỏi để hủy gán');
      return;
    }

    const questionIds = selectedRowKeys.map((key) => key.toString());
    if (!onUnassignQuestions) {
      // Chức năng chưa được cài đặt
      message.info('Chức năng hủy gán câu hỏi chưa được cài đặt');
      return;
    }
    let response = await onUnassignQuestions(questionIds);

    if (
      response &&
      response.payload &&
      response.payload.unassignedQuestionIds.length > 0
    ) {
      message.success(
        `Đã hủy gán ${response.payload.unassignedQuestionIds.length} câu hỏi thành công`
      );
    } else {
      message.error('Hủy gán câu hỏi thất bại');
    }
  };

  // Trong component QuestionStoreManager, thêm hàm để tạo filter tags
  const getActiveFilterTags = (): FilterTag[] => {
    const tags: FilterTag[] = [];

    // Tag for search text
    if (searchState && searchState.searchText) {
      tags.push({
        key: 'search',
        label: 'Tìm kiếm',
        value: searchState.searchText,
        onRemove: () => {
          handleSpecificFilterChange('searchText', '');
        },
      });
    }

    // Tag for status
    if (searchState.status.length > 0) {
      const statusNames = searchState.status
        .map((status) => getExamStatusDisplayVN(status))
        .join(', ');

      tags.push({
        key: 'status',
        label: 'Trạng thái',
        value: statusNames,
        onRemove: () => {
          handleSpecificFilterChange('status', []);
        },
      });
    }

    // Tag for subjects
    if (searchState.subjectIds.length > 0) {
      const subjectNames = searchState.subjectIds
        .map((id) => subjects.find((s) => s.id === id)?.name || id)
        .join(', ');

      tags.push({
        key: 'subjects',
        label: 'Môn học',
        value: subjectNames,
        onRemove: () => {
          handleSpecificFilterChange('subject', []);
        },
      });
    }

    // Tag for lesson grades
    if (searchState.lessonGradeIds.length > 0) {
      const gradeNames = searchState.lessonGradeIds
        .map((id) => lessonGrades.find((g) => g.id === id)?.name || id)
        .join(', ');

      tags.push({
        key: 'grades',
        label: 'Lớp',
        value: gradeNames,
        onRemove: () => {
          handleSpecificFilterChange('lessonGrade', []);
        },
      });
    }

    // Tag for question types
    if (searchState.questionTypes.length > 0) {
      const typeNames = searchState.questionTypes
        .map((type) => questionTypeMap[type])
        .join(', ');

      tags.push({
        key: 'types',
        label: 'Loại câu hỏi',
        value: typeNames,
        onRemove: () => {
          handleSpecificFilterChange('questionType', []);
        },
      });
    }

    // Tag for tags
    if (searchState.tagSearchText) {
      tags.push({
        key: 'tag',
        label: 'Thẻ',
        value: searchState.tagSearchText,
        onRemove: () => {
          handleSpecificFilterChange('tag', '');
        },
      });
    }

    // Tag for topics
    if (searchState.topicSearchText) {
      tags.push({
        key: 'topic',
        label: 'Chủ đề',
        value: searchState.topicSearchText,
        onRemove: () => {
          handleSpecificFilterChange('topic', '');
        },
      });
    }

    // Tag for assigned users
    if (searchState.assignedUserIds.length > 0) {
      const userNames = searchState.assignedUserIds
        .map((id) => users.find((u) => u.id === id)?.name || id)
        .join(', ');

      tags.push({
        key: 'assignedUsers',
        label: 'Người xử lý',
        value: userNames,
        onRemove: () => {
          handleSpecificFilterChange('assignedUser', []);
        },
      });
    }

    // Tag for sourceType
    if (searchState.sourceTypes.length > 0) {
      const sourceTypeNames = searchState.sourceTypes
        .map((type) => sourceTypeMap[type])
        .join(', ');

      tags.push({
        key: 'sourceTypes',
        label: 'Nguồn gốc',
        value: sourceTypeNames,
        onRemove: () => {
          handleSpecificFilterChange('sourceType', []);
        },
      });
    }

    return tags;
  };

  // Thêm component FilterTags
  const FilterTags: React.FC<{ tags: FilterTag[] }> = ({ tags }) => {
    if (tags.length === 0) return null;

    return (
      <div>
        <Space wrap>
          {tags.map((tag) => (
            <Tag
              key={tag.key}
              closable
              onClose={tag.onRemove}
              closeIcon={<CloseOutlined />}
              style={{
                padding: '4px 8px',
                marginBottom: 8,
                backgroundColor: '#f0f5ff',
                borderColor: '#d6e4ff',
                color: '#2f54eb',
              }}
            >
              <span style={{ fontWeight: 500 }}>{tag.label}:</span>{' '}
              {Array.isArray(tag.value) ? tag.value.join(', ') : tag.value}
            </Tag>
          ))}
        </Space>
      </div>
    );
  };

  return (
    <div
      className="question-store-manager"
      style={{
        width: '100%',
      }}
    >
      <Card
        title={
          <Title level={4}>
            Ngân hàng câu hỏi{' '}
            <Badge
              className="site-badge-count-109"
              count={
                totalCount !== undefined ? totalCount : filteredQuestions.length
              }
              overflowCount={999999}
              style={{ backgroundColor: '#52c41a' }}
            />
          </Title>
        }
        variant="borderless"
        style={{ width: '100%' }}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <div
            className="tailwind-flex tailwind-w-full"
            style={{
              marginBottom: 16,
              justifyContent: 'space-between',
              width: '100%',
            }}
          >
            <Space
              // wrap
              className="search-space"
              size={'middle'}
              style={{ width: '100%' }}
            >
              <Search
                style={{ width: '100%', minWidth: '300px' }}
                className="tailwind-w-full"
                placeholder="Tìm kiếm câu hỏi..."
                // onChange={(e) => handleSearchTextChange(e.target.value)}
                onSearch={(value) =>
                  handleSpecificFilterChange('searchText', value)
                }
                // suffix={<SearchOutlined />}
                allowClear
              />
              <Button icon={<FilterOutlined />} onClick={handleReset}>
                Xóa bộ lọc
              </Button>
              <Button icon={<ReloadOutlined />} onClick={handleRefresh}>
                Tải lại
              </Button>
              <Button
                icon={<CaretRightOutlined />}
                onClick={handleNextPage}
                disabled={
                  !pagination.total ||
                  (pagination.current || 1) >=
                    Math.ceil(pagination.total / (pagination.pageSize || 10))
                }
              >
                Trang tiếp
              </Button>
              <Tooltip title="Cài đặt nâng cao">
                <Badge dot={hasAdvancedFilters}>
                  <Button icon={<SettingOutlined />} onClick={showSettings} />
                </Badge>
              </Tooltip>
              {extraButtons}
            </Space>
          </div>

          {/* Thêm FilterTags component ở đây */}
          {/* <FilterTags tags={getActiveFilterTags()} /> */}

          {/* Tag and Topic search filters */}
          <Collapse
            bordered={false}
            defaultActiveKey={['0']}
            expandIcon={({ isActive }) => (
              <CaretRightOutlined rotate={isActive ? 90 : 0} />
            )}
            items={getAdvanceFilteringItems()}
          />

          {/* Bulk actions toolbar - only visible when rows are selected */}
          {selectedRowKeys.length > 0 && (
            <div
              style={{
                padding: '8px 16px',
                backgroundColor: '#f5f5f5',
                borderRadius: '4px',
                marginBottom: '16px',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <span>Đã chọn {selectedRowKeys.length} câu hỏi</span>
              <Space>
                {/* Status change buttons */}
                <Tooltip title="Chuyển về bản nháp">
                  <Button
                    icon={<EditOutlined />}
                    onClick={() => handleStatusChange(ExamStatus.Draft)}
                  >
                    Soạn thảo
                  </Button>
                </Tooltip>
                <Tooltip title="Gửi duyệt">
                  <Button
                    type="primary"
                    style={{
                      backgroundColor: '#faad14',
                      borderColor: '#faad14',
                    }}
                    onClick={() => handleStatusChange(ExamStatus.Submitted)}
                  >
                    Gửi duyệt
                  </Button>
                </Tooltip>
                <Tooltip title="Duyệt">
                  <Button
                    type="primary"
                    icon={<CheckOutlined />}
                    onClick={() => handleStatusChange(ExamStatus.Approved)}
                  >
                    Duyệt
                  </Button>
                </Tooltip>
                <Tooltip title="Từ chối">
                  <Button
                    danger
                    icon={<CloseCircleOutlined />}
                    onClick={() => handleStatusChange(ExamStatus.Rejected)}
                  >
                    Từ chối
                  </Button>
                </Tooltip>
                <Tooltip title="Công bố">
                  <Button
                    type="primary"
                    icon={<CheckCircleOutlined />}
                    onClick={() => handleStatusChange(ExamStatus.Published)}
                    style={{
                      backgroundColor: '#52c41a',
                      borderColor: '#52c41a',
                    }}
                  >
                    Công bố
                  </Button>
                </Tooltip>
                <Button
                  type="primary"
                  danger
                  icon={<DeleteFilled />}
                  onClick={handleBulkDelete}
                >
                  Xóa đã chọn
                </Button>

                {selectedRowKeys.length > 0 && (
                  <>
                    <Button
                      className="assignment-action-btn assign"
                      icon={<UserOutlined />}
                      onClick={handleAssignQuestions}
                      disabled={!onAssignQuestions}
                    >
                      Gán người xử lý
                    </Button>
                    <Button
                      className="assignment-action-btn unassign"
                      icon={<UserDeleteOutlined />}
                      onClick={handleUnassignQuestions}
                      disabled={!onUnassignQuestions}
                    >
                      Hủy gán
                    </Button>
                  </>
                )}
                <Button
                  onClick={() => {
                    setSelectedRowKeys([]);
                  }}
                >
                  Bỏ chọn
                </Button>
              </Space>
            </div>
          )}

          <Table
            rowSelection={rowSelection}
            columns={getTableColumns}
            dataSource={filteredQuestions}
            rowKey="id"
            onChange={handleTableChange}
            pagination={pagination}
            loading={loading}
            scroll={{ x: 'max-content' }}
            bordered
          />
        </Space>

        {/* Modal for advanced settings instead of sidebar */}
        <QuestionTableAdvancedSettings
          columns={tableColumns}
          onColumnsChange={handleColumnsChange}
          onDrawerClose={hideSettings}
          visible={settingsVisible}
          pageSize={pagination.pageSize as number}
          onPageSizeChange={handlePageSizeChange}
        />
      </Card>
      {/* Modal gán người dùng */}
      <Modal
        title="Gán người xử lý câu hỏi"
        open={assignModalVisible}
        onOk={handleAssignSubmit}
        onCancel={() => setAssignModalVisible(false)}
        okText="Gán"
        cancelText="Hủy"
      >
        <div style={{ marginBottom: 16 }}>
          <div style={{ marginBottom: 8 }}>Chọn người xử lý:</div>
          <Select
            style={{ width: '100%' }}
            placeholder="Chọn người dùng"
            value={selectedUserId}
            onChange={setSelectedUserId}
            showSearch
            optionFilterProp="children"
          >
            {users.map((user) => (
              <Option key={user.id} value={user.id}>
                {user.name || user.userName} ({user.email})
              </Option>
            ))}
          </Select>
        </div>

        <div>
          <div style={{ marginBottom: 8 }}>Hạn xử lý (tùy chọn):</div>
          <DatePicker
            style={{ width: '100%' }}
            value={assignDueDate}
            onChange={setAssignDueDate}
            placeholder="Chọn hạn xử lý"
            format="DD/MM/YYYY"
          />
        </div>
      </Modal>
    </div>
  );
};

export default QuestionStoreManager;
