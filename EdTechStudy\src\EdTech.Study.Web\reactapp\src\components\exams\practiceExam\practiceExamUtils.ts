import { ExamBase } from '../../../interfaces/exams/examBase';
import { PracticeExamStatus } from '../../../interfaces/exams/practiceExam';
import { Subject } from '../../../interfaces/lessons/subject';
import { LessonGrade } from '../../../interfaces/lessons/lessonGrade';
import { IExamCardStatusConfig } from '../../../interfaces/exams/examCard';

/**
 * Interface for mapped exam data with user status
 */
export interface IMappedExamData extends ExamBase {
  questionCount: number;
  estimatedDuration: number;
  userProgress: number;
  userStatus: PracticeExamStatus;
  userBestScore: number;
  subject?: {
    id: string;
    name: string;
    code: string;
  };
  grade?: {
    id: string;
    name: string;
    code: string;
  };
}

export const getStatusConfig = (
  status: PracticeExamStatus,
  onStartExam: () => void,
  onContinueExam: () => void,
  onReviewExam: () => void
): IExamCardStatusConfig => {
  switch (status) {
    case PracticeExamStatus.NOT_STARTED:
      return {
        text: 'Chưa làm',
        color: '#d9d9d9',
        buttonText: 'Làm bài',
        buttonAction: onStartExam,
        buttonVariant: 'primary',
      };
    case PracticeExamStatus.IN_PROGRESS:
      return {
        text: 'Đang làm',
        color: '#faad14',
        buttonText: 'Làm bài',
        buttonAction: onContinueExam,
        buttonVariant: 'primary',
      };
    case PracticeExamStatus.COMPLETED:
      return {
        text: 'Hoàn thành',
        color: '#52c41a',
        buttonText: 'Xem lại',
        buttonAction: onReviewExam,
        buttonVariant: 'secondary',
      };
    default:
      return {
        text: 'Chưa làm',
        color: '#d9d9d9',
        buttonText: 'Làm bài',
        buttonAction: onStartExam,
        buttonVariant: 'primary',
      };
  }
};

/**
 * Map exam data with user status and additional computed fields
 * This function simulates the logic that should come from backend
 */
export const mapExamWithUserStatus = (
  exam: ExamBase,
  subject?: Subject,
  grade?: LessonGrade,
  userId?: string
): IMappedExamData => {
  // Calculate question count from sections
  const questionCount =
    exam.sections?.reduce((total, section) => {
      const sectionQuestions = section.questions?.length || 0;
      const groupQuestions =
        section.groupQuestions?.reduce((groupTotal, group) => {
          return groupTotal + (group.questions?.length || 0);
        }, 0) || 0;
      return total + sectionQuestions + groupQuestions;
    }, 0) || 0;

  // Simulate user status based on exam ID and user ID
  // In a real implementation, this would come from backend API
  const userStatus = getUserStatusForExam(exam.id || exam.clientId, userId);

  // Simulate user progress based on status
  const userProgress = getUserProgressForStatus(userStatus);

  // Simulate best score
  const userBestScore =
    userStatus === PracticeExamStatus.COMPLETED
      ? getSimulatedScore(exam.id || exam.clientId)
      : 0;

  return {
    ...exam,
    questionCount,
    estimatedDuration: exam.duration || 0,
    userProgress,
    userStatus,
    userBestScore,
    subject: subject
      ? {
          id: subject.id,
          name: subject.name,
          code: subject.code || '',
        }
      : undefined,
    grade: grade
      ? {
          id: grade.id,
          name: grade.name,
          code: grade.code || '',
        }
      : undefined,
  };
};

/**
 * Simulate user status for an exam
 * In a real implementation, this would query the backend
 */
const getUserStatusForExam = (
  examId: string,
  userId?: string
): PracticeExamStatus => {
  return PracticeExamStatus.NOT_STARTED;
};

/**
 * Get user progress based on status
 */
const getUserProgressForStatus = (status: PracticeExamStatus): number => {
  switch (status) {
    case PracticeExamStatus.NOT_STARTED:
      return 0;
    case PracticeExamStatus.IN_PROGRESS:
      // Random progress between 10-90%
      return Math.floor(Math.random() * 80) + 10;
    case PracticeExamStatus.COMPLETED:
      return 100;
    default:
      return 0;
  }
};

/**
 * Simulate a score for completed exams
 */
const getSimulatedScore = (examId: string): number => {
  if (!examId) return 0;

  // Use exam ID to generate consistent score
  const seed = examId
    .split('')
    .reduce((acc, char) => acc + char.charCodeAt(0), 0);

  // Generate score between 60-95
  return 60 + (seed % 36);
};

/**
 * Map array of exams with user status
 */
export const mapExamsWithUserStatus = (
  exams: ExamBase[],
  subjects: Subject[],
  grades: LessonGrade[],
  userId?: string
): IMappedExamData[] => {
  // Create lookup maps for better performance
  const subjectMap = new Map(subjects.map((s) => [s.id, s]));
  const gradeMap = new Map(grades.map((g) => [g.id, g]));

  return exams.map((exam) => {
    const subject = exam.subjectId ? subjectMap.get(exam.subjectId) : undefined;
    const grade = exam.gradeId ? gradeMap.get(exam.gradeId) : undefined;

    return mapExamWithUserStatus(exam, subject, grade, userId);
  });
};

/**
 * Filter exams by user status
 */
export const filterExamsByStatus = (
  mappedExams: IMappedExamData[],
  statuses: PracticeExamStatus[]
): IMappedExamData[] => {
  if (!statuses || statuses.length === 0) {
    return mappedExams;
  }

  return mappedExams.filter((exam) => statuses.includes(exam.userStatus));
};

/**
 * Get status statistics for all exams
 */
export const getExamStatusStats = (mappedExams: IMappedExamData[]) => {
  const stats = {
    total: mappedExams.length,
    notStarted: 0,
    inProgress: 0,
    completed: 0,
  };

  mappedExams.forEach((exam) => {
    switch (exam.userStatus) {
      case PracticeExamStatus.NOT_STARTED:
        stats.notStarted++;
        break;
      case PracticeExamStatus.IN_PROGRESS:
        stats.inProgress++;
        break;
      case PracticeExamStatus.COMPLETED:
        stats.completed++;
        break;
    }
  });

  return stats;
};
