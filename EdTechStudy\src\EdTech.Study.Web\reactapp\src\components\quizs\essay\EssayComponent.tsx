import React, { useContext, useEffect, useState, useMemo } from 'react';
import {
  Card,
  Button,
  message,
  Input,
  Form,
  PopconfirmProps,
  InputNumber,
  Switch,
  Row,
  Col,
  Collapse,
  Space,
} from 'antd';
import { DeleteFilled } from '@ant-design/icons';
import { PracticeEngineContext } from '../../../interfaces/quizs/questionBase';
import '../quiz/QuizAnimations.css';
import {
  EssayAnswer,
  EssayComponentProps,
  EssayQuestion,
} from '../../../interfaces/quizs/essay.interface';
import { Guid } from 'guid-typescript';
import PopconfirmAntdCustom from '../../customs/antd/PopconfirmAntdCustom';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import DOMPurify from 'dompurify';
import practiceLocalization, { quizLocalization } from '../localization';
import BaseQuestionPreviewComponent from '../base/BaseQuestionPreviewComponent';
import useUpdateQuestion from '../hooks/useUpdateQuestion';
import { essayCheckCorrectAnswer } from './essayUtils';

const { TextArea } = Input;

// Define modules for the rich text editor
const modules = {
  toolbar: [
    [{ header: [1, 2, 3, false] }],
    ['bold', 'italic', 'underline', 'strike', 'blockquote'],
    [
      { list: 'ordered' },
      { list: 'bullet' },
      { indent: '-1' },
      { indent: '+1' },
    ],
    ['link', 'image'],
    ['clean'],
  ],
};

const formats = [
  'header',
  'bold',
  'italic',
  'underline',
  'strike',
  'blockquote',
  'list',
  'bullet',
  'indent',
  'link',
  'image',
];

const EssayComponent: React.FC<EssayComponentProps> = ({
  question,
  onComplete,
  configMode = false,
  disabled = false,
  options = {
    hideDeleteButton: false,
    hideSaveButton: false,
    hideFeedback: false,
  },
  isMarked = false,
  onToggleMark,
  questionIndex,
  showResult = false,
}) => {
  const [userAnswer, setUserAnswer] = useState<EssayAnswer>({
    id: Guid.create().toString(),
    text: '',
    isCorrect: false,
  } as EssayAnswer);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const {
    handleEditQuestion: handleChangeQuestion,
    handleDeleteQuestion: externalHandleDeleteQuestion,
    handlePendingState,
  } = useContext(PracticeEngineContext);

  const { handleUpdateQuestion, isPending } = useUpdateQuestion();
  useEffect(() => {
    if (handlePendingState) handlePendingState(isPending);
  }, [isPending, handlePendingState]);

  // Config mode states
  const [editedQuestion, setEditedQuestion] = useState<EssayQuestion>({
    ...question,
  });
  const [form] = Form.useForm();

  useEffect(() => {
    setEditedQuestion({ ...question });
    // Initialize form values
    form.setFieldsValue({
      title: question.title,
      content: question.content,
      correctAnswer: question.correctAnswer,
      points: question.points,
      caseSensitive: question.caseSensitive,
      allowPartialMatch: question.allowPartialMatch,
      maxLength: question.maxLength,
      explanation: question.explanation,
    });

    // Nếu có câu trả lời của người dùng đã lưu
    if (question.userAnswer) {
      setUserAnswer(question.userAnswer);
      if (showResult) {
        // Chỉ set isSubmitted khi ở chế độ xem kết quả
        setIsSubmitted(true);
      } else {
        // Khi ở chế độ làm bài, luôn cho phép nhập
        setIsSubmitted(false);
      }
    } else {
      // Reset nếu không có câu trả lời của người dùng
      setUserAnswer({
        id: Guid.create().toString(),
        text: '',
        isCorrect: false,
      } as EssayAnswer);
      setIsSubmitted(false);
    }
  }, [question, question.userAnswer, showResult, disabled]);

  const isAnswered = useMemo(() => {
    return !!userAnswer.text && userAnswer.text.trim() !== '';
  }, [userAnswer.text]);

  const isCorrect = useMemo(() => {
    if (!showResult || !isAnswered) return false;
    const checkResult = essayCheckCorrectAnswer(question, userAnswer);
    return checkResult === true;
  }, [showResult, isAnswered, question, userAnswer]);

  // Handle input change
  const handleInputChange = (value: string) => {
    // Chỉ kiểm tra disabled, bỏ điều kiện isSubmitted
    if (!showResult && !disabled) {
      const updatedAnswer = {
        ...userAnswer,
        text: value,
      };

      setUserAnswer(updatedAnswer);

      // Store answer without auto-submitting
      if (onComplete) {
        onComplete(question.clientId, updatedAnswer);
      }
    }
  };

  // Handle form value changes
  const handleFormValueChange = (changedValues: any, allValues: any) => {
    console.log('🚀 ~ handleFormValueChange ~ allValues:', allValues);
    console.log('🚀 ~ handleFormValueChange ~ changedValues:', changedValues);
    // Only update the state with the fields that have actually changed
    const updatedFields = Object.keys(changedValues).reduce((acc, key) => {
      acc[key] = changedValues[key];
      return acc;
    }, {} as Partial<EssayQuestion>);

    setEditedQuestion((prevState) => ({
      ...prevState,
      ...updatedFields,
    }));

    handleUpdateQuestion({
      ...editedQuestion,
      ...updatedFields,
      clientId: question.clientId,
      parentId: question.parentId,
    });
  };

  // For ReactQuill component
  const handleEditorChange = (value: string) => {
    form.setFieldsValue({ content: value });
    setEditedQuestion((prevState) => ({
      ...prevState,
      content: value,
    }));
  };

  // Handle save configuration
  const handleSaveConfig = () => {
    // Validate the question before saving
    const hasTitle = editedQuestion.title.trim() !== '';
    const hasQuestionText = editedQuestion.content.trim() !== '';
    const hasCorrectAnswer = editedQuestion.correctAnswer.trim() !== '';

    if (!hasTitle) {
      message.error('Tiêu đề không được để trống!');
      return;
    }

    if (!hasQuestionText) {
      message.error('Nội dung câu hỏi không được để trống!');
      return;
    }

    if (!hasCorrectAnswer) {
      message.error('Đáp án đúng không được để trống!');
      return;
    }

    handleChangeQuestion({
      ...editedQuestion,
      parentId: question.parentId,
      clientId: question.clientId,
    });
    message.success('Lưu cấu hình thành công!');
  };

  const deleteConfirm: PopconfirmProps['onConfirm'] = () => {
    externalHandleDeleteQuestion(question.clientId);
  };

  // Create a variable for advanced configuration items
  const collapseItems = [
    {
      key: '1',
      label: practiceLocalization['Advanced Options'],
      children: (
        <>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="points" label="Điểm">
                <InputNumber min={1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="caseSensitive"
                label="Phân biệt chữ hoa/thường"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="allowPartialMatch"
                label="Chấp nhận đáp án gần đúng"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item name="maxLength" label="Giới hạn ký tự">
            <InputNumber
              min={0}
              placeholder="0 = không giới hạn"
              style={{ width: '100%' }}
            />
          </Form.Item>
          <Form.Item name="explanation" label="Giải thích đáp án">
            <TextArea rows={3} placeholder="Nhập giải thích cho đáp án đúng" />
          </Form.Item>
        </>
      ),
    },
  ];

  // Render config mode
  if (configMode) {
    return (
      <Card className="quiz-card">
        <Form
          form={form}
          layout="vertical"
          onValuesChange={handleFormValueChange}
          initialValues={{
            title: editedQuestion.title,
            content: editedQuestion.content,
            correctAnswer: editedQuestion.correctAnswer,
            points: editedQuestion.points,
            caseSensitive: editedQuestion.caseSensitive,
            allowPartialMatch: editedQuestion.allowPartialMatch,
            maxLength: editedQuestion.maxLength,
            explanation: editedQuestion.explanation,
          }}
        >
          <Form.Item name="title" label="Tiêu đề" required hidden>
            <Input placeholder="Nhập tiêu đề câu hỏi" />
          </Form.Item>

          <Form.Item name="content" label="Nội dung" required>
            <ReactQuill
              theme="snow"
              placeholder="Nhập nội dung câu hỏi"
              modules={modules}
              formats={formats}
              defaultValue={editedQuestion.content}
              onChange={handleEditorChange}
            />
          </Form.Item>

          <Form.Item name="correctAnswer" label="Đáp án đúng" required>
            <TextArea rows={2} placeholder="Nhập đáp án đúng" />
          </Form.Item>

          <Collapse defaultActiveKey={[]} ghost items={collapseItems} />
          <div>
            <Space>
              {!options.hideSaveButton && (
                <Button type="primary" onClick={handleSaveConfig}>
                  {quizLocalization.buttons.saveChanges}
                </Button>
              )}
              {!options.hideDeleteButton && (
                <PopconfirmAntdCustom
                  title={quizLocalization.buttons.deleteQuestion.confirmTitle}
                  onConfirm={deleteConfirm}
                  onCancel={() => {}}
                  okText={quizLocalization.buttons.deleteQuestion.yes}
                  cancelText={quizLocalization.buttons.deleteQuestion.no}
                >
                  <Button danger icon={<DeleteFilled />}>
                    {quizLocalization.buttons.deleteQuestion.button}
                  </Button>
                </PopconfirmAntdCustom>
              )}
            </Space>
          </div>
        </Form>
      </Card>
    );
  }

  // Render normal quiz mode

  // Render phần nội dung câu hỏi
  const renderContent = () => {
    return (
      <div
        dangerouslySetInnerHTML={{
          __html: DOMPurify.sanitize(question.content),
        }}
      />
    );
  };

  // Render phần tương tác
  const renderInteraction = () => {
    // Xác định trạng thái disabled - chỉ disabled khi showResult hoặc disabled prop là true
    const isDisabled = showResult || disabled;

    // Xác định class CSS cho TextArea
    let inputClassName = '';
    if (isSubmitted || showResult) {
      inputClassName = isCorrect ? 'correct' : 'incorrect';
    }

    return (
      <div className="essay-input tailwind-mb-2">
        <TextArea
          value={userAnswer.text}
          onChange={(e) => handleInputChange(e.target.value)}
          disabled={isDisabled}
          placeholder="Nhập câu trả lời của bạn"
          autoSize={{ minRows: 2, maxRows: 6 }}
          className={inputClassName}
        />
      </div>
    );
  };

  return (
    <BaseQuestionPreviewComponent
      question={editedQuestion}
      questionIndex={questionIndex}
      hideFeedback={options.hideFeedback}
      showResult={showResult}
      isCorrect={isCorrect}
      isAnswered={isAnswered}
      cardClassName="essay-card"
      guidanceText="Nhập câu trả lời ngắn gọn và chính xác."
      renderContent={renderContent}
      renderInteraction={renderInteraction}
      isMarked={isMarked}
      onToggleMark={onToggleMark}
    />
  );
};

export default EssayComponent;
