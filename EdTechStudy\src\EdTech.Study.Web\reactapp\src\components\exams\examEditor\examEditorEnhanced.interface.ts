import { MenuProps } from 'antd';
import { FormInstance } from 'antd';
import {
  ExamBase,
  ExamSection,
  ExamQuestion,
  ExamGroupQuestion,
} from '../../../interfaces/exams/examBase';
import { ExamStatus } from '../../../interfaces/exams/examEnums';

import { IBookMarkItem } from '../../../interfaces/commonInterfaces';
import { createRef } from 'react';

export interface ExamEditorProps extends ExamEditorDispatchToProps {
  examId?: string;
  onSaveSuccess?: () => void;
  onCancel?: () => void;
  disableCreateQuestion?: boolean;
  // Redux state props
  currentExam: ExamBase | null;
  loading: boolean;
  stateSavePending: boolean;
  error: string | null;
}

export interface ExamEditorDispatchToProps {
  // Redux dispatch props
  fetchExamById: (id: string) => void;
  setCurrentExam: (exam: ExamBase | null) => void;
  updateCurrentExamField: (data: { field: string; value: any }) => void;
  addSection: (section: ExamSection) => void;
  updateSection: (data: {
    sectionId: string;
    sectionData: Partial<ExamSection>;
  }) => void;
  removeSection: (sectionId: string) => void;
  addQuestion: (data: { sectionId: string; question: ExamQuestion }) => void;
  updateQuestion: (data: {
    sectionId: string;
    questionId: string;
    questionData: Partial<ExamQuestion> & {
      clientId: string;
      parentId: string;
    };
  }) => void;
  removeQuestion: (data: { sectionId: string; questionId: string }) => void;
  reorderSections: (data: {
    sourceIndex: number;
    destinationIndex: number;
  }) => void;
  reorderQuestions: (data: {
    sectionId: string;
    sourceIndex: number;
    destinationIndex: number;
  }) => void;
  createExam: (examData: Partial<ExamBase>) => Promise<any>;
  updateExamData: (data: {
    examId: string;
    examData: Partial<ExamBase>;
  }) => Promise<any>;
  updateExamStatus: (data: {
    examId: string;
    status: ExamStatus;
  }) => Promise<any>;
  setStateSavePending: (data: { stateSavePending: boolean }) => Promise<any>;
  addGroupQuestion: (payload: {
    sectionId: string;
    groupQuestion: ExamGroupQuestion;
  }) => Promise<any>;
  updateGroupQuestion: (payload: {
    sectionId: string;
    groupQuestionId: string;
    groupQuestionData: Partial<ExamGroupQuestion>;
  }) => Promise<any>;
  removeGroupQuestion: (payload: {
    sectionId: string;
    groupQuestionId: string;
  }) => Promise<any>;
}

export interface SectionBookmarkRefs {
  [key: string]: React.RefObject<HTMLDivElement>;
}

export type ExamEditorContext = {
  currentExam?: ExamBase | null;
  form?: FormInstance;
  examId?: string;
  editedSections: ExamSection[];
  currentPosition: {
    sectionIndex: number;
    questionOrGroupIndex: number;
    subIndex?: number;
  };
  isFullscreen: boolean;
  sectionQuestionContainerRef: React.RefObject<HTMLDivElement>;
  handleAddSection: () => any;
  handleSave: (cb?: (id: string) => void) => any;
  handleOpenModalSortSections: () => any;
  handleToggleFullscreen: (name: string) => any;
  handleShowDemo: () => any;
  handleOpenMenu: () => any;
  handleCreateQuestionFromEngine: (
    newQuestion: ExamQuestion,
    position?: number
  ) => any;
  handleDelete: () => any;
  handleTitleChange: (e: React.ChangeEvent<HTMLInputElement>) => any;
  // handleFocusIdChange: (id: string) => any;
  // handleBlurIdChange: () => any;
  handleMovePosition: (position: {
    sectionIndex: number;
    questionOrGroupIndex: number;
    subIndex?: number;
  }) => any;
  examEditorContentRef: React.RefObject<HTMLDivElement> | null;
  menuItems: IBookMarkItem[];
  setMenuItems: (items: IBookMarkItem[]) => any;
  setEditedSections: (sections: ExamSection[]) => any;
  sortSectionsModalVisible: boolean;
  setSortSectionsModalVisible: (visible: boolean) => any;

  handleUpdateSection: (
    clientId: string,
    updateSection: Partial<ExamSection>
  ) => any;
  statusMenuItems: MenuProps['items'];
  loading: boolean;
  isPending: boolean;
  setStateSavePending: (data: { stateSavePending: boolean }) => any;
  handleAddGroup: (index?: number) => any;
};

export const defaultExamEditorContext = {
  currentExam: undefined,
  subjects: [],
  grades: [],
  form: undefined,
  description: '',
  editedSections: [],
  currentPosition: {
    sectionIndex: 0,
    questionOrGroupIndex: 0,
  },
  isFullscreen: false,
  sectionQuestionContainerRef: createRef<HTMLDivElement>(),
  examEditorContentRef: null,
  menuItems: [],
  sortSectionsModalVisible: false,
  statusMenuItems: [],
  loading: false,
  isPending: false,
  handleChangeDescription: () => {},
  handleFocusIdChange: () => {},
  handleBlurIdChange: () => {},
  handleAddSection: () => {},
  handleSave: () => {},
  handleOpenModalSortSections: () => {},
  handleToggleFullscreen: () => {},
  handleShowDemo: () => {},
  handleOpenMenu: () => {},
  handleCreateQuestionFromEngine: () => {},
  handleDelete: () => {},
  handleSelectSection: () => {},
  handleTitleChange: () => {},
  handleMovePosition: (_: {
    sectionIndex: number;
    questionOrGroupIndex: number;
    subIndex?: number;
  }) => {},
  handleMoveSection: (_: number) => {},
  setMenuItems: (_: IBookMarkItem[]) => {},
  setEditedSections: (_: ExamSection[]) => {},
  setCurrentPosition: (_: {
    sectionIndex: number;
    questionOrGroupIndex: number;
  }) => {},
  setSortSectionsModalVisible: (_: boolean) => {},
  handleUpdateSection: () => {},
  setStateSavePending: () => {},
  handleAddGroup: () => {},
} as ExamEditorContext;

export const MAIN_PARTS = {
  BASIC_INFORMATION: 'Basic-Information',
  SECTIONS_QUESTIONS: 'Sections-Questions',
};

export const buildSectionChildItemsToUpdate = (
  questions: ExamQuestion[],
  groupQuestions: ExamGroupQuestion[]
) => {
  let itemsToUpdate = [...questions, ...groupQuestions].map((p, index) => ({
    ...p,
    order: index,
  }));
  itemsToUpdate.sort((a, b) => (a.order ?? 0) - (b.order ?? 0));
  itemsToUpdate = itemsToUpdate.map((p, index) => ({
    clientId: p.clientId,
    order: index,
  }));
  const mapOrder = new Map(itemsToUpdate.map((p) => [p.clientId, p.order]));
  const res = {
    questions: questions.map((p) => ({
      ...p,
      order: mapOrder.get(p.clientId),
    })),
    groupQuestions: groupQuestions.map((p) => ({
      ...p,
      order: mapOrder.get(p.clientId),
    })),
  };
  return res;
};
export const buildSectionToUpdate = (sections: ExamSection): ExamSection => {
  const { questions, groupQuestions } = buildSectionChildItemsToUpdate(
    sections.questions,
    sections.groupQuestions
  );
  const sectionToUpdate = {
    ...sections,
    questions,
    groupQuestions,
  };
  return sectionToUpdate;
};
