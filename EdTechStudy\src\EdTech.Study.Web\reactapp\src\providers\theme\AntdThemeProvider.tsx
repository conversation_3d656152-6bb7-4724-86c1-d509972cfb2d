import React from 'react';
import { ConfigProvider, theme } from 'antd';
import type { ThemeConfig } from 'antd';

interface AntdThemeProviderProps {
  children: React.ReactNode;
}

const AntdThemeProvider: React.FC<AntdThemeProviderProps> = ({ children }) => {
  // Common component configurations
  const componentConfig: ThemeConfig['components'] = {
    Card: {
      colorText: 'var(--edtt-color-text-default)',
      colorBgContainer: 'var(--edtt-color-bg-default)',
      boxShadow: 'var(--edtt-shadow)',
    },
    Layout: {
      headerBg: 'var(--edtt-color-bg-default)',
      siderBg: 'var(--edtt-color-bg-default)',
    },
    Button: {
      // Primary button customization
      colorPrimary: 'var(--edtt-color-primary)',
    },
    Switch: {
      colorPrimary: 'var(--edtt-color-text-default)',
      handleSize: 27,
      trackHeight: 32,
      innerMaxMargin: 32,
    },
    Select: {
      colorBgContainer: 'var(--edtt-color-bg-default)',
      colorBorder: 'var(--edtt-color-primary)',
      hoverBorderColor: 'var(--edtt-color-quaternary-base)',
      activeBorderColor: 'var(--edtt-color-secondary-base)',
      colorText: 'var(--edtt-color-text)',
      colorTextPlaceholder: 'var(--edtt-color-disabled-text)',
      colorTextDisabled: 'var(--edtt-color-disabled-text)',
      colorBgContainerDisabled: 'var(--edtt-color-disabled-bg)',
      colorPrimary: 'var(--edtt-color-primary)',
      colorBgElevated: 'var(--edtt-color-bg-default)',
      optionSelectedBg: 'var(--edtt-color-selected)',
      optionActiveBg: 'var(--edtt-color-selected)',
    },
    // Table: {
    //   fixedHeaderSortActiveBg: 'var(--edtt-color-bg-default)',
    //   headerFilterHoverBg: 'var(--edtt-color-bg-default)',
    //   headerSortActiveBg: 'var(--edtt-color-bg-default)',
    //   rowHoverBg: 'var(--edtt-color-bg-default)',
    //   rowSelectedBg: 'var(--edtt-color-bg-selected)',
    //   rowSelectedHoverBg: 'var(--edtt-color-bg-selected)',
    //   colorBgContainer: 'var(--edtt-color-bg-default)',
    //   headerBg: 'var(--edtt-color-bg-secondary)',
    //   colorText: 'var(--edtt-color-text)',
    //   colorTextHeading: 'var(--edtt-color-text)',
    //   colorBorderSecondary: 'var(--edtt-color-primary)',
    //   colorBorder: 'var(--edtt-color-primary)',
    //   colorFillAlter: 'var(--edtt-color-selected)',
    //   colorFillContent: 'var(--edtt-color-disabled-bg)',
    //   colorPrimary: 'var(--edtt-color-primary)',
    //   colorTextDisabled: 'var(--edtt-color-disabled-text)',
    // },
    Menu: {
      colorItemBg: 'var(--edtt-color-bg-default)',
      colorItemText: 'var(--edtt-color-text)',
      colorItemTextSelected: 'var(--edtt-color-primary)',
      colorItemTextHover: 'var(--edtt-color-primary)',
      colorItemBgSelected: 'var(--edtt-color-selected)',
      colorItemBgHover: 'var(--edtt-color-selected)',
      itemSelectedBg: 'var(--edtt-color-selected)',
      colorBgContainer: 'var(--edtt-color-bg-default)',
      colorItemTextDisabled: 'var(--edtt-color-disabled-text)',
      colorPrimary: 'var(--edtt-color-primary)',
      colorPrimaryHover: 'var(--edtt-color-quaternary-base)',
      colorPrimaryActive: 'var(--edtt-color-secondary-base)',
      colorBgTextHover: 'var(--edtt-color-selected)',
      colorBgTextActive: 'var(--edtt-color-selected)',
      colorErrorOutline: 'var(--edtt-color-status-error)',
      colorFillContent: 'var(--edtt-color-selected)',
      colorFillContentHover: 'var(--edtt-color-quaternary-base)',
      colorBorder: 'var(--edtt-color-primary)',
      colorBorderSecondary: 'var(--edtt-color-primary)',
      colorTextDescription: 'var(--edtt-color-disabled-text)',
      colorTextDisabled: 'var(--edtt-color-disabled-text)',
      boxShadow: 'var(--edtt-shadow)',
      motionDurationSlow: '0.3s',
      motionDurationMid: '0.2s',
    },
    Drawer: {
      colorBgElevated: 'var(--edtt-color-bg-default)',
      colorText: 'var(--edtt-color-text)',
      colorBorder: 'var(--edtt-color-primary)',
      colorIcon: 'var(--edtt-color-text)',
      colorIconHover: 'var(--edtt-color-primary)',
      colorBgMask: 'rgba(0, 0, 0, 0.45)',
      colorTextHeading: 'var(--edtt-color-text)',
      colorTextDisabled: 'var(--edtt-color-disabled-text)',
      colorBgContainer: 'var(--edtt-color-bg-default)',
    },
    Modal: {
      colorBgElevated: 'var(--edtt-color-bg-default)',
      colorText: 'var(--edtt-color-text)',
      colorBorder: 'var(--edtt-color-primary)',
      colorIcon: 'var(--edtt-color-text)',
      colorIconHover: 'var(--edtt-color-primary)',
      colorBgMask: 'rgba(0, 0, 0, 0.45)',
      colorTextHeading: 'var(--edtt-color-text)',
      colorTextDisabled: 'var(--edtt-color-disabled-text)',
      colorBgContainer: 'var(--edtt-color-bg-default)',
      colorPrimary: 'var(--edtt-color-primary)',
    },
    ColorPicker: {
      colorTextQuaternary: 'var(--edtt-color-text)',
      colorBorder: 'var(--edtt-color-primary)',
      colorText: 'var(--edtt-color-text)',
      colorBgElevated: 'var(--edtt-color-bg-default)',
      colorBgContainer: 'var(--edtt-color-bg-default)',
      colorPrimary: 'var(--edtt-color-primary)',
      colorTextDisabled: 'var(--edtt-color-disabled-text)',
    },
    Checkbox: {
      colorPrimary: 'var(--edtt-color-primary)',
      colorBgContainer: 'var(--edtt-color-bg-default)',
      colorBorder: 'var(--edtt-color-primary-400)',
      colorPrimaryHover: 'var(--edtt-color-primary)',
      colorPrimaryBorder: 'var(--edtt-color-primary)',
      colorTextDisabled: 'var(--edtt-color-primary)',
      colorBgContainerDisabled: 'var(--edtt-color-bg-default)',
    },
    Radio: {
      // Primary colors - Màu chính cho radio
      colorPrimary: 'var(--edtt-color-primary)',
      colorPrimaryHover: 'var(--edtt-color-primary)',
      colorPrimaryActive: 'var(--edtt-color-primary-600)',
      colorPrimaryBorder: 'var(--edtt-color-primary)',

      // Background colors - Màu nền
      colorBgContainer: 'var(--edtt-color-bg-default)',
      colorBgContainerDisabled: 'var(--edtt-color-disabled-bg)',
      colorBgElevated: 'var(--edtt-color-bg-default)',

      // Border colors - Màu viền
      colorBorder: 'var(--edtt-color-border-default)',
      colorBorderSecondary: 'var(--edtt-color-border-light)',

      // Text colors - Màu chữ
      colorText: 'var(--edtt-color-text-default)',
      colorTextDisabled: 'var(--edtt-color-text-disabled)',
      colorTextDescription: 'var(--edtt-color-text-secondary)',
      colorTextHeading: 'var(--edtt-color-text-default)',
      colorTextLabel: 'var(--edtt-color-text-default)',
      colorTextPlaceholder: 'var(--edtt-color-placeholder)',
      colorTextQuaternary: 'var(--edtt-color-text-tertiary)',

      // Fill colors - Màu fill
      colorFill: 'var(--edtt-color-bg-secondary)',
      colorFillAlter: 'var(--edtt-color-selected)',
      colorFillContent: 'var(--edtt-color-bg-secondary)',
      colorFillContentHover: 'var(--edtt-color-selected)',
      colorFillSecondary: 'var(--edtt-color-bg-tertiary)',
      colorFillTertiary: 'var(--edtt-color-bg-secondary)',
      colorFillQuaternary: 'var(--edtt-color-disabled-bg)',

      // Icon colors - Màu icon
      colorIcon: 'var(--edtt-color-text-default)',
      colorIconHover: 'var(--edtt-color-primary)',

      // Focus and interaction states - Trạng thái focus và tương tác
      controlOutline: 'var(--edtt-shadow-focus)',
      controlOutlineWidth: 2,

      // Radio button styles - Kiểu radio button
      buttonSolidCheckedBg: 'var(--edtt-color-primary)',
      buttonSolidCheckedHoverBg: 'var(--edtt-color-primary-600)',
      buttonSolidCheckedActiveBg: 'var(--edtt-color-primary-700)',
      buttonCheckedBg: 'var(--edtt-color-selected)',
      buttonCheckedBgDisabled: 'var(--edtt-color-disabled-bg)',
      buttonCheckedColorDisabled: 'var(--edtt-color-text-disabled)',
    },
    Input: {
      colorBgContainer: 'var(--edtt-color-bg-default)',
      colorBorder: 'var(--edtt-color-primary)',
      hoverBorderColor: 'var(--edtt-color-primary)',
      activeBorderColor: 'var(--edtt-color-primary)',
      colorText: 'var(--edtt-color-text)',
      colorTextDisabled: 'var(--edtt-color-text)',
      colorBgContainerDisabled: 'var(--edtt-color-bg-default)',
      colorPrimary: 'var(--edtt-color-primary)',
      colorBgElevated: 'var(--edtt-color-bg-default)',
      colorErrorOutline: 'var(--edtt-color-status-error)',
      colorError: 'var(--edtt-color-status-error)',
      colorWarning: 'var(--edtt-color-status-warning)',
      colorSuccess: 'var(--edtt-color-status-success)',
      boxShadow: 'var(--edtt-shadow)',
      addonBg: 'var(--edtt-color-bg-secondary)',
      activeShadow: 'var(--edtt-shadow-active)',
      colorTextDescription: 'var(--edtt-color-disabled-text)',
      colorIcon: 'var(--edtt-color-text)',
      colorIconHover: 'var(--edtt-color-primary)',
    },
    Pagination: {
      // Background colors - Màu nền
      colorBgContainer: 'var(--edtt-color-bg-default)',
      colorBgContainerDisabled: 'var(--edtt-color-bg-default)',
      colorBgElevated: 'var(--edtt-color-bg-default)',

      // Primary colors - Màu chính
      colorPrimary: 'var(--edtt-color-white)',
      colorPrimaryHover: 'var(--edtt-color-primary)',
      colorPrimaryActive: 'var(--edtt-color-primary)',
      colorPrimaryBorder: 'var(--edtt-color-primary)',

      // Border colors - Màu viền
      colorBorder: 'var(--edtt-color-primary)',
      colorBorderSecondary: 'var(--edtt-color-border-light)',

      // Text colors - Màu chữ
      colorText: 'var(--edtt-color-text-default)',
      colorTextDisabled: 'var(--edtt-color-text-disabled)',
      colorTextDescription: 'var(--edtt-color-text-secondary)',
      colorTextHeading: 'var(--edtt-color-text-default)',
      colorTextPlaceholder: 'var(--edtt-color-placeholder)',

      // Fill colors - Màu fill cho items
      colorFill: 'var(--edtt-color-bg-secondary)',
      colorFillAlter: 'var(--edtt-color-selected)',
      colorFillContent: 'var(--edtt-color-bg-secondary)',
      colorFillContentHover: 'var(--edtt-color-selected)',
      colorFillSecondary: 'var(--edtt-color-bg-tertiary)',

      // Icon colors - Màu icon navigation
      colorIcon: 'var(--edtt-color-white)',
      colorIconHover: 'var(--edtt-color-primary)',

      // Item states - Trạng thái của các item
      itemBg: 'var(--edtt-color-bg-white)',
      itemActiveBg: 'var(--edtt-color-primary)',
    },
    // FloatButton: {
    //   colorPrimary: 'var(--edtt-color-primary)',
    //   colorIcon: 'var(--edtt-color-primary)',
    //   colorIconHover: 'var(--edtt-color-primary)',
    // },
  };
  return (
    <ConfigProvider
      theme={{
        algorithm: theme.defaultAlgorithm,
        components: componentConfig,
        token: {
          colorText: 'var(--edtt-color-text-default)',
        },
      }}
    >
      {children}
    </ConfigProvider>
  );
};

export default AntdThemeProvider;
