import React, { useState, useEffect, useRef } from 'react';
import '../MiniGameResponsive.css';
import {
  MillionaireGameConfig,
  LifelineState,
  PrizeLevel,
} from './MillionaireGameConfig';
import {
  Question,
  QuestionAnswer,
  MultipleChoiceQuestion,
} from '../../common/QuestionComponent/QuestionBankConfig';
import {
  <PERSON>ton,
  Tag,
  Typography,
  Badge,
  Alert,
  Tooltip,
  Progress,
  Splitter,
} from 'antd';
import {
  TrophyOutlined,
  PhoneOutlined,
  TeamOutlined,
  ScissorOutlined,
} from '@ant-design/icons';
import MultipleChoiceQuestionComponent from '../../common/QuestionComponent/question/MultipleChoiceQuestionComponent';

// Add custom CSS class for no scrollbar
// Only add the style if it doesn't already exist
if (!document.getElementById('no-scrollbar-style')) {
  const hideScrollbarCSS = document.createElement('style');
  hideScrollbarCSS.id = 'no-scrollbar-style';
  hideScrollbarCSS.innerHTML = `
    .no-scrollbar::-webkit-scrollbar {
      display: none;
    }
    .no-scrollbar {
      -ms-overflow-style: none;
      scrollbar-width: none;
    }
  `;
  document.head.appendChild(hideScrollbarCSS);
}

const { Title, Paragraph } = Typography;

// Props for the game component
interface MillionaireGameComponentProps {
  config: MillionaireGameConfig;
  isFullscreen?: boolean;
}

// Define the GameStatus type explicitly
type GameStatus = 'ready' | 'playing' | 'complete';

// Add this as a new component before the main MillionaireGameComponent
interface PrizeLadderProps {
  prizes: PrizeLevel[];
  currentLevel: number;
  guaranteedLevels: number[];
  isFullscreen: boolean;
  awardType?: string;
}

const PrizeLadder: React.FC<PrizeLadderProps> = ({
  prizes,
  currentLevel,
  guaranteedLevels,
  isFullscreen,
  awardType = 'Điểm',
}) => {
  const getTextSizeClass = (defaultClass: string, fullscreenClass: string) =>
    isFullscreen ? fullscreenClass : defaultClass;

  const formatAmount = (amount: number, prizeAwardType?: string): string => {
    return (
      new Intl.NumberFormat('vi-VN').format(amount) +
      ' ' +
      (prizeAwardType || awardType)
    );
  };

  const prizeLadderRef = useRef<HTMLDivElement>(null);
  const currentPrizeRef = useRef<HTMLDivElement>(null);

  // Scroll to current prize level when it changes
  useEffect(() => {
    if (prizeLadderRef.current && currentPrizeRef.current) {
      // Get the container and the element
      const container = prizeLadderRef.current;
      const element = currentPrizeRef.current;

      // Calculate the scroll position
      const elementTop = element.offsetTop;
      const containerHeight = container.clientHeight;

      // Scroll only within the container, not the whole page
      container.scrollTo({
        top: elementTop - containerHeight / 2,
        behavior: 'smooth',
      });
    }
  }, [currentLevel]);

  return (
    <div
      className={`
      tailwind-w-full tailwind-rounded-lg tailwind-p-3
      tailwind-border-3 tailwind-border-blue-700 tailwind-shadow-lg
      tailwind-bg-gradient-to-b tailwind-from-blue-100 tailwind-to-indigo-100
      mini-game-prize-ladder
      ${
        isFullscreen
          ? 'tailwind-p-4 tailwind-max-h-[80vh] tailwind-border-4'
          : ''
      }
    `}
    >
      <div
        className={`tailwind-text-center tailwind-mb-3 tailwind-pb-2 tailwind-border-b-2 tailwind-border-indigo-500 ${getTextSizeClass(
          'tailwind-text-base',
          'tailwind-text-lg'
        )} tailwind-font-bold tailwind-text-indigo-800 tailwind-sticky tailwind-top-0 tailwind-bg-gradient-to-b tailwind-from-blue-100 tailwind-to-indigo-100 tailwind-z-10`}
      >
        Thang điểm
      </div>

      {/* Prize ladder items - scrollable area */}
      <div
        className="tailwind-space-y-2 tailwind-overflow-auto tailwind-max-h-[550px] no-scrollbar"
        ref={prizeLadderRef}
      >
        {prizes
          .slice()
          .reverse()
          .map((prize, index) => {
            const reversedIndex = prizes.length - 1 - index;
            const isCurrent = reversedIndex === currentLevel;
            const isPassed = reversedIndex < currentLevel;
            const isMilestone = guaranteedLevels.includes(reversedIndex + 1);

            return (
              <div
                key={reversedIndex}
                ref={isCurrent ? currentPrizeRef : null}
                className={`
                  tailwind-flex tailwind-items-center tailwind-py-2 tailwind-px-3 tailwind-rounded tailwind-text-xs
                  ${
                    isCurrent
                      ? 'tailwind-bg-blue-600 tailwind-text-white tailwind-shadow-md'
                      : isPassed
                      ? 'tailwind-bg-blue-100 tailwind-text-gray-500'
                      : 'tailwind-bg-indigo-50'
                  }
                  ${
                    isMilestone
                      ? 'tailwind-font-bold tailwind-border-2 tailwind-border-purple-400'
                      : ''
                  }
                  ${
                    isFullscreen
                      ? 'tailwind-py-2 tailwind-px-4 tailwind-text-sm'
                      : ''
                  }
                  hover:tailwind-bg-blue-100 tailwind-transition-colors tailwind-duration-200
                `}
              >
                <span
                  className={
                    isCurrent
                      ? 'tailwind-text-white'
                      : 'tailwind-text-indigo-700'
                  }
                >
                  {reversedIndex + 1}.
                </span>
                <span
                  className={`
                  tailwind-ml-2 tailwind-flex-1
                  ${
                    isMilestone
                      ? 'tailwind-text-purple-700'
                      : isCurrent
                      ? 'tailwind-text-white'
                      : ''
                  }
                  ${isFullscreen ? 'tailwind-text-sm' : 'tailwind-text-xs'}
                `}
                >
                  {formatAmount(prize.amount, prize.awardType)}
                </span>
                {isMilestone && (
                  <Tag
                    color="purple"
                    className="tailwind-ml-1 tailwind-text-xs"
                    style={{ padding: '0 6px', margin: 0 }}
                  >
                    Mốc
                  </Tag>
                )}
              </div>
            );
          })}
      </div>
    </div>
  );
};

export const MillionaireGameComponent: React.FC<
  MillionaireGameComponentProps
> = ({ config, isFullscreen = false }) => {
  // Game states
  const [gameStatus, setGameStatus] = useState<GameStatus>('ready');
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState<number>(0);
  const [currentLevel, setCurrentLevel] = useState<number>(0);
  const [finalPrize, setFinalPrize] = useState<number>(0);
  const [finalLevel, setFinalLevel] = useState<number>(0);
  const [questions, setQuestions] = useState<Question[]>(
    config?.questions || []
  );
  const [timeLeft, setTimeLeft] = useState<number | undefined>(
    config?.timeLimit
  );
  const [selectedAnswer, setSelectedAnswer] = useState<QuestionAnswer>(null);
  const [showAnswer, setShowAnswer] = useState<boolean>(false);
  // Lifelines states
  const [lifelines, setLifelines] = useState<LifelineState[]>(
    config.lifelines.map((type) => ({ type, used: false }))
  );
  const [eliminatedOptions, setEliminatedOptions] = useState<number[]>([]);
  const [audiencePoll, setAudiencePoll] = useState<number[]>([]);
  const [phoneAFriendAdvice, setPhoneAFriendAdvice] = useState<string | null>(
    null
  );

  // Timer ref
  const timerRef = useRef<ReturnType<typeof setInterval> | null>(null);

  // Removed unused refs
  // Update UI elements based on fullscreen state
  useEffect(() => {
    if (isFullscreen) {
      // Additional fullscreen adjustments can be added here if needed
    }
  }, [isFullscreen]);

  // Removed unused function

  // Helper function to get appropriate text size class based on fullscreen
  const getTextSizeClass = (baseSize: string, largeSize: string) =>
    isFullscreen ? largeSize : baseSize;

  // Initialize game questions when component mounts or config changes
  useEffect(() => {
    let gameQuestions = [...config.questions];

    // Filter to only include multiple choice questions
    gameQuestions = gameQuestions.filter((q) => q.type === 'multiplechoice');

    if (gameQuestions.length === 0) {
      console.error(
        'No multiple choice questions available for Millionaire Game'
      );
      gameQuestions = [];
    }

    // Sort questions by difficulty if progression is enabled
    if (config.difficultyProgression) {
      const difficultyOrder = { easy: 0, medium: 1, hard: 2, expert: 3 };
      gameQuestions.sort(
        (a, b) =>
          difficultyOrder[a.difficulty as keyof typeof difficultyOrder] -
          difficultyOrder[b.difficulty as keyof typeof difficultyOrder]
      );
    }

    // Shuffle questions if enabled
    if (config.shuffleQuestions) {
      gameQuestions = [...gameQuestions].sort(() => Math.random() - 0.5);
    }

    // Ensure the number of questions doesn't exceed the prize tree levels
    const maxPrizeLevels = config.prizeTree.length;
    if (gameQuestions.length > maxPrizeLevels) {
      console.warn(
        `Number of questions (${gameQuestions.length}) exceeds prize levels (${maxPrizeLevels}). Limiting questions to match prize levels.`
      );
      gameQuestions = gameQuestions.slice(0, maxPrizeLevels);
    }

    // Limit the number of questions if maxQuestions is set
    if (config.maxQuestions && config.maxQuestions < gameQuestions.length) {
      gameQuestions = gameQuestions.slice(0, config.maxQuestions);
    }

    setQuestions(gameQuestions);
  }, [config]);

  // Start timer when question is displayed
  useEffect(() => {
    if (gameStatus === 'playing' && !showAnswer) {
      setTimeLeft(config.timeLimit);

      timerRef.current = setInterval(() => {
        setTimeLeft((prev) => {
          const currentTime = prev ?? 0;
          if (currentTime > 0) {
            return currentTime - 1;
          } else {
            // Time's up - handle as wrong answer
            clearInterval(timerRef.current!);

            // Get last milestone prize when time runs out
            const lastMilestoneLevel = getLastMilestone(currentLevel);
            const milestoneAmount =
              lastMilestoneLevel > 0
                ? getCurrentPrizeAmount(lastMilestoneLevel)
                : 0;
            setFinalPrize(milestoneAmount);

            handleAnswer(null);
            return 0;
          }
        });
      }, 1000);
    } else if (gameStatus === 'playing' && showAnswer) {
      // Auto-proceed to next question after delay when showing answer
      const AUTO_PROCEED_DELAY = 3000; // 3 seconds delay before auto-proceeding

      const timer = setTimeout(() => {
        if (showAnswer) {
          const currentQuestion = questions[currentQuestionIndex];
          const isCorrect =
            selectedAnswer !== null &&
            selectedAnswer ===
              (currentQuestion as MultipleChoiceQuestion).correctOptionIndex;

          if (isCorrect) {
            // If answer was correct, proceed to next question
            if (currentQuestionIndex < questions.length - 1) {
              // Use requestAnimationFrame to ensure DOM updates before scrolling
              requestAnimationFrame(() => {
                setCurrentQuestionIndex(currentQuestionIndex + 1);
                setSelectedAnswer(null);
                setShowAnswer(false);
                setEliminatedOptions([]);
                setAudiencePoll([]);
                setPhoneAFriendAdvice(null);
                setTimeLeft(config.timeLimit);
              });
            } else {
              // Game completed successfully - all questions answered correctly
              completeGame(currentLevel);
            }
          } else {
            completeGame(currentLevel);
          }
        }
      }, AUTO_PROCEED_DELAY);

      return () => clearTimeout(timer);
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [gameStatus, currentQuestionIndex, showAnswer]);

  // This effect is redundant and can be removed as the PrizeLadder component handles its own scrolling

  // Handle starting a new game
  const handleStartGame = () => {
    setGameStatus('playing');
    setCurrentQuestionIndex(0);
    setCurrentLevel(0);
    setFinalPrize(0);
    setFinalLevel(0);
    setSelectedAnswer(null);
    setShowAnswer(false);
    // Removed unused state update
    setTimeLeft(config?.timeLimit);
    setLifelines(config.lifelines.map((type) => ({ type, used: false })));
    setEliminatedOptions([]);
    setAudiencePoll([]);
    setPhoneAFriendAdvice(null);
  };

  // Get the current prize amount based on level
  const getCurrentPrizeAmount = (level: number): number => {
    if (level <= 0 || level > config.prizeTree.length) return 0;
    return config.prizeTree[level - 1].amount;
  };

  // Removed unused function

  // Get the last milestone level the player has passed
  const getLastMilestone = (currentLevel: number): number => {
    for (let i = currentLevel - 1; i >= 0; i--) {
      if (config.prizeTree[i].isMilestone) {
        return i + 1; // Return the level (1-indexed)
      }
    }
    return 0;
  };

  // Format money amount with currency
  const formatMoney = (amount: number, level?: number): string => {
    let awardType = config.awardType || '';

    // If level is provided, try to get the specific award type for that level
    if (level !== undefined && level > 0 && level <= config.prizeTree.length) {
      awardType = config.prizeTree[level - 1].awardType || awardType;
    }

    return new Intl.NumberFormat('vi-VN').format(amount) + ' ' + awardType;
  };

  // Type guards for question types
  const isMultipleChoiceQuestion = (
    question: Question
  ): question is Question & { type: 'multiplechoice' } => {
    return question.type === 'multiplechoice';
  };

  // Removed unused effect

  // Handle answering a question
  const handleAnswer = (selectedAnswer: QuestionAnswer) => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    if (
      questions.length === 0 ||
      currentQuestionIndex >= questions.length ||
      showAnswer
    )
      return;

    const currentQuestion = questions[currentQuestionIndex];

    // Check if answer is correct (only for multiple choice)
    const correct =
      selectedAnswer !== null &&
      selectedAnswer ===
        (currentQuestion as MultipleChoiceQuestion).correctOptionIndex;

    setSelectedAnswer(selectedAnswer);
    setShowAnswer(true);
    // Update score and level if correct
    if (correct) {
      // Move to the next level
      setCurrentLevel(currentLevel + 1);
    } else {
      // If answer is wrong (or time ran out), end the game with the last milestone prize
      const lastMilestoneLevel = getLastMilestone(currentLevel);
      const milestoneAmount =
        lastMilestoneLevel > 0 ? getCurrentPrizeAmount(lastMilestoneLevel) : 0;

      // Set finalPrize so it displays correctly on the completion screen
      setFinalPrize(milestoneAmount);

      // Slight delay to show the wrong answer before ending
      setTimeout(() => {
        completeGame(lastMilestoneLevel);
      }, 2000);
    }
  };

  // Handle the 50:50 lifeline to eliminate two incorrect options
  const handleFiftyFifty = () => {
    if (
      !isMultipleChoiceQuestion(questions[currentQuestionIndex]) ||
      showAnswer
    )
      return;

    // Find which lifeline this is
    const fiftyFiftyLifeline = lifelines.find(
      (lifeline) => lifeline.type === '50-50' && !lifeline.used
    );
    if (!fiftyFiftyLifeline) return;

    // Mark the lifeline as used
    setLifelines(
      lifelines.map((lifeline) =>
        lifeline === fiftyFiftyLifeline ? { ...lifeline, used: true } : lifeline
      )
    );

    // Get the correct option index
    const correctIndex = (
      questions[currentQuestionIndex] as MultipleChoiceQuestion
    ).correctOptionIndex;

    // Create an array of incorrect option indices
    const incorrectIndices = Array.from(
      {
        length: (questions[currentQuestionIndex] as MultipleChoiceQuestion)
          .options.length,
      },
      (_, i) => i
    ).filter((i) => i !== correctIndex);

    // Randomly select two incorrect options to eliminate
    const toEliminate = incorrectIndices
      .sort(() => Math.random() - 0.5)
      .slice(0, Math.min(2, incorrectIndices.length));

    setEliminatedOptions(toEliminate);
  };

  // Use the audience poll lifeline
  const handleAudiencePoll = () => {
    if (questions.length === 0 || showAnswer) return;

    const currentQuestion = questions[currentQuestionIndex];

    // Only apply to multiple choice questions
    if (!isMultipleChoiceQuestion(currentQuestion)) return;

    const correctIndex = currentQuestion.correctOptionIndex;
    const options = currentQuestion.options;
    let pollResults: number[] = Array(options.length).fill(0);

    let correctAnswerPercentage;
    switch (currentQuestion.difficulty) {
      case 'easy':
        correctAnswerPercentage = Math.floor(Math.random() * 31) + 60;
        break;
      case 'medium':
        correctAnswerPercentage = Math.floor(Math.random() * 36) + 45;
        break;
      case 'hard':
        correctAnswerPercentage = Math.floor(Math.random() * 41) + 30;
        break;
      case 'expert':
        correctAnswerPercentage = Math.floor(Math.random() * 31) + 20;
        break;
      default:
        correctAnswerPercentage = Math.floor(Math.random() * 41) + 40;
    }

    let remainingPercentage = 100 - correctAnswerPercentage;
    const availableOptions = Array.from(
      { length: options.length },
      (_, i) => i
    ).filter((i) => !eliminatedOptions.includes(i));

    pollResults[correctIndex] = correctAnswerPercentage;

    const availableIncorrect = availableOptions.filter(
      (opt) => opt !== correctIndex
    );

    availableIncorrect.forEach((index, i) => {
      if (eliminatedOptions.includes(index)) {
        pollResults[index] = 0;
      } else if (i === availableIncorrect.length - 1) {
        pollResults[index] = remainingPercentage;
      } else {
        const percentage = Math.floor(
          Math.random() * (remainingPercentage / availableIncorrect.length)
        );
        pollResults[index] = percentage;
        remainingPercentage -= percentage;
      }
    });

    setAudiencePoll(pollResults);

    setLifelines((prev) =>
      prev.map((lifeline) =>
        lifeline.type === 'audience-poll'
          ? { ...lifeline, used: true }
          : lifeline
      )
    );
  };

  // Use the phone-a-friend lifeline
  const handlePhoneAFriend = () => {
    if (questions.length === 0 || showAnswer) return;

    const currentQuestion = questions[currentQuestionIndex];

    // Only apply to multiple choice questions
    if (!isMultipleChoiceQuestion(currentQuestion)) return;

    const correctIndex = currentQuestion.correctOptionIndex;
    const options = currentQuestion.options;

    let isCorrect;
    switch (currentQuestion.difficulty) {
      case 'easy':
        isCorrect = Math.random() < 0.9;
        break;
      case 'medium':
        isCorrect = Math.random() < 0.7;
        break;
      case 'hard':
        isCorrect = Math.random() < 0.5;
        break;
      case 'expert':
        isCorrect = Math.random() < 0.3;
        break;
      default:
        isCorrect = Math.random() < 0.6;
    }

    let advice;
    if (isCorrect) {
      const confidenceLevel = ['chắc chắn', 'khá chắc', 'nghĩ', 'đoán'];
      const randomConfidence =
        confidenceLevel[Math.floor(Math.random() * confidenceLevel.length)];
      advice = `Mình ${randomConfidence} đáp án đúng là ${String.fromCharCode(
        65 + correctIndex
      )}. ${options[correctIndex]}`;
    } else {
      let incorrectOptions = Array.from(
        { length: options.length },
        (_, i) => i
      ).filter((i) => i !== correctIndex && !eliminatedOptions.includes(i));
      if (incorrectOptions.length === 0) {
        advice =
          'Mình không chắc lắm, nhưng nghĩ có thể là ' +
          String.fromCharCode(65 + correctIndex);
      } else {
        const wrongAnswer =
          incorrectOptions[Math.floor(Math.random() * incorrectOptions.length)];
        advice = `Mình nghĩ câu trả lời là ${String.fromCharCode(
          65 + wrongAnswer
        )}. ${options[wrongAnswer]}`;
      }
    }

    setPhoneAFriendAdvice(advice);

    setLifelines((prev) =>
      prev.map((lifeline) =>
        lifeline.type === 'phone-a-friend'
          ? { ...lifeline, used: true }
          : lifeline
      )
    );
  };

  // Complete the game and provide summary
  const completeGame = (finalLevelValue: number) => {
    // Set the final level for use in formatting
    setFinalLevel(finalLevelValue);
    setTimeout(() => {
      setGameStatus('complete');
    }, 3000); // Give some time to show the answer before ending
  };

  // Render the lifelines
  const renderLifelines = () => {
    return (
      <div
        className={`tailwind-flex tailwind-justify-center tailwind-gap-4 ${
          isFullscreen ? 'tailwind-gap-6' : ''
        }`}
      >
        {lifelines.map((lifeline, index) => (
          <Tooltip
            key={index}
            title={
              lifeline.type === '50-50'
                ? 'Loại bỏ hai đáp án sai'
                : lifeline.type === 'audience-poll'
                ? 'Hỏi ý kiến khán giả'
                : 'Gọi điện thoại cho người thân'
            }
          >
            <Button
              type="primary"
              shape="circle"
              size={isFullscreen ? 'large' : 'middle'}
              icon={
                lifeline.type === '50-50' ? (
                  <ScissorOutlined />
                ) : lifeline.type === 'audience-poll' ? (
                  <TeamOutlined />
                ) : (
                  <PhoneOutlined />
                )
              }
              disabled={lifeline.used || showAnswer}
              onClick={() => {
                if (lifeline.type === '50-50') {
                  handleFiftyFifty();
                } else if (lifeline.type === 'audience-poll') {
                  handleAudiencePoll();
                } else if (lifeline.type === 'phone-a-friend') {
                  handlePhoneAFriend();
                }
              }}
              className={`${lifeline.used ? 'tailwind-opacity-50' : ''}`}
              style={{ fontSize: isFullscreen ? '20px' : '16px' }}
            />
          </Tooltip>
        ))}
      </div>
    );
  };

  // Render the main game content
  const renderGameContent = () => {
    if (gameStatus === 'playing') {
      return (
        <Splitter
          className="tailwind-w-full tailwind-min-h-[80vh] mini-game-splitter"
          onResize={(sizes) => {
            // Optional: save panel sizes to local storage or state if needed
            console.log('Panel sizes changed:', sizes);
          }}
        >
          <Splitter.Panel
            className="tailwind-h-full"
            defaultSize="25%"
            min="20%"
            max="40%"
          >
            <div className="tailwind-h-full tailwind-p-0 tailwind-rounded-lg tailwind-overflow-auto no-scrollbar">
              <PrizeLadder
                prizes={config.prizeTree}
                currentLevel={currentLevel}
                guaranteedLevels={config.prizeTree
                  .map((prize, index) => (prize.isMilestone ? index + 1 : 0))
                  .filter((level) => level > 0)}
                isFullscreen={isFullscreen}
                awardType={config.awardType}
              />
            </div>
          </Splitter.Panel>

          <Splitter.Panel className="tailwind-h-full">
            <div className="tailwind-space-y-4 tailwind-h-full tailwind-pl-4 tailwind-overflow-auto no-scrollbar">
              {/* Question header with timer and level */}
              <div className="tailwind-flex tailwind-justify-between tailwind-items-center tailwind-mb-2">
                <Badge
                  count={`Câu ${currentQuestionIndex + 1}/${questions.length}`}
                  style={{
                    backgroundColor: '#1890ff',
                    fontSize: isFullscreen ? '1rem' : '0.8rem',
                    padding: isFullscreen ? '0 10px' : '0 8px',
                  }}
                />
                <div className="tailwind-flex tailwind-items-center tailwind-gap-2">
                  {/* Timer display */}
                  <Badge
                    count={`${timeLeft}s`}
                    style={{
                      backgroundColor:
                        timeLeft && timeLeft < 10 ? '#ff4d4f' : '#52c41a',
                      fontSize: isFullscreen ? '1rem' : '0.8rem',
                      padding: isFullscreen ? '0 10px' : '0 8px',
                    }}
                  />
                  {/* Level display */}
                  <Badge
                    count={`Mốc ${currentLevel + 1}`}
                    style={{
                      backgroundColor: '#722ed1',
                      fontSize: isFullscreen ? '1rem' : '0.8rem',
                      padding: isFullscreen ? '0 10px' : '0 8px',
                    }}
                  />
                </div>
              </div>

              {/* Lifelines moved to the top */}
              <div
                className={`tailwind-flex tailwind-justify-center tailwind-gap-4 tailwind-mb-4 tailwind-bg-blue-50 tailwind-p-3 tailwind-rounded-lg tailwind-border tailwind-border-blue-200 ${
                  isFullscreen ? 'tailwind-gap-6 tailwind-p-4' : ''
                }`}
              >
                {renderLifelines()}
              </div>

              {/* Show audience poll chart when activated */}
              {audiencePoll.length > 0 && (
                <div className="tailwind-bg-blue-50 tailwind-p-4 tailwind-rounded-lg tailwind-mb-4 tailwind-border tailwind-border-blue-200">
                  <div className="tailwind-text-center tailwind-font-medium tailwind-mb-3 tailwind-text-blue-800">
                    Ý kiến khán giả trong trường quay
                  </div>
                  <div className="tailwind-grid tailwind-grid-cols-2 tailwind-gap-x-4 tailwind-gap-y-3">
                    {audiencePoll.map((percentage, index) => {
                      const letterOptions = ['A', 'B', 'C', 'D'];
                      const isEliminated = eliminatedOptions.includes(index);

                      return (
                        <div
                          key={index}
                          className={`tailwind-flex tailwind-flex-col ${
                            isEliminated ? 'tailwind-opacity-50' : ''
                          }`}
                        >
                          <div className="tailwind-flex tailwind-items-center tailwind-mb-1">
                            <span
                              className={`
                              tailwind-inline-block tailwind-rounded-full tailwind-mr-2 tailwind-flex-shrink-0
                              tailwind-w-6 tailwind-h-6 tailwind-leading-6 tailwind-text-center
                              tailwind-bg-blue-500 tailwind-text-white tailwind-text-sm
                            `}
                            >
                              {letterOptions[index]}
                            </span>
                            <span className="tailwind-text-sm tailwind-truncate tailwind-font-medium">
                              {percentage}%
                            </span>
                          </div>
                          <Progress
                            percent={percentage}
                            showInfo={false}
                            strokeColor="#1890ff"
                            trailColor="#e6f7ff"
                            className="tailwind-mb-1"
                          />
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* Current question */}
              <div
                className={`tailwind-bg-blue-50 tailwind-p-4 tailwind-rounded-lg tailwind-mb-4 tailwind-border tailwind-border-blue-200 ${
                  isFullscreen ? 'tailwind-p-6' : ''
                }`}
              >
                <Typography.Title
                  level={4}
                  className={getTextSizeClass(
                    'tailwind-text-lg',
                    'tailwind-text-xl'
                  )}
                  style={{
                    marginBottom: isFullscreen ? '16px' : '12px',
                    marginTop: 0,
                  }}
                >
                  {questions[currentQuestionIndex]?.questionText ||
                    'Câu hỏi không có sẵn'}
                </Typography.Title>

                {/* Use the MultipleChoiceQuestionComponent instead of custom implementation */}
                {isMultipleChoiceQuestion(questions[currentQuestionIndex]) &&
                  !showAnswer && (
                    <div className="tailwind-relative">
                      <MultipleChoiceQuestionComponent
                        question={
                          questions[
                            currentQuestionIndex
                          ] as MultipleChoiceQuestion
                        }
                        onAnswer={handleAnswer}
                        disabled={showAnswer}
                      />

                      {/* Overlay for eliminated options - removed pointer-events-none to allow clicking */}
                      {eliminatedOptions.length > 0 && (
                        <div className="tailwind-absolute tailwind-inset-0 tailwind-pointer-events-none">
                          {(
                            questions[
                              currentQuestionIndex
                            ] as MultipleChoiceQuestion
                          ).options.map((_, index) => {
                            if (eliminatedOptions.includes(index)) {
                              const optionsCount = (
                                questions[
                                  currentQuestionIndex
                                ] as MultipleChoiceQuestion
                              ).options.length;

                              // Calculate position and height for each option
                              const optionHeight = 100 / optionsCount;
                              const topPosition = index * optionHeight;

                              return (
                                <div
                                  key={`eliminated-${index}`}
                                  className="tailwind-absolute tailwind-bg-gray-200 tailwind-opacity-70 tailwind-rounded-md"
                                  style={{
                                    top: `${topPosition}%`,
                                    height: `${optionHeight}%`,
                                    left: 0,
                                    right: 0,
                                  }}
                                />
                              );
                            }
                            return null;
                          })}
                        </div>
                      )}
                    </div>
                  )}

                {/* Show custom styled options if we're displaying the answer */}
                {isMultipleChoiceQuestion(questions[currentQuestionIndex]) &&
                  showAnswer && (
                    <div className="tailwind-w-full tailwind-flex tailwind-flex-col tailwind-space-y-3">
                      {(
                        questions[
                          currentQuestionIndex
                        ] as MultipleChoiceQuestion
                      )?.options?.map((option, index) => {
                        const isEliminated = eliminatedOptions.includes(index);
                        const letterOptions = ['A', 'B', 'C', 'D'];
                        const isCorrect =
                          (
                            questions[
                              currentQuestionIndex
                            ] as MultipleChoiceQuestion
                          ).correctOptionIndex === index;

                        return (
                          <Button
                            key={index}
                            className={`
                            tailwind-text-left tailwind-flex tailwind-items-center tailwind-w-full
                            tailwind-h-auto tailwind-py-3 tailwind-px-4
                            ${
                              showAnswer && selectedAnswer === index
                                ? 'tailwind-bg-blue-100 tailwind-border-blue-400'
                                : ''
                            }
                            ${
                              showAnswer && isCorrect
                                ? 'tailwind-bg-green-100 tailwind-border-green-400'
                                : ''
                            }
                            ${
                              showAnswer &&
                              selectedAnswer === index &&
                              !isCorrect
                                ? 'tailwind-bg-red-100 tailwind-border-red-400'
                                : ''
                            }
                            ${isEliminated ? 'tailwind-opacity-50' : ''}
                          `}
                            disabled={true}
                            size={isFullscreen ? 'large' : 'middle'}
                            style={{
                              height: 'auto',
                              whiteSpace: 'normal',
                            }}
                          >
                            <div className="tailwind-w-8 tailwind-h-8 tailwind-flex tailwind-items-center tailwind-justify-center tailwind-bg-indigo-100 tailwind-text-indigo-800 tailwind-rounded-full tailwind-mr-4 tailwind-font-medium tailwind-flex-shrink-0">
                              {letterOptions[index]}
                            </div>
                            <span className="tailwind-flex-1 tailwind-break-words">
                              {option}
                            </span>
                          </Button>
                        );
                      })}
                    </div>
                  )}
              </div>

              {/* Phone-a-friend advice */}
              {phoneAFriendAdvice && (
                <Alert
                  message="Gợi ý từ người thân"
                  description={phoneAFriendAdvice}
                  type="info"
                  className="tailwind-mb-4"
                  showIcon
                  style={{ padding: isFullscreen ? '12px 16px' : '8px 12px' }}
                />
              )}

              {/* Show correct answer info when showAnswer is true */}
              {showAnswer && (
                <Alert
                  message={
                    selectedAnswer ===
                    (questions[currentQuestionIndex] as MultipleChoiceQuestion)
                      .correctOptionIndex
                      ? 'Chính xác!'
                      : 'Không chính xác!'
                  }
                  description={
                    <div>
                      <p className="tailwind-mb-2">
                        {selectedAnswer === null
                          ? 'Bạn đã hết thời gian.'
                          : selectedAnswer ===
                            (
                              questions[
                                currentQuestionIndex
                              ] as MultipleChoiceQuestion
                            ).correctOptionIndex
                          ? 'Câu trả lời của bạn chính xác.'
                          : `Câu trả lời đúng là: ${
                              ['A', 'B', 'C', 'D'][
                                (
                                  questions[
                                    currentQuestionIndex
                                  ] as MultipleChoiceQuestion
                                ).correctOptionIndex
                              ]
                            } - ${
                              (
                                questions[
                                  currentQuestionIndex
                                ] as MultipleChoiceQuestion
                              ).options[
                                (
                                  questions[
                                    currentQuestionIndex
                                  ] as MultipleChoiceQuestion
                                ).correctOptionIndex
                              ]
                            }`}
                      </p>
                      {questions[currentQuestionIndex].explanation && (
                        <p className="tailwind-text-gray-700">
                          <strong>Giải thích:</strong>{' '}
                          {questions[currentQuestionIndex].explanation}
                        </p>
                      )}
                    </div>
                  }
                  type={
                    selectedAnswer ===
                    (questions[currentQuestionIndex] as MultipleChoiceQuestion)
                      .correctOptionIndex
                      ? 'success'
                      : 'error'
                  }
                  className="tailwind-mb-4"
                  showIcon
                />
              )}
            </div>
          </Splitter.Panel>
        </Splitter>
      );
    }

    return (
      <div className="tailwind-flex tailwind-flex-col md:tailwind-flex-row tailwind-gap-4 mini-game-container">
        {/* Main game area for non-playing states */}
        <div className="tailwind-flex-1 tailwind-w-full tailwind-max-w-full">
          {gameStatus === 'ready' && (
            <div className="tailwind-text-center tailwind-py-8 tailwind-space-y-4">
              <Title
                level={3}
                className={getTextSizeClass(
                  'tailwind-text-xl',
                  'tailwind-text-2xl'
                )}
              >
                {config.titleProps.text || 'Ai Là Triệu Phú'}
              </Title>
              <Paragraph
                className={getTextSizeClass(
                  'tailwind-text-base',
                  'tailwind-text-lg'
                )}
              >
                Sẵn sàng thử thách trí tuệ của bạn?
              </Paragraph>
              <Button
                type="primary"
                size={isFullscreen ? 'large' : 'middle'}
                onClick={handleStartGame}
                className="tailwind-mt-4"
              >
                Bắt đầu chơi
              </Button>
            </div>
          )}

          {gameStatus === 'complete' && (
            <div className="tailwind-text-center tailwind-py-8 tailwind-space-y-6">
              <div className="tailwind-flex tailwind-justify-center tailwind-mb-6">
                <TrophyOutlined
                  className={getTextSizeClass(
                    'tailwind-text-5xl',
                    'tailwind-text-6xl'
                  )}
                  style={{ color: '#faad14' }}
                />
              </div>

              <Title
                level={3}
                className={getTextSizeClass(
                  'tailwind-text-2xl',
                  'tailwind-text-3xl'
                )}
              >
                Kết thúc trò chơi!
              </Title>

              <div
                className={`tailwind-bg-yellow-50 tailwind-rounded-lg tailwind-p-6 tailwind-mb-6 ${
                  isFullscreen ? 'tailwind-p-8' : ''
                }`}
              >
                <Title
                  level={4}
                  className={getTextSizeClass(
                    'tailwind-text-xl',
                    'tailwind-text-2xl'
                  )}
                >
                  Bạn đã giành được:
                </Title>
                <div
                  className={`tailwind-text-center tailwind-my-6 ${getTextSizeClass(
                    'tailwind-text-3xl',
                    'tailwind-text-4xl'
                  )} tailwind-font-bold tailwind-text-blue-600`}
                >
                  {formatMoney(finalPrize, finalLevel)}
                </div>
              </div>

              <Button
                type="primary"
                onClick={handleStartGame}
                size={isFullscreen ? 'large' : 'middle'}
                className="tailwind-mt-6"
              >
                Chơi lại
              </Button>
            </div>
          )}
        </div>
      </div>
    );
  };

  return renderGameContent();
};

export default MillionaireGameComponent;
