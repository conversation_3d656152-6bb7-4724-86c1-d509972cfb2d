import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import subjectApi, { SubjectParams } from '../../../api/subjectApi';
import { Subject } from '../../../interfaces/lessons/subject';

// Define the state interface
export interface SubjectState {
  subjects?: Subject[];
  currentSubject: Subject | null;
  loading: boolean;
  error: string | null;
  totalCount: number;
  currentPage: number;
  pageSize: number;
}

// Initial state
const initialState: SubjectState = {
  currentSubject: null,
  loading: false,
  error: null,
  totalCount: 0,
  currentPage: 1,
  pageSize: 1000,
};

// Create thunk actions
export const fetchSubjects = createAsyncThunk(
  'subject/fetchSubjects',
  async (params: SubjectParams = {}, { rejectWithValue }) => {
    try {
      const response = await subjectApi.getSubjects(params);
      if (response.success) {
        return {
          data: response.data,
          total: response.total,
        };
      }
      return rejectWithValue(response.error || 'Failed to fetch subjects');
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Unknown error'
      );
    }
  }
);

export const fetchSubjectById = createAsyncThunk(
  'subject/fetchSubjectById',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await subjectApi.getSubjectById(id);
      if (response.success) {
        return response.data;
      }
      return rejectWithValue(response.error || 'Failed to fetch subject');
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Unknown error'
      );
    }
  }
);

// Create the slice
const subjectSlice = createSlice({
  name: 'subject',
  initialState,
  reducers: {
    setCurrentPage: (state, action: PayloadAction<number>) => {
      state.currentPage = action.payload;
    },
    setPageSize: (state, action: PayloadAction<number>) => {
      state.pageSize = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // fetchSubjects
      .addCase(fetchSubjects.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSubjects.fulfilled, (state, action) => {
        state.loading = false;
        state.subjects = action.payload.data;
        state.totalCount = action.payload.total;
      })
      .addCase(fetchSubjects.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // fetchSubjectById
      .addCase(fetchSubjectById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSubjectById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentSubject = action.payload as Subject;
      })
      .addCase(fetchSubjectById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

// Export actions and reducer
export const { setCurrentPage, setPageSize, clearError } = subjectSlice.actions;
export default subjectSlice.reducer;
