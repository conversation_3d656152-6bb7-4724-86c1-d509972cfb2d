.practice-exam-header {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
}
.practice-exam-header .ant-typography {
  margin: 0;
  text-align: center;
}
.practice-exam-content {
  position: relative;
  z-index: 2;
}

.practice-exam-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.85);
  z-index: 1;
}

.practice-exam-filters {
  position: relative;
}

.practice-exam-grid {
  position: relative;
  z-index: 2;
}

.practice-exam-pagination {
  position: relative;
  z-index: 2;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 16px;
  display: inline-block;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .practice-exam-page {
    background-attachment: scroll;
  }
  
  .practice-exam-filters {
    margin: 16px;
    padding: 16px;
  }
}