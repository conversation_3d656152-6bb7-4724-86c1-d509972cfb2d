import { TrueFalseQuestion } from '../components/common/QuestionComponent';
import {
  FillBlankItem,
  FillBlanksQuestion,
} from '../components/quizs/fillblanks';
import practiceLocalization from '../components/quizs/localization';
import {
  ExamFillInBlankAnswer,
  ExamMatchingAnswer,
  ExamMatchingItem,
  ExamQuestionOption,
} from '../interfaces/exams/examBase';
import {
  ContentFormatType,
  ExamSourceType,
} from '../interfaces/exams/examEnums';
import { IQuestion } from '../interfaces/questions/question';
import {
  MultiSelectAnswer,
  MultiSelectQuestion,
} from '../interfaces/quizs/multiSelectQuiz.interface';
import {
  BaseQuestion,
  MatchingItemType,
  MatchingQuestion,
  MatchingQuestionAnswer,
  QuestionType,
  QuizAnswer,
  QuizQuestion,
} from '../interfaces/quizs/questionBase';
import { EssayQuestion } from '../interfaces/quizs/essay.interface';

/**
 * Chuyển đổi chuỗi thành loại câu hỏi
 * @param questionType Chuỗi đại diện cho loại câu hỏi
 * @returns Loại câu hỏi
 */
export const getQuestionTypeFromString = (questionType: string) => {
  switch (questionType?.toLocaleLowerCase()) {
    case practiceLocalization.quiz:
    case practiceLocalization.singlechoice:
      return QuestionType.SingleChoice;
    case practiceLocalization.multiselect:
    case practiceLocalization.multiplechoice:
      return QuestionType.MultipleChoice;
    case practiceLocalization.matching:
      return QuestionType.Matching;
    case practiceLocalization.fillblanks:
      return QuestionType.FillInBlank;
    case practiceLocalization.essay:
      return QuestionType.Essay;
    default:
      return QuestionType.SingleChoice; // Default fallback
  }
};

/**
 * Chuyển đổi chuỗi thành loại nguồn câu hỏi
 * @param sourceType Chuỗi đại diện cho loại nguồn câu hỏi
 * @returns Loại nguồn câu hỏi
 */
export const getSourceTypeFromString = (sourceType: string) => {
  switch (sourceType?.toLocaleLowerCase()) {
    case 'banked':
      return ExamSourceType.Banked;
    case 'default':
      return ExamSourceType.Default;
    default:
      return ExamSourceType.Default; // Default fallback
  }
};

/**
 * Chuyển đổi loại câu hỏi thành chuỗi
 * @param questionType Loại câu hỏi
 * @returns Chuỗi đại diện cho loại câu hỏi
 */
export const getQuestionTypeString = (questionType: QuestionType) => {
  switch (questionType) {
    case QuestionType.SingleChoice:
      return practiceLocalization.quiz;
    case QuestionType.MultipleChoice:
      return practiceLocalization.multiselect;
    case QuestionType.Essay:
      return practiceLocalization.essay;
    case QuestionType.Matching:
      return practiceLocalization.matching;
    case QuestionType.FillInBlank:
      return practiceLocalization.fillblanks;
    case QuestionType.Essay:
      return practiceLocalization.essay;
    default:
      return practiceLocalization.quiz;
  }
};

/**
 * Lấy tên hiển thị việt nam của loại câu hỏi
 * @param questionType Loại câu hỏi
 * @returns Tên hiển thị việt nam của loại câu hỏi
 */
export const getQuestionTypeDisplayVN = (questionType: QuestionType) => {
  switch (questionType) {
    case QuestionType.SingleChoice:
      return 'Trắc nghiệm 1 đáp án';
    case QuestionType.MultipleChoice:
      return 'Trắc nghiệm nhiều đáp án';
    case QuestionType.Essay:
      return 'Câu hỏi tự luận';
    case QuestionType.Matching:
      return 'Câu hỏi nối';
    case QuestionType.FillInBlank:
      return 'Câu hỏi điền từ';
    default:
      return 'Trắc nghiệm 1 đáp án';
  }
};

/**
 * Lấy tên hiển thị việt nam của loại câu hỏi
 * @param type string Loại câu hỏi
 * @returns Tên hiển thị việt nam của loại câu hỏi
 */
export const getQuestionTypeDisplayVNFromString = (type: string) => {
  const questionType = getQuestionTypeFromString(type);
  return getQuestionTypeDisplayVN(questionType);
};

/**
 * map IQuestion  to BaseQuestion by questionType
 * @param IQuestion question
 * @returns BaseQuestion
 */
export const mapQuestionToBaseQuestion = (
  question: IQuestion
): BaseQuestion => {
  const baseQuestion: BaseQuestion = {
    ...question,
    id: question.id,
    clientId: question.clientId,
    type: getQuestionTypeString(
      question.questionType || QuestionType.SingleChoice
    ),
  };

  // switch question type
  switch (question.questionType) {
    case QuestionType.SingleChoice:
      // cast to QuizQuestion
      let quizQuestion = baseQuestion as QuizQuestion;
      quizQuestion.options = question.options?.map((option) => ({
        id: option.id,
        clientId: option.clientId,
        content: option.content,
        contentFormat: option.contentFormat,
        isCorrect: option.isCorrect,
        order: option.order,
      }));
      return quizQuestion as QuizQuestion;

    case QuestionType.MultipleChoice: // cast to MultiSelectQuestion
      let multiSelectQuestion = baseQuestion as MultiSelectQuestion;
      multiSelectQuestion.options =
        question.options?.map(
          (option) =>
            ({
              id: option.id,
              clientId: option.clientId,
              content: option.content,
              contentFormat: option.contentFormat,
              isCorrect: option.isCorrect,
              order: option.order,
            } as MultiSelectAnswer)
        ) || [];
      return multiSelectQuestion as MultiSelectQuestion;
    case QuestionType.TrueFalse: // cast to TrueFalseQuestion
      break;
    case QuestionType.Essay:
      let essayQuestion = baseQuestion as EssayQuestion;
      essayQuestion.correctAnswer = question.modelAnswer || '';
      essayQuestion.content = question.content || '';
      return essayQuestion;
    // cast to EssayQuestion
    case QuestionType.Matching:
      let matchingQuestion = baseQuestion as MatchingQuestion;
      matchingQuestion.leftItems = question.matchingItems
        ?.filter((x) => x.type == 0)
        .map(
          (x) =>
            ({
              clientId: x.clientId,
              id: x.id,
              content: x.content,
              type: 'text',
              matchId: x.clientId,
            } as MatchingItemType)
        );
      matchingQuestion.rightItems = question.matchingItems
        ?.filter((x) => x.type == 1)
        .map(
          (x) =>
            ({
              clientId: x.clientId,
              id: x.id,
              content: x.content,
              type: 'text',
              matchId: x.clientId,
            } as MatchingItemType)
        );
      matchingQuestion.options =
        question.matchingAnswers?.map(
          (answer) =>
            ({
              clientId: answer.clientId,
              id: answer.id,
              left: answer.premiseId,
              right: answer.responseId,
              content: '',
              contentFormat: '',
              isCorrect: true,
              order: 0,
            } as MatchingQuestionAnswer)
        ) || [];

      return matchingQuestion as MatchingQuestion;
    case QuestionType.FillInBlank:
      // cast to FillInBlankQuestion
      let fillInBlankQuestion = baseQuestion as FillBlanksQuestion;
      fillInBlankQuestion.blanks =
        question.fillInBlankAnswers?.map(
          (answer) =>
            ({
              clientId: answer.clientId,
              id: answer.id,
              correctAnswer: answer.correctAnswers.join(', '),
              alternativeAnswers: answer.correctAnswers,
              hint: '',
            } as FillBlankItem)
        ) || [];
      return fillInBlankQuestion as FillBlanksQuestion;
    default:
      return baseQuestion as QuizQuestion;
  }

  return baseQuestion;
};

/**
 * Map BaseQuestion to IQuestion
 * @param question BaseQuestion
 * @returns IQuestion
 */
export const mapBaseQuestionToQuestion = (question: BaseQuestion) => {
  const entity: IQuestion = {
    ...question,
    id: question.id,
    title: question.title || '',
    content: question.content || '',
    contentFormat: ContentFormatType.Html,
    difficultyLevel: question.points || 1,
    description: question.title || '',
    topics: Array.isArray(question.topics)
      ? question.topics?.join(',')
      : question.topics,
    tags: Array.isArray(question.tags)
      ? question.tags?.join(',')
      : question.tags,
    source: question.metadata?.source,
    subjectId: question.subjectId,
    options: [],
    matchingItems: [],
    matchingAnswers: [],
    fillInBlankAnswers: [],
    type: question.type,
    questionType: getQuestionTypeFromString(question.type),
    status: question.status,
    statusEntity: question.statusEntity,
    statusEntityString: question.statusEntityString,
    clientId: question.clientId,
    difficulty: question.difficulty,
    syncQuestion: question.syncQuestion,
    order: question.order,
    score: question.score,
    questionId: question.questionId,
    lastSyncQuestionId: question.lastSyncQuestionId,
    creationTime: question.creationTime,
    lastModificationTime: question.lastModificationTime,
    comment: question.comment,
    explanation: question.explanation,
    shuffleOptions: question.shuffleOptions,
    sourceType: question.sourceType,
  };

  switch (question.type) {
    case practiceLocalization.singlechoice:
    case practiceLocalization.quiz:
      entity.options = question.options
        ? quizQuestionToOptions((question as QuizQuestion).options ?? [])
        : [];
      break;
    case practiceLocalization.multiplechoice:
    case practiceLocalization.multiselect:
      entity.options = question.options
        ? multiselectQuestionToOptions(
            (question as MultiSelectQuestion).options
          )
        : [];
      break;
    case practiceLocalization.matching:
      entity.matchingItems = matchingQuestionToMatchingItems(
        question as MatchingQuestion
      );

      entity.matchingAnswers = matchingQuestionToMatchingAnswers(
        question as MatchingQuestion
      );
      break;
    case practiceLocalization.fillblanks:
      entity.fillInBlankAnswers = fillInBlankQuestionToFillInBlankAnswers(
        question as FillBlanksQuestion
      );
      break;
    case practiceLocalization.essay:
      // Todo
      break;
    case practiceLocalization.essay:
      break;
    default:
      break;
  }
  return entity;
};

const quizQuestionToOptions = (answers: QuizAnswer[]): ExamQuestionOption[] => {
  return answers.map((answer) => ({
    ...answer,
    contentFormat:
      typeof answer.contentFormat === 'string'
        ? ContentFormatType[
            answer.contentFormat as keyof typeof ContentFormatType
          ]
        : answer.contentFormat,
  }));
};

const multiselectQuestionToOptions = (
  answers: MultiSelectAnswer[]
): ExamQuestionOption[] => {
  return answers.map((answer) => ({
    ...answer,
    contentFormat:
      typeof answer.contentFormat === 'string'
        ? ContentFormatType[
            answer.contentFormat as keyof typeof ContentFormatType
          ]
        : answer.contentFormat,
  }));
};

const fillInBlankQuestionToFillInBlankAnswers = (
  arg0: FillBlanksQuestion
): ExamFillInBlankAnswer[] => {
  let output = arg0.blanks.map((blank) => ({
    ...blank,
    blankIndex: arg0.blanks.indexOf(blank),
    correctAnswers: blank.correctAnswer.split(','),
    caseSensitive: false,
  }));
  return output;
};
const matchingQuestionToMatchingAnswers = (
  arg0: MatchingQuestion
): ExamMatchingAnswer[] => {
  let output: ExamMatchingAnswer[] = [];

  if (arg0.options) {
    output = arg0.options.map(
      (answer: MatchingQuestionAnswer) =>
        ({
          id: answer.id,
          clientId: answer.clientId,
          premiseId: answer.left,
          responseId: answer.right,
        } as ExamMatchingAnswer)
    );
  }

  return output;
};

const matchingQuestionToMatchingItems = (
  question: MatchingQuestion
): ExamMatchingItem[] | undefined => {
  let output: ExamMatchingItem[] = [];

  if (question.leftItems) {
    output = question.leftItems.map(
      (item: MatchingItemType) =>
        ({
          id: item.id,
          clientId: item.clientId,
          type: 0,
          content: item.content,
          contentFormat: ContentFormatType.Html,
        } as ExamMatchingItem)
    );
  }

  if (question.rightItems) {
    output = output.concat(
      question.rightItems.map(
        (item: MatchingItemType) =>
          ({
            id: item.id,
            clientId: item.clientId,
            type: 1,
            content: item.content,
            contentFormat: ContentFormatType.Html,
          } as ExamMatchingItem)
      )
    );
  }

  return output;
};
