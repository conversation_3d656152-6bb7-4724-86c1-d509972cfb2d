{"version": 2, "dgSpecHash": "QaKwxjI/d9E=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.MongoDB\\EdTech.Study.MongoDB.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\asynckeyedlock\\6.3.4\\asynckeyedlock.6.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\automapper\\12.0.1\\automapper.12.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\awssdk.core\\3.7.100.14\\awssdk.core.3.7.100.14.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\awssdk.securitytoken\\3.7.100.14\\awssdk.securitytoken.3.7.100.14.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\configureawait.fody\\3.3.1\\configureawait.fody.3.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\dnsclient\\1.6.1\\dnsclient.1.6.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\fody\\6.5.3\\fody.6.5.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\jetbrains.annotations\\2023.3.0\\jetbrains.annotations.2023.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authorization\\8.0.0\\microsoft.aspnetcore.authorization.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.cryptography.internal\\8.0.0\\microsoft.aspnetcore.cryptography.internal.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.cryptography.keyderivation\\8.0.0\\microsoft.aspnetcore.cryptography.keyderivation.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.metadata\\8.0.0\\microsoft.aspnetcore.metadata.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\8.0.0\\microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.csharp\\4.7.0\\microsoft.csharp.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.abstractions\\8.0.0\\microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.memory\\8.0.0\\microsoft.extensions.caching.memory.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration\\8.0.0\\microsoft.extensions.configuration.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\8.0.0\\microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.binder\\8.0.0\\microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.commandline\\8.0.0\\microsoft.extensions.configuration.commandline.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.environmentvariables\\8.0.0\\microsoft.extensions.configuration.environmentvariables.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.fileextensions\\8.0.0\\microsoft.extensions.configuration.fileextensions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.json\\8.0.0\\microsoft.extensions.configuration.json.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.usersecrets\\8.0.0\\microsoft.extensions.configuration.usersecrets.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\8.0.0\\microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\8.0.0\\microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics.abstractions\\8.0.0\\microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.abstractions\\8.0.0\\microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.composite\\8.0.0\\microsoft.extensions.fileproviders.composite.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.embedded\\8.0.0\\microsoft.extensions.fileproviders.embedded.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.physical\\8.0.0\\microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.filesystemglobbing\\8.0.0\\microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.hosting.abstractions\\8.0.0\\microsoft.extensions.hosting.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.identity.core\\8.0.0\\microsoft.extensions.identity.core.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.localization\\8.0.0\\microsoft.extensions.localization.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.localization.abstractions\\8.0.0\\microsoft.extensions.localization.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\8.0.0\\microsoft.extensions.logging.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\8.0.0\\microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\8.0.0\\microsoft.extensions.options.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options.configurationextensions\\8.0.0\\microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\8.0.0\\microsoft.extensions.primitives.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\1.1.0\\microsoft.netcore.platforms.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\5.0.0\\microsoft.netcore.platforms.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.targets\\1.1.0\\microsoft.netcore.targets.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.registry\\5.0.0\\microsoft.win32.registry.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mongodb.bson\\2.22.0\\mongodb.bson.2.22.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mongodb.driver\\2.22.0\\mongodb.driver.2.22.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mongodb.driver.core\\2.22.0\\mongodb.driver.core.2.22.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mongodb.libmongocrypt\\1.8.0\\mongodb.libmongocrypt.1.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\netstandard.library\\2.0.3\\netstandard.library.2.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nito.asyncex.context\\5.1.2\\nito.asyncex.context.5.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nito.asyncex.tasks\\5.1.2\\nito.asyncex.tasks.5.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nito.disposables\\2.2.1\\nito.disposables.2.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sharpcompress\\0.30.1\\sharpcompress.0.30.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\snappier\\1.0.0\\snappier.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.buffers\\4.5.1\\system.buffers.4.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections\\4.3.0\\system.collections.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.immutable\\8.0.0\\system.collections.immutable.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel.annotations\\5.0.0\\system.componentmodel.annotations.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.debug\\4.3.0\\system.diagnostics.debug.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.diagnosticsource\\8.0.0\\system.diagnostics.diagnosticsource.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization\\4.3.0\\system.globalization.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq\\4.3.0\\system.linq.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq.dynamic.core\\1.3.5\\system.linq.dynamic.core.1.3.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq.expressions\\4.3.0\\system.linq.expressions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq.queryable\\4.3.0\\system.linq.queryable.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.5\\system.memory.4.5.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.numerics.vectors\\4.4.0\\system.numerics.vectors.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.objectmodel\\4.3.0\\system.objectmodel.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit\\4.3.0\\system.reflection.emit.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit.ilgeneration\\4.3.0\\system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit.lightweight\\4.3.0\\system.reflection.emit.lightweight.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.extensions\\4.3.0\\system.reflection.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.typeextensions\\4.3.0\\system.reflection.typeextensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.resources.resourcemanager\\4.3.0\\system.resources.resourcemanager.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime\\4.3.0\\system.runtime.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\5.0.0\\system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.extensions\\4.3.0\\system.runtime.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.loader\\4.3.0\\system.runtime.loader.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.accesscontrol\\5.0.0\\system.security.accesscontrol.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal.windows\\5.0.0\\system.security.principal.windows.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding.codepages\\5.0.0\\system.text.encoding.codepages.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encodings.web\\8.0.0\\system.text.encodings.web.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\8.0.0\\system.text.json.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading\\4.3.0\\system.threading.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.extensions\\4.5.4\\system.threading.tasks.extensions.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\timezoneconverter\\6.1.0\\timezoneconverter.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.auditing\\8.1.4\\volo.abp.auditing.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.auditing.contracts\\8.1.4\\volo.abp.auditing.contracts.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.authorization.abstractions\\8.1.4\\volo.abp.authorization.abstractions.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.automapper\\8.1.4\\volo.abp.automapper.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.backgroundworkers\\8.1.4\\volo.abp.backgroundworkers.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.blobstoring\\8.1.4\\volo.abp.blobstoring.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.caching\\8.1.4\\volo.abp.caching.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.core\\8.1.4\\volo.abp.core.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.data\\8.1.4\\volo.abp.data.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.ddd.domain\\8.1.4\\volo.abp.ddd.domain.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.ddd.domain.shared\\8.1.4\\volo.abp.ddd.domain.shared.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.distributedlocking.abstractions\\8.1.4\\volo.abp.distributedlocking.abstractions.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.eventbus\\8.1.4\\volo.abp.eventbus.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.eventbus.abstractions\\8.1.4\\volo.abp.eventbus.abstractions.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.exceptionhandling\\8.1.4\\volo.abp.exceptionhandling.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.features\\8.1.4\\volo.abp.features.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.guids\\8.1.4\\volo.abp.guids.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identity.domain\\8.1.4\\volo.abp.identity.domain.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identity.domain.shared\\8.1.4\\volo.abp.identity.domain.shared.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.json\\8.1.4\\volo.abp.json.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.json.abstractions\\8.1.4\\volo.abp.json.abstractions.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.json.systemtextjson\\8.1.4\\volo.abp.json.systemtextjson.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.localization\\8.1.4\\volo.abp.localization.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.localization.abstractions\\8.1.4\\volo.abp.localization.abstractions.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.mongodb\\8.1.4\\volo.abp.mongodb.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.multitenancy\\8.1.4\\volo.abp.multitenancy.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.multitenancy.abstractions\\8.1.4\\volo.abp.multitenancy.abstractions.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.objectextending\\8.1.4\\volo.abp.objectextending.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.objectmapping\\8.1.4\\volo.abp.objectmapping.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.security\\8.1.4\\volo.abp.security.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.serialization\\8.1.4\\volo.abp.serialization.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.settings\\8.1.4\\volo.abp.settings.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.specifications\\8.1.4\\volo.abp.specifications.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.threading\\8.1.4\\volo.abp.threading.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.timing\\8.1.4\\volo.abp.timing.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.uow\\8.1.4\\volo.abp.uow.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.users.abstractions\\8.1.4\\volo.abp.users.abstractions.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.users.domain\\8.1.4\\volo.abp.users.domain.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.users.domain.shared\\8.1.4\\volo.abp.users.domain.shared.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.validation\\8.1.4\\volo.abp.validation.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.validation.abstractions\\8.1.4\\volo.abp.validation.abstractions.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.virtualfilesystem\\8.1.4\\volo.abp.virtualfilesystem.8.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\zstdsharp.port\\0.6.2\\zstdsharp.port.0.6.2.nupkg.sha512"], "logs": [{"code": "NU1903", "level": "Warning", "warningLevel": 1, "message": "Package 'Microsoft.Extensions.Caching.Memory' 8.0.0 has a known high severity vulnerability, https://github.com/advisories/GHSA-qj66-m88j-hmgj", "libraryId": "Microsoft.Extensions.Caching.Memory", "targetGraphs": [".NETStandard,Version=v2.0", ".NETStandard,Version=v2.1", "net8.0"]}, {"code": "NU1903", "level": "Warning", "warningLevel": 1, "message": "Package 'System.Linq.Dynamic.Core' 1.3.5 has a known high severity vulnerability, https://github.com/advisories/GHSA-4cv2-4hjh-77rx", "libraryId": "System.Linq.Dynamic.Core", "targetGraphs": [".NETStandard,Version=v2.0", ".NETStandard,Version=v2.1", "net8.0"]}, {"code": "NU1903", "level": "Warning", "warningLevel": 1, "message": "Package 'System.Text.Json' 8.0.0 has a known high severity vulnerability, https://github.com/advisories/GHSA-8g4q-xg66-9fp4", "libraryId": "System.Text.Json", "targetGraphs": [".NETStandard,Version=v2.0", ".NETStandard,Version=v2.1", "net8.0"]}, {"code": "NU1903", "level": "Warning", "warningLevel": 1, "message": "Package 'System.Text.Json' 8.0.0 has a known high severity vulnerability, https://github.com/advisories/GHSA-hh2w-p6rv-4g7w", "libraryId": "System.Text.Json", "targetGraphs": [".NETStandard,Version=v2.0", ".NETStandard,Version=v2.1", "net8.0"]}]}