﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EdTech.Study.Questions.Dtos
{
    public class CreateUpdateFillInBlankAnswer
    {
        public Guid? Id { get; set; }

        public Guid ClientId { get; set; }

        /// <summary>
        /// Chỉ số của ô trống trong câu hỏi (bắt đầu từ 0).
        /// </summary>
        public int BlankIndex { get; set; }

        /// <summary>
        /// Mảng các đáp án đúng có thể chấp nhận cho ô trống này.
        /// </summary>
        public List<string> CorrectAnswers { get; set; } = new();

        /// <summary>
        /// C<PERSON> phân biệt chữ hoa chữ thường hay không.
        /// </summary>
        public bool CaseSensitive { get; set; }

        /// <summary>
        /// Phản hồi hiển thị khi học sinh trả lời đúng hoặc sai.
        /// </summary>
        public string? Feedback { get; set; }

        /// <summary>
        /// Điể<PERSON> số cho ô trống này.
        /// </summary>
        public float? Score { get; set; }
    }
}
