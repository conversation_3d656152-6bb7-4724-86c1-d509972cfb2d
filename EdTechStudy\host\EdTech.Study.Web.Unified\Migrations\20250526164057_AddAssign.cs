﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EdTech.Study.Migrations
{
    /// <inheritdoc />
    public partial class AddAssign : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "AssignedDate",
                table: "StudyQuestions",
                type: "datetime(6)",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "AssignedUserId",
                table: "StudyQuestions",
                type: "char(36)",
                nullable: true,
                collation: "ascii_general_ci");

            migrationBuilder.AddColumn<DateTime>(
                name: "DueDate",
                table: "StudyQuestions",
                type: "datetime(6)",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AssignedDate",
                table: "StudyQuestions");

            migrationBuilder.DropColumn(
                name: "AssignedUserId",
                table: "StudyQuestions");

            migrationBuilder.DropColumn(
                name: "DueDate",
                table: "StudyQuestions");
        }
    }
}
