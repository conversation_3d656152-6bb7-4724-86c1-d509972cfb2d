﻿using System;
using System.Collections.Generic;
using System.Text;
using Volo.Abp.Domain.Entities;

namespace EdTech.Study.Exams
{
    public class SectionGroupQuestionEntity : Entity<Guid>
    {
        public SectionGroupQuestionEntity()
        {

        }

        public SectionGroupQuestionEntity(Guid key) : base(key)
        {
        }

        /// <summary>
        /// ID Phan thi
        /// </summary>
        public Guid SectionId { get; set; }
        /// <summary>
        /// Id Nhom cau hoi
        /// </summary>
        public Guid GroupQuestionId { get; set; }

        /// <summary>
        /// Phan thi
        /// </summary>
        public SectionEntity Section { get; set; }
        /// <summary>
        /// Nhom cau hoi
        /// </summary>
        public GroupQuestions.GroupQuestionEntity GroupQuestion { get; set; }
        /// <summary>
        /// Thứ tự câu hỏi trong phần thi (tùy chọn thêm).
        /// </summary>
        public int Order { get; set; }
        /// <summary>
        /// Cho biết câu hỏi có đang đồng bộ với mẫu hay không.
        /// Nếu là true, câu hỏi sẽ được đồng bộ với mẫu câu hỏi trong ngân hàng đề.
        /// và không thể chỉnh sửa nội dung câu hỏi này.
        /// </summary>
        public bool SyncGroupQuestion { get; set; }
    }
}
