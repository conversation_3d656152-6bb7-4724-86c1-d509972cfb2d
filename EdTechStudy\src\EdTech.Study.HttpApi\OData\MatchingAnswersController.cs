﻿using EdTech.Study.Questions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace EdTech.Study.OData
{
    public class MatchingAnswersController : BaseOdataEntityController<MatchingAnswerEntity>
    {
        public MatchingAnswersController(IRepository<MatchingAnswerEntity, Guid> repository) : base(repository)
        {
        }
    }
}