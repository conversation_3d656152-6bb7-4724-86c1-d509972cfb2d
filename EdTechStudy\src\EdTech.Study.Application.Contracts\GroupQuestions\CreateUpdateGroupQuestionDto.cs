﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using EdTech.Study.Questions;
using Volo.Abp.Application.Dtos;
using EdTech.Study.Exams;
using EdTech.Study.Exams.Dtos;
using System.Drawing;

namespace EdTech.Study.GroupQuestions
{
    public class CreateUpdateGroupQuestionDto
    {
        /// <summary>
        /// Id cua SectionGroupQuestion
        /// </summary>
        public Guid? Id { get; set; }

        /// <summary>
        /// Id cua GroupQuestion trong Client
        /// </summary>
        public Guid? ClientId { get; set; }

        /// <summary>
        /// Id cua Section trong phần thi
        /// </summary>
        public Guid? SectionId { get; set; }

        /// <summary>
        /// Id cua GroupQuestion trong phần thi
        /// </summary>
        public Guid? GroupQuestionId { get; set; }

        [Required]
        public string Content { get; set; }

        [Required]
        [EnumDataType(typeof(ContentFormatType))]
        public ContentFormatType ContentFormat { get; set; } = ContentFormatType.Html;

        public string? Instructions { get; set; }

        public List<Guid>? QuestionIds { get; set; }
        public List<CreateUpdateExamQuestionDto>? Questions { get; set; }

        [MaxLength(256)]
        public string? IdempotentKey { get; set; }

        public int Order { get; set; } = 0;
    }
}
