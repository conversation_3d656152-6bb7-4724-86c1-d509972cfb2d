import React, { useState, useEffect } from 'react';
import {
  Modal,
  Checkbox,
  Divider,
  Button,
  Space,
  Row,
  Col,
  Card,
  Typography,
  Form,
  Select,
  Input,
  Tag,
} from 'antd';
import {
  SettingOutlined,
  FilterOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import { LessonGrade } from '../../../interfaces/lessons/lessonGrade';
import { Subject } from '../../../interfaces/lessons/subject';

const { Text, Title } = Typography;
const { Option } = Select;

export interface ColumnConfig {
  key: string;
  title: string;
  visible: boolean;
  displayOrder: number;
}

export interface AdvancedFilter {
  field: string;
  operator: string;
  value: string | number | boolean;
}

interface ExamListAdvancedSettingsProps {
  visible: boolean;
  onClose: () => void;
  onApply: (columns: ColumnConfig[], filters: AdvancedFilter[]) => void;
  subjects: Subject[];
  grades: LessonGrade[];
  initialColumns: ColumnConfig[];
  initialFilters: AdvancedFilter[];
}

const operatorOptions = [
  { label: 'Equals', value: 'equals' },
  { label: 'Contains', value: 'contains' },
  { label: 'Greater than', value: 'gt' },
  { label: 'Less than', value: 'lt' },
  { label: 'Not equals', value: 'ne' },
];

const fieldOptions = [
  { label: 'Title', value: 'title' },
  { label: 'Subject', value: 'subject' },
  { label: 'Type', value: 'type' },
  { label: 'Grade', value: 'grade' },
  { label: 'Sections Count', value: 'sections' },
  { label: 'Questions Count', value: 'questions' },
];

const ExamListAdvancedSettings: React.FC<ExamListAdvancedSettingsProps> = ({
  visible,
  onClose,
  onApply,
  subjects,
  grades,
  initialColumns,
  initialFilters,
}) => {
  const [columns, setColumns] = useState<ColumnConfig[]>(initialColumns);
  const [filters, setFilters] = useState<AdvancedFilter[]>(
    initialFilters.length > 0
      ? initialFilters
      : [{ field: 'title', operator: 'contains', value: '' }]
  );
  const [form] = Form.useForm();

  // Reset form when modal is opened
  useEffect(() => {
    if (visible) {
      setColumns(initialColumns);
      setFilters(
        initialFilters.length > 0
          ? initialFilters
          : [{ field: 'title', operator: 'contains', value: '' }]
      );
      form.resetFields();
    }
  }, [visible, initialColumns, initialFilters, form]);

  const handleColumnChange = (columnKey: string, checked: boolean) => {
    setColumns(
      columns.map((col) =>
        col.key === columnKey ? { ...col, visible: checked } : col
      )
    );
  };

  const handleAddFilter = () => {
    setFilters([
      ...filters,
      { field: 'title', operator: 'contains', value: '' },
    ]);
  };

  const handleRemoveFilter = (index: number) => {
    setFilters(filters.filter((_, i) => i !== index));
  };

  const handleFilterChange = (index: number, field: string, value: any) => {
    const newFilters = [...filters];
    newFilters[index] = { ...newFilters[index], [field]: value };
    setFilters(newFilters);
  };

  const handleApply = () => {
    onApply(columns, filters);
    onClose();
  };

  const handleReset = () => {
    setColumns(initialColumns.map((col) => ({ ...col, visible: true })));
    setFilters([{ field: 'title', operator: 'contains', value: '' }]);
  };

  return (
    <Modal
      title={
        <div className="tailwind-flex tailwind-items-center">
          <SettingOutlined className="tailwind-mr-2" />
          <span>Advanced Settings</span>
        </div>
      }
      open={visible}
      onCancel={onClose}
      width={800}
      footer={[
        <Button key="reset" onClick={handleReset}>
          Reset
        </Button>,
        <Button key="cancel" onClick={onClose}>
          Cancel
        </Button>,
        <Button key="apply" type="primary" onClick={handleApply}>
          Apply
        </Button>,
      ]}
      className="tailwind-text-left"
    >
      <Row gutter={[24, 24]}>
        {/* Column Selection */}
        <Col span={24}>
          <Card
            title={
              <div className="tailwind-flex tailwind-items-center">
                <EyeOutlined className="tailwind-mr-2" />
                <span>Column Visibility</span>
              </div>
            }
            className="tailwind-shadow-sm"
          >
            <div className="tailwind-grid tailwind-grid-cols-3 tailwind-gap-4">
              {columns.map((column) => (
                <Checkbox
                  key={column.key}
                  checked={column.visible}
                  onChange={(e) =>
                    handleColumnChange(column.key, e.target.checked)
                  }
                >
                  {column.title}
                </Checkbox>
              ))}
            </div>
          </Card>
        </Col>

        {/* Advanced Filtering */}
        <Col span={24}>
          <Card
            title={
              <div className="tailwind-flex tailwind-items-center">
                <FilterOutlined className="tailwind-mr-2" />
                <span>Advanced Filtering</span>
              </div>
            }
            className="tailwind-shadow-sm"
          >
            <Form form={form} layout="vertical">
              {filters.map((filter, index) => (
                <div
                  key={index}
                  className="tailwind-mb-4 tailwind-p-3 tailwind-border tailwind-border-gray-200 tailwind-rounded-md tailwind-bg-gray-50"
                >
                  <div className="tailwind-flex tailwind-items-center tailwind-justify-between tailwind-mb-2">
                    <Text strong>Filter {index + 1}</Text>
                    {filters.length > 1 && (
                      <Button
                        danger
                        size="small"
                        onClick={() => handleRemoveFilter(index)}
                      >
                        Remove
                      </Button>
                    )}
                  </div>
                  <Row gutter={[16, 16]}>
                    <Col span={8}>
                      <Form.Item label="Field" className="tailwind-mb-0">
                        <Select
                          value={filter.field}
                          onChange={(value) =>
                            handleFilterChange(index, 'field', value)
                          }
                          className="tailwind-w-full"
                        >
                          {fieldOptions.map((option) => (
                            <Option key={option.value} value={option.value}>
                              {option.label}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="Operator" className="tailwind-mb-0">
                        <Select
                          value={filter.operator}
                          onChange={(value) =>
                            handleFilterChange(index, 'operator', value)
                          }
                          className="tailwind-w-full"
                        >
                          {operatorOptions.map((option) => (
                            <Option key={option.value} value={option.value}>
                              {option.label}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="Value" className="tailwind-mb-0">
                        {filter.field === 'subject' ? (
                          <Select
                            value={filter.value}
                            onChange={(value) =>
                              handleFilterChange(index, 'value', value)
                            }
                            className="tailwind-w-full"
                          >
                            {subjects.map((subject) => (
                              <Option key={subject.id} value={subject.id}>
                                {subject.name}
                              </Option>
                            ))}
                          </Select>
                        ) : filter.field === 'grade' ? (
                          <Select
                            value={filter.value}
                            onChange={(value) =>
                              handleFilterChange(index, 'value', value)
                            }
                            className="tailwind-w-full"
                          >
                            {grades.map((grade) => (
                              <Option key={grade.id} value={grade.id}>
                                {grade.name}
                              </Option>
                            ))}
                          </Select>
                        ) : filter.field === 'type' ? (
                          <Select
                            value={filter.value}
                            onChange={(value) =>
                              handleFilterChange(index, 'value', value)
                            }
                            className="tailwind-w-full"
                          >
                            <Option value={0}>Default</Option>
                            <Option value={1}>Official</Option>
                          </Select>
                        ) : (
                          <Input
                            value={filter.value as string}
                            onChange={(e) =>
                              handleFilterChange(index, 'value', e.target.value)
                            }
                          />
                        )}
                      </Form.Item>
                    </Col>
                  </Row>
                </div>
              ))}
              <Button
                type="dashed"
                onClick={handleAddFilter}
                block
                className="tailwind-mt-3"
              >
                + Add Filter
              </Button>
            </Form>
          </Card>
        </Col>

        {/* Current Applied Filters */}
        {filters.some((f) => f.value !== '') && (
          <Col span={24}>
            <div className="tailwind-p-3 tailwind-border tailwind-border-gray-200 tailwind-rounded-md tailwind-bg-gray-50">
              <Text strong className="tailwind-mb-2 tailwind-block">
                Applied Filters:
              </Text>
              <div className="tailwind-flex tailwind-flex-wrap tailwind-gap-2">
                {filters
                  .filter((f) => f.value !== '')
                  .map((filter, index) => {
                    const fieldLabel = fieldOptions.find(
                      (f) => f.value === filter.field
                    )?.label;
                    const operatorLabel = operatorOptions.find(
                      (o) => o.value === filter.operator
                    )?.label;
                    let valueLabel = filter.value;

                    if (filter.field === 'subject') {
                      valueLabel =
                        subjects.find((s) => s.id === filter.value)?.name ||
                        filter.value;
                    } else if (filter.field === 'grade') {
                      valueLabel =
                        grades.find((g) => g.id === filter.value)?.name ||
                        filter.value;
                    } else if (filter.field === 'type') {
                      valueLabel = filter.value === 0 ? 'Default' : 'Official';
                    }

                    return (
                      <Tag
                        key={index}
                        closable
                        onClose={() => handleRemoveFilter(index)}
                        color="blue"
                      >
                        {fieldLabel} {operatorLabel} {valueLabel}
                      </Tag>
                    );
                  })}
              </div>
            </div>
          </Col>
        )}
      </Row>
    </Modal>
  );
};

export default ExamListAdvancedSettings;
