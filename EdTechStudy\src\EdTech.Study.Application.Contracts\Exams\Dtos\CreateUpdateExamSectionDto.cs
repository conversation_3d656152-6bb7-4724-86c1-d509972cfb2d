using EdTech.Study.GroupQuestions;
using EdTech.Study.Questions.Dtos;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace EdTech.Study.Exams.Dtos
{
    public class CreateUpdateExamSectionDto
    {
        public Guid? Id { get; set; }

        public Guid ClientId { get; set; }

        [MaxLength(256)]
        public string? Title { get; set; }

        public string? Content { get; set; }

        [EnumDataType(typeof(ContentFormatType))]
        public ContentFormatType? ContentFormat { get; set; }

        public int OrderIndex { get; set; }

        public float? SectionScore { get; set; }

        public string? Instructions { get; set; }

        public List<CreateUpdateExamQuestionDto> Questions { get; set; } = new List<CreateUpdateExamQuestionDto>();
        public List<CreateUpdateGroupQuestionDto> GroupQuestions { get; set; } = new List<CreateUpdateGroupQuestionDto>();
        public string CreateIdempotentKey(string rootKey)
        {
            return $"{rootKey}_section-{OrderIndex}";
        }
    }
}