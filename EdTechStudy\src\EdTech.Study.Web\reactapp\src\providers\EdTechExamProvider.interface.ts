import { ThemeConfig } from 'antd';
import { ExamBase } from '../interfaces/exams/examBase';

export interface IEdTechExamProviderProps {
  /**
   * Initial data for the application
   */
  initialData?: {
    exams?: ExamBase[];
    subjects?: any[]; // Replace with the correct interface
    grades?: any[]; // Replace with the correct interface
  };

  /**
   * Theme configuration for Ant Design
   */
  theme?: ThemeConfig;

  /**
   * API endpoint base URL (optional, defaults to configured environment)
   */
  apiEndpoint?: string;

  /**
   * Authentication token (if required)
   */
  token?: string;

  /**
   * Callback triggered when an error occurs
   */
  onError?: (error: Error) => void;

  /**
   * Enable debug mode
   */
  debug?: boolean;
}
