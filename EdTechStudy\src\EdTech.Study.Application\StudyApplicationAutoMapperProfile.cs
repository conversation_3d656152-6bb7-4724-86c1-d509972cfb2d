﻿using AutoMapper;
using EdTech.Study.Exams;
using EdTech.Study.Exams.Dtos;
using EdTech.Study.Questions;
using EdTech.Study.Questions.Dtos;
using EdTech.Study.Menus;
using EdTech.Study.Subject;
using EdTech.Study.Subject.Dtos;
using System.Collections.Generic;
using System.Reflection;
using EdTech.Study.GroupQuestions;

namespace EdTech.Study;

public class StudyApplicationAutoMapperProfile : Profile
{
    public StudyApplicationAutoMapperProfile()
    {
        /* You can configure your AutoMapper mapping configuration here.
         * Alternatively, you can split your mapping configurations
         * into multiple profile classes for a better organization. */
        CreateMap<MenuEntity, MenuDto>().IgnoreAllNonExisting();
        CreateMap<CreateOrUpdateMenuDto, MenuEntity>().IgnoreAllNonExisting();

        CreateMap<QuestionDraftEntity, QuestionDraftDto>().IgnoreAllNonExisting()
         .ForMember(dest => dest.SubjectName, opt => opt.MapFrom(src => src.Subject != null ? src.Subject.Name : null));

        CreateMap<CreateQuestionDraftDto, QuestionDraftEntity>().IgnoreAllNonExisting()
            .ForMember(dest => dest.Options, opt => opt.Ignore()); // Explicitly ignore Options

        CreateMap<UpdateQuestionDraftDto, QuestionDraftEntity>().IgnoreAllNonExisting()
            .ForMember(dest => dest.Options, opt => opt.Ignore()); // Explicitly ignore Options

        CreateMap<QuestionOptionDraftEntity, QuestionOptionDraftDto>().IgnoreAllNonExisting();

        CreateMap<CreateQuestionOptionDraftDto, QuestionOptionDraftEntity>().IgnoreAllNonExisting()
            .ForMember(dest => dest.QuestionDraftId, opt => opt.MapFrom((src, dest) => dest.QuestionDraftId)) // Preserve existing QuestionDraftId
            .ForMember(dest => dest.QuestionDraft, opt => opt.Ignore());

        CreateMap<CreateUpdateQuestionOptionDraftDto, QuestionOptionDraftEntity>().IgnoreAllNonExisting()
            .ForMember(dest => dest.QuestionDraftId, opt => opt.MapFrom((src, dest) => dest.QuestionDraftId)) // Preserve existing QuestionDraftId
            .ForMember(dest => dest.QuestionDraft, opt => opt.Ignore());

        CreateMap<QuestionOptionDraftEntity, CreateQuestionOptionDraftDto>().IgnoreAllNonExisting();
        CreateMap<QuestionOptionDraftEntity, CreateUpdateQuestionOptionDraftDto>().IgnoreAllNonExisting();
        CreateMap<CreateUpdateQuestionOptionDraftDto, QuestionOptionDraftDto>().IgnoreAllNonExisting();

        // question store dtos
        CreateMap<QuestionDraftEntity, QuestionDraftDto>().IgnoreAllNonExisting();

        // Subjects
        CreateMap<Subject.Subject, SubjectDto>().IgnoreAllNonExisting();

        // Exam mappings
        CreateMap<ExamEntity, ExamDto>().IgnoreAllNonExisting();
        CreateMap<CreateUpdateExamDto, ExamEntity>().ForMember(x => x.Sections, m => m.Ignore()).IgnoreAllNonExisting();

        // ExamSection mappings
        CreateMap<SectionEntity, ExamSectionDto>().IgnoreAllNonExisting();
        CreateMap<CreateUpdateExamSectionDto, SectionEntity>()
            .ForMember(dest => dest.GroupQuestions, opt => opt.Ignore())
            .IgnoreAllNonExisting();

        // Question mappings
        CreateMap<QuestionEntity, QuestionDto>().IgnoreAllNonExisting();
        CreateMap<SectionQuestionEntity, ExamSectionQuestionDto>().IgnoreAllNonExisting();
        CreateMap<CreateUpdateExamQuestionDto, SectionQuestionEntity>().IgnoreAllNonExisting();

        CreateMap<QuestionOptionEntity, QuestionOptionDto>().IgnoreAllNonExisting().ForMember(x => x.ClientId, m => m.MapFrom(n => n.Id)); ;
        CreateMap<CreateQuestionOptionDto, QuestionOptionEntity>().IgnoreAllNonExisting();
        CreateMap<UpdateQuestionOptionDto, QuestionOptionEntity>().IgnoreAllNonExisting();

        CreateMap<FillInBlankAnswerEntity, FillInBlankAnswerDto>().IgnoreAllNonExisting().ForMember(x => x.ClientId, m => m.MapFrom(n => n.Id));
        CreateMap<MatchingAnswerEntity, MatchingAnswerDto>().IgnoreAllNonExisting().ForMember(x => x.ClientId, m => m.MapFrom(n => n.Id));
        CreateMap<MatchingItemEntity, MatchingItemDto>().IgnoreAllNonExisting().ForMember(x => x.ClientId, m => m.MapFrom(n => n.Id));


        CreateMap<CreateUpdateQuestionDto, QuestionEntity>().IgnoreAllNonExisting();
        CreateMap<CreateUpdateMatchingItemDto, MatchingItemEntity>().IgnoreAllNonExisting();
        CreateMap<CreateUpdateMatchingAnswerDto, MatchingAnswerEntity>().IgnoreAllNonExisting();
        CreateMap<CreateUpdateFillInBlankAnswer, FillInBlankAnswerEntity>().IgnoreAllNonExisting();

        // GroupQuestion mappings
        CreateMap<GroupQuestionEntity, GroupQuestionDto>().IgnoreAllNonExisting();
        CreateMap<CreateUpdateGroupQuestionDto, GroupQuestionEntity>()
            .ForMember(dest => dest.Questions, opt => opt.Ignore())
            .IgnoreAllNonExisting();
        CreateMap<GroupQuestionEntity, CreateUpdateGroupQuestionDto>()
            .ForMember(dest => dest.Questions, opt => opt.Ignore())
            .IgnoreAllNonExisting();
        CreateMap<GroupQuestionDto, CreateUpdateGroupQuestionDto>()
            .ForMember(dest => dest.Questions, opt => opt.Ignore())
            .IgnoreAllNonExisting();
        CreateMap<SectionGroupQuestionEntity, ExamSectionGroupQuestionDto>().IgnoreAllNonExisting();
    }
}

public static class AutoMapperExtensions
{
    public static IMappingExpression<TSource, TDestination> IgnoreAllNonExisting<TSource, TDestination>
    (this IMappingExpression<TSource, TDestination> expression)
    {
        var flags = BindingFlags.Public | BindingFlags.Instance;
        var sourceType = typeof(TSource);
        var destinationProperties = typeof(TDestination).GetProperties(flags);

        foreach (var property in destinationProperties)
        {
            if (sourceType.GetProperty(property.Name, flags) == null)
            {
                expression.ForMember(property.Name, opt => opt.Ignore());
            }
        }
        return expression;
    }
}
