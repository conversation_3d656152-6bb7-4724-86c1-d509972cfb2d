import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { Button, Typography } from 'antd';
import { useEffect, useRef, useState } from 'react';

export interface TruncatedTitleProps {
  title: string;
}

export const TruncatedTitle: React.FC<TruncatedTitleProps> = ({ title }) => {
  const [expanded, setExpanded] = useState(false);
  const [wasTruncated, setWasTruncated] = useState(false);
  const titleRef = useRef<HTMLDivElement>(null);
  const { Title } = Typography;
  useEffect(() => {
    const checkTruncation = () => {
      if (titleRef.current) {
        const element = titleRef.current;
        const isTruncated = element.scrollWidth > element.clientWidth;

        if (isTruncated) {
          setWasTruncated(true);
        }
      }
    };

    if (!expanded) {
      checkTruncation();
    }

    window.addEventListener('resize', checkTruncation);
    return () => {
      window.removeEventListener('resize', checkTruncation);
    };
  }, [title, expanded]);

  const toggleExpand = () => {
    setExpanded(!expanded);
  };

  return (
    <div className="tailwind-flex tailwind-items-start tailwind-w-full">
      <div className="tailwind-flex-1 tailwind-min-w-0">
        <Title
          ref={titleRef}
          level={3}
          className="tailwind-m-0"
          style={{
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: expanded ? 'normal' : 'nowrap',
            wordBreak: expanded ? 'break-word' : 'normal',
            lineHeight: '1.3',
            marginBottom: expanded ? '8px' : '0',
          }}
        >
          {title}
        </Title>
      </div>
      {wasTruncated && (
        <Button
          type="text"
          size="small"
          icon={expanded ? <UpOutlined /> : <DownOutlined />}
          onClick={toggleExpand}
          className="tailwind-flex tailwind-items-center tailwind-justify-center tailwind-flex-shrink-0"
          style={{ marginTop: '8px', marginLeft: '4px' }}
        />
      )}
    </div>
  );
};
