﻿@page "~/questions"
@model EdTech.Study.Web.Pages.Questions.IndexModel
@using Microsoft.Extensions.Localization
@using EdTech.Study.Localization
@using Newtonsoft.Json
@using Newtonsoft.Json.Serialization
@inject IStringLocalizer<StudyResource> L

@{
}
                   
@section Styles{
    <link rel="stylesheet" href="/EdTech/reactapp/assets/css/app.css" asp-append-version="true">
    <link rel="stylesheet" href="/EdTech/reactapp/assets/css/vendor.css" asp-append-version="true">
}

<div id="QuestionPage"></div>

@section Scripts {
    <script src="/EdTech/reactapp/runtime.js" asp-append-version="true"></script>
    <script type="module" src="/EdTech/reactapp/QuestionPage.js" asp-append-version="true"></script>
}