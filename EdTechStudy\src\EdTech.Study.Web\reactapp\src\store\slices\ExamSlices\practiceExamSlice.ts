import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import { ExamBase, ExamSection } from '../../../interfaces/exams/examBase';
import ExamsService from '../../../services/exams/ExamsService';
import { Guid } from 'guid-typescript';
import { GetExamListDto } from '../../../interfaces/exams/examDtos';
import { ExamStatus } from '../../../interfaces/exams/examEnums';
import { mapSectionFromApi } from './examDataManagerSlice';
import PracticeExamService from '../../../services/exams/PracticeExamService';

/**
 * Interface for the Exam Data Manager state
 */
export interface IPracticeExamState {
  // All available exams
  exams: ExamBase[];

  // Current exam being edited or viewed
  currentExam: ExamBase | null;
  // Loading state
  loading: boolean;
  // Error message
  error: string | null;

  // Pagination
  totalCount: number;
  currentPage: number;
  pageSize: number;
  skipCount: number;
  maxResultCount: number;

  // Search parameters
  filter: string | undefined;
  examType: number | undefined;
  status: ExamStatus | undefined;
  subjectId: string | undefined;
  gradeId: string | undefined;
  subjectIds: string[] | undefined;
  gradeIds: string[] | undefined;
  startDate: string | undefined;
  endDate: string | undefined;
  sorting: string | undefined;
}

/**
 * Initial state for the Exam Data Manager
 */
const initialState: IPracticeExamState = {
  exams: [],
  currentExam: null,
  loading: false,
  error: null,
  totalCount: 0,
  currentPage: 1,
  pageSize: 12,
  skipCount: 0,
  maxResultCount: 12,
  filter: undefined,
  examType: undefined,
  status: undefined,
  subjectId: undefined,
  gradeId: undefined,
  subjectIds: undefined,
  gradeIds: undefined,
  startDate: undefined,
  endDate: undefined,
  sorting: undefined,
};

/**
 * Exam Data Manager slice
 */
export const fetchPracticeExams = createAsyncThunk(
  'examDataManager/fetchExams',
  async (params: GetExamListDto | undefined, thunk) => {
    return await PracticeExamService.fetchPracticeExams(params)(thunk.dispatch);
  }
);

/**
 * Fetch an exam by ID
 */
export const fetchPracticeExamById = createAsyncThunk(
  'examDataManager/fetchExamById',
  async (examId: string, { dispatch }) => {
    const response = await ExamsService.fetchExamById(examId)(dispatch);
    return response;
  }
);

const practiceExamSlice = createSlice({
  name: 'practiceExam',
  initialState,
  reducers: {
    /**
     * Set all exams
     */
    setPracticeExams: (state, action: PayloadAction<ExamBase[]>) => {
      try {
        let payload = action.payload;
        payload = payload.map(
          (p) =>
            ({
              ...p,
              clientId: p.id ?? Guid.create().toString(),
            } as ExamBase)
        );
        state.exams = payload;
      } catch (error) {
        console.error(error);
      }
    },

    /**
     * Set total count for pagination
     */
    setPracticeExamTotalCount: (state, action: PayloadAction<number>) => {
      state.totalCount = action.payload;
    },

    /**
     * Set current page for pagination
     */
    setPracticeExamCurrentPage: (state, action: PayloadAction<number>) => {
      state.currentPage = action.payload;
    },

    /**
     * Set page size for pagination
     */
    setPracticeExamPageSize: (state, action: PayloadAction<number>) => {
      state.pageSize = action.payload;
      state.maxResultCount = action.payload;
    },

    /**
     * Set skip count for pagination
     */
    setPracticeExamSkipCount: (state, action: PayloadAction<number>) => {
      state.skipCount = action.payload;
    },

    /**
     * Set max result count for pagination
     */
    setPracticeExamMaxResultCount: (state, action: PayloadAction<number>) => {
      state.maxResultCount = action.payload;
    },

    /**
     * Update pagination settings
     */
    updatePracticeExamPagination: (
      state,
      action: PayloadAction<{
        page?: number;
        pageSize?: number;
        totalCount?: number;
      }>
    ) => {
      const { page, pageSize, totalCount } = action.payload;

      if (totalCount !== undefined) {
        state.totalCount = totalCount;
      }

      if (pageSize !== undefined) {
        state.pageSize = pageSize;
        state.maxResultCount = pageSize;
      }

      if (page !== undefined) {
        state.currentPage = page;
        state.skipCount = (page - 1) * state.pageSize;
      }
    },

    /**
     * Set loading state
     */
    setPracticeExamLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },

    /**
     * Set error state
     */
    setPracticeExamError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },

    /**
     * Reset state to initial values
     */
    resetPracticeExamState: () => {
      return initialState;
    },

    /**
     * Update search parameters
     */
    updatePracticeExamSearchParams: (
      state,
      action: PayloadAction<
        Partial<GetExamListDto & { subjectIds?: string[]; gradeIds?: string[] }>
      >
    ) => {
      const {
        filter,
        examType,
        status,
        subjectId,
        gradeId,
        subjectIds,
        gradeIds,
        startDate,
        endDate,
        sorting,
      } = action.payload;

      if (filter !== undefined) state.filter = filter;
      if (examType !== undefined) state.examType = examType;
      if (status !== undefined) state.status = status;
      if (subjectId !== undefined) state.subjectId = subjectId;
      if (gradeId !== undefined) state.gradeId = gradeId;
      if (subjectIds !== undefined) state.subjectIds = subjectIds;
      if (gradeIds !== undefined) state.gradeIds = gradeIds;
      if (startDate !== undefined) state.startDate = startDate;
      if (endDate !== undefined) state.endDate = endDate;
      if (sorting !== undefined) state.sorting = sorting;
    },

    /**
     * Reset search parameters
     */
    resetPracticeExamSearchParams: (state) => {
      state.filter = undefined;
      state.examType = undefined;
      state.status = undefined;
      state.subjectId = undefined;
      state.gradeId = undefined;
      state.subjectIds = undefined;
      state.gradeIds = undefined;
      state.startDate = undefined;
      state.endDate = undefined;
      state.sorting = undefined;
    },
  },
  extraReducers: (builder) => {
    // Handle async thunk actions
    builder
      // fetchPracticeExams
      .addCase(fetchPracticeExams.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchPracticeExams.fulfilled, (state, action) => {
        state.loading = false;
      })
      .addCase(fetchPracticeExams.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch exams';
      })

      // fetchPracticeExamById
      .addCase(fetchPracticeExamById.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchPracticeExamById.fulfilled, (state, action) => {
        state.loading = false;
        if (action.payload) {
          state.currentExam = {
            ...action.payload,
            sections: action.payload.sections
              ?.map((section: any, index: number) =>
                mapSectionFromApi(section, state.currentExam?.id, index)
              )
              .sort(
                (a: ExamSection, b: ExamSection) => a.orderIndex - b.orderIndex
              ),
          } as ExamBase;
        }
      })
      .addCase(fetchPracticeExamById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch exam';
      });
  },
});

// Export actions
export const {
  setPracticeExams,
  setPracticeExamLoading,
  setPracticeExamError,
  resetPracticeExamState,
  setPracticeExamTotalCount,
  setPracticeExamCurrentPage,
  setPracticeExamPageSize,
  setPracticeExamSkipCount,
  setPracticeExamMaxResultCount,
  updatePracticeExamPagination,
  updatePracticeExamSearchParams,
  resetPracticeExamSearchParams,
} = practiceExamSlice.actions;

// Export reducer
export default practiceExamSlice.reducer;
