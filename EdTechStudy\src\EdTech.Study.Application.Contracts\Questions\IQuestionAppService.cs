using EdTech.Study.Exams.Dtos;
using EdTech.Study.GroupQuestions;
using EdTech.Study.Questions.Dtos;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace EdTech.Study.Questions
{
    public interface IQuestionAppService :
        ICrudAppService<
            QuestionDto,
            Guid,
            QuestionPagedAndSortedResultRequestDto,
            CreateUpdateQuestionDto>
    {
        Task<QuestionDto> PublishAsync(Guid id);
        Task<QuestionDto> UnpublishAsync(Guid id);
        Task<List<QuestionDto>> GetByIdsAsync(List<Guid> ids);
        Task<PagedResultDto<QuestionDto>> GetQuestionsBySubjectAsync(Guid subjectId, QuestionPagedAndSortedResultRequestDto input);
        Task<QuestionDto> UpdateStatusAsync(Guid id, UpdateQuestionStatusRequest status);
        Task<List<Guid>> DeleteManyAsync(List<Guid> ids);
        Task<BatchUpdateStatusResultDto> UpdateBatchStatusAsync(BatchUpdateStatusRequestDto request);
        Task<List<ExamDto>> GetExamsByQuestionIdAsync(Guid questionId);
        Task<GroupQuestionDto> GetGroupQuestionByQuestionIdAsync(Guid questionId);
        Task<GroupQuestionDto> UpdateGroupQuestionAsync(Guid groupQuestionId, UpdateGroupInfoDto input);
        Task<QuestionDto> AssignToUserAsync(Guid id, AssignQuestionDto input);
        Task<QuestionDto> UnassignAsync(Guid id);
        Task<List<QuestionDto>> GetQuestionsByAssignedUserAsync(Guid userId);
        Task<BatchAssignResultDto> BatchAssignToUserAsync(BatchAssignQuestionsDto input);
        Task<BatchUnassignResultDto> BatchUnassignQuestionsAsync(List<Guid> questionIds);
    }
}
