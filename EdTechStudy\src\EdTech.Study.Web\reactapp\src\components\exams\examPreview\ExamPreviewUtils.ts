import {
  ExamBase,
  ExamGroupQuestion,
  ExamQuestion,
  ExamSection,
  QuestionListItem,
} from '../../../interfaces/exams/examBase';
import { BaseAnswer } from '../../../interfaces/quizs/questionBase';

/**
 * Sort questions by order index
 */
export const sortQuestionsByOrder = (
  questions: ExamQuestion[]
): ExamQuestion[] => {
  return [...questions].sort((a, b) => (a.order || 0) - (b.order || 0));
};

/**
 * Sort group questions by order index
 */
export const sortGroupQuestionsByOrder = (
  groups: ExamGroupQuestion[]
): ExamGroupQuestion[] => {
  return [...groups].sort((a, b) => (a.order || 0) - (b.order || 0));
};

/**
 * Get all questions in a section (both independent and grouped)
 */
export const getAllQuestionsInSection = (
  section: ExamSection
): ExamQuestion[] => {
  const allQuestions: ExamQuestion[] = [];

  // Add independent questions
  if (section.questions) {
    allQuestions.push(...sortQuestionsByOrder(section.questions));
  }

  // Add questions from groups (sorted by group order, then question order)
  if (section.groupQuestions) {
    const sortedGroups = sortGroupQuestionsByOrder(section.groupQuestions);
    sortedGroups.forEach((group) => {
      if (group.questions) {
        allQuestions.push(...sortQuestionsByOrder(group.questions));
      }
    });
  }

  return allQuestions;
};

export const getQuestionNumberInSection = (
  section: ExamSection,
  questionId: string
): number => {
  let questionNumber = 0;

  // Duyệt qua câu hỏi độc lập trước
  if (section.questions) {
    const sortedQuestions = sortQuestionsByOrder(section.questions);
    for (let i = 0; i < sortedQuestions.length; i++) {
      questionNumber++;
      if (sortedQuestions[i].clientId === questionId) {
        return questionNumber;
      }
    }
  }

  // Sau đó duyệt qua các nhóm câu hỏi
  if (section.groupQuestions) {
    const sortedGroups = sortGroupQuestionsByOrder(section.groupQuestions);
    for (const group of sortedGroups) {
      if (group.questions) {
        const sortedGroupQuestions = sortQuestionsByOrder(group.questions);
        for (const question of sortedGroupQuestions) {
          questionNumber++;
          if (question.clientId === questionId) {
            return questionNumber;
          }
        }
      }
    }
  }

  return questionNumber;
};

/**
 * Find the position of a question within a section
 */
export const findQuestionPosition = (
  section: ExamSection,
  targetQuestionId: string
): {
  type: 'independent' | 'group';
  index: number;
  groupIndex?: number;
} | null => {
  // Search in independent questions
  const independentIndex =
    section.questions?.findIndex(
      (q: ExamQuestion) => q.clientId === targetQuestionId
    ) ?? -1;

  if (independentIndex !== -1) {
    return { type: 'independent', index: independentIndex };
  }

  // Search in group questions
  if (section.groupQuestions) {
    for (
      let groupIndex = 0;
      groupIndex < section.groupQuestions.length;
      groupIndex++
    ) {
      const group = section.groupQuestions[groupIndex];
      const questionIndex =
        group.questions?.findIndex(
          (q: ExamQuestion) => q.clientId === targetQuestionId
        ) ?? -1;

      if (questionIndex !== -1) {
        return { type: 'group', index: questionIndex, groupIndex };
      }
    }
  }

  return null;
};

/**
 * Calculate total questions in an exam
 */
export const calculateTotalQuestions = (exam: ExamBase): number => {
  if (!exam.sections) return 0;

  return exam.sections.reduce((total: number, section: ExamSection) => {
    const independentQuestions = section.questions?.length || 0;
    const groupQuestions =
      section.groupQuestions?.reduce(
        (groupTotal: number, group: ExamGroupQuestion) => {
          return groupTotal + (group.questions?.length || 0);
        },
        0
      ) || 0;

    return total + independentQuestions + groupQuestions;
  }, 0);
};

/**
 * Calculate total questions in a section
 */
export const calculateSectionQuestions = (section: ExamSection): number => {
  const independentQuestions = section.questions?.length || 0;
  const groupQuestions =
    section.groupQuestions?.reduce(
      (groupTotal: number, group: ExamGroupQuestion) => {
        return groupTotal + (group.questions?.length || 0);
      },
      0
    ) || 0;

  return independentQuestions + groupQuestions;
};

/**
 * Create question list for sidebar navigation
 */
export const createQuestionList = (
  sections: ExamSection[]
): QuestionListItem[] => {
  const questionList: QuestionListItem[] = [];
  let globalQuestionIndex = 0;

  sections.forEach((section, sectionIndex) => {
    // Add independent questions
    if (section.questions) {
      const sortedQuestions = sortQuestionsByOrder(section.questions);
      sortedQuestions.forEach((question, questionIndex) => {
        questionList.push({
          sectionIndex,
          questionIndex,
          questionType: 'independent',
          question,
          displayIndex: globalQuestionIndex + 1,
        });
        globalQuestionIndex++;
      });
    }

    // Add questions from groups
    if (section.groupQuestions) {
      const sortedGroups = sortGroupQuestionsByOrder(section.groupQuestions);

      sortedGroups.forEach((group, groupIndex) => {
        if (group.questions) {
          const sortedGroupQuestions = sortQuestionsByOrder(group.questions);
          sortedGroupQuestions.forEach((question, questionIndex) => {
            questionList.push({
              sectionIndex,
              questionIndex,
              questionType: 'group',
              groupIndex,
              question,
              displayIndex: globalQuestionIndex + 1,
              groupTitle: group.content,
            });
            globalQuestionIndex++;
          });
        }
      });
    }
  });

  return questionList;
};

/**
 * Find the global index of current question
 */
export const getCurrentQuestionGlobalIndex = (
  sections: ExamSection[],
  currentSection: number,
  currentQuestionIndex: number,
  inExamMode: boolean
): number => {
  if (!inExamMode) return -1;

  const questionList = createQuestionList(sections);
  const currentSectionQuestions = getAllQuestionsInSection(
    sections[currentSection]
  );
  const currentQuestionId =
    currentSectionQuestions[currentQuestionIndex]?.clientId;

  return questionList.findIndex(
    (item) => item.question.clientId === currentQuestionId
  );
};

/**
 * Check if a question has been answered
 */
export const isQuestionAnswered = (
  questionId: string,
  selectedAnswers: Record<string, BaseAnswer | BaseAnswer[]>
): boolean => {
  return (
    questionId in selectedAnswers && selectedAnswers[questionId] !== undefined
  );
};

/**
 * Calculate score for the exam
 */
export const calculateExamScore = (
  exam: ExamBase,
  selectedAnswers: Record<string, BaseAnswer | BaseAnswer[]>,
  isAnswerCorrect: (
    question: ExamQuestion,
    answer: BaseAnswer | BaseAnswer[] | undefined
  ) => boolean
): {
  correctAnswers: number;
  totalQuestions: number;
  percentage: number;
  actualScore: number;
} => {
  if (!exam.sections) {
    return {
      correctAnswers: 0,
      totalQuestions: 0,
      percentage: 0,
      actualScore: 0,
    };
  }

  let correctAnswers = 0;
  let totalQuestions = 0;
  let actualScore = 0;

  exam.sections.forEach((section: ExamSection) => {
    // Calculate from independent questions
    if (section.questions) {
      section.questions.forEach((question: ExamQuestion) => {
        totalQuestions++;
        const userAnswer = selectedAnswers[question.clientId];
        const isCorrect = isAnswerCorrect(question, userAnswer);
        if (isCorrect) {
          correctAnswers++;
          actualScore += question.points || 1;
        }
      });
    }

    // Calculate from group questions
    if (section.groupQuestions) {
      section.groupQuestions.forEach((group: ExamGroupQuestion) => {
        if (group.questions) {
          group.questions.forEach((question: ExamQuestion) => {
            totalQuestions++;
            const userAnswer = selectedAnswers[question.clientId];
            const isCorrect = isAnswerCorrect(question, userAnswer);
            if (isCorrect) {
              correctAnswers++;
              actualScore += question.points || 1;
            }
          });
        }
      });
    }
  });

  const percentage =
    totalQuestions > 0
      ? Math.round((correctAnswers / totalQuestions) * 100)
      : 0;

  return { correctAnswers, totalQuestions, percentage, actualScore };
};

/**
 * Find a question by its clientId across all sections and groups
 */
export const findQuestionById = (
  sections: ExamSection[],
  questionId: string
): ExamQuestion | null => {
  for (const section of sections) {
    // Search in independent questions
    if (section.questions) {
      const found = section.questions.find(
        (q: ExamQuestion) => q.clientId === questionId
      );
      if (found) return found;
    }

    // Search in group questions
    if (section.groupQuestions) {
      for (const group of section.groupQuestions) {
        if (group.questions) {
          const found = group.questions.find(
            (q: ExamQuestion) => q.clientId === questionId
          );
          if (found) return found;
        }
      }
    }
  }

  return null;
};

/**
 * Get group information for a question
 */
export const getQuestionGroupInfo = (
  sections: ExamSection[],
  questionId: string
): ExamGroupQuestion | null => {
  for (const section of sections) {
    if (section.groupQuestions) {
      for (const group of section.groupQuestions) {
        if (
          group.questions?.some((q: ExamQuestion) => q.clientId === questionId)
        ) {
          return group;
        }
      }
    }
  }

  return null;
};

/**
 * Navigate to next question in exam mode
 */
export const getNextQuestionNavigation = (
  sections: ExamSection[],
  currentSection: number,
  currentQuestionIndex: number
): {
  newSection: number;
  newQuestionIndex: number;
  questionId?: string;
  hasNext: boolean;
} => {
  const section = sections[currentSection];
  const allQuestions = getAllQuestionsInSection(section);

  if (currentQuestionIndex < allQuestions.length - 1) {
    // Next question in same section
    const nextQuestionId = allQuestions[currentQuestionIndex + 1]?.clientId;
    return {
      newSection: currentSection,
      newQuestionIndex: currentQuestionIndex + 1,
      questionId: nextQuestionId,
      hasNext: true,
    };
  } else if (currentSection < sections.length - 1) {
    // First question of next section
    const nextSection = sections[currentSection + 1];
    const nextSectionQuestions = getAllQuestionsInSection(nextSection);
    const nextSectionFirstQuestionId = nextSectionQuestions[0]?.clientId;

    return {
      newSection: currentSection + 1,
      newQuestionIndex: 0,
      questionId: nextSectionFirstQuestionId,
      hasNext: true,
    };
  }

  return {
    newSection: currentSection,
    newQuestionIndex: currentQuestionIndex,
    hasNext: false,
  };
};

/**
 * Navigate to previous question in exam mode
 */
export const getPreviousQuestionNavigation = (
  sections: ExamSection[],
  currentSection: number,
  currentQuestionIndex: number
): {
  newSection: number;
  newQuestionIndex: number;
  questionId?: string;
  hasPrevious: boolean;
} => {
  const section = sections[currentSection];
  const allQuestions = getAllQuestionsInSection(section);

  if (currentQuestionIndex > 0) {
    // Previous question in same section
    const prevQuestionId = allQuestions[currentQuestionIndex - 1]?.clientId;
    return {
      newSection: currentSection,
      newQuestionIndex: currentQuestionIndex - 1,
      questionId: prevQuestionId,
      hasPrevious: true,
    };
  } else if (currentSection > 0) {
    // Last question of previous section
    const prevSection = sections[currentSection - 1];
    const prevSectionQuestions = getAllQuestionsInSection(prevSection);
    const prevQuestionIndex = prevSectionQuestions.length - 1;
    const prevSectionLastQuestionId =
      prevSectionQuestions[prevQuestionIndex]?.clientId;

    return {
      newSection: currentSection - 1,
      newQuestionIndex: prevQuestionIndex,
      questionId: prevSectionLastQuestionId,
      hasPrevious: true,
    };
  }

  return {
    newSection: currentSection,
    newQuestionIndex: currentQuestionIndex,
    hasPrevious: false,
  };
};

/**
 * Validate exam structure for group questions
 */
export const validateExamStructure = (
  exam: ExamBase
): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];

  if (!exam.sections || exam.sections.length === 0) {
    errors.push('Exam must have at least one section');
    return { isValid: false, errors };
  }

  exam.sections.forEach((section: ExamSection, sectionIndex: number) => {
    const totalQuestions = calculateSectionQuestions(section);

    if (totalQuestions === 0) {
      errors.push(
        `Section ${sectionIndex + 1} must have at least one question`
      );
    }

    // Validate independent questions
    if (section.questions) {
      section.questions.forEach(
        (question: ExamQuestion, questionIndex: number) => {
          if (!question.clientId) {
            errors.push(
              `Question ${questionIndex + 1} in section ${
                sectionIndex + 1
              } missing clientId`
            );
          }
          if (!question.content) {
            errors.push(
              `Question ${questionIndex + 1} in section ${
                sectionIndex + 1
              } missing content`
            );
          }
        }
      );
    }

    // Validate group questions
    if (section.groupQuestions) {
      section.groupQuestions.forEach(
        (group: ExamGroupQuestion, groupIndex: number) => {
          if (!group.clientId) {
            errors.push(
              `Group ${groupIndex + 1} in section ${
                sectionIndex + 1
              } missing clientId`
            );
          }
          if (!group.content) {
            errors.push(
              `Group ${groupIndex + 1} in section ${
                sectionIndex + 1
              } missing title`
            );
          }
          if (!group.questions || group.questions.length === 0) {
            errors.push(
              `Group ${groupIndex + 1} in section ${
                sectionIndex + 1
              } must have at least one question`
            );
          }

          if (group.questions) {
            group.questions.forEach(
              (question: ExamQuestion, questionIndex: number) => {
                if (!question.clientId) {
                  errors.push(
                    `Question ${questionIndex + 1} in group ${
                      groupIndex + 1
                    }, section ${sectionIndex + 1} missing clientId`
                  );
                }
                if (!question.content) {
                  errors.push(
                    `Question ${questionIndex + 1} in group ${
                      groupIndex + 1
                    }, section ${sectionIndex + 1} missing content`
                  );
                }
              }
            );
          }
        }
      );
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Export exam data for printing or sharing
 */
export const exportExamData = (
  exam: ExamBase
): {
  title: string;
  sections: {
    title: string;
    instructions?: string;
    content: string;
    independentQuestions: ExamQuestion[];
    groupQuestions: {
      title: string;
      instructions?: string;
      questions: ExamQuestion[];
    }[];
    totalQuestions: number;
  }[];
  totalQuestions: number;
} => {
  const exportData = {
    title: exam.title,
    sections:
      exam.sections?.map((section: ExamSection) => ({
        title: section.title,
        instructions: section.instructions,
        content: section.content,
        independentQuestions: section.questions || [],
        groupQuestions:
          section.groupQuestions?.map((group: ExamGroupQuestion) => ({
            title: group.content || '',
            instructions: group.instructions,
            questions: group.questions || [],
          })) || [],
        totalQuestions: calculateSectionQuestions(section),
      })) || [],
    totalQuestions: calculateTotalQuestions(exam),
  };

  return exportData;
};
