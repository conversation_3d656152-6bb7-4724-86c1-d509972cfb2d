﻿using JetBrains.Annotations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Auditing;

namespace EdTech.Study.Users
{
    public class IdentityUserDto
    {
        public Guid Id { get; set; }

        public Guid? TenantId { get; set; }

        /// <summary>
        /// Gets or sets the user name for this user.
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// Gets or sets the Name for the user.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the Surname for the user.
        /// </summary>
        public string? Surname { get; set; }

        /// <summary>
        /// Gets or sets the email address for this user.
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// Gets or sets a telephone number for the user.
        /// </summary>
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// Gets or sets a flag indicating if the user is active.
        /// </summary>
        public bool IsActive { get; set; }
    }
}
