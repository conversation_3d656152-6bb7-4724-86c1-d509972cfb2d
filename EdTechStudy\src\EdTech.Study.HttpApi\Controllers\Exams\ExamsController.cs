﻿using EdTech.Study.Enum;
using EdTech.Study.Exams;
using EdTech.Study.Exams.Dtos;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;

namespace EdTech.Study.Controllers.Exams
{
    public class ExamsController : ControllerBase, IExamAppService
    {
        private readonly IExamAppService _examAppService;

        public ExamsController(IExamAppService examAppService)
        {
            _examAppService = examAppService;
        }

        public Task<ExamDto> CreateAsync(CreateUpdateExamDto input)
        {
            return _examAppService.CreateAsync(input);
        }

        public Task<ExamDto> CreateOrUpdateAsync(CreateUpdateExamDto input)
        {
            return _examAppService.CreateOrUpdateAsync(input);
        }

        public Task DeleteAsync(Guid id)
        {
            return _examAppService.DeleteAsync(id);
        }

        public Task<ExamDto> GetAsync(Guid id)
        {
            return _examAppService.GetAsync(id);
        }

        public Task<PagedResultDto<ExamDto>> GetListAsync(GetExamListDto input)
        {
            return _examAppService.GetListAsync(input);
        }

        public Task<PagedResultDto<ExamDto>> GetListExamAsync(GetExamListDto input)
        {
            return _examAppService.GetListExamAsync(input);
        }

        public Task<ExamDto> UpdateAsync(Guid id, CreateUpdateExamDto input)
        {
            return _examAppService.UpdateAsync(id, input);
        }

        public Task UpdateExamStatusAsync(Guid examId, UpdateStatusRequest input)
        {
            return _examAppService.UpdateExamStatusAsync(examId, input);
        }
    }
}