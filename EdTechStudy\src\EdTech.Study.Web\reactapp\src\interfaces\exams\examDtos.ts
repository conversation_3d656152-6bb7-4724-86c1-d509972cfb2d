import { QuestionType } from '../quizs/questionBase';
import {
  ContentFormatType,
  ExamSourceType,
  ExamStatus,
  ExamType,
} from './examEnums';

export interface CreateUpdateExamDto {
  id?: string;
  title: string;
  description?: string;
  examCode?: string;
  examType: ExamType;
  examPeriod?: string;
  examDate?: Date;
  duration?: number;
  totalScore?: number;
  status: ExamStatus;
  subjectId?: string;
  gradeId?: string;
  sourceType: ExamSourceType;
  sections: CreateUpdateExamSectionDto[];
}

export interface CreateUpdateExamSectionDto {
  id?: string;
  clientId: string;
  title: string;
  content: string;
  contentFormat?: ContentFormatType | string;
  orderIndex: number;
  sectionScore?: number;
  instructions?: string;
  questions: CreateUpdateQuestionDto[];
  groupQuestions: CreateUpdateGroupQuestionDto[];
}

export interface CreateUpdateQuestionDto {
  id?: string;
  clientId: string;
  title: string;
  content: string;
  contentFormat: ContentFormatType | string;
  questionType: QuestionType;
  difficulty: number;
  comment?: string;
  subjectId?: string;
  gradeId?: string;
  shuffleOptions: boolean;
  explanation?: string;
  sourceType: ExamSourceType;
  topics?: string;
  tags?: string;
  options?: CreateQuestionOptionDto[];
  matchingItems?: CreateUpdateMatchingItemDto[];
  matchingAnswers?: CreateUpdateMatchingAnswerDto[];
  fillInBlankAnswers?: CreateUpdateFillInBlankAnswer[];
  syncQuestion: boolean;
  lastSyncQuestionId?: string;
  order: number;
  score?: number;
  questionId?: string;
  status: ExamStatus;
  correctAnswer?: string;
}

export interface CreateQuestionOptionDto {
  id?: string;
  clientId: string;
  content: string;
  contentFormat: ContentFormatType | string;
  isCorrect: boolean;
  order: number;
  explanation: string;
  score?: number;
}

export interface CreateUpdateMatchingItemDto {
  id?: string;
  clientId: string;
  type: MatchingItemType;
  content: string;
  contentFormat: ContentFormatType | string;
  order: number;
}

export interface CreateUpdateFillInBlankAnswer {
  id?: string;
  clientId: string;
  blankIndex: number;
  correctAnswers: string[];
  caseSensitive: boolean;
  feedback?: string;
  score?: number;
}

export enum MatchingItemType {
  Premise = 0,
  Response = 1,
  // Add other values as needed
}

// The CreateUpdateMatchingAnswerDto was referenced but not defined in the provided files
export interface CreateUpdateMatchingAnswerDto {
  id?: string;
  clientId: string;
  leftItemId: string;
  rightItemId: string;
  // Add other properties based on your needs
}

export interface CreateUpdateGroupQuestionDto {
  id?: string;
  clientId: string;
  groupQuestionId?: string;
  content: string;
  contentFormat: ContentFormatType | string;
  instructions?: string;
  questionIds?: string[];
  questions?: CreateUpdateQuestionDto[];
  idempotentKey?: string;
  order: number;
}

/**
 * Data transfer object for exam list requests with filtering, sorting, and pagination
 * Matches the C# GetExamListDto in the backend
 */
export interface GetExamListDto {
  /**
   * Text to filter exams by title, description, etc.
   */
  filter?: string;

  /**
   * Filter by exam type (Test15Minutes, Test45Minutes, etc.)
   */
  examType?: number;

  /**
   * Filter by exam date range (start date)
   */
  startDate?: string;

  /**
   * Filter by exam date range (end date)
   */
  endDate?: string;

  /**
   * Filter by exam status (Draft, Published, Archived)
   */
  status?: number;

  /**
   * Filter by subject ID
   */
  subjectId?: string;

  /**
   * Filter by grade ID
   */
  gradeId?: string;

  /**
   * Number of items to skip for pagination
   * From PagedResultRequestDto
   */
  skipCount?: number;

  /**
   * Maximum number of results to return
   * From PagedResultRequestDto
   */
  maxResultCount?: number;

  /**
   * Sorting parameter in format "propertyName asc|desc"
   * From PagedAndSortedResultRequestDto
   */
  sorting?: string;
}
