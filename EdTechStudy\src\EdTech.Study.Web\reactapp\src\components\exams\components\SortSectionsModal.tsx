import React, { useState } from 'react';
import { Modal, Button, Typography, Space } from 'antd';
import {
  MenuOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
} from '@ant-design/icons';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { ExamSection } from '../../../interfaces/exams/examBase';

const { Title, Text } = Typography;

interface SortableSectionItemProps {
  section: ExamSection;
  index: number;
}

const SortableSectionItem: React.FC<SortableSectionItemProps> = ({
  section,
  index,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: section.clientId });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    backgroundColor: isDragging ? 'var(--edtt-color-bg-selected)' : 'white',
    borderColor: isDragging ? 'var(--edtt-color-primary)' : '#f0f0f0',
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="tailwind-p-3 tailwind-mb-3 tailwind-border tailwind-border-solid tailwind-rounded tailwind-shadow-sm tailwind-transition-all"
    >
      <div className="tailwind-flex tailwind-items-center tailwind-justify-between">
        <div className="tailwind-flex tailwind-items-center">
          <div
            {...attributes}
            {...listeners}
            className="tailwind-mr-3 tailwind-cursor-grab tailwind-text-gray-400 hover:tailwind-text-primary"
          >
            <MenuOutlined />
          </div>
          <Space direction="vertical" size={0}>
            <Title level={5} className="tailwind-m-0">
              {section.title || `Phần thi ${index + 1}`}
            </Title>
            <Text type="secondary" className="tailwind-text-xs">
              {section.questions.length} câu hỏi
            </Text>
          </Space>
        </div>
        <div className="tailwind-text-gray-600">#{index + 1}</div>
      </div>
    </div>
  );
};

interface SortSectionsModalProps {
  visible: boolean;
  sections: ExamSection[];
  onCancel: () => void;
  onSave: (reorderedSections: ExamSection[]) => void;
}

const SortSectionsModal: React.FC<SortSectionsModalProps> = ({
  visible,
  sections,
  onCancel,
  onSave,
}) => {
  const [items, setItems] = useState<ExamSection[]>(sections);

  // Reset items when the modal becomes visible or sections change
  React.useEffect(() => {
    setItems([...sections]);
  }, [sections, visible]);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      setItems((prevItems) => {
        const oldIndex = prevItems.findIndex(
          (item) => item.clientId === active.id
        );
        const newIndex = prevItems.findIndex(
          (item) => item.clientId === over.id
        );

        return arrayMove(prevItems, oldIndex, newIndex);
      });
    }
  };

  const handleMoveItem = (index: number, direction: 'up' | 'down') => {
    if (
      (direction === 'up' && index === 0) ||
      (direction === 'down' && index === items.length - 1)
    ) {
      return;
    }

    const newIndex = direction === 'up' ? index - 1 : index + 1;
    setItems(arrayMove(items, index, newIndex));
  };

  const handleSave = () => {
    // Update each section with its new order
    const updatedSections = items.map((section, index) => ({
      ...section,
      orderIndex: index,
    }));

    onSave(updatedSections);
  };

  return (
    <Modal
      title={
        <Title level={4} className="tailwind-m-0">
          Sắp xếp phần thi
        </Title>
      }
      open={visible}
      onCancel={onCancel}
      width={600}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          Hủy
        </Button>,
        <Button
          key="save"
          type="primary"
          onClick={handleSave}
          className="tailwind-bg-blue-600"
        >
          Lưu thay đổi
        </Button>,
      ]}
    >
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
      >
        <SortableContext
          items={items.map((section) => section.clientId)}
          strategy={verticalListSortingStrategy}
        >
          <div className="tailwind-max-h-96 tailwind-overflow-y-auto tailwind-pr-2">
            {items.map((section, index) => (
              <div key={section.clientId} className="tailwind-relative">
                <SortableSectionItem section={section} index={index} />

                <div className="tailwind-absolute tailwind-right-2 tailwind-top-1/2 tailwind-transform tailwind--translate-y-1/2 tailwind-z-10">
                  <Space>
                    <Button
                      icon={<ArrowUpOutlined />}
                      disabled={index === 0}
                      onClick={() => handleMoveItem(index, 'up')}
                      size="small"
                      type="text"
                    />
                    <Button
                      icon={<ArrowDownOutlined />}
                      disabled={index === items.length - 1}
                      onClick={() => handleMoveItem(index, 'down')}
                      size="small"
                      type="text"
                    />
                  </Space>
                </div>
              </div>
            ))}
          </div>
        </SortableContext>
      </DndContext>
    </Modal>
  );
};

export default SortSectionsModal;
