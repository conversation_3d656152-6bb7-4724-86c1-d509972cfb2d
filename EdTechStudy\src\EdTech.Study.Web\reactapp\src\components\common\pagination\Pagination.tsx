import {
  ArrowLeftOutlined,
  ArrowRightOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import { Button } from 'antd';
import { useMemo } from 'react';

interface PaginationProps {
  currentIndex: number;
  totalNumber: number;
  options?: {
    addButtonAfterActive?: (index: number) => void;
  };
  onClick: (index: number) => void;
  renderPageButton?: (pageNum: number, isCurrent: boolean) => React.ReactNode;
}

const Pagination = ({
  currentIndex,
  totalNumber,
  options = {
    addButtonAfterActive: undefined,
  },
  onClick,
  renderPageButton,
}: PaginationProps) => {
  const maxVisiblePages = 5;
  const totalPages = totalNumber; // Use totalNumber directly since we're showing question numbers

  // Memoize page numbers calculation
  const pageNumbers = useMemo(() => {
    // If total pages is less than or equal to max visible pages, return all pages
    if (totalPages <= maxVisiblePages) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    // Calculate the range of pages to show
    const halfVisible = Math.floor(maxVisiblePages / 2);
    const startPage = Math.max(
      1,
      Math.min(currentIndex - halfVisible, totalPages - maxVisiblePages + 1)
    );

    // Generate array of page numbers
    return Array.from({ length: maxVisiblePages }, (_, i) => startPage + i);
  }, [currentIndex, totalPages, maxVisiblePages]);

  return (
    <div className="tailwind-flex tailwind-items-center tailwind-justify-center tailwind-gap-2">
      <Button
        shape="circle"
        onClick={() => onClick(currentIndex - 1)}
        disabled={currentIndex === 1}
        className="tailwind-hover:bg-primary-100 tailwind-transition-normal"
        icon={<ArrowLeftOutlined />}
      />

      {pageNumbers.map((pageNum) =>
        renderPageButton ? (
          <>
            {renderPageButton(pageNum, pageNum === currentIndex)}
            {options.addButtonAfterActive && pageNum === currentIndex && (
              <Button
                className="tailwind-text-primary"
                shape="circle"
                icon={<PlusOutlined />}
                type="dashed"
                variant="filled"
                onClick={() => options.addButtonAfterActive!(pageNum)}
              />
            )}
          </>
        ) : (
          <>
            <Button
              key={pageNum}
              shape="circle"
              onClick={() => onClick(pageNum)}
              type={pageNum === currentIndex ? 'primary' : 'default'}
              className={`tailwind-transition-normal ${
                pageNum === currentIndex
                  ? 'tailwind-bg-primary-500 tailwind-text-white'
                  : 'tailwind-hover:bg-primary-100'
              }`}
            >
              {pageNum}
            </Button>
            {options.addButtonAfterActive && pageNum === currentIndex && (
              <Button
                shape="circle"
                icon={<PlusOutlined />}
                type="dashed"
                variant="filled"
                onClick={() => options.addButtonAfterActive!(pageNum)}
              />
            )}
          </>
        )
      )}

      <Button
        shape="circle"
        onClick={() => onClick(currentIndex + 1)}
        disabled={currentIndex === totalPages}
        className="tailwind-hover:bg-primary-100 tailwind-transition-normal"
        icon={<ArrowRightOutlined />}
      />
    </div>
  );
};

export default Pagination;
