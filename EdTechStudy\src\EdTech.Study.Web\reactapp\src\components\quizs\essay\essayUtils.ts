import {
  BaseAnswer,
  BaseQuestion,
} from '../../../interfaces/quizs/questionBase';
import {
  EssayQuestion,
  EssayAnswer,
} from '../../../interfaces/quizs/essay.interface';

/**
 * Check if the user's answer for essay question is correct
 * @param question
 * @param userSelect
 * @returns
 */
export const essayCheckCorrectAnswer = (
  question: BaseQuestion,
  userSelect?: BaseAnswer | BaseAnswer[]
) => {
  if (!userSelect || Array.isArray(userSelect)) return null;

  const essayQuestion = question as EssayQuestion;
  const userAnswer = userSelect as EssayAnswer;

  // If there's no user answer text, return null
  if (!userAnswer.text || userAnswer.text.trim() === '') return null;

  // Get the correct answer from the question
  const correctAnswer = essayQuestion.correctAnswer;
  if (!correctAnswer) return null;

  // Check if case sensitive is enabled
  if (essayQuestion.caseSensitive) {
    // If partial match is allowed
    if (essayQuestion.allowPartialMatch) {
      return (
        userAnswer.text.includes(correctAnswer) ||
        correctAnswer.includes(userAnswer.text)
      );
    }
    // Exact match required
    return userAnswer.text === correctAnswer;
  } else {
    // Case insensitive comparison
    const userText = userAnswer.text.toLowerCase();
    const correctText = correctAnswer.toLowerCase();

    // If partial match is allowed
    if (essayQuestion.allowPartialMatch) {
      return userText.includes(correctText) || correctText.includes(userText);
    }
    // Exact match required (case insensitive)
    return userText === correctText;
  }
};
