// src/interfaces/exams/examPreview.ts
import { ExamBase } from './examBase';
import {
  IPracticeExamData,
  IExamAttempt,
  IExamResultResponse,
} from './practiceExam';

export interface ExamPreviewProps {
  examId?: string;
  exams?: ExamBase[];
  loading: boolean;
  mode?: 'admin' | 'user' | 'review';
  backUrl?: string;
  fetchExamById: (id: string) => void;
  currentExam: ExamBase | null;
}

export interface IExamPreviewState {
  exam: IPracticeExamData | null;
  userAttempt: IExamAttempt | null;
  loading: boolean;
  error: string | null;
}

export interface IExamInstructionProps {
  exam: IPracticeExamData;
  onStart: () => void;
  onCancel?: () => void;
  className?: string;
}

export interface IExamResultProps {
  examId: string;
  attemptId: string;
  results?: IExamResultResponse;
  onRetake?: () => void;
  onReview?: () => void;
  className?: string;
}
