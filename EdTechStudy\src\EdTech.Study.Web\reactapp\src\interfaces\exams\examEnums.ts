/**
 * Defines the type of exam
 */
export enum ExamType {
  Default = 0,
  Test15Minutes = 1,
  Test45Minutes = 2,
  QualificationExam = 3,
  MidTermExam = 4,
  FinalExam = 5,
  MockExam = 6,
  NationalHighSchoolExam = 7,
}

/**
 * Defines the current status of an exam
 */
export enum ExamStatus {
  Draft = 0,
  Submitted = 1,
  Approved = 2,
  Published = 3,
  Rejected = 4,
}

/**
 * Defines the source type of an exam or question
 */
export enum ExamSourceType {
  /// <summary>
  /// Đề thi bình thường.
  /// </summary>
  Default = 0,

  /// <summary>
  /// Đề thi trong ngân hàng đề thi.
  /// </summary>
  Banked = 1,
}

/**
 * Defines the format type of content
 */
export enum ContentFormatType {
  Text = 0,
  Html = 1,
  Markdown = 2,
  LaTeX = 3,
}

export const ContentFormatTypeString = {
  [ContentFormatType.Text]: 'Text',
  [ContentFormatType.Html]: 'Html',
  [ContentFormatType.Markdown]: 'Markdown',
  [ContentFormatType.LaTeX]: 'LaTeX',
};

/**
 * Defines the type of matching item
 */
export enum MatchingItemType {
  Premise = 0, // Left side
  Response = 1, // Right side
}

export enum MatchingItemTypeString {
  Premise = 'Premise',
  Response = 'Response',
}

/**
 * Defines the question difficulty level
 */
export enum QuestionDifficulty {
  Easy = 1,
  Medium = 2,
  Hard = 3,
  VeryHard = 4,
}
