﻿using System;
using System.Collections.Generic;
using System.Text;
using Volo.Abp.Domain.Entities;

namespace EdTech.Study.Questions
{
    /// <summary>
    /// Đại diện cho một đáp án trong câu hỏi điền vào chỗ trống.
    /// </summary>
    public class FillInBlankAnswerEntity : Entity<Guid>
    {
        public FillInBlankAnswerEntity()
        {
            
        }

        public FillInBlankAnswerEntity(Guid id) : base(id)
        {

        }

        /// <summary>
        /// Liên kết đến câu hỏi cha.
        /// </summary>
        public Guid QuestionId { get; set; }

        public QuestionEntity Question { get; set; }

        /// <summary>
        /// Chỉ số của ô trống trong câu hỏi (bắt đầu từ 0).
        /// </summary>
        public int BlankIndex { get; set; }

        /// <summary>
        /// Mảng các đáp án đúng có thể chấp nhận cho ô trống này.
        /// </summary>
        public List<string> CorrectAnswers { get; set; } = new();

        /// <summary>
        /// C<PERSON> phân biệt chữ hoa chữ thường hay không.
        /// </summary>
        public bool CaseSensitive { get; set; }

        /// <summary>
        /// Phản hồi hiển thị khi học sinh trả lời đúng hoặc sai.
        /// </summary>
        public string? Feedback { get; set; }

        /// <summary>
        /// Điểm số cho ô trống này.
        /// </summary>
        public float? Score { get; set; }
    }
}