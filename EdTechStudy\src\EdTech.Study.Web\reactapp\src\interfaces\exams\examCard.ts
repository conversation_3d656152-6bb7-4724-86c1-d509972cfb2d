// src/interfaces/exams/examCard.ts
import { LessonGrade } from '../lessons/lessonGrade';
import { Subject } from '../lessons/subject';
import { ExamBase, ExamGrade } from './examBase';

/**
 * Props interface for ExamCard component
 * Updated for performance optimization - using single objects instead of arrays
 */
export interface IExamCardProps {
  examData: ExamBase;
  subject?: Subject;     // Changed from subjects array to single subject object
  grade?: LessonGrade;   // Changed from grades array to single grade object
  onStartExam: (exam: ExamBase) => void;
  onContinueExam: (exam: ExamBase) => void;
  onReviewExam: (exam: ExamBase) => void;
  className?: string;
}

/**
 * Legacy props interface for backward compatibility
 * @deprecated Use IExamCardProps instead
 */
export interface IExamCardPropsLegacy {
  examData: ExamBase;
  subjects: Subject[];
  grades: LessonGrade[];
  onStartExam: (exam: ExamBase) => void;
  onContinueExam: (exam: ExamBase) => void;
  onReviewExam: (exam: ExamBase) => void;
  className?: string;
}

/**
 * Status configuration for exam card
 */
export interface IExamCardStatusConfig {
  text: string;
  color: string;
  buttonText: string;
  buttonAction: () => void;
  buttonVariant: 'primary' | 'secondary' | 'success';
}
