import React from 'react';
import { Typography, Space, Tag } from 'antd';
import { QuestionCircleFilled, TrophyOutlined } from '@ant-design/icons';

const { Text } = Typography;

export interface ExamSectionTitleProps {
  /**
   * Section index (0-based)
   */
  sectionIndex: number;

  /**
   * Section title
   */
  title: string;

  /**
   * Number of questions in the section
   */
  questionCount: number;

  /**
   * Section score (optional)
   */
  sectionScore?: number;

  /**
   * Custom class name for the container
   */
  className?: string;

  /**
   * Custom background color class
   * Default: 'tailwind-bg-selected'
   */
  backgroundColorClass?: string;

  /**
   * Custom text color class for the title
   * Default: 'tailwind-text-primary'
   */
  textColorClass?: string;

  /**
   * Custom padding class
   * Default: 'tailwind-p-2'
   */
  paddingClass?: string;

  /**
   * Custom margin bottom class
   * Default: 'tailwind-mb-6'
   */
  marginBottomClass?: string;

  /**
   * Custom border radius class
   * Default: 'tailwind-rounded-md tailwind-rounded-radius-xl'
   */
  borderRadiusClass?: string;

  /**
   * Custom question count tag style
   */
  questionTagStyle?: React.CSSProperties;

  /**
   * Custom score tag style
   */
  scoreTagStyle?: React.CSSProperties;
}

/**
 * A component for displaying exam section title with question count and score
 */
const ExamSectionTitleComponent: React.FC<ExamSectionTitleProps> = ({
  sectionIndex,
  title,
  questionCount,
  sectionScore,
  className = '',
  backgroundColorClass = 'tailwind-bg-selected',
  textColorClass = 'tailwind-text-primary',
  questionTagStyle,
  scoreTagStyle,
}) => {
  // Default styles for question count tag
  const defaultQuestionTagStyle: React.CSSProperties = {
    color: '#2D956D',
    height: '26px',
    backgroundColor: '#C4FDE1',
    borderColor: '#C4FDE1',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  };

  // Default styles for score tag
  const defaultScoreTagStyle: React.CSSProperties = {
    color: '#3CAEBD',
    height: '26px',
    backgroundColor: '#CEF5FD',
    borderColor: '#CEF5FD',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  };

  return (
    <div
      className={`${className} ${backgroundColorClass} tailwind-p-2 tailwind-rounded-md tailwind-rounded-radius-xl`}
    >
      <div className="tailwind-flex tailwind-justify-between tailwind-items-center">
        <Text
          className={`tailwind-mb-0 ${textColorClass} tailwind-text-xl tailwind-font-medium`}
        >
          Phần {sectionIndex + 1}: {title}
        </Text>
        <Space>
          <Tag
            className="tailwind-text-sm"
            style={{ ...defaultQuestionTagStyle, ...questionTagStyle }}
          >
            <QuestionCircleFilled />
            &nbsp;
            {questionCount} câu
          </Tag>
          {sectionScore && (
            <Tag
              className="tailwind-text-sm"
              style={{ ...defaultScoreTagStyle, ...scoreTagStyle }}
            >
              <TrophyOutlined />
              &nbsp;
              {sectionScore} điểm
            </Tag>
          )}
        </Space>
      </div>
    </div>
  );
};

export default ExamSectionTitleComponent;
