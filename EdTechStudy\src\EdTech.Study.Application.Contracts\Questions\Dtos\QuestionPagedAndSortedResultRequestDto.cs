using EdTech.Study.Enum;
using System;
using Volo.Abp.Application.Dtos;

namespace EdTech.Study.Questions.Dtos
{
    public class QuestionPagedAndSortedResultRequestDto : PagedAndSortedResultRequestDto
    {
        public string? Filter { get; set; }
        public QuestionType? QuestionType { get; set; }
        public ExamStatus? Status { get; set; }
        public Guid? SubjectId { get; set; }
        public Guid? GradeId { get; set; }
        public string? Topic { get; set; }
        public string? Tag { get; set; }
        public int? MinDifficulty { get; set; }
        public int? MaxDifficulty { get; set; }
    }
}