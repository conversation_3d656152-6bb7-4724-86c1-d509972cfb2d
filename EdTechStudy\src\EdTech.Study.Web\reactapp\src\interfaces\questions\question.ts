import {
  ExamFillInBlankAnswer,
  ExamGroupQuestion,
  ExamMatchingAnswer,
  ExamMatchingItem,
  ExamQuestionOption,
} from '../exams/examBase';
import { ContentFormatType, ExamSourceType } from '../exams/examEnums';
import { BaseQuestion } from '../quizs/questionBase';

export interface IQuestion extends BaseQuestion {
  content: string;
  contentFormat: ContentFormatType;
  comment?: string;
  shuffleOptions: boolean;
  explanation?: string;
  sourceType?: ExamSourceType;
  topics?: string | string[];
  tags?: string | string[];
  options?: ExamQuestionOption[];
  matchingItems?: ExamMatchingItem[];
  matchingAnswers?: ExamMatchingAnswer[];
  fillInBlankAnswers?: ExamFillInBlankAnswer[];
  groupQuestionId?: string;
  groupQuestion?: ExamGroupQuestion;
}
