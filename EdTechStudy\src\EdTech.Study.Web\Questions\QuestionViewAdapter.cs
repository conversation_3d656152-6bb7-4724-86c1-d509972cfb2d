﻿using EdTech.Study.Questions;
using EdTech.Study.Questions.Dtos;

namespace EdTech.Study.Web.Questions
{
    public class QuestionViewAdapter
    {
        public QuestionDraftDto Adaptee { get; set; }

        public QuestionViewAdapter(QuestionDraftDto adaptee)
        {
            Adaptee = adaptee;
        }

        public QuestionViewDto Adapt()
        {
            if (Adaptee == null)
                return null;

            return new QuestionViewDto
            {
                Id = Adaptee.Id,
                Type = GetQuestionTypeString(Adaptee.Type),
                Title = Adaptee.Content,
                Description = Adaptee.Description,
                Points = Adaptee.DifficultyLevel,
                IsCompleted = Adaptee.Status == Enum.ExamStatus.Approved,
                StatusEntity = Adaptee.Status.ToString()
            };
        }

        public string GetQuestionTypeString(QuestionType type)
        {
            switch (type)
            {
                case QuestionType.SingleChoice:
                    return "quiz";
                case QuestionType.MultipleChoice:
                    return "multiselect";
                case QuestionType.Matching:
                    return "matching";
                case QuestionType.Fillblanks:
                    return "fillblanks";
            }
            return "";
        }

        public static QuestionViewDto CreateFrom(QuestionDraftDto adaptee)
        {
            return new QuestionViewAdapter(adaptee).Adapt();
        }
    }
}