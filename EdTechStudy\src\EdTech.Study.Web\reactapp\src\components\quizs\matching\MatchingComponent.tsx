import React, { useContext, useEffect, useMemo, useState, useRef } from 'react';
import {
  Card,
  Button,
  Row,
  Col,
  message,
  Tooltip,
  PopconfirmProps,
  Input,
  Switch,
  Typography,
  Form,
  Space,
  Collapse,
} from 'antd';
import FireworksComponent from '../quiz/FireworksComponent';
import {
  DeleteFilled,
  QuestionOutlined,
  PlusOutlined,
  CloseOutlined,
  InfoCircleOutlined,
  SwapOutlined,
} from '@ant-design/icons';
import './MatchingComponent.css';
import {
  MatchingQuestion,
  PracticeEngineContext,
  QuestionComponentBaseProps,
  MatchingQuestionAnswer,
} from '../../../interfaces/quizs/questionBase';

// Import custom components
import ClickableItem from './components/ClickableItem';
import MatchingFeedback from './components/MatchingFeedback';
import ConnectionLines from './components/ConnectionLines';

// Import custom hooks
import { useMatchingLogic } from './hooks/useMatchingLogic';
import { useMatchResults } from './hooks/useMatchResults';
import { Guid } from 'guid-typescript';
import practiceLocalization, { quizLocalization } from '../localization';
import useUpdateQuestion from '../hooks/useUpdateQuestion';
import PopconfirmAntdCustom from '../../customs/antd/PopconfirmAntdCustom';

// Import ReactQuill and needed modules/formats
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import DOMPurify from 'dompurify';
import CustomRichText from '../../common/elements/Text/RichTextEditorComponent/v0/CustomRichText';

// Import BaseQuestionPreviewComponent
import BaseQuestionPreviewComponent from '../base/BaseQuestionPreviewComponent';
import { matchingCheckCorrectAnswer } from './matchingUtils';

const { TextArea } = Input;
const { Text } = Typography;

// Define modules for the rich text editor
const modules = {
  toolbar: [
    [{ header: [1, 2, 3, false] }],
    ['bold', 'italic', 'underline', 'strike', 'blockquote'],
    [
      { list: 'ordered' },
      { list: 'bullet' },
      { indent: '-1' },
      { indent: '+1' },
    ],
    ['link', 'image'],
    ['clean'],
  ],
};

const formats = [
  'header',
  'bold',
  'italic',
  'underline',
  'strike',
  'blockquote',
  'list',
  'bullet',
  'indent',
  'link',
  'image',
];

export interface MatchingComponentProps extends QuestionComponentBaseProps {
  question: MatchingQuestion;
  onComplete?: (questionId: string, userSelect: any) => void;
  // Add new props for BaseQuestionPreviewComponent compatibility
  questionIndex?: number;
  showResult?: boolean;
  isMarked?: boolean;
  onToggleMark?: (questionId: string) => void;
}

const MatchingComponent: React.FC<MatchingComponentProps> = ({
  id: externalId,
  question,
  configMode = false,
  onComplete,
  options = {
    hideDeleteButton: false,
    hideSaveButton: false,
    hideFeedback: false,
  },
  // New props for BaseQuestionPreviewComponent
  questionIndex,
  showResult = false,
  isMarked = false,
  onToggleMark,
}) => {
  const [showFireworks, setShowFireworks] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const {
    handleEditQuestion: handleChangeQuestion,
    handleDeleteQuestion: externalHandleDeleteQuestion,
  } = useContext(PracticeEngineContext);

  const [focus, setFocus] = useState<string | null>(null);
  const [resetTrigger, setResetTrigger] = useState(false);

  // Fix: Initialize editableQuestion properly with all required fields
  const [editableQuestion, setEditableQuestion] = useState<MatchingQuestion>(
    () => ({
      ...question,
      leftItems: question.leftItems ?? [],
      rightItems: question.rightItems ?? [], // Fix: was incorrectly using leftItems
      options: question.options ?? [],
      type: 'matching' as const,
      clientId: question.clientId,
      title: question.title,
      difficulty: question.difficulty ?? 1,
      syncQuestion: question.syncQuestion ?? false,
    })
  );

  const id = useMemo(
    () => externalId || Guid.create().toString(),
    [externalId]
  );

  // Use custom hooks with configMode parameter
  const {
    leftItems,
    rightItems,
    leftIds,
    rightIds,
    selectedItemId,
    pairedItems,
    isItemPaired,
    getPairColor,
    handleItemClick,
    getPairs,
    resetExercise,
  } = useMatchingLogic({
    id: id,
    question,
    resetExternalTrigger: resetTrigger,
    configMode: configMode,
    handleChangeAnswer,
  });

  const { isChecking, completed, correctCount, checkAnswers, getMatchResult } =
    useMatchResults({
      id,
      question,
      leftIds,
      leftItems,
      rightItems,
      pairedItems,
      getPairs,
      onComplete: (isCorrect: boolean) => {
        if (isCorrect) {
          setTimeout(() => {
            setShowFireworks(true);
          }, 200);
        }
      },
    });

  // Calculate isAnswered and isCorrect for BaseQuestionPreviewComponent
  const isAnswered = useMemo(() => {
    const pairs = getPairs();
    return pairs.length > 0;
  }, [getPairs]);

  const isCorrect = useMemo(() => {
    if (!showResult || !isAnswered) return false;

    // Convert current pairs to MatchingQuestionAnswer format
    const pairs = getPairs();
    const userAnswers: MatchingQuestionAnswer[] = pairs.map((pair) => ({
      id: Guid.create().toString(),
      clientId: Guid.create().toString(),
      content: '',
      contentFormat: '',
      order: 0,
      isCorrect: true,
      left: pair.left,
      right: pair.right,
    }));

    const checkResult = matchingCheckCorrectAnswer(question, userAnswers);
    return checkResult === true;
  }, [showResult, isAnswered, question, getPairs]);

  // Handle completion and submit user answer
  function handleChangeAnswer(answer: MatchingQuestionAnswer) {
    if (onComplete) {
      const userAnswer = getPairs().map(
        (pair) =>
          ({
            id: Guid.create().toString(),
            clientId: Guid.create().toString(), // Fix: Add missing clientId
            content: '',
            contentFormat: '',
            order: 0,
            isCorrect: true,
            left: pair.left,
            right: pair.right,
          } as MatchingQuestionAnswer)
      );
      const isNew = !userAnswer.some((item) => item.left === answer.left);
      if (isNew) {
        onComplete(question.clientId, [...userAnswer, answer]);
      } else {
        onComplete(question.clientId, userAnswer);
      }
    }
  }

  const { handleUpdateQuestion } = useUpdateQuestion();

  const deleteConfirm: PopconfirmProps['onConfirm'] = () => {
    externalHandleDeleteQuestion(question.clientId);
  };

  const handleResetExercise = () => {
    resetExercise();
    setResetTrigger((prev) => !prev);
  };

  // Config Mode Methods
  const handleUpdateTitle = (e: React.ChangeEvent<HTMLInputElement>) => {
    const updatedQuestion = {
      ...editableQuestion,
      title: e.target.value,
    };
    setEditableQuestion(updatedQuestion);
    handleUpdateQuestion(updatedQuestion);
  };

  const handleUpdateDescription = (content: string) => {
    const updatedQuestion = {
      ...editableQuestion,
      content: content,
    };
    setEditableQuestion(updatedQuestion);
    handleUpdateQuestion(updatedQuestion);
  };

  const handleToggleShuffle = (checked: boolean) => {
    const updatedQuestion = {
      ...editableQuestion,
      shuffleItems: checked,
    };
    setEditableQuestion(updatedQuestion);
    handleUpdateQuestion(updatedQuestion);
  };

  const handleUpdateItemContent = (
    side: 'left' | 'right',
    index: number,
    content: string
  ) => {
    const updatedQuestion = { ...editableQuestion };
    const itemsToUpdate =
      side === 'left' ? updatedQuestion.leftItems : updatedQuestion.rightItems;
    if (!itemsToUpdate) return;
    if (index < 0 || index >= itemsToUpdate.length) return;

    itemsToUpdate[index] = {
      ...itemsToUpdate[index],
      content,
    };

    setEditableQuestion(updatedQuestion);
    handleUpdateQuestion(updatedQuestion);
  };

  const handleAddItem = () => {
    const updatedQuestion = { ...editableQuestion };
    // Create new item pairs with matching IDs
    const leftId = `${editableQuestion.clientId}L${
      (updatedQuestion.leftItems?.length ?? 0) + 1
    }`;
    const rightId = `${editableQuestion.clientId}R${
      (updatedQuestion.rightItems?.length ?? 0) + 1
    }`;

    // Add new item to left column
    if (!updatedQuestion.leftItems) {
      updatedQuestion.leftItems = [];
    }
    updatedQuestion.leftItems.push({
      clientId: leftId,
      content: `Mục ${updatedQuestion.leftItems.length + 1}`,
      type: 'text',
      matchId: rightId, // Match with corresponding right item
    });

    if (!updatedQuestion.rightItems) {
      updatedQuestion.rightItems = [];
    }
    // Add new item to right column
    updatedQuestion.rightItems.push({
      clientId: rightId,
      content: `Mục ${updatedQuestion.rightItems.length + 1}`,
      type: 'text',
      matchId: leftId, // Match with corresponding left item
    });

    setEditableQuestion(updatedQuestion);
    handleUpdateQuestion(updatedQuestion);
  };

  const handleRemoveItemPair = (index: number) => {
    const updatedQuestion = { ...editableQuestion };

    if (!updatedQuestion.leftItems) {
      return;
    }
    if (index < 0 || index >= updatedQuestion.leftItems.length) {
      // Make sure index is valid
      return;
    }

    // Remove items at the same position from both columns
    updatedQuestion.leftItems.splice(index, 1);

    if (!updatedQuestion.rightItems) return;
    if (index < updatedQuestion.rightItems.length) {
      updatedQuestion.rightItems.splice(index, 1);
    }

    setEditableQuestion(updatedQuestion);
    handleUpdateQuestion(updatedQuestion);
  };

  // Save the current configuration
  const handleSaveConfiguration = () => {
    // Update the matchIds based on current positions if needed
    const updatedQuestion = JSON.parse(
      JSON.stringify(editableQuestion)
    ) as MatchingQuestion;
    const currentLeftItems = updatedQuestion.leftItems;
    const currentRightItems = updatedQuestion.rightItems;
    // Ensure the matchIds are set correctly based on position
    const minLength = Math.min(
      currentLeftItems?.length ?? 0,
      currentRightItems?.length ?? 0
    );
    if (
      currentLeftItems &&
      currentRightItems &&
      updatedQuestion.leftItems &&
      updatedQuestion.rightItems
    ) {
      for (let i = 0; i < minLength; i++) {
        updatedQuestion.leftItems[i].matchId = currentRightItems[i].clientId;
        updatedQuestion.rightItems[i].matchId = currentLeftItems[i].clientId;
      }
    }

    updatedQuestion.options = [];
    updatedQuestion.leftItems?.forEach((p) => {
      const right = updatedQuestion.rightItems?.filter(
        (r) => r.matchId === p.clientId
      );
      if (right) {
        right.forEach((r) => {
          updatedQuestion.options.push({
            id: Guid.create().toString(),
            clientId: Guid.create().toString(),
            content: '',
            contentFormat: '',
            order: updatedQuestion.options.length,
            left: p.clientId,
            right: r.clientId,
            isCorrect: true,
          });
        });
      }
    });

    // Fix: handleEditQuestion expects specific parameter structure
    // Based on IPracticeEngineContext interface: Partial<BaseQuestion> & { clientId: string; parentId: string }
    try {
      handleChangeQuestion({
        ...updatedQuestion,
        parentId: updatedQuestion.clientId, // Use same ID as parentId since this is the question itself
      });
      message.success('Cấu hình đã được lưu!');
    } catch (error) {
      console.error('Error saving configuration:', error);
      message.error('Có lỗi xảy ra khi lưu cấu hình!');
    }
  };

  // Clear fireworks after display
  useEffect(() => {
    if (showFireworks) {
      const timer = setTimeout(() => {
        setShowFireworks(false);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [showFireworks]);

  // Update the editableQuestion when leftItems or rightItems change in config mode
  useEffect(() => {
    if (configMode) {
      setEditableQuestion((prevQuestion) => ({
        ...prevQuestion,
        leftItems: leftItems ?? [],
        rightItems: rightItems ?? [],
      }));
    }
  }, [leftItems, rightItems, configMode]); // Fix: Remove editableQuestion from deps to avoid infinite loop

  useEffect(() => {
    resetExercise();
  }, [id, resetExercise]); // Fix: Add resetExercise to dependencies

  // Fix: Handle options destructuring properly
  const {
    hideDeleteButton = false,
    hideSaveButton = false,
    hideFeedback = false,
  } = options || {};

  // Render content for BaseQuestionPreviewComponent
  const renderContent = () => {
    if (!question.content) return null;

    return (
      <div
        dangerouslySetInnerHTML={{
          __html: DOMPurify.sanitize(question.content),
        }}
      />
    );
  };

  // Render interaction for BaseQuestionPreviewComponent
  const renderInteraction = () => {
    return (
      <>
        <div className="matching-container item-config" ref={containerRef}>
          <Row gutter={24} className="tailwind-mt-4">
            <Col span={12}>
              <div className="matching-column">
                <h4>Cột A</h4>
                {leftItems.map((item, index) => (
                  <ClickableItem
                    key={item.clientId}
                    id={`${item.clientId}`}
                    item={item}
                    isSelected={selectedItemId === item.clientId}
                    isPaired={isItemPaired(item.clientId)}
                    pairColor={getPairColor(item.clientId)}
                    isMatched={
                      (isChecking || showResult) &&
                      getMatchResult(item.clientId) === true
                    }
                    isIncorrect={
                      (isChecking || showResult) &&
                      getMatchResult(item.clientId) === false
                    }
                    isLeftItem={true}
                    onClick={(id) => {
                      if (!showResult) {
                        // Only allow interaction if not in result mode
                        handleItemClick(id);
                        setFocus(`left-${index}`);
                      }
                    }}
                  />
                ))}
              </div>
            </Col>

            <Col span={12}>
              <div className="matching-column">
                <h4>Cột B</h4>
                {rightItems.map((item, index) => (
                  <ClickableItem
                    key={item.clientId}
                    id={`${item.clientId}`}
                    item={item}
                    isSelected={selectedItemId === item.clientId}
                    isPaired={isItemPaired(item.clientId)}
                    pairColor={getPairColor(item.clientId)}
                    isMatched={
                      (isChecking || showResult) &&
                      getMatchResult(item.clientId) === true
                    }
                    isIncorrect={
                      (isChecking || showResult) &&
                      getMatchResult(item.clientId) === false
                    }
                    isLeftItem={false}
                    onClick={(id) => {
                      if (!showResult) {
                        // Only allow interaction if not in result mode
                        handleItemClick(id);
                        setFocus(`right-${index}`);
                      }
                    }}
                  />
                ))}
              </div>
            </Col>
          </Row>

          {/* Connection Lines */}
          {!configMode && (
            <ConnectionLines
              leftItems={leftItems}
              rightItems={rightItems}
              pairedItems={pairedItems}
              containerRef={containerRef}
            />
          )}
        </div>

        {/* Action buttons - only show in non-result mode */}
        {!showResult && (
          <div className="tailwind-mt-5 tailwind-flex tailwind-justify-center">
            <Space>
              <Button onClick={handleResetExercise}>Làm lại</Button>
            </Space>
          </div>
        )}

        {/* Feedback - only show if not hideFeedback */}
        {!hideFeedback && (
          <MatchingFeedback
            isChecking={isChecking}
            isCompleted={completed}
            correctCount={correctCount}
            totalCount={Math.min(leftItems.length, rightItems.length)}
            showFeedback={true}
          />
        )}
      </>
    );
  };

  // Config mode rendering (unchanged)
  if (configMode) {
    return (
      <Card title={undefined} className="matching-component-card">
        <Form layout="vertical">
          <Form.Item required hidden>
            <Input
              value={editableQuestion.title}
              onChange={handleUpdateTitle}
              placeholder={quizLocalization.form.questionTitle.placeholder}
              autoFocus={focus === 'title'}
            />
          </Form.Item>

          <Form.Item label="Nội dung" required>
            <CustomRichText
              height={200}
              value={editableQuestion.content || ''}
              onChangeValue={(_name, value) => {
                handleUpdateDescription(value || '');
              }}
              placeholder="Nhập mô tả hoặc hướng dẫn cho bài tập"
              toolbarSettingItems={[
                'Bold',
                'Italic',
                'Underline',
                'OrderedList',
                'UnorderedList',
                'Image',
                'CreateLink',
              ]}
            />
          </Form.Item>

          <Form.Item label="Các tuỳ chọn cặp nối" required>
            <div className="matching-pairs-config">
              {editableQuestion.leftItems?.map((leftItem, index) => (
                <div
                  key={leftItem.clientId || index} // Fix: Use clientId as key, fallback to index
                  className="matching-pair-row tailwind-flex"
                >
                  <div className="matching-pair-inputs tailwind-flex tailwind-gap-4 tailwind-grow">
                    <Input
                      value={String(leftItem.content || '')}
                      onChange={(e) =>
                        handleUpdateItemContent('left', index, e.target.value)
                      }
                      placeholder={`Mục ${index + 1} (Cột A)`}
                      className="tailwind-flex-1"
                      autoFocus={focus === `left-${index}`}
                    />
                    <SwapOutlined
                      style={{
                        margin: '0 4px',
                        color: '#1890ff',
                        fontSize: '18px',
                      }}
                    />
                    <Input
                      value={
                        editableQuestion.rightItems
                          ? String(
                              editableQuestion.rightItems[index]?.content || ''
                            )
                          : ''
                      }
                      onChange={(e) =>
                        handleUpdateItemContent('right', index, e.target.value)
                      }
                      placeholder={`Mục ${index + 1} (Cột B)`}
                      className="tailwind-flex-1"
                      autoFocus={focus === `right-${index}`}
                    />
                  </div>
                  <Button
                    type="text"
                    className="tailwind-flex-none tailwind-ml-2"
                    icon={<CloseOutlined />}
                    onClick={() => handleRemoveItemPair(index)}
                    disabled={
                      editableQuestion.leftItems &&
                      editableQuestion.leftItems.length <= 2
                    }
                    danger
                  />
                </div>
              ))}
            </div>

            <Button
              type="dashed"
              onClick={handleAddItem}
              icon={<PlusOutlined />}
              style={{ width: '100%', marginTop: '8px' }}
            >
              Thêm cặp khớp mới
            </Button>
          </Form.Item>

          <Form.Item>
            <Collapse defaultActiveKey={[]} ghost>
              <Collapse.Panel
                header={practiceLocalization['Advanced Options']}
                key="1"
              >
                <Form.Item label={quizLocalization.questionType.label} required>
                  <Input
                    value={quizLocalization.questionType.matching}
                    disabled
                  />
                </Form.Item>

                <Form.Item label="Tùy chọn">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '8px',
                      }}
                    >
                      <Switch
                        checked={editableQuestion.shuffleItems}
                        onChange={handleToggleShuffle}
                      />
                      <Text>Xáo trộn các mục khi làm bài</Text>
                      <Tooltip title="Khi bật, vị trí các mục sẽ được xáo trộn ngẫu nhiên mỗi lần làm bài">
                        <InfoCircleOutlined style={{ color: '#1890ff' }} />
                      </Tooltip>
                    </div>
                  </Space>
                </Form.Item>
              </Collapse.Panel>
            </Collapse>
          </Form.Item>

          <Space>
            {!hideSaveButton && (
              <Button
                type="primary"
                onClick={handleSaveConfiguration}
                className="tailwind-flex-1 tailwind-bg-blue-500 hover:tailwind-bg-blue-600"
              >
                {quizLocalization.buttons.saveChanges}
              </Button>
            )}
            {!hideDeleteButton && ( // Fix: Check hideDeleteButton flag
              <PopconfirmAntdCustom
                title={quizLocalization.buttons.deleteQuestion.confirmTitle}
                onConfirm={deleteConfirm}
                onCancel={() => {}}
                okText={quizLocalization.buttons.deleteQuestion.yes}
                cancelText={quizLocalization.buttons.deleteQuestion.no}
              >
                <Button danger icon={<DeleteFilled />}>
                  {quizLocalization.buttons.deleteQuestion.button}
                </Button>
              </PopconfirmAntdCustom>
            )}
          </Space>
        </Form>
      </Card>
    );
  }

  // Exercise mode rendering - NOW USING BaseQuestionPreviewComponent
  return (
    <>
      <BaseQuestionPreviewComponent
        question={question}
        questionIndex={questionIndex}
        isCorrect={isCorrect}
        hideFeedback={hideFeedback}
        showResult={showResult}
        isAnswered={isAnswered}
        cardClassName="matching-component-card"
        guidanceText="Nhấn vào mỗi mục để chọn, sau đó nhấn vào mục khác để ghép cặp. Hãy ghép các mục có nội dung liên quan với nhau."
        renderContent={renderContent}
        renderInteraction={renderInteraction}
        isMarked={isMarked}
        onToggleMark={onToggleMark}
      />

      {/* Display fireworks when answers are correct */}
      <FireworksComponent show={showFireworks} />
    </>
  );
};

export default MatchingComponent;
