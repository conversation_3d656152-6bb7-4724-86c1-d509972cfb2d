import { LessonGrade } from '../../interfaces/lessons/lessonGrade';
import { Subject } from '../../interfaces/lessons/subject';
import { IUserInfo } from '../../interfaces/users/user';

export interface IQuestionProviderDispatchProps {
  getSubjects: () => any;
  getLessonGrades: () => any;
  getUsers: () => any;
}

export interface IQuestionProviderProps extends IQuestionProviderDispatchProps {
  children: any;
  subjects?: Subject[];
  lessonGrades?: LessonGrade[];
  users?: IUserInfo[];
}

export interface IQuestionContext {
  subjects?: Subject[];
  lessonGrades?: LessonGrade[];
  users?: IUserInfo[];
}
