{"Files": [{"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\175.js", "PackagePath": "staticwebassets\\EdTech\\reactapp\\175.js"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\175.js.map", "PackagePath": "staticwebassets\\EdTech\\reactapp\\175.js.map"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\185.js", "PackagePath": "staticwebassets\\EdTech\\reactapp\\185.js"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\185.js.LICENSE.txt", "PackagePath": "staticwebassets\\EdTech\\reactapp\\185.js.LICENSE.txt"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\185.js.map", "PackagePath": "staticwebassets\\EdTech\\reactapp\\185.js.map"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\525.js", "PackagePath": "staticwebassets\\EdTech\\reactapp\\525.js"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\525.js.LICENSE.txt", "PackagePath": "staticwebassets\\EdTech\\reactapp\\525.js.LICENSE.txt"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\525.js.map", "PackagePath": "staticwebassets\\EdTech\\reactapp\\525.js.map"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\652.js", "PackagePath": "staticwebassets\\EdTech\\reactapp\\652.js"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\652.js.LICENSE.txt", "PackagePath": "staticwebassets\\EdTech\\reactapp\\652.js.LICENSE.txt"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\652.js.map", "PackagePath": "staticwebassets\\EdTech\\reactapp\\652.js.map"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\915.js", "PackagePath": "staticwebassets\\EdTech\\reactapp\\915.js"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\915.js.LICENSE.txt", "PackagePath": "staticwebassets\\EdTech\\reactapp\\915.js.LICENSE.txt"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\915.js.map", "PackagePath": "staticwebassets\\EdTech\\reactapp\\915.js.map"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\BasePage.js", "PackagePath": "staticwebassets\\EdTech\\reactapp\\BasePage.js"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\BasePage.js.LICENSE.txt", "PackagePath": "staticwebassets\\EdTech\\reactapp\\BasePage.js.LICENSE.txt"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\BasePage.js.map", "PackagePath": "staticwebassets\\EdTech\\reactapp\\BasePage.js.map"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\DemoLessonPage.js", "PackagePath": "staticwebassets\\EdTech\\reactapp\\DemoLessonPage.js"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\DemoLessonPage.js.LICENSE.txt", "PackagePath": "staticwebassets\\EdTech\\reactapp\\DemoLessonPage.js.LICENSE.txt"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\DemoLessonPage.js.map", "PackagePath": "staticwebassets\\EdTech\\reactapp\\DemoLessonPage.js.map"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\ExamManagementPage.js", "PackagePath": "staticwebassets\\EdTech\\reactapp\\ExamManagementPage.js"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\ExamManagementPage.js.LICENSE.txt", "PackagePath": "staticwebassets\\EdTech\\reactapp\\ExamManagementPage.js.LICENSE.txt"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\ExamManagementPage.js.map", "PackagePath": "staticwebassets\\EdTech\\reactapp\\ExamManagementPage.js.map"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\IconStorePage.js", "PackagePath": "staticwebassets\\EdTech\\reactapp\\IconStorePage.js"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\IconStorePage.js.LICENSE.txt", "PackagePath": "staticwebassets\\EdTech\\reactapp\\IconStorePage.js.LICENSE.txt"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\IconStorePage.js.map", "PackagePath": "staticwebassets\\EdTech\\reactapp\\IconStorePage.js.map"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\PracticeExamPage.js", "PackagePath": "staticwebassets\\EdTech\\reactapp\\PracticeExamPage.js"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\PracticeExamPage.js.LICENSE.txt", "PackagePath": "staticwebassets\\EdTech\\reactapp\\PracticeExamPage.js.LICENSE.txt"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\PracticeExamPage.js.map", "PackagePath": "staticwebassets\\EdTech\\reactapp\\PracticeExamPage.js.map"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\PreviewLessonPage.js", "PackagePath": "staticwebassets\\EdTech\\reactapp\\PreviewLessonPage.js"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\PreviewLessonPage.js.LICENSE.txt", "PackagePath": "staticwebassets\\EdTech\\reactapp\\PreviewLessonPage.js.LICENSE.txt"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\PreviewLessonPage.js.map", "PackagePath": "staticwebassets\\EdTech\\reactapp\\PreviewLessonPage.js.map"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\QuestionPage.js", "PackagePath": "staticwebassets\\EdTech\\reactapp\\QuestionPage.js"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\QuestionPage.js.LICENSE.txt", "PackagePath": "staticwebassets\\EdTech\\reactapp\\QuestionPage.js.LICENSE.txt"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\QuestionPage.js.map", "PackagePath": "staticwebassets\\EdTech\\reactapp\\QuestionPage.js.map"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\assets\\css\\app.css", "PackagePath": "staticwebassets\\EdTech\\reactapp\\assets\\css\\app.css"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\assets\\css\\app.css.map", "PackagePath": "staticwebassets\\EdTech\\reactapp\\assets\\css\\app.css.map"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\assets\\css\\vendor.css", "PackagePath": "staticwebassets\\EdTech\\reactapp\\assets\\css\\vendor.css"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\assets\\css\\vendor.css.map", "PackagePath": "staticwebassets\\EdTech\\reactapp\\assets\\css\\vendor.css.map"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\assets\\images\\CoordinateFinderGame.jpg", "PackagePath": "staticwebassets\\EdTech\\reactapp\\assets\\images\\CoordinateFinderGame.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\assets\\images\\Demo.png", "PackagePath": "staticwebassets\\EdTech\\reactapp\\assets\\images\\Demo.png"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\assets\\images\\Game.jpg", "PackagePath": "staticwebassets\\EdTech\\reactapp\\assets\\images\\Game.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\assets\\images\\Layout.png", "PackagePath": "staticwebassets\\EdTech\\reactapp\\assets\\images\\Layout.png"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\assets\\images\\MillionaireGame.jpg", "PackagePath": "staticwebassets\\EdTech\\reactapp\\assets\\images\\MillionaireGame.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\assets\\images\\Quiz.jpg", "PackagePath": "staticwebassets\\EdTech\\reactapp\\assets\\images\\Quiz.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\assets\\images\\QuizCardGame.jpg", "PackagePath": "staticwebassets\\EdTech\\reactapp\\assets\\images\\QuizCardGame.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\assets\\images\\Simulator.jpg", "PackagePath": "staticwebassets\\EdTech\\reactapp\\assets\\images\\Simulator.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\assets\\images\\TreasureHuntGame.jpg", "PackagePath": "staticwebassets\\EdTech\\reactapp\\assets\\images\\TreasureHuntGame.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\assets\\images\\layers-2x.png", "PackagePath": "staticwebassets\\EdTech\\reactapp\\assets\\images\\layers-2x.png"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\assets\\images\\layers.png", "PackagePath": "staticwebassets\\EdTech\\reactapp\\assets\\images\\layers.png"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\assets\\images\\marker-icon.png", "PackagePath": "staticwebassets\\EdTech\\reactapp\\assets\\images\\marker-icon.png"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\assets\\images\\marker-shadow.png", "PackagePath": "staticwebassets\\EdTech\\reactapp\\assets\\images\\marker-shadow.png"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\assets\\images\\practiceBackground.png", "PackagePath": "staticwebassets\\EdTech\\reactapp\\assets\\images\\practiceBackground.png"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\demo.html", "PackagePath": "staticwebassets\\EdTech\\reactapp\\demo.html"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\exam-management-router.js", "PackagePath": "staticwebassets\\EdTech\\reactapp\\exam-management-router.js"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\exam-management-router.js.map", "PackagePath": "staticwebassets\\EdTech\\reactapp\\exam-management-router.js.map"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\exams.html", "PackagePath": "staticwebassets\\EdTech\\reactapp\\exams.html"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\iconStore.html", "PackagePath": "staticwebassets\\EdTech\\reactapp\\iconStore.html"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\index.html", "PackagePath": "staticwebassets\\EdTech\\reactapp\\index.html"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\practiceExams.html", "PackagePath": "staticwebassets\\EdTech\\reactapp\\practiceExams.html"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\preview.html", "PackagePath": "staticwebassets\\EdTech\\reactapp\\preview.html"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\question.html", "PackagePath": "staticwebassets\\EdTech\\reactapp\\question.html"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\runtime.js", "PackagePath": "staticwebassets\\EdTech\\reactapp\\runtime.js"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\EdTech\\reactapp\\runtime.js.map", "PackagePath": "staticwebassets\\EdTech\\reactapp\\runtime.js.map"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\client-proxies\\edtect-study-proxy.js", "PackagePath": "staticwebassets\\client-proxies\\edtect-study-proxy.js"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\css\\lesson-config-custom.css", "PackagePath": "staticwebassets\\css\\lesson-config-custom.css"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\css\\lesson-config.css", "PackagePath": "staticwebassets\\css\\lesson-config.css"}, {"Id": "C:\\Users\\<USER>\\source\\EdTech\\EdTechStudy\\src\\EdTech.Study.Web\\wwwroot\\js\\common\\lesson-init-module.js", "PackagePath": "staticwebassets\\js\\common\\lesson-init-module.js"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.EdTech.Study.Web.Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssetEndpoints.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.EdTech.Study.Web.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.build.EdTech.Study.Web.props", "PackagePath": "build\\EdTech.Study.Web.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.buildMultiTargeting.EdTech.Study.Web.props", "PackagePath": "buildMultiTargeting\\EdTech.Study.Web.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.buildTransitive.EdTech.Study.Web.props", "PackagePath": "buildTransitive\\EdTech.Study.Web.props"}], "ElementsToRemove": []}