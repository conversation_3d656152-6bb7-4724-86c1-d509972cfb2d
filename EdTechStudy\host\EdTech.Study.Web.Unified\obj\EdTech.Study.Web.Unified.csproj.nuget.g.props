﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <RestoreSuccess Condition=" '$(RestoreSuccess)' == '' ">True</RestoreSuccess>
    <RestoreTool Condition=" '$(RestoreTool)' == '' ">NuGet</RestoreTool>
    <ProjectAssetsFile Condition=" '$(ProjectAssetsFile)' == '' ">$(MSBuildThisFileDirectory)project.assets.json</ProjectAssetsFile>
    <NuGetPackageRoot Condition=" '$(NuGetPackageRoot)' == '' ">$(UserProfile)\.nuget\packages\</NuGetPackageRoot>
    <NuGetPackageFolders Condition=" '$(NuGetPackageFolders)' == '' ">C:\Users\<USER>\.nuget\packages\;C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages</NuGetPackageFolders>
    <NuGetProjectStyle Condition=" '$(NuGetProjectStyle)' == '' ">PackageReference</NuGetProjectStyle>
    <NuGetToolVersion Condition=" '$(NuGetToolVersion)' == '' ">6.12.1</NuGetToolVersion>
  </PropertyGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <SourceRoot Include="C:\Users\<USER>\.nuget\packages\" />
    <SourceRoot Include="C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages\" />
  </ItemGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Content Include="$(NuGetPackageRoot)volo.abp.featuremanagement.web\8.1.4\contentFiles\any\net8.0\Volo.Abp.FeatureManagement.Web.abppkg.analyze.json" Condition="Exists('$(NuGetPackageRoot)volo.abp.featuremanagement.web\8.1.4\contentFiles\any\net8.0\Volo.Abp.FeatureManagement.Web.abppkg.analyze.json')">
      <NuGetPackageId>Volo.Abp.FeatureManagement.Web</NuGetPackageId>
      <NuGetPackageVersion>8.1.4</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <Private>False</Private>
      <Link>Volo.Abp.FeatureManagement.Web.abppkg.analyze.json</Link>
    </Content>
  </ItemGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Content Include="$(NuGetPackageRoot)volo.abp.tenantmanagement.web\8.1.4\contentFiles\any\net8.0\Volo.Abp.TenantManagement.Web.abppkg.analyze.json" Condition="Exists('$(NuGetPackageRoot)volo.abp.tenantmanagement.web\8.1.4\contentFiles\any\net8.0\Volo.Abp.TenantManagement.Web.abppkg.analyze.json')">
      <NuGetPackageId>Volo.Abp.TenantManagement.Web</NuGetPackageId>
      <NuGetPackageVersion>8.1.4</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <Private>False</Private>
      <Link>Volo.Abp.TenantManagement.Web.abppkg.analyze.json</Link>
    </Content>
  </ItemGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Content Include="$(NuGetPackageRoot)volo.abp.identity.web\8.1.4\contentFiles\any\net8.0\Volo.Abp.Identity.Web.abppkg.analyze.json" Condition="Exists('$(NuGetPackageRoot)volo.abp.identity.web\8.1.4\contentFiles\any\net8.0\Volo.Abp.Identity.Web.abppkg.analyze.json')">
      <NuGetPackageId>Volo.Abp.Identity.Web</NuGetPackageId>
      <NuGetPackageVersion>8.1.4</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <Private>False</Private>
      <Link>Volo.Abp.Identity.Web.abppkg.analyze.json</Link>
    </Content>
  </ItemGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Content Include="$(NuGetPackageRoot)volo.abp.identity.httpapi\8.1.4\contentFiles\any\net8.0\Volo.Abp.Identity.HttpApi.abppkg.analyze.json" Condition="Exists('$(NuGetPackageRoot)volo.abp.identity.httpapi\8.1.4\contentFiles\any\net8.0\Volo.Abp.Identity.HttpApi.abppkg.analyze.json')">
      <NuGetPackageId>Volo.Abp.Identity.HttpApi</NuGetPackageId>
      <NuGetPackageVersion>8.1.4</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <Private>False</Private>
      <Link>Volo.Abp.Identity.HttpApi.abppkg.analyze.json</Link>
    </Content>
  </ItemGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Content Include="$(NuGetPackageRoot)volo.abp.aspnetcore.serilog\8.1.4\contentFiles\any\net8.0\Volo.Abp.AspNetCore.Serilog.abppkg.analyze.json" Condition="Exists('$(NuGetPackageRoot)volo.abp.aspnetcore.serilog\8.1.4\contentFiles\any\net8.0\Volo.Abp.AspNetCore.Serilog.abppkg.analyze.json')">
      <NuGetPackageId>Volo.Abp.AspNetCore.Serilog</NuGetPackageId>
      <NuGetPackageVersion>8.1.4</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <Private>False</Private>
      <Link>Volo.Abp.AspNetCore.Serilog.abppkg.analyze.json</Link>
    </Content>
  </ItemGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Content Include="$(NuGetPackageRoot)volo.abp.aspnetcore.mvc.ui.theme.basic\8.1.4\contentFiles\any\net8.0\Volo.Abp.AspNetCore.Mvc.UI.Theme.Basic.abppkg.analyze.json" Condition="Exists('$(NuGetPackageRoot)volo.abp.aspnetcore.mvc.ui.theme.basic\8.1.4\contentFiles\any\net8.0\Volo.Abp.AspNetCore.Mvc.UI.Theme.Basic.abppkg.analyze.json')">
      <NuGetPackageId>Volo.Abp.AspNetCore.Mvc.UI.Theme.Basic</NuGetPackageId>
      <NuGetPackageVersion>8.1.4</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <Private>False</Private>
      <Link>Volo.Abp.AspNetCore.Mvc.UI.Theme.Basic.abppkg.analyze.json</Link>
    </Content>
  </ItemGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Content Include="$(NuGetPackageRoot)volo.abp.account.web\8.1.4\contentFiles\any\net8.0\Volo.Abp.Account.Web.abppkg.analyze.json" Condition="Exists('$(NuGetPackageRoot)volo.abp.account.web\8.1.4\contentFiles\any\net8.0\Volo.Abp.Account.Web.abppkg.analyze.json')">
      <NuGetPackageId>Volo.Abp.Account.Web</NuGetPackageId>
      <NuGetPackageVersion>8.1.4</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <Private>False</Private>
      <Link>Volo.Abp.Account.Web.abppkg.analyze.json</Link>
    </Content>
  </ItemGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Content Include="$(NuGetPackageRoot)tsp.volo.abp.aspnetcore.mvc.ui.theme.leptonx\1.0.14\contentFiles\any\net8.0\Themes\LeptonX\Global\side-menu\libs\bootstrap-icons\font\bootstrap-icons.json" Condition="Exists('$(NuGetPackageRoot)tsp.volo.abp.aspnetcore.mvc.ui.theme.leptonx\1.0.14\contentFiles\any\net8.0\Themes\LeptonX\Global\side-menu\libs\bootstrap-icons\font\bootstrap-icons.json')">
      <NuGetPackageId>Tsp.Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonX</NuGetPackageId>
      <NuGetPackageVersion>1.0.14</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <Private>False</Private>
      <Link>Themes\LeptonX\Global\side-menu\libs\bootstrap-icons\font\bootstrap-icons.json</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)tsp.volo.abp.aspnetcore.mvc.ui.theme.leptonx\1.0.14\contentFiles\any\net8.0\Themes\LeptonX\Global\side-menu\libs\bootstrap-icons\package.json" Condition="Exists('$(NuGetPackageRoot)tsp.volo.abp.aspnetcore.mvc.ui.theme.leptonx\1.0.14\contentFiles\any\net8.0\Themes\LeptonX\Global\side-menu\libs\bootstrap-icons\package.json')">
      <NuGetPackageId>Tsp.Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonX</NuGetPackageId>
      <NuGetPackageVersion>1.0.14</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <Private>False</Private>
      <Link>Themes\LeptonX\Global\side-menu\libs\bootstrap-icons\package.json</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)tsp.volo.abp.aspnetcore.mvc.ui.theme.leptonx\1.0.14\contentFiles\any\net8.0\compilerconfig.json" Condition="Exists('$(NuGetPackageRoot)tsp.volo.abp.aspnetcore.mvc.ui.theme.leptonx\1.0.14\contentFiles\any\net8.0\compilerconfig.json')">
      <NuGetPackageId>Tsp.Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonX</NuGetPackageId>
      <NuGetPackageVersion>1.0.14</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <Private>False</Private>
      <Link>compilerconfig.json</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)tsp.volo.abp.aspnetcore.mvc.ui.theme.leptonx\1.0.14\contentFiles\any\net8.0\package-lock.json" Condition="Exists('$(NuGetPackageRoot)tsp.volo.abp.aspnetcore.mvc.ui.theme.leptonx\1.0.14\contentFiles\any\net8.0\package-lock.json')">
      <NuGetPackageId>Tsp.Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonX</NuGetPackageId>
      <NuGetPackageVersion>1.0.14</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <Private>False</Private>
      <Link>package-lock.json</Link>
    </Content>
  </ItemGroup>
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration.usersecrets\8.0.0\buildTransitive\net6.0\Microsoft.Extensions.Configuration.UserSecrets.props" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration.usersecrets\8.0.0\buildTransitive\net6.0\Microsoft.Extensions.Configuration.UserSecrets.props')" />
    <Import Project="$(NuGetPackageRoot)microsoft.entityframeworkcore\8.0.7\buildTransitive\net8.0\Microsoft.EntityFrameworkCore.props" Condition="Exists('$(NuGetPackageRoot)microsoft.entityframeworkcore\8.0.7\buildTransitive\net8.0\Microsoft.EntityFrameworkCore.props')" />
    <Import Project="$(NuGetPackageRoot)volo.abp.aspnetcore.components.web\8.1.4\buildTransitive\Volo.Abp.AspNetCore.Components.Web.props" Condition="Exists('$(NuGetPackageRoot)volo.abp.aspnetcore.components.web\8.1.4\buildTransitive\Volo.Abp.AspNetCore.Components.Web.props')" />
    <Import Project="$(NuGetPackageRoot)blazorise\1.4.1\buildTransitive\Blazorise.props" Condition="Exists('$(NuGetPackageRoot)blazorise\1.4.1\buildTransitive\Blazorise.props')" />
    <Import Project="$(NuGetPackageRoot)blazorise.snackbar\1.4.1\buildTransitive\Blazorise.Snackbar.props" Condition="Exists('$(NuGetPackageRoot)blazorise.snackbar\1.4.1\buildTransitive\Blazorise.Snackbar.props')" />
    <Import Project="$(NuGetPackageRoot)blazorise.datagrid\1.4.1\buildTransitive\Blazorise.DataGrid.props" Condition="Exists('$(NuGetPackageRoot)blazorise.datagrid\1.4.1\buildTransitive\Blazorise.DataGrid.props')" />
    <Import Project="$(NuGetPackageRoot)volo.abp.blazoriseui\8.1.4\buildTransitive\Volo.Abp.BlazoriseUI.props" Condition="Exists('$(NuGetPackageRoot)volo.abp.blazoriseui\8.1.4\buildTransitive\Volo.Abp.BlazoriseUI.props')" />
    <Import Project="$(NuGetPackageRoot)microsoft.entityframeworkcore.design\8.0.7\build\net8.0\Microsoft.EntityFrameworkCore.Design.props" Condition="Exists('$(NuGetPackageRoot)microsoft.entityframeworkcore.design\8.0.7\build\net8.0\Microsoft.EntityFrameworkCore.Design.props')" />
    <Import Project="$(NuGetPackageRoot)configureawait.fody\3.3.1\build\ConfigureAwait.Fody.props" Condition="Exists('$(NuGetPackageRoot)configureawait.fody\3.3.1\build\ConfigureAwait.Fody.props')" />
  </ImportGroup>
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <PkgMicrosoft_CodeAnalysis_Analyzers Condition=" '$(PkgMicrosoft_CodeAnalysis_Analyzers)' == '' ">C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.3</PkgMicrosoft_CodeAnalysis_Analyzers>
    <PkgMicrosoft_Extensions_ApiDescription_Server Condition=" '$(PkgMicrosoft_Extensions_ApiDescription_Server)' == '' ">C:\Users\<USER>\.nuget\packages\microsoft.extensions.apidescription.server\6.0.5</PkgMicrosoft_Extensions_ApiDescription_Server>
    <PkgMicrosoft_EntityFrameworkCore_Tools Condition=" '$(PkgMicrosoft_EntityFrameworkCore_Tools)' == '' ">C:\Users\<USER>\.nuget\packages\microsoft.entityframeworkcore.tools\8.0.7</PkgMicrosoft_EntityFrameworkCore_Tools>
    <PkgMicrosoft_AspNetCore_Components_WebAssembly_Server Condition=" '$(PkgMicrosoft_AspNetCore_Components_WebAssembly_Server)' == '' ">C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.components.webassembly.server\8.0.14</PkgMicrosoft_AspNetCore_Components_WebAssembly_Server>
  </PropertyGroup>
</Project>