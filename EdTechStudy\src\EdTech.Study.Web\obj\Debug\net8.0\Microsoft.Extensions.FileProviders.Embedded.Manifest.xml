﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?><Manifest><ManifestVersion>1.0</ManifestVersion><FileSystem><File Name="Microsoft.Extensions.FileProviders.Embedded.Manifest.xml"><ResourcePath>Microsoft.Extensions.FileProviders.Embedded.Manifest.xml</ResourcePath></File><Directory Name="wwwroot"><Directory Name="EdTech"><Directory Name="reactapp"><File Name="175.js"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.175.js</ResourcePath></File><File Name="175.js.map"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.175.js.map</ResourcePath></File><File Name="185.js"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.185.js</ResourcePath></File><File Name="185.js.LICENSE.txt"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.185.js.LICENSE.txt</ResourcePath></File><File Name="185.js.map"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.185.js.map</ResourcePath></File><File Name="525.js"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.525.js</ResourcePath></File><File Name="525.js.LICENSE.txt"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.525.js.LICENSE.txt</ResourcePath></File><File Name="525.js.map"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.525.js.map</ResourcePath></File><File Name="652.js"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.652.js</ResourcePath></File><File Name="652.js.LICENSE.txt"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.652.js.LICENSE.txt</ResourcePath></File><File Name="652.js.map"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.652.js.map</ResourcePath></File><File Name="915.js"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.915.js</ResourcePath></File><File Name="915.js.LICENSE.txt"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.915.js.LICENSE.txt</ResourcePath></File><File Name="915.js.map"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.915.js.map</ResourcePath></File><File Name="BasePage.js"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.BasePage.js</ResourcePath></File><File Name="BasePage.js.LICENSE.txt"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.BasePage.js.LICENSE.txt</ResourcePath></File><File Name="BasePage.js.map"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.BasePage.js.map</ResourcePath></File><File Name="DemoLessonPage.js"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.DemoLessonPage.js</ResourcePath></File><File Name="DemoLessonPage.js.LICENSE.txt"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.DemoLessonPage.js.LICENSE.txt</ResourcePath></File><File Name="DemoLessonPage.js.map"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.DemoLessonPage.js.map</ResourcePath></File><File Name="ExamManagementPage.js"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.ExamManagementPage.js</ResourcePath></File><File Name="ExamManagementPage.js.LICENSE.txt"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.ExamManagementPage.js.LICENSE.txt</ResourcePath></File><File Name="ExamManagementPage.js.map"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.ExamManagementPage.js.map</ResourcePath></File><File Name="IconStorePage.js"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.IconStorePage.js</ResourcePath></File><File Name="IconStorePage.js.LICENSE.txt"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.IconStorePage.js.LICENSE.txt</ResourcePath></File><File Name="IconStorePage.js.map"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.IconStorePage.js.map</ResourcePath></File><File Name="PracticeExamPage.js"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.PracticeExamPage.js</ResourcePath></File><File Name="PracticeExamPage.js.LICENSE.txt"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.PracticeExamPage.js.LICENSE.txt</ResourcePath></File><File Name="PracticeExamPage.js.map"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.PracticeExamPage.js.map</ResourcePath></File><File Name="PreviewLessonPage.js"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.PreviewLessonPage.js</ResourcePath></File><File Name="PreviewLessonPage.js.LICENSE.txt"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.PreviewLessonPage.js.LICENSE.txt</ResourcePath></File><File Name="PreviewLessonPage.js.map"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.PreviewLessonPage.js.map</ResourcePath></File><File Name="QuestionPage.js"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.QuestionPage.js</ResourcePath></File><File Name="QuestionPage.js.LICENSE.txt"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.QuestionPage.js.LICENSE.txt</ResourcePath></File><File Name="QuestionPage.js.map"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.QuestionPage.js.map</ResourcePath></File><Directory Name="assets"><Directory Name="css"><File Name="app.css"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.assets.css.app.css</ResourcePath></File><File Name="app.css.map"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.assets.css.app.css.map</ResourcePath></File><File Name="vendor.css"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.assets.css.vendor.css</ResourcePath></File><File Name="vendor.css.map"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.assets.css.vendor.css.map</ResourcePath></File></Directory><Directory Name="images"><File Name="CoordinateFinderGame.jpg"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.assets.images.CoordinateFinderGame.jpg</ResourcePath></File><File Name="Demo.png"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.assets.images.Demo.png</ResourcePath></File><File Name="Game.jpg"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.assets.images.Game.jpg</ResourcePath></File><File Name="Layout.png"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.assets.images.Layout.png</ResourcePath></File><File Name="MillionaireGame.jpg"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.assets.images.MillionaireGame.jpg</ResourcePath></File><File Name="Quiz.jpg"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.assets.images.Quiz.jpg</ResourcePath></File><File Name="QuizCardGame.jpg"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.assets.images.QuizCardGame.jpg</ResourcePath></File><File Name="Simulator.jpg"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.assets.images.Simulator.jpg</ResourcePath></File><File Name="TreasureHuntGame.jpg"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.assets.images.TreasureHuntGame.jpg</ResourcePath></File><File Name="layers-2x.png"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.assets.images.layers-2x.png</ResourcePath></File><File Name="layers.png"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.assets.images.layers.png</ResourcePath></File><File Name="marker-icon.png"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.assets.images.marker-icon.png</ResourcePath></File><File Name="marker-shadow.png"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.assets.images.marker-shadow.png</ResourcePath></File><File Name="practiceBackground.png"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.assets.images.practiceBackground.png</ResourcePath></File></Directory></Directory><File Name="demo.html"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.demo.html</ResourcePath></File><File Name="exam-management-router.js"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.exam-management-router.js</ResourcePath></File><File Name="exam-management-router.js.map"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.exam-management-router.js.map</ResourcePath></File><File Name="exams.html"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.exams.html</ResourcePath></File><File Name="iconStore.html"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.iconStore.html</ResourcePath></File><File Name="index.html"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.index.html</ResourcePath></File><File Name="practiceExams.html"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.practiceExams.html</ResourcePath></File><File Name="preview.html"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.preview.html</ResourcePath></File><File Name="question.html"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.question.html</ResourcePath></File><File Name="runtime.js"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.runtime.js</ResourcePath></File><File Name="runtime.js.map"><ResourcePath>EdTech.Study.Web.wwwroot.EdTech.reactapp.runtime.js.map</ResourcePath></File></Directory></Directory><Directory Name="client-proxies"><File Name="edtect-study-proxy.js"><ResourcePath>EdTech.Study.Web.wwwroot.client_proxies.edtect-study-proxy.js</ResourcePath></File></Directory><Directory Name="css"><Directory Name="home"><File Name="styles.css"><ResourcePath>EdTech.Study.Web.wwwroot.css.home.styles.css</ResourcePath></File></Directory><File Name="lesson-config-custom.css"><ResourcePath>EdTech.Study.Web.wwwroot.css.lesson-config-custom.css</ResourcePath></File><File Name="lesson-config.css"><ResourcePath>EdTech.Study.Web.wwwroot.css.lesson-config.css</ResourcePath></File></Directory><Directory Name="images"><Directory Name="classten"><Directory Name="geography"><Directory Name="mapreading"><File Name="agriculture-gps.png"><ResourcePath>EdTech.Study.Web.wwwroot.images.classten.geography.mapreading.agriculture-gps.png</ResourcePath></File><File Name="emergency-gps.png"><ResourcePath>EdTech.Study.Web.wwwroot.images.classten.geography.mapreading.emergency-gps.png</ResourcePath></File><File Name="tourism-gps.png"><ResourcePath>EdTech.Study.Web.wwwroot.images.classten.geography.mapreading.tourism-gps.png</ResourcePath></File></Directory></Directory></Directory><Directory Name="home"><Directory Name="contacts"><File Name="messenger.png"><ResourcePath>EdTech.Study.Web.wwwroot.images.home.contacts.messenger.png</ResourcePath></File><File Name="support.png"><ResourcePath>EdTech.Study.Web.wwwroot.images.home.contacts.support.png</ResourcePath></File><File Name="zalo.png"><ResourcePath>EdTech.Study.Web.wwwroot.images.home.contacts.zalo.png</ResourcePath></File></Directory><File Name="logo.png"><ResourcePath>EdTech.Study.Web.wwwroot.images.home.logo.png</ResourcePath></File><File Name="profile-avatar.png"><ResourcePath>EdTech.Study.Web.wwwroot.images.home.profile-avatar.png</ResourcePath></File><Directory Name="subjects"><File Name="language.png"><ResourcePath>EdTech.Study.Web.wwwroot.images.home.subjects.language.png</ResourcePath></File><File Name="literature.png"><ResourcePath>EdTech.Study.Web.wwwroot.images.home.subjects.literature.png</ResourcePath></File><File Name="math.png"><ResourcePath>EdTech.Study.Web.wwwroot.images.home.subjects.math.png</ResourcePath></File><File Name="naturalscience.png"><ResourcePath>EdTech.Study.Web.wwwroot.images.home.subjects.naturalscience.png</ResourcePath></File><File Name="socialscience.png"><ResourcePath>EdTech.Study.Web.wwwroot.images.home.subjects.socialscience.png</ResourcePath></File></Directory></Directory></Directory><Directory Name="js"><Directory Name="common"><File Name="lesson-init-module.js"><ResourcePath>EdTech.Study.Web.wwwroot.js.common.lesson-init-module.js</ResourcePath></File><File Name="questions-init-module.js"><ResourcePath>EdTech.Study.Web.wwwroot.js.common.questions-init-module.js</ResourcePath></File></Directory><Directory Name="config"><File Name="lesson-config.js"><ResourcePath>EdTech.Study.Web.wwwroot.js.config.lesson-config.js</ResourcePath></File></Directory><Directory Name="home"><File Name="main.js"><ResourcePath>EdTech.Study.Web.wwwroot.js.home.main.js</ResourcePath></File></Directory><Directory Name="menu"><File Name="index.js"><ResourcePath>EdTech.Study.Web.wwwroot.js.menu.index.js</ResourcePath></File></Directory><Directory Name="store"><File Name="lesson-config-db-module.js"><ResourcePath>EdTech.Study.Web.wwwroot.js.store.lesson-config-db-module.js</ResourcePath></File></Directory></Directory><Directory Name="template"><File Name="index.html"><ResourcePath>EdTech.Study.Web.wwwroot.template.index.html</ResourcePath></File></Directory></Directory></FileSystem></Manifest>