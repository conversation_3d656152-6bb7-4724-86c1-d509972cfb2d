import PracticeEngineWrapper from '../../components/quizs/practiceEngines/PracticeEngineWrapper';
import { ETypeEdTechComponent } from '../../enums/AppEnums';
import { IEdTechComponent } from '../../interfaces/AppComponents';

export const PRACTICES_ED_TECH_COMPONENT: IEdTechComponent[] = [
  {
    name: 'PracticeEngineWrapper',
    title: '<PERSON>à<PERSON> Tra Bài Họ<PERSON>',
    components: [
      {
        version: '1.0.0',
        component: PracticeEngineWrapper,
        schema: undefined,
      },
    ],
    type: ETypeEdTechComponent.QUIZ,
    tags: [
      'Đ<PERSON>h giá',
      'Thực hành',
      '<PERSON><PERSON> nhân',
      '<PERSON>hiều lựa chọn',
      'Đ<PERSON>h giá hiệu suất',
      'Ôn tập kiến thức',
      'Dựa trên văn bản',
    ],
    description:
      'Phần kiểm tra kiến thức bài học với các câu hỏi đa dạng, gi<PERSON><PERSON> người học đánh giá mức độ hiểu bài và rèn luyện kỹ năng.',
    schema: undefined,
  },
];

export const standardQuestionRichTextEditorItems = [
  'Undo',
  'Redo',
  'Cut',
  'Copy',
  'Paste',
  'Bold',
  'Italic',
  'Underline',
  'StrikeThrough',
  'FontName',
  'FontSize',
  'FontColor',
  'BackgroundColor',
  'LowerCase',
  'UpperCase',
  'Formats',
  'Alignments',
  'OrderedList',
  'UnorderedList',
  'Outdent',
  'Indent',
  'CreateLink',
  'Image',
  'CreateTable',
  'FormatPainter',
  'ClearFormat',
  'Print',
  'FullScreen',
  'Superscript',
  'Subscript',
  '|',
];
