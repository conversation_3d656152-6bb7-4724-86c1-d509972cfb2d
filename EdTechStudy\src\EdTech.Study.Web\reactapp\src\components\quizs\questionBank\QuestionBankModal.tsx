import React, { useState, useEffect } from 'react';
import {
  Button,
  Input,
  Table,
  Select,
  Form,
  Collapse,
  Tag,
  Typography,
  Divider,
  Badge,
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  CheckOutlined,
  BookOutlined,
} from '@ant-design/icons';
import axios from 'axios';
import DOMPurify from 'dompurify';
import './QuestionBankModal.css';
import { appConfig } from '../../../constants/AppContants';

import { ModalAntdCustom } from '../../../components/customs/antd/ModalAntdCustom';
import { QuestionDto } from './questionBankTemplate';
import { mapQuestionStatusString } from '../../../utils/adapters/questionDraftAdapter';
import { useQuestionContext } from '../../../providers/Questions/QuestionProvider';

const { Panel } = Collapse;
const { Option } = Select;

interface QuestionBankModalProps {
  visible: boolean;
  onCancel: () => void;
  onSelect: (selectedQuestions: QuestionDto[]) => void;
}
const difficultyLabels = {
  1: 'Rất dễ',
  2: '<PERSON><PERSON>',
  3: 'Trung b<PERSON>nh',
  4: 'Khó',
  5: 'Rất khó',
};

const questionTypeLabels = {
  SingleChoice: 'Trắc nghiệm 1 đáp án',
  MultipleChoice: 'Trắc nghiệm nhiều đáp án',
  Matching: 'Câu hỏi nối',
  FillBlanks: 'Điền từ',
  Essay: 'Câu hỏi tự luận',
};

const QuestionBankModal: React.FC<QuestionBankModalProps> = ({
  visible,
  onCancel,
  onSelect,
}) => {
  const { subjects = [], lessonGrades: grades = [] } = useQuestionContext();
  const [questions, setQuestions] = useState<QuestionDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  const [searchForm] = Form.useForm();
  const [advancedVisible, setAdvancedVisible] = useState(false);

  // Fetch questions based on filters and pagination
  const fetchQuestions = async (params: any = {}) => {
    setLoading(true);

    try {
      // Build OData query
      let query = `/odata/questions?$count=true&$skip=${
        (params.current - 1) * params.pageSize
      }&$top=${
        params.pageSize
      }&$expand=Subject,Grade,Options,FillInBlankAnswers,MatchingAnswers,MatchingItems`;

      // Add filters
      const filters = [];

      // Default filters for SourceType and Status using proper enum format
      filters.push(`SourceType eq EdTech.Study.Exams.ExamSourceType'Banked'`); // Banked questions only
      filters.push(
        `(Status eq EdTech.Study.Enum.ExamStatus'Approved' or Status eq EdTech.Study.Enum.ExamStatus'Published')`
      ); // Approved or Published questions only

      if (params.keyword) {
        filters.push(
          `contains(Content, '${params.keyword}') or contains(Subject/Name, '${params.keyword}') or contains(Grade/Name, '${params.keyword}') or contains(Topics, '${params.keyword}') or contains(Tags, '${params.keyword}')`
        );
      }

      if (params.subjectId) {
        filters.push(`SubjectId eq ${params.subjectId}`);
      }

      if (params.gradeId) {
        filters.push(`GradeId eq ${params.gradeId}`);
      }

      if (params.difficulty) {
        filters.push(`Difficulty eq ${params.difficulty}`);
      }

      if (params.questionType) {
        filters.push(`QuestionType eq '${params.questionType}'`);
      }

      if (params.topics) {
        filters.push(`contains(Topics, '${params.topics}')`);
      }

      if (params.tags) {
        filters.push(`contains(Tags, '${params.tags}')`);
      }

      if (filters.length > 0) {
        query += `&$filter=${filters.join(' and ')}`;
      }

      const response = await axios.get(appConfig.baseURL + query);
      let data: any[] = [];
      if (response.data.value) {
        data = response.data.value;
        data = data.map((question) => ({
          ...question,
          Status: mapQuestionStatusString(question.Status as unknown as string),
        }));
      }

      setQuestions(data);
      setPagination({
        ...params,
        total: response.data['@odata.count'],
      });
    } catch (error) {
      console.error('Error fetching questions:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load questions when modal becomes visible
  useEffect(() => {
    if (visible) {
      fetchQuestions({
        current: pagination.current,
        pageSize: pagination.pageSize,
      });
    } else {
      // Reset selection when modal closes
      setSelectedRowKeys([]);
    }
  }, [visible]);

  // Handle table change (pagination, filters, sorter)
  const handleTableChange = (newPagination: any) => {
    fetchQuestions({
      ...searchForm.getFieldsValue(),
      current: newPagination.current,
      pageSize: newPagination.pageSize,
    });
  };

  // Handle basic search
  const handleSearch = () => {
    const keyword = searchForm.getFieldValue('keyword');
    fetchQuestions({
      current: 1,
      pageSize: pagination.pageSize,
      keyword,
      ...searchForm.getFieldsValue(),
    });
  };

  // Handle advanced search
  const handleAdvancedSearch = () => {
    fetchQuestions({
      current: 1,
      pageSize: pagination.pageSize,
      ...searchForm.getFieldsValue(),
    });
    setAdvancedVisible(false);
  };

  // Handle row selection
  const onSelectChange = (
    newSelectedRowKeys: React.Key[],
    selectedRows: QuestionDto[]
  ) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  // Handle confirm selection
  const handleConfirmSelection = () => {
    const selectedQuestions = questions.filter((q) =>
      selectedRowKeys.includes(q.Id)
    );
    onSelect(selectedQuestions);
  };

  // Handle row click to select/deselect
  const handleRowClick = (record: QuestionDto) => {
    const key = record.Id;
    const newSelectedRowKeys = [...selectedRowKeys];

    if (newSelectedRowKeys.includes(key)) {
      // Deselect if already selected
      const index = newSelectedRowKeys.indexOf(key);
      newSelectedRowKeys.splice(index, 1);
    } else {
      // Select if not already selected
      newSelectedRowKeys.push(key);
    }

    setSelectedRowKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  // Custom row properties to handle click events
  const onRow = (record: QuestionDto) => ({
    onClick: () => handleRowClick(record),
    style: { cursor: 'pointer' },
  });

  const columns = [
    {
      title: 'Nội dung',
      dataIndex: 'Content',
      key: 'Content',
      width: 450,
      render: (text: string) => (
        <div
          className="question-content"
          dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(text) }}
        />
      ),
    },
    {
      title: 'Loại câu hỏi',
      dataIndex: 'QuestionType',
      key: 'QuestionType',
      width: 150,
      render: (type: string) => (
        <Tag color="blue">
          {questionTypeLabels[type as keyof typeof questionTypeLabels] || type}
        </Tag>
      ),
    },
    {
      title: 'Môn học',
      dataIndex: 'SubjectId',
      key: 'SubjectId',
      width: 90,
      render: (subject: string) =>
        subject ? (
          <Tag color="purple" className="tailwind-px-2 tailwind-py-1">
            {subjects.find((s) => s.id === subject)?.name || subject}
          </Tag>
        ) : (
          <Tag color="default" className="tailwind-px-2 tailwind-py-1">
            Chưa xác định
          </Tag>
        ),
    },
    {
      title: 'Lớp',
      dataIndex: 'GradeId',
      key: 'GradeId',
      width: 90,
      render: (grade: string) =>
        grade ? (
          <Tag color="blue" className="tailwind-px-2 tailwind-py-1">
            {grades.find((g) => g.id === grade)?.name || grade}
          </Tag>
        ) : (
          <Tag color="default" className="tailwind-px-2 tailwind-py-1">
            Chưa xác định
          </Tag>
        ),
    },
    {
      title: 'Độ khó',
      dataIndex: 'Difficulty',
      key: 'Difficulty',
      width: 80,
      render: (difficulty: number) => {
        const colors = ['', 'green', 'blue', 'orange', 'red'];
        return (
          <Tag color={colors[difficulty]}>
            {difficultyLabels[difficulty as keyof typeof difficultyLabels] ||
              difficulty}
          </Tag>
        );
      },
    },
    {
      title: 'Chủ đề',
      dataIndex: 'Topics',
      key: 'Topics',
      with: 150,
      render: (topics: string) => (
        <div className="topics-container">
          {topics?.split(',').map((topic, index) => (
            <Tag key={index} color="cyan">
              {topic.trim()}
            </Tag>
          ))}
        </div>
      ),
    },
    {
      title: 'Găn thẻ',
      dataIndex: 'Tags',
      key: 'Tags',
      with: 150,
      render: (tags: string) => (
        <div className="topics-container">
          {tags?.split(',').map((tags, index) => (
            <Tag key={index} color="pink">
              {tags.trim()}
            </Tag>
          ))}
        </div>
      ),
    },
  ];

  return (
    <ModalAntdCustom
      title={
        <div className="tailwind-flex tailwind-items-center">
          <BookOutlined className="tailwind-mr-2" />
          <span>Ngân hàng câu hỏi</span>
          <Badge count={selectedRowKeys.length} className="tailwind-ml-2" />
        </div>
      }
      open={visible}
      onCancel={onCancel}
      width="85%"
      style={{ top: 20 }}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          Hủy
        </Button>,
        <Button
          key="select"
          type="primary"
          icon={<CheckOutlined />}
          onClick={handleConfirmSelection}
          disabled={selectedRowKeys.length === 0}
          className="tailwind-bg-blue-500"
        >
          Chọn {selectedRowKeys.length > 0 ? `(${selectedRowKeys.length})` : ''}
        </Button>,
      ]}
      styles={{
        body: {
          padding: 0,
          maxHeight: 'calc(90vh - 100px)',
          overflow: 'auto',
        },
      }}
    >
      <div className="tailwind-mb-4">
        <Form form={searchForm} layout="vertical">
          <div className="tailwind-flex tailwind-gap-2">
            <Form.Item name="keyword" className="tailwind-flex-1 tailwind-mb-0">
              <Input
                placeholder="Tìm kiếm câu hỏi..."
                prefix={<SearchOutlined />}
                allowClear
                onPressEnter={handleSearch}
              />
            </Form.Item>
            <Button
              type="primary"
              onClick={handleSearch}
              className="tailwind-bg-blue-500"
            >
              Tìm kiếm
            </Button>
            <Button
              icon={<FilterOutlined />}
              onClick={() => setAdvancedVisible(!advancedVisible)}
              type={advancedVisible ? 'primary' : 'default'}
              className={advancedVisible ? 'tailwind-bg-blue-500' : ''}
            >
              Nâng cao
            </Button>
          </div>

          <Collapse
            activeKey={advancedVisible ? ['1'] : []}
            ghost
            className="tailwind-mt-2"
          >
            <Panel key="1" header={null} className="tailwind-p-0">
              <div className="tailwind-grid tailwind-grid-cols-1 md:tailwind-grid-cols-3 tailwind-gap-4">
                <Form.Item name="subjectId" label="Môn học">
                  <Select
                    placeholder="Chọn môn học"
                    allowClear
                    showSearch
                    optionFilterProp="children"
                  >
                    {subjects.map((subject) => (
                      <Option key={subject.id} value={subject.id}>
                        {subject.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
                <Form.Item name="gradeId" label="Lớp">
                  <Select
                    placeholder="Chọn lớp"
                    allowClear
                    showSearch
                    optionFilterProp="children"
                  >
                    {grades.map((grade) => (
                      <Option key={grade.id} value={grade.id}>
                        {grade.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
                <Form.Item name="difficulty" label="Độ khó">
                  <Select placeholder="Chọn độ khó" allowClear>
                    {Object.entries(difficultyLabels).map(([key, value]) => (
                      <Option key={key} value={Number(key)}>
                        {value}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
                <Form.Item name="questionType" label="Loại câu hỏi">
                  <Select placeholder="Chọn loại câu hỏi" allowClear>
                    {Object.entries(questionTypeLabels).map(([key, value]) => (
                      <Option key={key} value={key}>
                        {value}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
                <Form.Item name="topics" label="Chủ đề">
                  <Input placeholder="Nhập chủ đề" allowClear />
                </Form.Item>
                <Form.Item name="tags" label="Thẻ">
                  <Input placeholder="Nhập thẻ" allowClear />
                </Form.Item>
              </div>
              <div className="tailwind-flex tailwind-justify-end tailwind-mt-2">
                <Button
                  type="primary"
                  onClick={handleAdvancedSearch}
                  className="tailwind-bg-blue-500"
                >
                  Áp dụng bộ lọc
                </Button>
              </div>
            </Panel>
          </Collapse>
        </Form>
      </div>

      <Divider className="tailwind-my-2" />

      <Table
        rowSelection={rowSelection}
        columns={columns}
        dataSource={questions}
        rowKey="Id"
        pagination={pagination}
        loading={loading}
        onChange={handleTableChange}
        className="question-bank-table"
        scroll={{ x: 'max-content' }}
        onRow={onRow}
      />
    </ModalAntdCustom>
  );
};

export default QuestionBankModal;
