import { useEffect, useState, useCallback } from 'react';

import { useContext } from 'react';
import {
  BaseQuestion,
  PracticeEngineContext,
} from '../../../interfaces/quizs/questionBase';
import useDebouncedCallback from '../../../hooks/apps/useDebouncedCallback';

const useUpdateQuestion = () => {
  const { handleEditQuestion: handleChangeQuestion } = useContext(
    PracticeEngineContext
  );
  const [stack, setStack] = useState<
    (Partial<BaseQuestion> & { clientId: string })[]
  >([]);
  const [isPending, setPending] = useState(false);

  const handleUpdateQuestion = useDebouncedCallback(
    (update: Partial<BaseQuestion> & { clientId: string }) => {
      setStack((prev) => [...prev, update]);
    },
    [setStack]
  );

  useEffect(() => {
    const abortController = new AbortController();
    let promises: Promise<boolean>[] = [];
    if (stack.length > 0) {
      promises = stack.map(
        (update) =>
          new Promise<boolean>((resolve, reject) => {
            if (abortController.signal.aborted) {
              reject(new Error('Cancelled'));
              return;
            }
            try {
              handleChangeQuestion({
                ...update,
                clientId: update.clientId,
                parentId: update.parentId,
                userSelect: undefined,
              });
              resolve(true);
            } catch (error) {
              reject(error);
            }
          })
      );
      const handlePromise = async () => {
        await Promise.all(promises)
          .then(() => {
            setStack([]);
          })
          .catch((error) => {
            console.error('Failed to update questions:', error);
          });
      };
      setPending(true);
      handlePromise().then(() => {
        setPending(false);
      });
    }

    return () => {
      abortController.abort();
    };
  }, [stack, handleChangeQuestion]);

  return { handleUpdateQuestion, isPending };
};

export default useUpdateQuestion;
