/* Styles for time and score display */
.quiz-card .ant-tag {
  margin-right: 8px;
  font-size: 14px;
  padding: 4px 8px;
  display: flex;
  align-items: center;
}

.quiz-card .ant-tag .anticon {
  margin-right: 4px;
}

/* Orange tag for time */
.quiz-card .ant-tag-orange {
  color: #d46b08;
  background: #fff7e6;
  border-color: #ffd591;
}

/* Blue tag for score */
.quiz-card .ant-tag-blue {
  color: #1677ff;
  background: #e6f4ff;
  border-color: #91caff;
}

/* Question content container */
.question-content-container {
  padding: 8px 0;
}

/* Question number styling */
.question-number {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  min-width: fit-content;
  display: flex;
  align-items: center;
}

/* Question content styling */
.question-content {
  font-size: 15px;
  line-height: 1.6;
  color: #333;
  display: flex;
  align-items: center;
}

/* Explanation styling */
.explanation {
  padding: 16px 0px;
  font-style: italic;
}

/* Base question preview container */
.base-question-preview {
  margin-bottom: 16px;
}

/* ===== RESULT MODE STYLES ===== */

/* Result mode container */
.base-question-preview.result-mode {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  background: #fafafa;
}

/* Result mode correct styling */
.base-question-preview.result-mode.correct {
  border-color: #b7eb8f;
  background: #f6ffed;
}

/* Result mode incorrect styling */
.base-question-preview.result-mode.incorrect {
  border-color: #ffccc7;
  background: #fff2f0;
}

/* Result mode unanswered styling */
.base-question-preview.result-mode.unanswered {
  border-color: #d9d9d9;
  background: #f5f5f5;
}

/* Result content container */
.result-content-container {
  padding: 12px 0;
}

/* Result interaction container */
.result-interaction-container {
  padding: 12px 0;
}

/* Status icons styling */
.base-question-preview .tailwind-text-green-500 {
  color: #52c41a !important;
}

.base-question-preview .tailwind-text-red-500 {
  color: #ff4d4f !important;
}

.base-question-preview .tailwind-text-gray-400 {
  color: #bfbfbf !important;
}

/* Result mode explanation styling */
.base-question-preview.result-mode .explanation {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  margin-top: 12px;
}

.base-question-preview.result-mode .explanation.correct {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
}

.base-question-preview.result-mode .explanation.incorrect {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
}

/* Answer result containers */
.answer-result-container {
  margin-top: 16px;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

/* User answer display */
.user-answer-display {
  background-color: #f8f8f8;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 12px;
}

.user-answer-display .answer-label {
  font-weight: 600;
  color: #595959;
  margin-bottom: 8px;
  display: block;
}

.user-answer-display .answer-content {
  color: #262626;
  line-height: 1.6;
}

/* Correct answer display */
.correct-answer-display {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
  padding: 12px;
}

.correct-answer-display .answer-label {
  font-weight: 600;
  color: #389e0d;
  margin-bottom: 8px;
  display: block;
}

.correct-answer-display .answer-content {
  color: #135200;
  line-height: 1.6;
}

/* Explanation display in result mode */
.explanation-display {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 6px;
  padding: 12px;
}

.explanation-display .explanation-label {
  font-weight: 600;
  color: #0958d9;
  margin-bottom: 8px;
  display: block;
}

.explanation-display .explanation-content {
  color: #003a8c;
  line-height: 1.6;
  font-style: italic;
}

/* Multiple choice result styles */
.option-result-container {
  margin-bottom: 8px;
}

.option-result-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

/* Option states */
.option-result-item.selected-correct {
  background-color: #f6ffed;
  border-color: #b7eb8f;
}

.option-result-item.selected-incorrect {
  background-color: #fff2f0;
  border-color: #ffccc7;
}

.option-result-item.correct-not-selected {
  background-color: #f8f8f8;
  border-color: #d9d9d9;
}

.option-result-item.not-selected {
  background-color: #ffffff;
  border-color: #f0f0f0;
}

/* Option content */
.option-result-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.option-result-label {
  font-weight: 500;
  color: #595959;
  margin-right: 12px;
  min-width: 24px;
}

.option-result-text {
  color: #262626;
  line-height: 1.5;
}

/* Option feedback icons */
.option-result-feedback {
  margin-left: 12px;
}

.option-result-feedback .anticon {
  font-size: 16px;
}

.option-result-feedback .anticon.correct-icon {
  color: #52c41a;
}

.option-result-feedback .anticon.incorrect-icon {
  color: #ff4d4f;
}

/* Matching result styles */
.matching-result-container {
  margin-top: 16px;
}

.matching-visual-container {
  margin-bottom: 16px;
}

.matching-connection {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px 0;
}

.matching-line {
  height: 2px;
  width: 40px;
  margin: 0 8px;
}

.matching-line.correct {
  background-color: #52c41a;
}

.matching-line.incorrect {
  background-color: #ff4d4f;
}

.matching-feedback-icon {
  font-size: 14px;
}

.matching-feedback-icon.correct {
  color: #52c41a;
}

.matching-feedback-icon.incorrect {
  color: #ff4d4f;
}

.matching-item {
  padding: 8px 12px;
  background-color: #f8f8f8;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  margin-bottom: 8px;
}

.matching-item .item-label {
  font-weight: 500;
  color: #595959;
}

/* Fill blank result styles */
.fill-blank-result-container {
  margin-top: 16px;
}

.fill-blank-input-result {
  display: inline-block;
  padding: 4px 8px;
  margin: 0 4px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  min-width: 100px;
  text-align: center;
}

.fill-blank-input-result.correct {
  background-color: #f6ffed;
  border-color: #b7eb8f;
  color: #389e0d;
}

.fill-blank-input-result.incorrect {
  background-color: #fff2f0;
  border-color: #ffccc7;
  color: #cf1322;
}

.fill-blank-input-result.unanswered {
  background-color: #f5f5f5;
  border-color: #d9d9d9;
  color: #8c8c8c;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .base-question-preview.result-mode {
    padding: 12px;
    margin-bottom: 16px;
  }
  
  .option-result-item {
    padding: 10px;
  }
  
  .option-result-label {
    min-width: 20px;
    margin-right: 8px;
  }
  
  .user-answer-display,
  .correct-answer-display,
  .explanation-display {
    padding: 10px;
  }
  
  .matching-line {
    width: 30px;
  }
}

/* Animation for result reveal */
@keyframes resultReveal {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.base-question-preview.result-mode {
  animation: resultReveal 0.3s ease-out;
}

/* Hover effects for result mode */
.base-question-preview.result-mode:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Print styles */
@media print {
  .base-question-preview.result-mode {
    page-break-inside: avoid;
    border: 1px solid #000;
    background: white !important;
  }
  
  .option-result-item.selected-correct {
    background-color: #f0f0f0 !important;
    border-left: 4px solid #000;
  }
  
  .option-result-item.selected-incorrect {
    background-color: #f0f0f0 !important;
    border-left: 4px solid #666;
  }
}