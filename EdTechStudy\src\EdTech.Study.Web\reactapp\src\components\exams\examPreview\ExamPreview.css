.exam-header-card .card-body {
  padding: 16px!important;
}

/* Custom collapse styling */
.exam-section-header {
  padding: 0!important;
  border-radius: 4px!important;
  border: none!important;
  background-color: var(--edtt-color-selected);
  transition: background-color 0.3s ease;
}

.exam-section-header:hover {
  background-color: var(--edtt-color-selected-hover, #f0f7ff);
}

.exam-preview-container .ant-list-split .ant-list-item {
  border: none!important;
}

/* Question Sidebar Styles */
.exam-preview-container .ant-drawer.ant-drawer-open {
  z-index: var(--edtt-z-index-drawer) !important;
}

.question-sidebar {
  z-index: var(--edtt-z-index-drawer) !important;
}

/* Question sidebar statistics styles */
.question-sidebar-stats .exam-statistic-item {
  border: none;
  border-radius: 16px;
  padding: 8px 12px;
  font-weight: 500;
  font-size: 14px;
}

.question-sidebar-stats .total-tag {
  background-color: var(--edtt-color-primary-100);
  color: var(--edtt-color-primary-400);
  
}

.question-sidebar-stats .answered-tag {
  background-color: var(--edtt-color-quaternary-100);
  color: var(--edtt-color-quaternary-400);
}

.question-sidebar-stats .unanswered-tag {
  background-color: var(--edtt-color-warning-100);
  color: var(--edtt-color-warning-600);
}

/* Question sidebar item styles */
.question-sidebar-item {
  transition: all 0.2s ease;
  border-radius: 8px;
  margin: 2px 0;
}

.question-sidebar-item.question-current {
  background-color: var(--edtt-color-primary-100, #fcedf1) !important;
}

.question-sidebar-item.question-current:hover {
  background-color: var(--edtt-color-primary-100, #fcedf1) !important;
}