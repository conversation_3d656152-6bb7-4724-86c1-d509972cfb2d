import { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import {
  fetchExamInfoByQuestionId,
  getExamsForQuestion,
  QuestionDataManagerState,
} from '../../../../store/slices/QuestionSlices/questionDataManagerSlice';
import { ExamBase } from '../../../../interfaces/exams/examBase';
import { ExamStatus, ExamType } from '../../../../interfaces/exams/examEnums';
import { Spin, Table, Tag, Space, Button, Typography, Alert } from 'antd';
import { LinkOutlined } from '@ant-design/icons';
import { useQuestionContext } from '../../../../providers/Questions/QuestionProvider';

export interface ExamInfoTabProps {
  questionId?: string;
  loading: boolean;
  exams?: ExamBase[];
  fetchExamInfoByQuestionId: (questionId: string) => any;
}

const ExamInfoForQuestion: React.FC<ExamInfoTabProps> = ({
  questionId,
  exams,
  loading,
  fetchExamInfoByQuestionId,
}) => {
  const [loadingExams, setLoadingExams] = useState(false);

  useEffect(() => {
    if (questionId) {
      // Only fetch if we don't have the data
      setLoadingExams(true);
      fetchExamInfoByQuestionId(questionId).finally(() =>
        setLoadingExams(false)
      );
    }
  }, [questionId, fetchExamInfoByQuestionId]);

  if (loading || loadingExams) {
    return <Spin tip="Đang tải thông tin đề thi..." />;
  }

  if (!exams || exams.length === 0) {
    return (
      <Alert
        message="Câu hỏi này chưa được sử dụng trong đề thi nào"
        type="info"
        showIcon
        action={
          <Button
            type="link"
            icon={<LinkOutlined />}
            onClick={() => window.open(`/ExamManagement`, '_blank')}
          >
            Quản lý đề thi
          </Button>
        }
      />
    );
  }

  const { subjects = [], lessonGrades = [] } = useQuestionContext();

  // Helper functions to find subject and grade names by ID
  const getSubjectNameById = (subjectId: string) => {
    const subject = subjects.find((s) => s.id === subjectId);
    return subject?.name || 'Chưa xác định';
  };

  const getGradeNameById = (gradeId: string) => {
    const grade = lessonGrades.find((g) => g.id === gradeId);
    return grade?.name || 'Chưa xác định';
  };

  const columns = [
    {
      title: 'Tiêu đề',
      dataIndex: 'title',
      key: 'title',
      render: (text: string, record: ExamBase) => text || record.title,
    },
    {
      title: 'Mã đề',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: 'Môn học',
      dataIndex: 'subjectId',
      key: 'subjectId',
      render: (subjectId: string) =>
        subjectId ? (
          <Tag color="purple">{getSubjectNameById(subjectId)}</Tag>
        ) : (
          <Tag color="default">Chưa xác định</Tag>
        ),
    },
    {
      title: 'Lớp',
      dataIndex: 'gradeId',
      key: 'gradeId',
      render: (text: string) =>
        text ? (
          <Tag color="blue">{getGradeNameById(text)}</Tag>
        ) : (
          <Tag color="default">Chưa xác định</Tag>
        ),
    },
    {
      title: 'Thao tác',
      key: 'action',
      render: (_: any, record: ExamBase) => (
        <Button
          type="link"
          icon={<LinkOutlined />}
          onClick={() =>
            window.open(`/ExamManagement/edit/${record.id}`, '_blank')
          }
        >
          Xem đề thi
        </Button>
      ),
    },
  ];

  const expandedRowRender = (record: ExamBase) => {
    // Hàm chuyển đổi trạng thái thành text và màu sắc
    const getStatusDisplay = (status?: number) => {
      if (status === undefined)
        return { text: 'Chưa xác định', color: 'default' };

      switch (status) {
        case ExamStatus.Published:
          return { text: 'Đã xuất bản', color: 'success' };
        case ExamStatus.Approved:
          return { text: 'Đã duyệt', color: 'processing' };
        case ExamStatus.Submitted:
          return { text: 'Đang chờ duyệt', color: 'warning' };
        case ExamStatus.Rejected:
          return { text: 'Đã từ chối', color: 'error' };
        case ExamStatus.Draft:
        default:
          return { text: 'Bản nháp', color: 'default' };
      }
    };

    // Hàm chuyển đổi loại đề thi thành text
    const getExamTypeDisplay = (type?: ExamType) => {
      if (type === undefined) return 'Chưa xác định';

      switch (type) {
        case ExamType.Test15Minutes:
          return 'Kiểm tra 15 phút';
        case ExamType.Test45Minutes:
          return 'Kiểm tra 45 phút';
        case ExamType.MidTermExam:
          return 'Giữa học kì';
        case ExamType.FinalExam:
          return 'Cuối học kì';
        case ExamType.MockExam:
          return 'Thi thử';
        case ExamType.QualificationExam:
          return 'Thi tuyển sinh';
        case ExamType.NationalHighSchoolExam:
          return 'THPT Quốc gia';
        case ExamType.Default:
        default:
          return 'Mặc định';
      }
    };

    // Hàm định dạng thời gian làm bài
    const formatDuration = (minutes?: number) => {
      if (!minutes) return 'Chưa xác định';
      if (minutes < 60) return `${minutes} phút`;
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      return remainingMinutes > 0
        ? `${hours} giờ ${remainingMinutes} phút`
        : `${hours} giờ`;
    };

    const statusInfo = getStatusDisplay(record.status);

    return (
      <div className="tailwind-p-4 tailwind-bg-gray-50 tailwind-rounded-lg">
        <Typography.Title
          level={5}
          className="tailwind-mb-3 tailwind-mt-0  tailwind-text-blue-700"
        >
          {record.title}
        </Typography.Title>

        <Space wrap className="tailwind-mb-4">
          <Tag color="blue" className="tailwind-px-3 tailwind-py-1">
            <span className="tailwind-font-semibold">Mã đề:</span>{' '}
            {record.examCode || 'Chưa xác định'}
          </Tag>

          <Tag color={statusInfo.color} className="tailwind-px-3 tailwind-py-1">
            <span className="tailwind-font-semibold">Trạng thái:</span>{' '}
            {statusInfo.text}
          </Tag>

          <Tag color="cyan" className="tailwind-px-3 tailwind-py-1">
            <span className="tailwind-font-semibold">Loại đề:</span>{' '}
            {getExamTypeDisplay(record.examType)}
          </Tag>

          <Tag color="orange" className="tailwind-px-3 tailwind-py-1">
            <span className="tailwind-font-semibold">Thời gian:</span>{' '}
            {formatDuration(record.duration)}
          </Tag>

          <Tag color="purple" className="tailwind-px-3 tailwind-py-1">
            <span className="tailwind-font-semibold">Môn học:</span>{' '}
            {getSubjectNameById(record.subjectId || '')}
          </Tag>

          <Tag color="geekblue" className="tailwind-px-3 tailwind-py-1">
            <span className="tailwind-font-semibold">Lớp:</span>{' '}
            {getGradeNameById(record.gradeId || '')}
          </Tag>
        </Space>

        {record.description && (
          <div className="tailwind-mb-3">
            <Typography.Text
              strong
              className="tailwind-mr-2 tailwind-text-gray-700"
            >
              Mô tả:
            </Typography.Text>
            <Typography.Text>{record.description}</Typography.Text>
          </div>
        )}

        <Space className="tailwind-text-gray-500 tailwind-text-sm">
          {record.creationTime && (
            <span>
              <span className="tailwind-font-medium">Ngày tạo:</span>{' '}
              {new Date(record.creationTime).toLocaleDateString('vi-VN')}
            </span>
          )}

          {record.lastModificationTime && (
            <span>
              <span className="tailwind-font-medium">Cập nhật:</span>{' '}
              {new Date(record.lastModificationTime).toLocaleDateString(
                'vi-VN'
              )}
            </span>
          )}
        </Space>
      </div>
    );
  };

  return (
    <div className="exam-info-tab">
      {exams && exams.length > 0 ? (
        <>
          <Typography.Title level={5}>
            Câu hỏi này thuộc {exams.length} đề thi:
          </Typography.Title>
          <Table
            columns={columns}
            dataSource={exams}
            rowKey="id"
            expandable={{ expandedRowRender }}
            pagination={exams.length > 10 ? { pageSize: 10 } : false}
            className="tailwind-mt-4"
          />
        </>
      ) : (
        <Typography.Text>Câu hỏi này chưa thuộc đề thi nào.</Typography.Text>
      )}
    </div>
  );
};

const mapStateToProps = (
  state: {
    questionDataManager: QuestionDataManagerState;
  },
  ownProps: { questionId?: string }
) => {
  return {
    loading: state.questionDataManager.loading,
    exams: ownProps.questionId
      ? getExamsForQuestion(state.questionDataManager, ownProps.questionId)
      : undefined,
  };
};

const mapDispatchToProps = {
  fetchExamInfoByQuestionId,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(ExamInfoForQuestion);
