{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {"defines": ["TRACE", "DEBUG", "NET", "NET8_0", "NETCOREAPP", "NET5_0_OR_GREATER", "NET6_0_OR_GREATER", "NET7_0_OR_GREATER", "NET8_0_OR_GREATER", "NETCOREAPP1_0_OR_GREATER", "NETCOREAPP1_1_OR_GREATER", "NETCOREAPP2_0_OR_GREATER", "NETCOREAPP2_1_OR_GREATER", "NETCOREAPP2_2_OR_GREATER", "NETCOREAPP3_0_OR_GREATER", "NETCOREAPP3_1_OR_GREATER"], "languageVersion": "latest", "platform": "", "allowUnsafe": false, "warningsAsErrors": false, "optimize": false, "keyFile": "", "emitEntryPoint": false, "xmlDoc": false, "debugType": "portable"}, "targets": {".NETCoreApp,Version=v8.0": {"EdTech.Study.Application/0.1.0": {"dependencies": {"ConfigureAwait.Fody": "3.3.1", "EdTech.Study.Application.Contracts": "0.1.0", "EdTech.Study.Domain": "0.1.0", "Fody": "6.5.3", "Volo.Abp.AutoMapper": "8.1.4", "Volo.Abp.BackgroundJobs": "8.1.4", "Volo.Abp.Ddd.Application": "8.1.4", "Volo.Abp.VirtualFileSystem": "8.1.4", "Microsoft.AspNetCore.Antiforgery": "*******", "Microsoft.AspNetCore.Authentication.Abstractions": "*******", "Microsoft.AspNetCore.Authentication.BearerToken": "*******", "Microsoft.AspNetCore.Authentication.Cookies": "*******", "Microsoft.AspNetCore.Authentication.Core": "*******", "Microsoft.AspNetCore.Authentication": "*******", "Microsoft.AspNetCore.Authentication.OAuth": "*******", "Microsoft.AspNetCore.Authorization.Reference": "*******", "Microsoft.AspNetCore.Authorization.Policy": "*******", "Microsoft.AspNetCore.Components.Authorization": "*******", "Microsoft.AspNetCore.Components": "*******", "Microsoft.AspNetCore.Components.Endpoints": "*******", "Microsoft.AspNetCore.Components.Forms": "*******", "Microsoft.AspNetCore.Components.Server": "*******", "Microsoft.AspNetCore.Components.Web": "*******", "Microsoft.AspNetCore.Connections.Abstractions": "*******", "Microsoft.AspNetCore.CookiePolicy": "*******", "Microsoft.AspNetCore.Cors": "*******", "Microsoft.AspNetCore.Cryptography.Internal.Reference": "*******", "Microsoft.AspNetCore.Cryptography.KeyDerivation.Reference": "*******", "Microsoft.AspNetCore.DataProtection.Abstractions": "*******", "Microsoft.AspNetCore.DataProtection": "*******", "Microsoft.AspNetCore.DataProtection.Extensions": "*******", "Microsoft.AspNetCore.Diagnostics.Abstractions": "*******", "Microsoft.AspNetCore.Diagnostics": "*******", "Microsoft.AspNetCore.Diagnostics.HealthChecks": "*******", "Microsoft.AspNetCore": "*******", "Microsoft.AspNetCore.HostFiltering": "*******", "Microsoft.AspNetCore.Hosting.Abstractions": "*******", "Microsoft.AspNetCore.Hosting": "*******", "Microsoft.AspNetCore.Hosting.Server.Abstractions": "*******", "Microsoft.AspNetCore.Html.Abstractions": "*******", "Microsoft.AspNetCore.Http.Abstractions": "*******", "Microsoft.AspNetCore.Http.Connections.Common": "*******", "Microsoft.AspNetCore.Http.Connections": "*******", "Microsoft.AspNetCore.Http": "*******", "Microsoft.AspNetCore.Http.Extensions": "*******", "Microsoft.AspNetCore.Http.Features": "*******", "Microsoft.AspNetCore.Http.Results": "*******", "Microsoft.AspNetCore.HttpLogging": "*******", "Microsoft.AspNetCore.HttpOverrides": "*******", "Microsoft.AspNetCore.HttpsPolicy": "*******", "Microsoft.AspNetCore.Identity": "*******", "Microsoft.AspNetCore.Localization": "*******", "Microsoft.AspNetCore.Localization.Routing": "*******", "Microsoft.AspNetCore.Metadata.Reference": "*******", "Microsoft.AspNetCore.Mvc.Abstractions": "*******", "Microsoft.AspNetCore.Mvc.ApiExplorer": "*******", "Microsoft.AspNetCore.Mvc.Core": "*******", "Microsoft.AspNetCore.Mvc.Cors": "*******", "Microsoft.AspNetCore.Mvc.DataAnnotations": "*******", "Microsoft.AspNetCore.Mvc": "*******", "Microsoft.AspNetCore.Mvc.Formatters.Json": "*******", "Microsoft.AspNetCore.Mvc.Formatters.Xml": "*******", "Microsoft.AspNetCore.Mvc.Localization": "*******", "Microsoft.AspNetCore.Mvc.Razor": "*******", "Microsoft.AspNetCore.Mvc.RazorPages": "*******", "Microsoft.AspNetCore.Mvc.TagHelpers": "*******", "Microsoft.AspNetCore.Mvc.ViewFeatures": "*******", "Microsoft.AspNetCore.OutputCaching": "*******", "Microsoft.AspNetCore.RateLimiting": "*******", "Microsoft.AspNetCore.Razor": "*******", "Microsoft.AspNetCore.Razor.Runtime": "*******", "Microsoft.AspNetCore.RequestDecompression": "*******", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "*******", "Microsoft.AspNetCore.ResponseCaching": "*******", "Microsoft.AspNetCore.ResponseCompression": "*******", "Microsoft.AspNetCore.Rewrite": "*******", "Microsoft.AspNetCore.Routing.Abstractions": "*******", "Microsoft.AspNetCore.Routing": "*******", "Microsoft.AspNetCore.Server.HttpSys": "*******", "Microsoft.AspNetCore.Server.IIS": "*******", "Microsoft.AspNetCore.Server.IISIntegration": "*******", "Microsoft.AspNetCore.Server.Kestrel.Core": "*******", "Microsoft.AspNetCore.Server.Kestrel": "*******", "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes": "*******", "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic": "*******", "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets": "*******", "Microsoft.AspNetCore.Session": "*******", "Microsoft.AspNetCore.SignalR.Common": "*******", "Microsoft.AspNetCore.SignalR.Core": "*******", "Microsoft.AspNetCore.SignalR": "*******", "Microsoft.AspNetCore.SignalR.Protocols.Json": "*******", "Microsoft.AspNetCore.StaticFiles": "*******", "Microsoft.AspNetCore.WebSockets": "*******", "Microsoft.AspNetCore.WebUtilities": "*******", "Microsoft.CSharp.Reference": "*******", "Microsoft.Extensions.Caching.Abstractions.Reference": "*******", "Microsoft.Extensions.Caching.Memory.Reference": "*******", "Microsoft.Extensions.Configuration.Abstractions.Reference": "*******", "Microsoft.Extensions.Configuration.Binder.Reference": "*******", "Microsoft.Extensions.Configuration.CommandLine.Reference": "*******", "Microsoft.Extensions.Configuration.Reference": "*******", "Microsoft.Extensions.Configuration.EnvironmentVariables.Reference": "*******", "Microsoft.Extensions.Configuration.FileExtensions.Reference": "*******", "Microsoft.Extensions.Configuration.Ini": "*******", "Microsoft.Extensions.Configuration.Json.Reference": "*******", "Microsoft.Extensions.Configuration.KeyPerFile": "*******", "Microsoft.Extensions.Configuration.UserSecrets.Reference": "*******", "Microsoft.Extensions.Configuration.Xml": "*******", "Microsoft.Extensions.DependencyInjection.Abstractions.Reference": "*******", "Microsoft.Extensions.DependencyInjection.Reference": "*******", "Microsoft.Extensions.Diagnostics.Abstractions.Reference": "*******", "Microsoft.Extensions.Diagnostics": "*******", "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "*******", "Microsoft.Extensions.Diagnostics.HealthChecks": "*******", "Microsoft.Extensions.Features": "*******", "Microsoft.Extensions.FileProviders.Abstractions.Reference": "*******", "Microsoft.Extensions.FileProviders.Composite.Reference": "*******", "Microsoft.Extensions.FileProviders.Embedded.Reference": "*******", "Microsoft.Extensions.FileProviders.Physical.Reference": "*******", "Microsoft.Extensions.FileSystemGlobbing.Reference": "*******", "Microsoft.Extensions.Hosting.Abstractions.Reference": "*******", "Microsoft.Extensions.Hosting": "*******", "Microsoft.Extensions.Http": "*******", "Microsoft.Extensions.Identity.Core.Reference": "*******", "Microsoft.Extensions.Identity.Stores": "*******", "Microsoft.Extensions.Localization.Abstractions.Reference": "*******", "Microsoft.Extensions.Localization.Reference": "*******", "Microsoft.Extensions.Logging.Abstractions.Reference": "*******", "Microsoft.Extensions.Logging.Configuration": "*******", "Microsoft.Extensions.Logging.Console": "*******", "Microsoft.Extensions.Logging.Debug": "*******", "Microsoft.Extensions.Logging.Reference": "*******", "Microsoft.Extensions.Logging.EventLog": "*******", "Microsoft.Extensions.Logging.EventSource": "*******", "Microsoft.Extensions.Logging.TraceSource": "*******", "Microsoft.Extensions.ObjectPool": "*******", "Microsoft.Extensions.Options.ConfigurationExtensions.Reference": "*******", "Microsoft.Extensions.Options.DataAnnotations": "*******", "Microsoft.Extensions.Options.Reference": "*******", "Microsoft.Extensions.Primitives.Reference": "*******", "Microsoft.Extensions.WebEncoders": "*******", "Microsoft.JSInterop": "*******", "Microsoft.Net.Http.Headers": "*******", "Microsoft.VisualBasic.Core": "13.0.0.0", "Microsoft.VisualBasic": "10.0.0.0", "Microsoft.Win32.Primitives": "*******", "Microsoft.Win32.Registry": "*******", "mscorlib": "*******", "netstandard": "2.1.0.0", "System.AppContext": "*******", "System.Buffers": "*******", "System.Collections.Concurrent": "*******", "System.Collections.Reference": "*******", "System.Collections.Immutable.Reference": "*******", "System.Collections.NonGeneric": "*******", "System.Collections.Specialized": "*******", "System.ComponentModel.Annotations": "*******", "System.ComponentModel.DataAnnotations": "*******", "System.ComponentModel": "*******", "System.ComponentModel.EventBasedAsync": "*******", "System.ComponentModel.Primitives": "*******", "System.ComponentModel.TypeConverter": "*******", "System.Configuration": "*******", "System.Console": "*******", "System.Core": "*******", "System.Data.Common": "*******", "System.Data.DataSetExtensions": "*******", "System.Data": "*******", "System.Diagnostics.Contracts": "*******", "System.Diagnostics.Debug.Reference": "*******", "System.Diagnostics.DiagnosticSource.Reference": "*******", "System.Diagnostics.EventLog": "*******", "System.Diagnostics.FileVersionInfo": "*******", "System.Diagnostics.Process": "*******", "System.Diagnostics.StackTrace": "*******", "System.Diagnostics.TextWriterTraceListener": "*******", "System.Diagnostics.Tools": "*******", "System.Diagnostics.TraceSource": "*******", "System.Diagnostics.Tracing": "*******", "System": "*******", "System.Drawing": "*******", "System.Drawing.Primitives": "*******", "System.Dynamic.Runtime": "*******", "System.Formats.Asn1": "*******", "System.Formats.Tar": "*******", "System.Globalization.Calendars": "*******", "System.Globalization.Reference": "*******", "System.Globalization.Extensions": "*******", "System.IO.Compression.Brotli": "*******", "System.IO.Compression": "*******", "System.IO.Compression.FileSystem": "*******", "System.IO.Compression.ZipFile": "*******", "System.IO.Reference": "*******", "System.IO.FileSystem.AccessControl": "*******", "System.IO.FileSystem": "*******", "System.IO.FileSystem.DriveInfo": "*******", "System.IO.FileSystem.Primitives": "*******", "System.IO.FileSystem.Watcher": "*******", "System.IO.IsolatedStorage": "*******", "System.IO.MemoryMappedFiles": "*******", "System.IO.Pipelines": "*******", "System.IO.Pipes.AccessControl": "*******", "System.IO.Pipes": "*******", "System.IO.UnmanagedMemoryStream": "*******", "System.Linq.Reference": "*******", "System.Linq.Expressions.Reference": "*******", "System.Linq.Parallel": "*******", "System.Linq.Queryable.Reference": "*******", "System.Memory.Reference": "*******", "System.Net": "*******", "System.Net.Http": "*******", "System.Net.Http.Json": "*******", "System.Net.HttpListener": "*******", "System.Net.Mail": "*******", "System.Net.NameResolution": "*******", "System.Net.NetworkInformation": "*******", "System.Net.Ping": "*******", "System.Net.Primitives": "*******", "System.Net.Quic": "*******", "System.Net.Requests": "*******", "System.Net.Security": "*******", "System.Net.ServicePoint": "*******", "System.Net.Sockets": "*******", "System.Net.WebClient": "*******", "System.Net.WebHeaderCollection": "*******", "System.Net.WebProxy": "*******", "System.Net.WebSockets.Client": "*******", "System.Net.WebSockets": "*******", "System.Numerics": "*******", "System.Numerics.Vectors": "*******", "System.ObjectModel.Reference": "*******", "System.Reflection.DispatchProxy": "*******", "System.Reflection.Reference": "*******", "System.Reflection.Emit.Reference": "*******", "System.Reflection.Emit.ILGeneration.Reference": "*******", "System.Reflection.Emit.Lightweight.Reference": "*******", "System.Reflection.Extensions.Reference": "*******", "System.Reflection.Metadata.Reference": "*******", "System.Reflection.Primitives.Reference": "*******", "System.Reflection.TypeExtensions.Reference": "*******", "System.Resources.Reader": "*******", "System.Resources.ResourceManager.Reference": "*******", "System.Resources.Writer": "*******", "System.Runtime.CompilerServices.Unsafe.Reference": "*******", "System.Runtime.CompilerServices.VisualC": "*******", "System.Runtime.Reference": "*******", "System.Runtime.Extensions.Reference": "*******", "System.Runtime.Handles": "*******", "System.Runtime.InteropServices": "*******", "System.Runtime.InteropServices.JavaScript": "*******", "System.Runtime.InteropServices.RuntimeInformation": "*******", "System.Runtime.Intrinsics": "*******", "System.Runtime.Loader.Reference": "*******", "System.Runtime.Numerics": "*******", "System.Runtime.Serialization": "*******", "System.Runtime.Serialization.Formatters": "*******", "System.Runtime.Serialization.Json": "*******", "System.Runtime.Serialization.Primitives": "*******", "System.Runtime.Serialization.Xml": "*******", "System.Security.AccessControl": "*******", "System.Security.Claims": "*******", "System.Security.Cryptography.Algorithms": "*******", "System.Security.Cryptography.Cng": "*******", "System.Security.Cryptography.Csp": "*******", "System.Security.Cryptography": "*******", "System.Security.Cryptography.Encoding": "*******", "System.Security.Cryptography.OpenSsl": "*******", "System.Security.Cryptography.Primitives": "*******", "System.Security.Cryptography.X509Certificates": "*******", "System.Security.Cryptography.Xml": "*******", "System.Security": "*******", "System.Security.Principal": "*******", "System.Security.Principal.Windows": "*******", "System.Security.SecureString": "*******", "System.ServiceModel.Web": "*******", "System.ServiceProcess": "*******", "System.Text.Encoding.CodePages.Reference": "*******", "System.Text.Encoding.Reference": "*******", "System.Text.Encoding.Extensions": "*******", "System.Text.Encodings.Web.Reference": "*******", "System.Text.Json.Reference": "*******", "System.Text.RegularExpressions": "*******", "System.Threading.Channels": "*******", "System.Threading.Reference": "*******", "System.Threading.Overlapped": "*******", "System.Threading.RateLimiting": "*******", "System.Threading.Tasks.Dataflow": "*******", "System.Threading.Tasks.Reference": "*******", "System.Threading.Tasks.Extensions.Reference": "*******", "System.Threading.Tasks.Parallel": "*******", "System.Threading.Thread": "*******", "System.Threading.ThreadPool": "*******", "System.Threading.Timer": "*******", "System.Transactions": "*******", "System.Transactions.Local": "*******", "System.ValueTuple": "*******", "System.Web": "*******", "System.Web.HttpUtility": "*******", "System.Windows": "*******", "System.Xml": "*******", "System.Xml.Linq": "*******", "System.Xml.ReaderWriter": "*******", "System.Xml.Serialization": "*******", "System.Xml.XDocument": "*******", "System.Xml.XmlDocument": "*******", "System.Xml.XmlSerializer": "*******", "System.Xml.XPath": "*******", "System.Xml.XPath.XDocument": "*******", "WindowsBase": "*******"}, "runtime": {"EdTech.Study.Application.dll": {}}, "compile": {"EdTech.Study.Application.dll": {}}}, "Asp.Versioning.Abstractions/8.1.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Asp.Versioning.Abstractions.dll": {"assemblyVersion": "8.1.0.0", "fileVersion": "8.1.8851.31619"}}, "compile": {"lib/net8.0/Asp.Versioning.Abstractions.dll": {}}}, "Asp.Versioning.Http/8.1.0": {"dependencies": {"Asp.Versioning.Abstractions": "8.1.0"}, "runtime": {"lib/net8.0/Asp.Versioning.Http.dll": {"assemblyVersion": "8.1.0.0", "fileVersion": "8.1.8851.31627"}}, "compile": {"lib/net8.0/Asp.Versioning.Http.dll": {}}}, "Asp.Versioning.Mvc/8.1.0": {"dependencies": {"Asp.Versioning.Http": "8.1.0"}, "runtime": {"lib/net8.0/Asp.Versioning.Mvc.dll": {"assemblyVersion": "8.1.0.0", "fileVersion": "8.1.8851.31627"}}, "compile": {"lib/net8.0/Asp.Versioning.Mvc.dll": {}}}, "Asp.Versioning.Mvc.ApiExplorer/8.1.0": {"dependencies": {"Asp.Versioning.Mvc": "8.1.0"}, "runtime": {"lib/net8.0/Asp.Versioning.Mvc.ApiExplorer.dll": {"assemblyVersion": "8.1.0.0", "fileVersion": "8.1.8851.31636"}}, "compile": {"lib/net8.0/Asp.Versioning.Mvc.ApiExplorer.dll": {}}}, "AsyncKeyedLock/6.3.4": {"runtime": {"lib/net8.0/AsyncKeyedLock.dll": {"assemblyVersion": "6.3.4.0", "fileVersion": "6.3.4.0"}}, "compile": {"lib/net8.0/AsyncKeyedLock.dll": {}}}, "AutoMapper/12.0.1": {"dependencies": {"Microsoft.CSharp": "4.7.0"}, "runtime": {"lib/netstandard2.1/AutoMapper.dll": {"assemblyVersion": "12.0.0.0", "fileVersion": "12.0.1.0"}}, "compile": {"lib/netstandard2.1/AutoMapper.dll": {}}}, "ConfigureAwait.Fody/3.3.1": {"dependencies": {"Fody": "6.5.3"}, "runtime": {"lib/netstandard2.0/ConfigureAwait.dll": {"assemblyVersion": "3.3.1.0", "fileVersion": "3.3.1.0"}}, "compile": {"lib/netstandard2.0/ConfigureAwait.dll": {}}}, "Fody/6.5.3": {}, "IdentityModel/6.2.0": {"runtime": {"lib/net6.0/IdentityModel.dll": {"assemblyVersion": "6.2.0.0", "fileVersion": "6.2.0.0"}}, "compile": {"lib/net6.0/IdentityModel.dll": {}}}, "JetBrains.Annotations/2023.3.0": {"runtime": {"lib/netstandard2.0/JetBrains.Annotations.dll": {"assemblyVersion": "4242.42.42.42", "fileVersion": "2023.3.0.0"}}, "compile": {"lib/netstandard2.0/JetBrains.Annotations.dll": {}}}, "Microsoft.AspNetCore.Authentication.OpenIdConnect/8.0.0": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.0.3"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authentication.OpenIdConnect.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Authentication.OpenIdConnect.dll": {}}}, "Microsoft.AspNetCore.Authorization/8.0.0": {"dependencies": {"Microsoft.AspNetCore.Metadata": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.AspNetCore.Cryptography.Internal/8.0.0": {}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/8.0.0": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "8.0.0"}}, "Microsoft.AspNetCore.Metadata/8.0.0": {}, "Microsoft.AspNetCore.Mvc.Razor.Extensions/6.0.0": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "6.0.0", "Microsoft.CodeAnalysis.Razor": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.Extensions.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52608"}}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.Extensions.dll": {}}}, "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation/8.0.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Razor.Extensions": "6.0.0", "Microsoft.CodeAnalysis.Razor": "6.0.0", "Microsoft.Extensions.DependencyModel": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.dll": {}}}, "Microsoft.AspNetCore.Razor.Language/6.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52608"}}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {}}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "compile": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.2": {}, "Microsoft.CodeAnalysis.Common/4.0.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.2", "System.Collections.Immutable": "8.0.0", "System.Memory": "4.5.4", "System.Reflection.Metadata": "5.0.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Text.Encoding.CodePages": "4.5.1", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "*******", "fileVersion": "4.0.21.51404"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}, "compile": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll": {}}}, "Microsoft.CodeAnalysis.CSharp/4.0.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.0.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "4.0.21.51404"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}, "compile": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll": {}}}, "Microsoft.CodeAnalysis.Razor/6.0.0": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "6.0.0", "Microsoft.CodeAnalysis.CSharp": "4.0.0", "Microsoft.CodeAnalysis.Common": "4.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52608"}}, "compile": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll": {}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "System.Text.Json": "8.0.0"}}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {}, "Microsoft.Extensions.DependencyModel/8.0.0": {"dependencies": {"System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "compile": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {}}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0"}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.FileProviders.Composite/8.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.FileProviders.Embedded/8.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0"}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Identity.Core/8.0.0": {"dependencies": {"Microsoft.AspNetCore.Cryptography.KeyDerivation": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Localization/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Localization.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Localization.Abstractions/8.0.0": {}, "Microsoft.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Options/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Primitives/8.0.0": {}, "Microsoft.IdentityModel.Abstractions/7.0.3": {"runtime": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "7.0.3.0", "fileVersion": "7.0.3.41017"}}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {}}}, "Microsoft.IdentityModel.JsonWebTokens/7.0.3": {"dependencies": {"Microsoft.IdentityModel.Tokens": "7.0.3"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "7.0.3.0", "fileVersion": "7.0.3.41017"}}, "compile": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {}}}, "Microsoft.IdentityModel.Logging/7.0.3": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "7.0.3"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "7.0.3.0", "fileVersion": "7.0.3.41017"}}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {}}}, "Microsoft.IdentityModel.Protocols/7.0.3": {"dependencies": {"Microsoft.IdentityModel.Logging": "7.0.3", "Microsoft.IdentityModel.Tokens": "7.0.3"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "7.0.3.0", "fileVersion": "7.0.3.41017"}}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.0.3": {"dependencies": {"Microsoft.IdentityModel.Protocols": "7.0.3", "System.IdentityModel.Tokens.Jwt": "7.0.3"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "7.0.3.0", "fileVersion": "7.0.3.41017"}}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {}}}, "Microsoft.IdentityModel.Tokens/7.0.3": {"dependencies": {"Microsoft.IdentityModel.Logging": "7.0.3"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "7.0.3.0", "fileVersion": "7.0.3.41017"}}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {}}}, "Microsoft.NETCore.Platforms/2.1.2": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Nito.AsyncEx.Context/5.1.2": {"dependencies": {"Nito.AsyncEx.Tasks": "5.1.2"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Context.dll": {"assemblyVersion": "5.1.2.0", "fileVersion": "5.1.2.0"}}, "compile": {"lib/netstandard2.0/Nito.AsyncEx.Context.dll": {}}}, "Nito.AsyncEx.Tasks/5.1.2": {"dependencies": {"Nito.Disposables": "2.2.1"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Tasks.dll": {"assemblyVersion": "5.1.2.0", "fileVersion": "5.1.2.0"}}, "compile": {"lib/netstandard2.0/Nito.AsyncEx.Tasks.dll": {}}}, "Nito.Disposables/2.2.1": {"dependencies": {"System.Collections.Immutable": "8.0.0"}, "runtime": {"lib/netstandard2.1/Nito.Disposables.dll": {"assemblyVersion": "2.2.1.0", "fileVersion": "2.2.1.0"}}, "compile": {"lib/netstandard2.1/Nito.Disposables.dll": {}}}, "NUglify/1.21.0": {"runtime": {"lib/net5.0/NUglify.dll": {"assemblyVersion": "1.21.0.0", "fileVersion": "1.21.0.0"}}, "compile": {"lib/net5.0/NUglify.dll": {}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Immutable/8.0.0": {}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/8.0.0": {}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/7.0.3": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "7.0.3", "Microsoft.IdentityModel.Tokens": "7.0.3"}, "runtime": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "7.0.3.0", "fileVersion": "7.0.3.41017"}}, "compile": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Dynamic.Core/1.3.5": {"runtime": {"lib/net7.0/System.Linq.Dynamic.Core.dll": {"assemblyVersion": "1.3.5.0", "fileVersion": "1.3.5.0"}}, "compile": {"lib/net7.0/System.Linq.Dynamic.Core.dll": {}}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Linq.Queryable/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Memory/4.5.4": {}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Metadata/5.0.0": {}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Loader/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages/4.5.1": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "System.Runtime.CompilerServices.Unsafe": "5.0.0"}}, "System.Text.Encodings.Web/8.0.0": {}, "System.Text.Json/8.0.0": {"dependencies": {"System.Text.Encodings.Web": "8.0.0"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "TimeZoneConverter/6.1.0": {"runtime": {"lib/net6.0/TimeZoneConverter.dll": {"assemblyVersion": "6.1.0.0", "fileVersion": "6.1.0.0"}}, "compile": {"lib/net6.0/TimeZoneConverter.dll": {}}}, "Volo.Abp.ApiVersioning.Abstractions/8.1.4": {"dependencies": {"Volo.Abp.Core": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.ApiVersioning.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.ApiVersioning.Abstractions.dll": {}}}, "Volo.Abp.AspNetCore/8.1.4": {"dependencies": {"IdentityModel": "6.2.0", "Microsoft.AspNetCore.Authentication.OpenIdConnect": "8.0.0", "Volo.Abp.Auditing": "8.1.4", "Volo.Abp.Authorization": "8.1.4", "Volo.Abp.ExceptionHandling": "8.1.4", "Volo.Abp.Http": "8.1.4", "Volo.Abp.Security": "8.1.4", "Volo.Abp.Uow": "8.1.4", "Volo.Abp.Validation": "8.1.4", "Volo.Abp.VirtualFileSystem": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.AspNetCore.dll": {}}}, "Volo.Abp.AspNetCore.Mvc/8.1.4": {"dependencies": {"Asp.Versioning.Mvc": "8.1.0", "Asp.Versioning.Mvc.ApiExplorer": "8.1.0", "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation": "8.0.0", "Volo.Abp.ApiVersioning.Abstractions": "8.1.4", "Volo.Abp.AspNetCore": "8.1.4", "Volo.Abp.AspNetCore.Mvc.Contracts": "8.1.4", "Volo.Abp.Ddd.Application": "8.1.4", "Volo.Abp.GlobalFeatures": "8.1.4", "Volo.Abp.Localization": "8.1.4", "Volo.Abp.UI.Navigation": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.dll": {}}}, "Volo.Abp.AspNetCore.Mvc.Contracts/8.1.4": {"dependencies": {"Volo.Abp.Ddd.Application.Contracts": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.Contracts.dll": {}}}, "Volo.Abp.AspNetCore.Mvc.UI/8.1.4": {"dependencies": {"NUglify": "1.21.0", "Volo.Abp.AspNetCore.Mvc": "8.1.4", "Volo.Abp.UI.Navigation": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.dll": {}}}, "Volo.Abp.AspNetCore.Mvc.UI.Bootstrap/8.1.4": {"dependencies": {"Volo.Abp.AspNetCore.Mvc.UI": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.dll": {}}}, "Volo.Abp.AspNetCore.Mvc.UI.Bundling/8.1.4": {"dependencies": {"Volo.Abp.AspNetCore.Mvc.UI.Bootstrap": "8.1.4", "Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions": "8.1.4", "Volo.Abp.Minify": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Bundling.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Bundling.dll": {}}}, "Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions/8.1.4": {"dependencies": {"Volo.Abp.Core": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions.dll": {}}}, "Volo.Abp.AspNetCore.Mvc.UI.Packages/8.1.4": {"dependencies": {"Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions": "8.1.4", "Volo.Abp.Localization": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Packages.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Packages.dll": {}}}, "Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared/8.1.4": {"dependencies": {"Volo.Abp.AspNetCore.Mvc.UI.Bootstrap": "8.1.4", "Volo.Abp.AspNetCore.Mvc.UI.Packages": "8.1.4", "Volo.Abp.AspNetCore.Mvc.UI.Widgets": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.dll": {}}}, "Volo.Abp.AspNetCore.Mvc.UI.Widgets/8.1.4": {"dependencies": {"Volo.Abp.AspNetCore.Mvc.UI.Bundling": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Widgets.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.UI.Widgets.dll": {}}}, "Volo.Abp.Auditing/8.1.4": {"dependencies": {"Volo.Abp.Auditing.Contracts": "8.1.4", "Volo.Abp.Data": "8.1.4", "Volo.Abp.Json": "8.1.4", "Volo.Abp.MultiTenancy": "8.1.4", "Volo.Abp.Security": "8.1.4", "Volo.Abp.Threading": "8.1.4", "Volo.Abp.Timing": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Auditing.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.Auditing.dll": {}}}, "Volo.Abp.Auditing.Contracts/8.1.4": {"dependencies": {"Volo.Abp.Core": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Auditing.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.Auditing.Contracts.dll": {}}}, "Volo.Abp.Authorization/8.1.4": {"dependencies": {"Volo.Abp.Authorization.Abstractions": "8.1.4", "Volo.Abp.Localization": "8.1.4", "Volo.Abp.MultiTenancy": "8.1.4", "Volo.Abp.Security": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.Authorization.dll": {}}}, "Volo.Abp.Authorization.Abstractions/8.1.4": {"dependencies": {"Microsoft.AspNetCore.Authorization": "8.0.0", "Volo.Abp.MultiTenancy.Abstractions": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Authorization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.Authorization.Abstractions.dll": {}}}, "Volo.Abp.AutoMapper/8.1.4": {"dependencies": {"AutoMapper": "12.0.1", "Volo.Abp.Auditing": "8.1.4", "Volo.Abp.ObjectExtending": "8.1.4", "Volo.Abp.ObjectMapping": "8.1.4"}, "runtime": {"lib/netstandard2.1/Volo.Abp.AutoMapper.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.1/Volo.Abp.AutoMapper.dll": {}}}, "Volo.Abp.BackgroundJobs/8.1.4": {"dependencies": {"Volo.Abp.BackgroundJobs.Abstractions": "8.1.4", "Volo.Abp.BackgroundWorkers": "8.1.4", "Volo.Abp.DistributedLocking.Abstractions": "8.1.4", "Volo.Abp.Guids": "8.1.4", "Volo.Abp.MultiTenancy": "8.1.4", "Volo.Abp.Timing": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.BackgroundJobs.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.BackgroundJobs.dll": {}}}, "Volo.Abp.BackgroundJobs.Abstractions/8.1.4": {"dependencies": {"Volo.Abp.Json": "8.1.4", "Volo.Abp.MultiTenancy.Abstractions": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.BackgroundJobs.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.BackgroundJobs.Abstractions.dll": {}}}, "Volo.Abp.BackgroundWorkers/8.1.4": {"dependencies": {"Volo.Abp.Threading": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.BackgroundWorkers.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.BackgroundWorkers.dll": {}}}, "Volo.Abp.BlobStoring/8.1.4": {"dependencies": {"Volo.Abp.Core": "8.1.4", "Volo.Abp.MultiTenancy": "8.1.4", "Volo.Abp.Threading": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.BlobStoring.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.BlobStoring.dll": {}}}, "Volo.Abp.Caching/8.1.4": {"dependencies": {"Microsoft.Extensions.Caching.Memory": "8.0.0", "Volo.Abp.Json": "8.1.4", "Volo.Abp.MultiTenancy": "8.1.4", "Volo.Abp.Serialization": "8.1.4", "Volo.Abp.Threading": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.Caching.dll": {}}}, "Volo.Abp.Core/8.1.4": {"dependencies": {"JetBrains.Annotations": "2023.3.0", "Microsoft.Extensions.Configuration.CommandLine": "8.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "8.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Localization": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0", "Nito.AsyncEx.Context": "5.1.2", "System.Collections.Immutable": "8.0.0", "System.Linq.Dynamic.Core": "1.3.5", "System.Linq.Queryable": "4.3.0", "System.Runtime.Loader": "4.3.0", "System.Text.Encodings.Web": "8.0.0"}, "runtime": {"lib/net8.0/Volo.Abp.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.Core.dll": {}}}, "Volo.Abp.Data/8.1.4": {"dependencies": {"Volo.Abp.EventBus.Abstractions": "8.1.4", "Volo.Abp.ObjectExtending": "8.1.4", "Volo.Abp.Uow": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Data.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.Data.dll": {}}}, "Volo.Abp.Ddd.Application/8.1.4": {"dependencies": {"Volo.Abp.Authorization": "8.1.4", "Volo.Abp.Ddd.Application.Contracts": "8.1.4", "Volo.Abp.Ddd.Domain": "8.1.4", "Volo.Abp.Features": "8.1.4", "Volo.Abp.GlobalFeatures": "8.1.4", "Volo.Abp.Http.Abstractions": "8.1.4", "Volo.Abp.Localization": "8.1.4", "Volo.Abp.ObjectMapping": "8.1.4", "Volo.Abp.Security": "8.1.4", "Volo.Abp.Settings": "8.1.4", "Volo.Abp.Validation": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Ddd.Application.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.Ddd.Application.dll": {}}}, "Volo.Abp.Ddd.Application.Contracts/8.1.4": {"dependencies": {"Volo.Abp.Auditing.Contracts": "8.1.4", "Volo.Abp.Data": "8.1.4", "Volo.Abp.Localization": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Ddd.Application.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.Ddd.Application.Contracts.dll": {}}}, "Volo.Abp.Ddd.Domain/8.1.4": {"dependencies": {"Volo.Abp.Auditing": "8.1.4", "Volo.Abp.Caching": "8.1.4", "Volo.Abp.Data": "8.1.4", "Volo.Abp.Ddd.Domain.Shared": "8.1.4", "Volo.Abp.EventBus": "8.1.4", "Volo.Abp.ExceptionHandling": "8.1.4", "Volo.Abp.Guids": "8.1.4", "Volo.Abp.ObjectMapping": "8.1.4", "Volo.Abp.Specifications": "8.1.4", "Volo.Abp.Timing": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Ddd.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.Ddd.Domain.dll": {}}}, "Volo.Abp.Ddd.Domain.Shared/8.1.4": {"dependencies": {"Volo.Abp.EventBus.Abstractions": "8.1.4", "Volo.Abp.MultiTenancy.Abstractions": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Ddd.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.Ddd.Domain.Shared.dll": {}}}, "Volo.Abp.DistributedLocking.Abstractions/8.1.4": {"dependencies": {"AsyncKeyedLock": "6.3.4", "Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Volo.Abp.Core": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.DistributedLocking.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.DistributedLocking.Abstractions.dll": {}}}, "Volo.Abp.EventBus/8.1.4": {"dependencies": {"Volo.Abp.BackgroundWorkers": "8.1.4", "Volo.Abp.DistributedLocking.Abstractions": "8.1.4", "Volo.Abp.EventBus.Abstractions": "8.1.4", "Volo.Abp.Guids": "8.1.4", "Volo.Abp.Json": "8.1.4", "Volo.Abp.MultiTenancy": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.EventBus.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.EventBus.dll": {}}}, "Volo.Abp.EventBus.Abstractions/8.1.4": {"dependencies": {"Volo.Abp.ObjectExtending": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.EventBus.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.EventBus.Abstractions.dll": {}}}, "Volo.Abp.ExceptionHandling/8.1.4": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "8.0.0", "Volo.Abp.Data": "8.1.4", "Volo.Abp.Localization": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.ExceptionHandling.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.ExceptionHandling.dll": {}}}, "Volo.Abp.Features/8.1.4": {"dependencies": {"Volo.Abp.Authorization.Abstractions": "8.1.4", "Volo.Abp.Localization": "8.1.4", "Volo.Abp.MultiTenancy": "8.1.4", "Volo.Abp.Validation": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Features.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.Features.dll": {}}}, "Volo.Abp.GlobalFeatures/8.1.4": {"dependencies": {"Volo.Abp.Authorization.Abstractions": "8.1.4", "Volo.Abp.Localization": "8.1.4", "Volo.Abp.VirtualFileSystem": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.GlobalFeatures.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.GlobalFeatures.dll": {}}}, "Volo.Abp.Guids/8.1.4": {"dependencies": {"Volo.Abp.Core": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Guids.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.Guids.dll": {}}}, "Volo.Abp.Http/8.1.4": {"dependencies": {"Volo.Abp.Http.Abstractions": "8.1.4", "Volo.Abp.Json": "8.1.4", "Volo.Abp.Minify": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Http.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.Http.dll": {}}}, "Volo.Abp.Http.Abstractions/8.1.4": {"dependencies": {"Volo.Abp.Core": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Http.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.Http.Abstractions.dll": {}}}, "Volo.Abp.Identity.Domain/8.1.4": {"dependencies": {"Microsoft.Extensions.Identity.Core": "8.0.0", "Volo.Abp.AutoMapper": "8.1.4", "Volo.Abp.Ddd.Domain": "8.1.4", "Volo.Abp.Identity.Domain.Shared": "8.1.4", "Volo.Abp.Users.Domain": "8.1.4"}, "runtime": {"lib/netstandard2.1/Volo.Abp.Identity.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.1/Volo.Abp.Identity.Domain.dll": {}}}, "Volo.Abp.Identity.Domain.Shared/8.1.4": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "8.0.0", "Volo.Abp.Auditing.Contracts": "8.1.4", "Volo.Abp.Features": "8.1.4", "Volo.Abp.Users.Domain.Shared": "8.1.4", "Volo.Abp.Validation": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Identity.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.Identity.Domain.Shared.dll": {}}}, "Volo.Abp.Json/8.1.4": {"dependencies": {"Volo.Abp.Json.SystemTextJson": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Json.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.Json.dll": {}}}, "Volo.Abp.Json.Abstractions/8.1.4": {"dependencies": {"Volo.Abp.Core": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Json.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.Json.Abstractions.dll": {}}}, "Volo.Abp.Json.SystemTextJson/8.1.4": {"dependencies": {"System.Text.Json": "8.0.0", "Volo.Abp.Data": "8.1.4", "Volo.Abp.Json.Abstractions": "8.1.4", "Volo.Abp.Timing": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Json.SystemTextJson.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.Json.SystemTextJson.dll": {}}}, "Volo.Abp.Localization/8.1.4": {"dependencies": {"Volo.Abp.Localization.Abstractions": "8.1.4", "Volo.Abp.Settings": "8.1.4", "Volo.Abp.Threading": "8.1.4", "Volo.Abp.VirtualFileSystem": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Localization.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.Localization.dll": {}}}, "Volo.Abp.Localization.Abstractions/8.1.4": {"dependencies": {"Volo.Abp.Core": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Localization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.Localization.Abstractions.dll": {}}}, "Volo.Abp.Minify/8.1.4": {"dependencies": {"NUglify": "1.21.0", "Volo.Abp.Core": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Minify.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.Minify.dll": {}}}, "Volo.Abp.MultiTenancy/8.1.4": {"dependencies": {"Volo.Abp.Data": "8.1.4", "Volo.Abp.EventBus.Abstractions": "8.1.4", "Volo.Abp.MultiTenancy.Abstractions": "8.1.4", "Volo.Abp.Security": "8.1.4", "Volo.Abp.Settings": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.MultiTenancy.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.MultiTenancy.dll": {}}}, "Volo.Abp.MultiTenancy.Abstractions/8.1.4": {"dependencies": {"Volo.Abp.Localization": "8.1.4", "Volo.Abp.VirtualFileSystem": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.MultiTenancy.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.MultiTenancy.Abstractions.dll": {}}}, "Volo.Abp.ObjectExtending/8.1.4": {"dependencies": {"Volo.Abp.Localization.Abstractions": "8.1.4", "Volo.Abp.Validation.Abstractions": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.ObjectExtending.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.ObjectExtending.dll": {}}}, "Volo.Abp.ObjectMapping/8.1.4": {"dependencies": {"Volo.Abp.Core": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.ObjectMapping.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.ObjectMapping.dll": {}}}, "Volo.Abp.Security/8.1.4": {"dependencies": {"Volo.Abp.Core": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Security.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.Security.dll": {}}}, "Volo.Abp.Serialization/8.1.4": {"dependencies": {"Volo.Abp.Core": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.Serialization.dll": {}}}, "Volo.Abp.Settings/8.1.4": {"dependencies": {"Volo.Abp.Data": "8.1.4", "Volo.Abp.Localization.Abstractions": "8.1.4", "Volo.Abp.Security": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Settings.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.Settings.dll": {}}}, "Volo.Abp.Specifications/8.1.4": {"dependencies": {"Volo.Abp.Core": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Specifications.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.Specifications.dll": {}}}, "Volo.Abp.Threading/8.1.4": {"dependencies": {"Volo.Abp.Core": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.Threading.dll": {}}}, "Volo.Abp.Timing/8.1.4": {"dependencies": {"TimeZoneConverter": "6.1.0", "Volo.Abp.Localization": "8.1.4", "Volo.Abp.Settings": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Timing.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.Timing.dll": {}}}, "Volo.Abp.UI/8.1.4": {"dependencies": {"Volo.Abp.ExceptionHandling": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.UI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.UI.dll": {}}}, "Volo.Abp.UI.Navigation/8.1.4": {"dependencies": {"Volo.Abp.Authorization": "8.1.4", "Volo.Abp.MultiTenancy": "8.1.4", "Volo.Abp.UI": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.UI.Navigation.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.UI.Navigation.dll": {}}}, "Volo.Abp.Uow/8.1.4": {"dependencies": {"Volo.Abp.Core": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Uow.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.Uow.dll": {}}}, "Volo.Abp.Users.Abstractions/8.1.4": {"dependencies": {"Volo.Abp.EventBus": "8.1.4", "Volo.Abp.MultiTenancy": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Users.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.Users.Abstractions.dll": {}}}, "Volo.Abp.Users.Domain/8.1.4": {"dependencies": {"Volo.Abp.Ddd.Domain": "8.1.4", "Volo.Abp.Users.Abstractions": "8.1.4", "Volo.Abp.Users.Domain.Shared": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Users.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.Users.Domain.dll": {}}}, "Volo.Abp.Users.Domain.Shared/8.1.4": {"dependencies": {"Volo.Abp.Core": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Users.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.Users.Domain.Shared.dll": {}}}, "Volo.Abp.Validation/8.1.4": {"dependencies": {"Volo.Abp.Localization": "8.1.4", "Volo.Abp.Validation.Abstractions": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Validation.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.Validation.dll": {}}}, "Volo.Abp.Validation.Abstractions/8.1.4": {"dependencies": {"Volo.Abp.Core": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.Validation.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.Validation.Abstractions.dll": {}}}, "Volo.Abp.VirtualFileSystem/8.1.4": {"dependencies": {"Microsoft.Extensions.FileProviders.Composite": "8.0.0", "Microsoft.Extensions.FileProviders.Embedded": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Volo.Abp.Core": "8.1.4"}, "runtime": {"lib/net8.0/Volo.Abp.VirtualFileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Volo.Abp.VirtualFileSystem.dll": {}}}, "EdTech.Study.Application.Contracts/0.1.0": {"dependencies": {"EdTech.Study.Domain": "0.1.0", "EdTech.Study.Domain.Shared": "0.1.0", "Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared": "8.1.4", "Volo.Abp.Authorization": "8.1.4", "Volo.Abp.Ddd.Application.Contracts": "8.1.4"}, "runtime": {"EdTech.Study.Application.Contracts.dll": {"assemblyVersion": "0.1.0", "fileVersion": "0.1.0.0"}}, "compile": {"EdTech.Study.Application.Contracts.dll": {}}}, "EdTech.Study.Domain/0.1.0": {"dependencies": {"EdTech.Study.Domain.Shared": "0.1.0", "Volo.Abp.BlobStoring": "8.1.4", "Volo.Abp.Ddd.Domain": "8.1.4", "Volo.Abp.Identity.Domain": "8.1.4", "Volo.Abp.VirtualFileSystem": "8.1.4"}, "runtime": {"EdTech.Study.Domain.dll": {"assemblyVersion": "0.1.0", "fileVersion": "0.1.0.0"}}, "compile": {"EdTech.Study.Domain.dll": {}}}, "EdTech.Study.Domain.Shared/0.1.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "8.0.0", "Volo.Abp.Ddd.Domain.Shared": "8.1.4", "Volo.Abp.Validation": "8.1.4"}, "runtime": {"EdTech.Study.Domain.Shared.dll": {"assemblyVersion": "0.1.0", "fileVersion": "0.1.0.0"}}, "compile": {"EdTech.Study.Domain.Shared.dll": {}}}, "Microsoft.AspNetCore.Antiforgery/*******": {"compile": {"Microsoft.AspNetCore.Antiforgery.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Authentication.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.BearerToken/*******": {"compile": {"Microsoft.AspNetCore.Authentication.BearerToken.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Cookies/*******": {"compile": {"Microsoft.AspNetCore.Authentication.Cookies.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Core/*******": {"compile": {"Microsoft.AspNetCore.Authentication.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication/*******": {"compile": {"Microsoft.AspNetCore.Authentication.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.OAuth/*******": {"compile": {"Microsoft.AspNetCore.Authentication.OAuth.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authorization.Reference/*******": {"compile": {"Microsoft.AspNetCore.Authorization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authorization.Policy/*******": {"compile": {"Microsoft.AspNetCore.Authorization.Policy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Authorization/*******": {"compile": {"Microsoft.AspNetCore.Components.Authorization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components/*******": {"compile": {"Microsoft.AspNetCore.Components.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Endpoints/*******": {"compile": {"Microsoft.AspNetCore.Components.Endpoints.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Forms/*******": {"compile": {"Microsoft.AspNetCore.Components.Forms.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Server/*******": {"compile": {"Microsoft.AspNetCore.Components.Server.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Web/*******": {"compile": {"Microsoft.AspNetCore.Components.Web.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Connections.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Connections.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.CookiePolicy/*******": {"compile": {"Microsoft.AspNetCore.CookiePolicy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Cors/*******": {"compile": {"Microsoft.AspNetCore.Cors.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Cryptography.Internal.Reference/*******": {"compile": {"Microsoft.AspNetCore.Cryptography.Internal.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Cryptography.KeyDerivation.Reference/*******": {"compile": {"Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.DataProtection.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection/*******": {"compile": {"Microsoft.AspNetCore.DataProtection.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection.Extensions/*******": {"compile": {"Microsoft.AspNetCore.DataProtection.Extensions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Diagnostics.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics/*******": {"compile": {"Microsoft.AspNetCore.Diagnostics.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics.HealthChecks/*******": {"compile": {"Microsoft.AspNetCore.Diagnostics.HealthChecks.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore/*******": {"compile": {"Microsoft.AspNetCore.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HostFiltering/*******": {"compile": {"Microsoft.AspNetCore.HostFiltering.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Hosting.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting/*******": {"compile": {"Microsoft.AspNetCore.Hosting.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Html.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Html.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Http.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Connections.Common/*******": {"compile": {"Microsoft.AspNetCore.Http.Connections.Common.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Connections/*******": {"compile": {"Microsoft.AspNetCore.Http.Connections.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http/*******": {"compile": {"Microsoft.AspNetCore.Http.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Extensions/*******": {"compile": {"Microsoft.AspNetCore.Http.Extensions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Features/*******": {"compile": {"Microsoft.AspNetCore.Http.Features.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Results/*******": {"compile": {"Microsoft.AspNetCore.Http.Results.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HttpLogging/*******": {"compile": {"Microsoft.AspNetCore.HttpLogging.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HttpOverrides/*******": {"compile": {"Microsoft.AspNetCore.HttpOverrides.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HttpsPolicy/*******": {"compile": {"Microsoft.AspNetCore.HttpsPolicy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Identity/*******": {"compile": {"Microsoft.AspNetCore.Identity.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Localization/*******": {"compile": {"Microsoft.AspNetCore.Localization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Localization.Routing/*******": {"compile": {"Microsoft.AspNetCore.Localization.Routing.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Metadata.Reference/*******": {"compile": {"Microsoft.AspNetCore.Metadata.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.ApiExplorer/*******": {"compile": {"Microsoft.AspNetCore.Mvc.ApiExplorer.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Core/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Cors/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Cors.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.DataAnnotations/*******": {"compile": {"Microsoft.AspNetCore.Mvc.DataAnnotations.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc/*******": {"compile": {"Microsoft.AspNetCore.Mvc.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Formatters.Json/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Formatters.Json.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Formatters.Xml/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Formatters.Xml.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Localization/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Localization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Razor/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Razor.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.RazorPages/*******": {"compile": {"Microsoft.AspNetCore.Mvc.RazorPages.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.TagHelpers/*******": {"compile": {"Microsoft.AspNetCore.Mvc.TagHelpers.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.ViewFeatures/*******": {"compile": {"Microsoft.AspNetCore.Mvc.ViewFeatures.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.OutputCaching/*******": {"compile": {"Microsoft.AspNetCore.OutputCaching.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.RateLimiting/*******": {"compile": {"Microsoft.AspNetCore.RateLimiting.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Razor/*******": {"compile": {"Microsoft.AspNetCore.Razor.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Razor.Runtime/*******": {"compile": {"Microsoft.AspNetCore.Razor.Runtime.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.RequestDecompression/*******": {"compile": {"Microsoft.AspNetCore.RequestDecompression.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCaching/*******": {"compile": {"Microsoft.AspNetCore.ResponseCaching.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCompression/*******": {"compile": {"Microsoft.AspNetCore.ResponseCompression.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Rewrite/*******": {"compile": {"Microsoft.AspNetCore.Rewrite.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Routing.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Routing.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Routing/*******": {"compile": {"Microsoft.AspNetCore.Routing.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.HttpSys/*******": {"compile": {"Microsoft.AspNetCore.Server.HttpSys.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.IIS/*******": {"compile": {"Microsoft.AspNetCore.Server.IIS.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.IISIntegration/*******": {"compile": {"Microsoft.AspNetCore.Server.IISIntegration.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Core/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Transport.Quic.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Session/*******": {"compile": {"Microsoft.AspNetCore.Session.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Common/*******": {"compile": {"Microsoft.AspNetCore.SignalR.Common.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Core/*******": {"compile": {"Microsoft.AspNetCore.SignalR.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR/*******": {"compile": {"Microsoft.AspNetCore.SignalR.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Protocols.Json/*******": {"compile": {"Microsoft.AspNetCore.SignalR.Protocols.Json.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.StaticFiles/*******": {"compile": {"Microsoft.AspNetCore.StaticFiles.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.WebSockets/*******": {"compile": {"Microsoft.AspNetCore.WebSockets.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.WebUtilities/*******": {"compile": {"Microsoft.AspNetCore.WebUtilities.dll": {}}, "compileOnly": true}, "Microsoft.CSharp.Reference/*******": {"compile": {"Microsoft.CSharp.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Caching.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Caching.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Caching.Memory.Reference/*******": {"compile": {"Microsoft.Extensions.Caching.Memory.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Binder.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.Binder.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.CommandLine.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.CommandLine.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.EnvironmentVariables.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.FileExtensions.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.FileExtensions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Ini/*******": {"compile": {"Microsoft.Extensions.Configuration.Ini.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Json.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.Json.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.KeyPerFile/*******": {"compile": {"Microsoft.Extensions.Configuration.KeyPerFile.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.UserSecrets.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.UserSecrets.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Xml/*******": {"compile": {"Microsoft.Extensions.Configuration.Xml.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.DependencyInjection.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.DependencyInjection.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.DependencyInjection.Reference/*******": {"compile": {"Microsoft.Extensions.DependencyInjection.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Diagnostics.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics/*******": {"compile": {"Microsoft.Extensions.Diagnostics.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/*******": {"compile": {"Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics.HealthChecks/*******": {"compile": {"Microsoft.Extensions.Diagnostics.HealthChecks.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Features/*******": {"compile": {"Microsoft.Extensions.Features.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.FileProviders.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Composite.Reference/*******": {"compile": {"Microsoft.Extensions.FileProviders.Composite.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Embedded.Reference/*******": {"compile": {"Microsoft.Extensions.FileProviders.Embedded.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Physical.Reference/*******": {"compile": {"Microsoft.Extensions.FileProviders.Physical.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileSystemGlobbing.Reference/*******": {"compile": {"Microsoft.Extensions.FileSystemGlobbing.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Hosting.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Hosting.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Hosting/*******": {"compile": {"Microsoft.Extensions.Hosting.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Http/*******": {"compile": {"Microsoft.Extensions.Http.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Identity.Core.Reference/*******": {"compile": {"Microsoft.Extensions.Identity.Core.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Identity.Stores/*******": {"compile": {"Microsoft.Extensions.Identity.Stores.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Localization.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Localization.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Localization.Reference/*******": {"compile": {"Microsoft.Extensions.Localization.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Logging.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Configuration/*******": {"compile": {"Microsoft.Extensions.Logging.Configuration.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Console/*******": {"compile": {"Microsoft.Extensions.Logging.Console.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Debug/*******": {"compile": {"Microsoft.Extensions.Logging.Debug.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Reference/*******": {"compile": {"Microsoft.Extensions.Logging.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.EventLog/*******": {"compile": {"Microsoft.Extensions.Logging.EventLog.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.EventSource/*******": {"compile": {"Microsoft.Extensions.Logging.EventSource.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.TraceSource/*******": {"compile": {"Microsoft.Extensions.Logging.TraceSource.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.ObjectPool/*******": {"compile": {"Microsoft.Extensions.ObjectPool.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Options.ConfigurationExtensions.Reference/*******": {"compile": {"Microsoft.Extensions.Options.ConfigurationExtensions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Options.DataAnnotations/*******": {"compile": {"Microsoft.Extensions.Options.DataAnnotations.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Options.Reference/*******": {"compile": {"Microsoft.Extensions.Options.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Primitives.Reference/*******": {"compile": {"Microsoft.Extensions.Primitives.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.WebEncoders/*******": {"compile": {"Microsoft.Extensions.WebEncoders.dll": {}}, "compileOnly": true}, "Microsoft.JSInterop/*******": {"compile": {"Microsoft.JSInterop.dll": {}}, "compileOnly": true}, "Microsoft.Net.Http.Headers/*******": {"compile": {"Microsoft.Net.Http.Headers.dll": {}}, "compileOnly": true}, "Microsoft.VisualBasic.Core/13.0.0.0": {"compile": {"Microsoft.VisualBasic.Core.dll": {}}, "compileOnly": true}, "Microsoft.VisualBasic/10.0.0.0": {"compile": {"Microsoft.VisualBasic.dll": {}}, "compileOnly": true}, "Microsoft.Win32.Primitives/*******": {"compile": {"Microsoft.Win32.Primitives.dll": {}}, "compileOnly": true}, "Microsoft.Win32.Registry/*******": {"compile": {"Microsoft.Win32.Registry.dll": {}}, "compileOnly": true}, "mscorlib/*******": {"compile": {"mscorlib.dll": {}}, "compileOnly": true}, "netstandard/2.1.0.0": {"compile": {"netstandard.dll": {}}, "compileOnly": true}, "System.AppContext/*******": {"compile": {"System.AppContext.dll": {}}, "compileOnly": true}, "System.Buffers/*******": {"compile": {"System.Buffers.dll": {}}, "compileOnly": true}, "System.Collections.Concurrent/*******": {"compile": {"System.Collections.Concurrent.dll": {}}, "compileOnly": true}, "System.Collections.Reference/*******": {"compile": {"System.Collections.dll": {}}, "compileOnly": true}, "System.Collections.Immutable.Reference/*******": {"compile": {"System.Collections.Immutable.dll": {}}, "compileOnly": true}, "System.Collections.NonGeneric/*******": {"compile": {"System.Collections.NonGeneric.dll": {}}, "compileOnly": true}, "System.Collections.Specialized/*******": {"compile": {"System.Collections.Specialized.dll": {}}, "compileOnly": true}, "System.ComponentModel.Annotations/*******": {"compile": {"System.ComponentModel.Annotations.dll": {}}, "compileOnly": true}, "System.ComponentModel.DataAnnotations/*******": {"compile": {"System.ComponentModel.DataAnnotations.dll": {}}, "compileOnly": true}, "System.ComponentModel/*******": {"compile": {"System.ComponentModel.dll": {}}, "compileOnly": true}, "System.ComponentModel.EventBasedAsync/*******": {"compile": {"System.ComponentModel.EventBasedAsync.dll": {}}, "compileOnly": true}, "System.ComponentModel.Primitives/*******": {"compile": {"System.ComponentModel.Primitives.dll": {}}, "compileOnly": true}, "System.ComponentModel.TypeConverter/*******": {"compile": {"System.ComponentModel.TypeConverter.dll": {}}, "compileOnly": true}, "System.Configuration/*******": {"compile": {"System.Configuration.dll": {}}, "compileOnly": true}, "System.Console/*******": {"compile": {"System.Console.dll": {}}, "compileOnly": true}, "System.Core/*******": {"compile": {"System.Core.dll": {}}, "compileOnly": true}, "System.Data.Common/*******": {"compile": {"System.Data.Common.dll": {}}, "compileOnly": true}, "System.Data.DataSetExtensions/*******": {"compile": {"System.Data.DataSetExtensions.dll": {}}, "compileOnly": true}, "System.Data/*******": {"compile": {"System.Data.dll": {}}, "compileOnly": true}, "System.Diagnostics.Contracts/*******": {"compile": {"System.Diagnostics.Contracts.dll": {}}, "compileOnly": true}, "System.Diagnostics.Debug.Reference/*******": {"compile": {"System.Diagnostics.Debug.dll": {}}, "compileOnly": true}, "System.Diagnostics.DiagnosticSource.Reference/*******": {"compile": {"System.Diagnostics.DiagnosticSource.dll": {}}, "compileOnly": true}, "System.Diagnostics.EventLog/*******": {"compile": {"System.Diagnostics.EventLog.dll": {}}, "compileOnly": true}, "System.Diagnostics.FileVersionInfo/*******": {"compile": {"System.Diagnostics.FileVersionInfo.dll": {}}, "compileOnly": true}, "System.Diagnostics.Process/*******": {"compile": {"System.Diagnostics.Process.dll": {}}, "compileOnly": true}, "System.Diagnostics.StackTrace/*******": {"compile": {"System.Diagnostics.StackTrace.dll": {}}, "compileOnly": true}, "System.Diagnostics.TextWriterTraceListener/*******": {"compile": {"System.Diagnostics.TextWriterTraceListener.dll": {}}, "compileOnly": true}, "System.Diagnostics.Tools/*******": {"compile": {"System.Diagnostics.Tools.dll": {}}, "compileOnly": true}, "System.Diagnostics.TraceSource/*******": {"compile": {"System.Diagnostics.TraceSource.dll": {}}, "compileOnly": true}, "System.Diagnostics.Tracing/*******": {"compile": {"System.Diagnostics.Tracing.dll": {}}, "compileOnly": true}, "System/*******": {"compile": {"System.dll": {}}, "compileOnly": true}, "System.Drawing/*******": {"compile": {"System.Drawing.dll": {}}, "compileOnly": true}, "System.Drawing.Primitives/*******": {"compile": {"System.Drawing.Primitives.dll": {}}, "compileOnly": true}, "System.Dynamic.Runtime/*******": {"compile": {"System.Dynamic.Runtime.dll": {}}, "compileOnly": true}, "System.Formats.Asn1/*******": {"compile": {"System.Formats.Asn1.dll": {}}, "compileOnly": true}, "System.Formats.Tar/*******": {"compile": {"System.Formats.Tar.dll": {}}, "compileOnly": true}, "System.Globalization.Calendars/*******": {"compile": {"System.Globalization.Calendars.dll": {}}, "compileOnly": true}, "System.Globalization.Reference/*******": {"compile": {"System.Globalization.dll": {}}, "compileOnly": true}, "System.Globalization.Extensions/*******": {"compile": {"System.Globalization.Extensions.dll": {}}, "compileOnly": true}, "System.IO.Compression.Brotli/*******": {"compile": {"System.IO.Compression.Brotli.dll": {}}, "compileOnly": true}, "System.IO.Compression/*******": {"compile": {"System.IO.Compression.dll": {}}, "compileOnly": true}, "System.IO.Compression.FileSystem/*******": {"compile": {"System.IO.Compression.FileSystem.dll": {}}, "compileOnly": true}, "System.IO.Compression.ZipFile/*******": {"compile": {"System.IO.Compression.ZipFile.dll": {}}, "compileOnly": true}, "System.IO.Reference/*******": {"compile": {"System.IO.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.AccessControl/*******": {"compile": {"System.IO.FileSystem.AccessControl.dll": {}}, "compileOnly": true}, "System.IO.FileSystem/*******": {"compile": {"System.IO.FileSystem.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.DriveInfo/*******": {"compile": {"System.IO.FileSystem.DriveInfo.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.Primitives/*******": {"compile": {"System.IO.FileSystem.Primitives.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.Watcher/*******": {"compile": {"System.IO.FileSystem.Watcher.dll": {}}, "compileOnly": true}, "System.IO.IsolatedStorage/*******": {"compile": {"System.IO.IsolatedStorage.dll": {}}, "compileOnly": true}, "System.IO.MemoryMappedFiles/*******": {"compile": {"System.IO.MemoryMappedFiles.dll": {}}, "compileOnly": true}, "System.IO.Pipelines/*******": {"compile": {"System.IO.Pipelines.dll": {}}, "compileOnly": true}, "System.IO.Pipes.AccessControl/*******": {"compile": {"System.IO.Pipes.AccessControl.dll": {}}, "compileOnly": true}, "System.IO.Pipes/*******": {"compile": {"System.IO.Pipes.dll": {}}, "compileOnly": true}, "System.IO.UnmanagedMemoryStream/*******": {"compile": {"System.IO.UnmanagedMemoryStream.dll": {}}, "compileOnly": true}, "System.Linq.Reference/*******": {"compile": {"System.Linq.dll": {}}, "compileOnly": true}, "System.Linq.Expressions.Reference/*******": {"compile": {"System.Linq.Expressions.dll": {}}, "compileOnly": true}, "System.Linq.Parallel/*******": {"compile": {"System.Linq.Parallel.dll": {}}, "compileOnly": true}, "System.Linq.Queryable.Reference/*******": {"compile": {"System.Linq.Queryable.dll": {}}, "compileOnly": true}, "System.Memory.Reference/*******": {"compile": {"System.Memory.dll": {}}, "compileOnly": true}, "System.Net/*******": {"compile": {"System.Net.dll": {}}, "compileOnly": true}, "System.Net.Http/*******": {"compile": {"System.Net.Http.dll": {}}, "compileOnly": true}, "System.Net.Http.Json/*******": {"compile": {"System.Net.Http.Json.dll": {}}, "compileOnly": true}, "System.Net.HttpListener/*******": {"compile": {"System.Net.HttpListener.dll": {}}, "compileOnly": true}, "System.Net.Mail/*******": {"compile": {"System.Net.Mail.dll": {}}, "compileOnly": true}, "System.Net.NameResolution/*******": {"compile": {"System.Net.NameResolution.dll": {}}, "compileOnly": true}, "System.Net.NetworkInformation/*******": {"compile": {"System.Net.NetworkInformation.dll": {}}, "compileOnly": true}, "System.Net.Ping/*******": {"compile": {"System.Net.Ping.dll": {}}, "compileOnly": true}, "System.Net.Primitives/*******": {"compile": {"System.Net.Primitives.dll": {}}, "compileOnly": true}, "System.Net.Quic/*******": {"compile": {"System.Net.Quic.dll": {}}, "compileOnly": true}, "System.Net.Requests/*******": {"compile": {"System.Net.Requests.dll": {}}, "compileOnly": true}, "System.Net.Security/*******": {"compile": {"System.Net.Security.dll": {}}, "compileOnly": true}, "System.Net.ServicePoint/*******": {"compile": {"System.Net.ServicePoint.dll": {}}, "compileOnly": true}, "System.Net.Sockets/*******": {"compile": {"System.Net.Sockets.dll": {}}, "compileOnly": true}, "System.Net.WebClient/*******": {"compile": {"System.Net.WebClient.dll": {}}, "compileOnly": true}, "System.Net.WebHeaderCollection/*******": {"compile": {"System.Net.WebHeaderCollection.dll": {}}, "compileOnly": true}, "System.Net.WebProxy/*******": {"compile": {"System.Net.WebProxy.dll": {}}, "compileOnly": true}, "System.Net.WebSockets.Client/*******": {"compile": {"System.Net.WebSockets.Client.dll": {}}, "compileOnly": true}, "System.Net.WebSockets/*******": {"compile": {"System.Net.WebSockets.dll": {}}, "compileOnly": true}, "System.Numerics/*******": {"compile": {"System.Numerics.dll": {}}, "compileOnly": true}, "System.Numerics.Vectors/*******": {"compile": {"System.Numerics.Vectors.dll": {}}, "compileOnly": true}, "System.ObjectModel.Reference/*******": {"compile": {"System.ObjectModel.dll": {}}, "compileOnly": true}, "System.Reflection.DispatchProxy/*******": {"compile": {"System.Reflection.DispatchProxy.dll": {}}, "compileOnly": true}, "System.Reflection.Reference/*******": {"compile": {"System.Reflection.dll": {}}, "compileOnly": true}, "System.Reflection.Emit.Reference/*******": {"compile": {"System.Reflection.Emit.dll": {}}, "compileOnly": true}, "System.Reflection.Emit.ILGeneration.Reference/*******": {"compile": {"System.Reflection.Emit.ILGeneration.dll": {}}, "compileOnly": true}, "System.Reflection.Emit.Lightweight.Reference/*******": {"compile": {"System.Reflection.Emit.Lightweight.dll": {}}, "compileOnly": true}, "System.Reflection.Extensions.Reference/*******": {"compile": {"System.Reflection.Extensions.dll": {}}, "compileOnly": true}, "System.Reflection.Metadata.Reference/*******": {"compile": {"System.Reflection.Metadata.dll": {}}, "compileOnly": true}, "System.Reflection.Primitives.Reference/*******": {"compile": {"System.Reflection.Primitives.dll": {}}, "compileOnly": true}, "System.Reflection.TypeExtensions.Reference/*******": {"compile": {"System.Reflection.TypeExtensions.dll": {}}, "compileOnly": true}, "System.Resources.Reader/*******": {"compile": {"System.Resources.Reader.dll": {}}, "compileOnly": true}, "System.Resources.ResourceManager.Reference/*******": {"compile": {"System.Resources.ResourceManager.dll": {}}, "compileOnly": true}, "System.Resources.Writer/*******": {"compile": {"System.Resources.Writer.dll": {}}, "compileOnly": true}, "System.Runtime.CompilerServices.Unsafe.Reference/*******": {"compile": {"System.Runtime.CompilerServices.Unsafe.dll": {}}, "compileOnly": true}, "System.Runtime.CompilerServices.VisualC/*******": {"compile": {"System.Runtime.CompilerServices.VisualC.dll": {}}, "compileOnly": true}, "System.Runtime.Reference/*******": {"compile": {"System.Runtime.dll": {}}, "compileOnly": true}, "System.Runtime.Extensions.Reference/*******": {"compile": {"System.Runtime.Extensions.dll": {}}, "compileOnly": true}, "System.Runtime.Handles/*******": {"compile": {"System.Runtime.Handles.dll": {}}, "compileOnly": true}, "System.Runtime.InteropServices/*******": {"compile": {"System.Runtime.InteropServices.dll": {}}, "compileOnly": true}, "System.Runtime.InteropServices.JavaScript/*******": {"compile": {"System.Runtime.InteropServices.JavaScript.dll": {}}, "compileOnly": true}, "System.Runtime.InteropServices.RuntimeInformation/*******": {"compile": {"System.Runtime.InteropServices.RuntimeInformation.dll": {}}, "compileOnly": true}, "System.Runtime.Intrinsics/*******": {"compile": {"System.Runtime.Intrinsics.dll": {}}, "compileOnly": true}, "System.Runtime.Loader.Reference/*******": {"compile": {"System.Runtime.Loader.dll": {}}, "compileOnly": true}, "System.Runtime.Numerics/*******": {"compile": {"System.Runtime.Numerics.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization/*******": {"compile": {"System.Runtime.Serialization.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Formatters/*******": {"compile": {"System.Runtime.Serialization.Formatters.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Json/*******": {"compile": {"System.Runtime.Serialization.Json.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Primitives/*******": {"compile": {"System.Runtime.Serialization.Primitives.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Xml/*******": {"compile": {"System.Runtime.Serialization.Xml.dll": {}}, "compileOnly": true}, "System.Security.AccessControl/*******": {"compile": {"System.Security.AccessControl.dll": {}}, "compileOnly": true}, "System.Security.Claims/*******": {"compile": {"System.Security.Claims.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Algorithms/*******": {"compile": {"System.Security.Cryptography.Algorithms.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Cng/*******": {"compile": {"System.Security.Cryptography.Cng.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Csp/*******": {"compile": {"System.Security.Cryptography.Csp.dll": {}}, "compileOnly": true}, "System.Security.Cryptography/*******": {"compile": {"System.Security.Cryptography.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Encoding/*******": {"compile": {"System.Security.Cryptography.Encoding.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.OpenSsl/*******": {"compile": {"System.Security.Cryptography.OpenSsl.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Primitives/*******": {"compile": {"System.Security.Cryptography.Primitives.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.X509Certificates/*******": {"compile": {"System.Security.Cryptography.X509Certificates.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Xml/*******": {"compile": {"System.Security.Cryptography.Xml.dll": {}}, "compileOnly": true}, "System.Security/*******": {"compile": {"System.Security.dll": {}}, "compileOnly": true}, "System.Security.Principal/*******": {"compile": {"System.Security.Principal.dll": {}}, "compileOnly": true}, "System.Security.Principal.Windows/*******": {"compile": {"System.Security.Principal.Windows.dll": {}}, "compileOnly": true}, "System.Security.SecureString/*******": {"compile": {"System.Security.SecureString.dll": {}}, "compileOnly": true}, "System.ServiceModel.Web/*******": {"compile": {"System.ServiceModel.Web.dll": {}}, "compileOnly": true}, "System.ServiceProcess/*******": {"compile": {"System.ServiceProcess.dll": {}}, "compileOnly": true}, "System.Text.Encoding.CodePages.Reference/*******": {"compile": {"System.Text.Encoding.CodePages.dll": {}}, "compileOnly": true}, "System.Text.Encoding.Reference/*******": {"compile": {"System.Text.Encoding.dll": {}}, "compileOnly": true}, "System.Text.Encoding.Extensions/*******": {"compile": {"System.Text.Encoding.Extensions.dll": {}}, "compileOnly": true}, "System.Text.Encodings.Web.Reference/*******": {"compile": {"System.Text.Encodings.Web.dll": {}}, "compileOnly": true}, "System.Text.Json.Reference/*******": {"compile": {"System.Text.Json.dll": {}}, "compileOnly": true}, "System.Text.RegularExpressions/*******": {"compile": {"System.Text.RegularExpressions.dll": {}}, "compileOnly": true}, "System.Threading.Channels/*******": {"compile": {"System.Threading.Channels.dll": {}}, "compileOnly": true}, "System.Threading.Reference/*******": {"compile": {"System.Threading.dll": {}}, "compileOnly": true}, "System.Threading.Overlapped/*******": {"compile": {"System.Threading.Overlapped.dll": {}}, "compileOnly": true}, "System.Threading.RateLimiting/*******": {"compile": {"System.Threading.RateLimiting.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Dataflow/*******": {"compile": {"System.Threading.Tasks.Dataflow.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Reference/*******": {"compile": {"System.Threading.Tasks.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Extensions.Reference/*******": {"compile": {"System.Threading.Tasks.Extensions.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Parallel/*******": {"compile": {"System.Threading.Tasks.Parallel.dll": {}}, "compileOnly": true}, "System.Threading.Thread/*******": {"compile": {"System.Threading.Thread.dll": {}}, "compileOnly": true}, "System.Threading.ThreadPool/*******": {"compile": {"System.Threading.ThreadPool.dll": {}}, "compileOnly": true}, "System.Threading.Timer/*******": {"compile": {"System.Threading.Timer.dll": {}}, "compileOnly": true}, "System.Transactions/*******": {"compile": {"System.Transactions.dll": {}}, "compileOnly": true}, "System.Transactions.Local/*******": {"compile": {"System.Transactions.Local.dll": {}}, "compileOnly": true}, "System.ValueTuple/*******": {"compile": {"System.ValueTuple.dll": {}}, "compileOnly": true}, "System.Web/*******": {"compile": {"System.Web.dll": {}}, "compileOnly": true}, "System.Web.HttpUtility/*******": {"compile": {"System.Web.HttpUtility.dll": {}}, "compileOnly": true}, "System.Windows/*******": {"compile": {"System.Windows.dll": {}}, "compileOnly": true}, "System.Xml/*******": {"compile": {"System.Xml.dll": {}}, "compileOnly": true}, "System.Xml.Linq/*******": {"compile": {"System.Xml.Linq.dll": {}}, "compileOnly": true}, "System.Xml.ReaderWriter/*******": {"compile": {"System.Xml.ReaderWriter.dll": {}}, "compileOnly": true}, "System.Xml.Serialization/*******": {"compile": {"System.Xml.Serialization.dll": {}}, "compileOnly": true}, "System.Xml.XDocument/*******": {"compile": {"System.Xml.XDocument.dll": {}}, "compileOnly": true}, "System.Xml.XmlDocument/*******": {"compile": {"System.Xml.XmlDocument.dll": {}}, "compileOnly": true}, "System.Xml.XmlSerializer/*******": {"compile": {"System.Xml.XmlSerializer.dll": {}}, "compileOnly": true}, "System.Xml.XPath/*******": {"compile": {"System.Xml.XPath.dll": {}}, "compileOnly": true}, "System.Xml.XPath.XDocument/*******": {"compile": {"System.Xml.XPath.XDocument.dll": {}}, "compileOnly": true}, "WindowsBase/*******": {"compile": {"WindowsBase.dll": {}}, "compileOnly": true}}}, "libraries": {"EdTech.Study.Application/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "Asp.Versioning.Abstractions/8.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-mpeNZyMdvrHztJwR1sXIUQ+3iioEU97YMBnFA9WLbsPOYhGwDJnqJMmEd8ny7kcmS9OjTHoEuX/bSXXY3brIFA==", "path": "asp.versioning.abstractions/8.1.0", "hashPath": "asp.versioning.abstractions.8.1.0.nupkg.sha512"}, "Asp.Versioning.Http/8.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Xu4xF62Cu9JqYi/CTa2TiK5kyHoa4EluPynj/bPFWDmlTIPzuJQbBI5RgFYVRFHjFVvWMoA77acRaFu7i7Wzqg==", "path": "asp.versioning.http/8.1.0", "hashPath": "asp.versioning.http.8.1.0.nupkg.sha512"}, "Asp.Versioning.Mvc/8.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-BMAJM2sGsTUw5FQ9upKQt6GFoldWksePgGpYjl56WSRvIuE3UxKZh0gAL+wDTIfLshUZm97VCVxlOGyrcjWz9Q==", "path": "asp.versioning.mvc/8.1.0", "hashPath": "asp.versioning.mvc.8.1.0.nupkg.sha512"}, "Asp.Versioning.Mvc.ApiExplorer/8.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-a90gW/4TF/14Bjiwg9LqNtdKGC4G3gu02+uynq3bCISfQm48km5chny4Yg5J4hixQPJUwwJJ9Do1G+jM8L9h3g==", "path": "asp.versioning.mvc.apiexplorer/8.1.0", "hashPath": "asp.versioning.mvc.apiexplorer.8.1.0.nupkg.sha512"}, "AsyncKeyedLock/6.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-+0YPXvamnopzHSgBpv0vg+9/NJH+6jm1ZODuMMs7p43yH7hA/NxNi+/U7T7eazZqW+V+0ZsbreYruhu6i7N4PA==", "path": "asynckeyedlock/6.3.4", "hashPath": "asynckeyedlock.6.3.4.nupkg.sha512"}, "AutoMapper/12.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-hvV62vl6Hp/WfQ24yzo3Co9+OPl8wH8hApwVtgWpiAynVJkUcs7xvehnSftawL8Pe8FrPffBRM3hwzLQqWDNjA==", "path": "automapper/12.0.1", "hashPath": "automapper.12.0.1.nupkg.sha512"}, "ConfigureAwait.Fody/3.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-R9PQYf0AT4RBZcUXm22xWkCpSmNHdTzQ0dOyLIsxIK6dwXH4S9pY/rZdXU/63i8vZvSzZ99sB1kP7xer8MCe6w==", "path": "configureawait.fody/3.3.1", "hashPath": "configureawait.fody.3.3.1.nupkg.sha512"}, "Fody/6.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-sRkrGVPJWG5vVKF/3kExAwZhFMUzK/Zksgcv113ehyuYuTDMuqBC4lr6y0qqZ6ga5nT1uueebDzrsRZsNIrqLg==", "path": "fody/6.5.3", "hashPath": "fody.6.5.3.nupkg.sha512"}, "IdentityModel/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-4AXZ6Tp+DNwrSSeBziiX/231i8ZpD77A9nEMyc68gLSCWG0kgWsIBeFquYcBebiIPkfB7GEXzCYuuLeR1QZJIQ==", "path": "identitymodel/6.2.0", "hashPath": "identitymodel.6.2.0.nupkg.sha512"}, "JetBrains.Annotations/2023.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PHfnvdBUdGaTVG9bR/GEfxgTwWM0Z97Y6X3710wiljELBISipSfF5okn/vz+C2gfO+ihoEyVPjaJwn8ZalVukA==", "path": "jetbrains.annotations/2023.3.0", "hashPath": "jetbrains.annotations.2023.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.OpenIdConnect/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-vBAcj4GpCpvJXIXFnYXDVF0yQtsRAaGib+DiMc79KZNyb/TKhxpoHQwOP7v3aMAoIqUC0HUbf1RQJUoOygakbQ==", "path": "microsoft.aspnetcore.authentication.openidconnect/8.0.0", "hashPath": "microsoft.aspnetcore.authentication.openidconnect.8.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OGIGJMnlWvQgcweHcv1Mq/P24Zx/brUHeEdD05NzqkSXmQSnFomTvVyCuBtCXT4JPfv2m70y1RSocmd9bIbJRg==", "path": "microsoft.aspnetcore.authorization/8.0.0", "hashPath": "microsoft.aspnetcore.authorization.8.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.Internal/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-buuMMCTxFcVkOkEftb2OafYxrveNGre9KJF4Oi1DkR4rxIj6oLam7Wq3g0Fp9hNVpJteKEPiupsxYnPrD/oUGA==", "path": "microsoft.aspnetcore.cryptography.internal/8.0.0", "hashPath": "microsoft.aspnetcore.cryptography.internal.8.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-65w93R5wqUUs35R9wjHHDf75GqAbxJsNByKZo5TbQOWSXcUbLWrDUWBQHv78iXIT0PL1pXNqKQz7OHiHMvo0/A==", "path": "microsoft.aspnetcore.cryptography.keyderivation/8.0.0", "hashPath": "microsoft.aspnetcore.cryptography.keyderivation.8.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Metadata/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OmuSztiZMitRTYlbMNDkBk3BinSsVcOApSNBAsrw+KYNJh6ALarPhWLlKdtvMgrKzpyCY06xtLAjTmQLURHSlQ==", "path": "microsoft.aspnetcore.metadata/8.0.0", "hashPath": "microsoft.aspnetcore.metadata.8.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Razor.Extensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-M0h+ChPgydX2xY17agiphnAVa/Qh05RAP8eeuqGGhQKT10claRBlLNO6d2/oSV8zy0RLHzwLnNZm5xuC/gckGA==", "path": "microsoft.aspnetcore.mvc.razor.extensions/6.0.0", "hashPath": "microsoft.aspnetcore.mvc.razor.extensions.6.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-HyLDtyWwpavSEFBOL0qOdymY8f+VwN5QhhE7gj3wBw53j9EA0ZcYkbfTvkhvMeV9PavgCcMIe4sAsBGEE/YnNA==", "path": "microsoft.aspnetcore.mvc.razor.runtimecompilation/8.0.0", "hashPath": "microsoft.aspnetcore.mvc.razor.runtimecompilation.8.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Language/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yCtBr1GSGzJrrp1NJUb4ltwFYMKHw/tJLnIDvg9g/FnkGIEzmE19tbCQqXARIJv5kdtBgsoVIdGLL+zmjxvM/A==", "path": "microsoft.aspnetcore.razor.language/6.0.0", "hashPath": "microsoft.aspnetcore.razor.language.6.0.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3WA9q9yVqJp222P3x1wYIGDAkpjAku0TMUaaQV22g6L67AI0LdOIrVS7Ht2vJfLHGSPVuqN94vIr15qn+HEkHw==", "path": "microsoft.bcl.asyncinterfaces/8.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7xt6zTlIEizUgEsYAIgm37EbdkiMmr6fP6J9pDoKEpiGM4pi32BCPGr/IczmSJI9Zzp0a6HOzpr9OvpMP+2veA==", "path": "microsoft.codeanalysis.analyzers/3.3.2", "hashPath": "microsoft.codeanalysis.analyzers.3.3.2.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-d02ybMhUJl1r/dI6SkJPHrTiTzXBYCZeJdOLMckV+jyoMU/GGkjqFX/sRbv1K0QmlpwwKuLTiYVQvfYC+8ox2g==", "path": "microsoft.codeanalysis.common/4.0.0", "hashPath": "microsoft.codeanalysis.common.4.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2UVTGtyQGgTCazvnT6t82f+7AV2L+kqJdyb61rT9GQed4yK+tVh5IkaKcsm70VqyZQhBbDqsfZFNHnY65xhrRw==", "path": "microsoft.codeanalysis.csharp/4.0.0", "hashPath": "microsoft.codeanalysis.csharp.4.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Razor/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uqdzuQXxD7XrJCbIbbwpI/LOv0PBJ9VIR0gdvANTHOfK5pjTaCir+XcwvYvBZ5BIzd0KGzyiamzlEWw1cK1q0w==", "path": "microsoft.codeanalysis.razor/6.0.0", "hashPath": "microsoft.codeanalysis.razor.6.0.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "path": "microsoft.extensions.caching.abstractions/8.0.0", "hashPath": "microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7pqivmrZDzo1ADPkRwjy+8jtRKWRCPag9qPI+p7sgu7Q4QreWhcvbiWXsbhP+yY8XSiDvZpu2/LWdBv7PnmOpQ==", "path": "microsoft.extensions.caching.memory/8.0.0", "hashPath": "microsoft.extensions.caching.memory.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "path": "microsoft.extensions.configuration/8.0.0", "hashPath": "microsoft.extensions.configuration.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-mBMoXLsr5s1y2zOHWmKsE9veDcx8h1x/c3rz4baEdQKTeDcmQAPNbB54Pi/lhFO3K431eEq6PFbMgLaa6PHFfA==", "path": "microsoft.extensions.configuration.binder/8.0.0", "hashPath": "microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NZuZMz3Q8Z780nKX3ifV1fE7lS+6pynDHK71OfU4OZ1ItgvDOhyOC7E6z+JMZrAj63zRpwbdldYFk499t3+1dQ==", "path": "microsoft.extensions.configuration.commandline/8.0.0", "hashPath": "microsoft.extensions.configuration.commandline.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-plvZ0ZIpq+97gdPNNvhwvrEZ92kNml9hd1pe3idMA7svR0PztdzVLkoWLcRFgySYXUJc3kSM3Xw3mNFMo/bxRA==", "path": "microsoft.extensions.configuration.environmentvariables/8.0.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-McP+Lz/EKwvtCv48z0YImw+L1gi1gy5rHhNaNIY2CrjloV+XY8gydT8DjMR6zWeL13AFK+DioVpppwAuO1Gi1w==", "path": "microsoft.extensions.configuration.fileextensions/8.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-C2wqUoh9OmRL1akaCcKSTmRU8z0kckfImG7zLNI8uyi47Lp+zd5LWAD17waPQEqCz3ioWOCrFUo+JJuoeZLOBw==", "path": "microsoft.extensions.configuration.json/8.0.0", "hashPath": "microsoft.extensions.configuration.json.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ihDHu2dJYQird9pl2CbdwuNDfvCZdOS0S7SPlNfhPt0B81UTT+yyZKz2pimFZGUp3AfuBRnqUCxB2SjsZKHVUw==", "path": "microsoft.extensions.configuration.usersecrets/8.0.0", "hashPath": "microsoft.extensions.configuration.usersecrets.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "path": "microsoft.extensions.dependencyinjection/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NSmDw3K0ozNDgShSIpsZcbFIzBX4w28nDag+TfaQujkXGazBm+lid5onlWoCBy4VsLxqnnKjEBbGSJVWJMf43g==", "path": "microsoft.extensions.dependencymodel/8.0.0", "hashPath": "microsoft.extensions.dependencymodel.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JHYCQG7HmugNYUhOl368g+NMxYE/N/AiclCYRNlgCY9eVyiBkOHMwK4x60RYMxv9EL3+rmj1mqHvdCiPpC+D4Q==", "path": "microsoft.extensions.diagnostics.abstractions/8.0.0", "hashPath": "microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Composite/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ynMjdZ5B3Fd3A9GxJaNhIcTrjLY1bXDQltyVIMVOxbT0ssTOCpFYWc977bVBAocB62fYWu/RN6/1HLnX/HjVuQ==", "path": "microsoft.extensions.fileproviders.composite/8.0.0", "hashPath": "microsoft.extensions.fileproviders.composite.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Embedded/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-TuRh62KcoOvaSDCbtHT8K0WYptZysYQHPRRNfOgqF7ZUtUL4O0WMV8RdxbtDFJDsg3jv9bgHwXbrgwTeI9+5uQ==", "path": "microsoft.extensions.fileproviders.embedded/8.0.0", "hashPath": "microsoft.extensions.fileproviders.embedded.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "path": "microsoft.extensions.fileproviders.physical/8.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AG7HWwVRdCHlaA++1oKDxLsXIBxmDpMPb3VoyOoAghEWnkUvEAdYQUwnV4jJbAaa/nMYNiEh5ByoLauZBEiovg==", "path": "microsoft.extensions.hosting.abstractions/8.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Identity.Core/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hnXHyIQc+uc2uNMcIbr43+oNBAPEhMpW6lE8ux3MOegRz50WBna4AItlZDY7Y+Id1LLBbf73osUqeTw7CQ371w==", "path": "microsoft.extensions.identity.core/8.0.0", "hashPath": "microsoft.extensions.identity.core.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Localization/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-I4xyK9RaXyJBkU5+jQpkZITR/54tXGbdt5CsBmogf8Irrz+RXXsp1zJgrmQzxEOpxj5VMAA/GQ4SGZMeM5nVSg==", "path": "microsoft.extensions.localization/8.0.0", "hashPath": "microsoft.extensions.localization.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Localization.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LIMzXjjzv7o3tx3XXvQVHNcrMNPfSoUp4R/YJucdyBixJGRp5p8v2rF1CnRbo+wwtzB4Se/gJYyUpNN8F/TMGw==", "path": "microsoft.extensions.localization.abstractions/8.0.0", "hashPath": "microsoft.extensions.localization.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "path": "microsoft.extensions.logging/8.0.0", "hashPath": "microsoft.extensions.logging.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "path": "microsoft.extensions.logging.abstractions/8.0.0", "hashPath": "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JOVOfqpnqlVLUzINQ2fox8evY2SKLYJ3BV8QDe/Jyp21u1T7r45x/R/5QdteURMR5r01GxeJSBBUOCOyaNXA3g==", "path": "microsoft.extensions.options/8.0.0", "hashPath": "microsoft.extensions.options.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "path": "microsoft.extensions.options.configurationextensions/8.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/7.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-cfPUWdjigLIRIJSKz3uaZxShgf86RVDXHC1VEEchj1gnY25akwPYpbrfSoIGDCqA9UmOMdlctq411+2pAViFow==", "path": "microsoft.identitymodel.abstractions/7.0.3", "hashPath": "microsoft.identitymodel.abstractions.7.0.3.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/7.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-vxjHVZbMKD3rVdbvKhzAW+7UiFrYToUVm3AGmYfKSOAwyhdLl/ELX1KZr+FaLyyS5VReIzWRWJfbOuHM9i6ywg==", "path": "microsoft.identitymodel.jsonwebtokens/7.0.3", "hashPath": "microsoft.identitymodel.jsonwebtokens.7.0.3.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/7.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-b6GbGO+2LOTBEccHhqoJsOsmemG4A/MY+8H0wK/ewRhiG+DCYwEnucog1cSArPIY55zcn+XdZl0YEiUHkpDISQ==", "path": "microsoft.identitymodel.logging/7.0.3", "hashPath": "microsoft.identitymodel.logging.7.0.3.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/7.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-BtwR+tctBYhPNygyZmt1Rnw74GFrJteW+1zcdIgyvBCjkek6cNwPPqRfdhzCv61i+lwyNomRi8+iI4QKd4YCKA==", "path": "microsoft.identitymodel.protocols/7.0.3", "hashPath": "microsoft.identitymodel.protocols.7.0.3.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-W97TraHApDNArLwpPcXfD+FZH7njJsfEwZE9y9BoofeXMS8H0LBBobz0VOmYmMK4mLdOKxzN7SFT3Ekg0FWI3Q==", "path": "microsoft.identitymodel.protocols.openidconnect/7.0.3", "hashPath": "microsoft.identitymodel.protocols.openidconnect.7.0.3.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/7.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-wB+LlbDjhnJ98DULjmFepqf9eEMh/sDs6S6hFh68iNRHmwollwhxk+nbSSfpA5+j+FbRyNskoaY4JsY1iCOKCg==", "path": "microsoft.identitymodel.tokens/7.0.3", "hashPath": "microsoft.identitymodel.tokens.7.0.3.nupkg.sha512"}, "Microsoft.NETCore.Platforms/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-mOJy3M0UN+LUG21dLGMxaWZEP6xYpQEpLuvuEQBaownaX4YuhH6NmNUlN9si+vNkAS6dwJ//N1O4DmLf2CikVg==", "path": "microsoft.netcore.platforms/2.1.2", "hashPath": "microsoft.netcore.platforms.2.1.2.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Nito.AsyncEx.Context/5.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-rMwL7Nj3oNyvFu/jxUzQ/YBobEkM2RQHe+5mpCDRyq6mfD7vCj7Z3rjB6XgpM6Mqcx1CA2xGv0ascU/2Xk8IIg==", "path": "nito.asyncex.context/5.1.2", "hashPath": "nito.asyncex.context.5.1.2.nupkg.sha512"}, "Nito.AsyncEx.Tasks/5.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-jEkCfR2/M26OK/U4G7SEN063EU/F4LiVA06TtpZILMdX/quIHCg+wn31Zerl2LC+u1cyFancjTY3cNAr2/89PA==", "path": "nito.asyncex.tasks/5.1.2", "hashPath": "nito.asyncex.tasks.5.1.2.nupkg.sha512"}, "Nito.Disposables/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-6sZ5uynQeAE9dPWBQGKebNmxbY4xsvcc5VplB5WkYEESUS7oy4AwnFp0FhqxTSKm/PaFrFqLrYr696CYN8cugg==", "path": "nito.disposables/2.2.1", "hashPath": "nito.disposables.2.2.1.nupkg.sha512"}, "NUglify/1.21.0": {"type": "package", "serviceable": true, "sha512": "sha512-9J44o89PRdcS4GOyj0obkLfjCAuJItI4FrNmwALkjRKlzvHVlTB2ALbC9aigIoCMqzy0Xlc0mIVD/jO9WVDHiA==", "path": "nuglify/1.21.0", "hashPath": "nuglify.1.21.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AurL6Y5BA1WotzlEvVaIDpqzpIPvYnnldxru8oXJU2yFxFUy3+pNXjXd1ymO+RA0rq0+590Q8gaz2l3Sr7fmqg==", "path": "system.collections.immutable/8.0.0", "hashPath": "system.collections.immutable.8.0.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-c9xLpVz6PL9lp/djOWtk5KPDZq3cSYpmXoJQY524EOtuFl5z9ZtsotpsyrDW40U1DRnQSYvcPKEUV0X//u6gkQ==", "path": "system.diagnostics.diagnosticsource/8.0.0", "hashPath": "system.diagnostics.diagnosticsource.8.0.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/7.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-caEe+OpQNYNiyZb+DJpUVROXoVySWBahko2ooNfUcllxa9ZQUM8CgM/mDjP6AoFn6cQU9xMmG+jivXWub8cbGg==", "path": "system.identitymodel.tokens.jwt/7.0.3", "hashPath": "system.identitymodel.tokens.jwt.7.0.3.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Dynamic.Core/1.3.5": {"type": "package", "serviceable": true, "sha512": "sha512-G8aRAVHSQ/Jmt6dekX+z4K3L6ecX3CizaIYSQoDUolPRIPBYmBCabT+/pq5fMAFM57aOchFn8PUufuVfLjvEZQ==", "path": "system.linq.dynamic.core/1.3.5", "hashPath": "system.linq.dynamic.core.1.3.5.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Linq.Queryable/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-In1Bmmvl/j52yPu3xgakQSI0YIckPUr870w4K5+Lak3JCCa8hl+my65lABOuKfYs4ugmZy25ScFerC4nz8+b6g==", "path": "system.linq.queryable/4.3.0", "hashPath": "system.linq.queryable.4.3.0.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "path": "system.reflection.emit/4.3.0", "hashPath": "system.reflection.emit.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Metadata/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-5NecZgXktdGg34rh1OenY1rFNDCI8xSjFr+Z4OU4cU06AQHUdRnIIEeWENu3Wl4YowbzkymAIMvi3WyK9U53pQ==", "path": "system.reflection.metadata/5.0.0", "hashPath": "system.reflection.metadata.5.0.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZD9TMpsmYJLrxbbmdvhwt9YEgG5WntEnZ/d1eH8JBX9LBp+Ju8BSBhUGbZMNVHHomWo2KVImJhTDl2hIgw/6MA==", "path": "system.runtime.compilerservices.unsafe/5.0.0", "hashPath": "system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Loader/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHMaRn8D8YCK2GG2pw+UzNxn/OHVfaWx7OTLBD/hPegHZZgcZh3H6seWegrC4BYwsfuGrywIuT+MQs+rPqRLTQ==", "path": "system.runtime.loader/4.3.0", "hashPath": "system.runtime.loader.4.3.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-4J2JQXbftjPMppIHJ7IC+VXQ9XfEagN92vZZNoG12i+zReYlim5dMoXFC1Zzg7tsnKDM7JPo5bYfFK4Jheq44w==", "path": "system.text.encoding.codepages/4.5.1", "hashPath": "system.text.encoding.codepages.4.5.1.nupkg.sha512"}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "path": "system.text.encodings.web/8.0.0", "hashPath": "system.text.encodings.web.8.0.0.nupkg.sha512"}, "System.Text.Json/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON>drZ<PERSON>2WjkiEG6ajEFRABTRCi/wuXQPxeV6g8xvUJqdxMvvuCCEk86zPla8UiIQJz3durtUEbNyY/3lIhS0yZvQ==", "path": "system.text.json/8.0.0", "hashPath": "system.text.json.8.0.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "TimeZoneConverter/6.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-UGdtyKWJqXXinyvGB9X6NVoIYbTAidoZYmn3aXzxeEYC9+OL8vF36eDt1qjb6RqBkWDl4v7iE84ecI+dFhA80A==", "path": "timezoneconverter/6.1.0", "hashPath": "timezoneconverter.6.1.0.nupkg.sha512"}, "Volo.Abp.ApiVersioning.Abstractions/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-qJYic/gwI/3TvmYgEgz1x3u/OYOGbdKzNQkM8Pp9/cM76i/VDsobdz6u4OMnt/uap2rqI2VusFlRpco092jAsQ==", "path": "volo.abp.apiversioning.abstractions/8.1.4", "hashPath": "volo.abp.apiversioning.abstractions.8.1.4.nupkg.sha512"}, "Volo.Abp.AspNetCore/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-t7jkUDhM4vuVCYlUEj+TnTs8JFlyhUo+gY0lizyont8ja/lsC+hFZHgBKmu2oaOY39jkA2V0M/+RYS3AE2f3xQ==", "path": "volo.abp.aspnetcore/8.1.4", "hashPath": "volo.abp.aspnetcore.8.1.4.nupkg.sha512"}, "Volo.Abp.AspNetCore.Mvc/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-+EFG8X82aHi7mBlYgwxopQq7hmSJpmX0f4vhPO9uncDXQXIPO5a2kDntjrFbz2qGpIHMgEDvZmT/VKV2YjwT2g==", "path": "volo.abp.aspnetcore.mvc/8.1.4", "hashPath": "volo.abp.aspnetcore.mvc.8.1.4.nupkg.sha512"}, "Volo.Abp.AspNetCore.Mvc.Contracts/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-I2tRJ5AuCs99KY5Hn+e1hekcSRmOTnCKoSxjzDcpqrgs3YTCoPSBjfkk7ArZwKmGCmcUwAJxW5yWjnyN9ZlyZA==", "path": "volo.abp.aspnetcore.mvc.contracts/8.1.4", "hashPath": "volo.abp.aspnetcore.mvc.contracts.8.1.4.nupkg.sha512"}, "Volo.Abp.AspNetCore.Mvc.UI/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-SUJxBtVZZVaZ+eFX6PsKRszvVtaggrpyETM88SDEfY8f9VLtOp/5ME7H7aS9ieHWqPLfya5zHNZpYufRT/8cCA==", "path": "volo.abp.aspnetcore.mvc.ui/8.1.4", "hashPath": "volo.abp.aspnetcore.mvc.ui.8.1.4.nupkg.sha512"}, "Volo.Abp.AspNetCore.Mvc.UI.Bootstrap/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-pzhkzbPyjY7/gQN16TQ4XXycrZS5y/aPy4yGE6gTIuF2KAlv80K+mWUxo8ngpBM6DkWdBGDcdDxjHigB/5Xtqw==", "path": "volo.abp.aspnetcore.mvc.ui.bootstrap/8.1.4", "hashPath": "volo.abp.aspnetcore.mvc.ui.bootstrap.8.1.4.nupkg.sha512"}, "Volo.Abp.AspNetCore.Mvc.UI.Bundling/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-/1RsgYyVjSf8wMV8dHqkVp2EG7A3HSVdFYddQno1cp5dDoNcpLHgQy86CU/q5IFmQn9SbgDMYEI/lJA6UW9kgw==", "path": "volo.abp.aspnetcore.mvc.ui.bundling/8.1.4", "hashPath": "volo.abp.aspnetcore.mvc.ui.bundling.8.1.4.nupkg.sha512"}, "Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-I+ft81UfNlx3W19hHi9Rw2trZI3I0ri2foR47hpMziemGKt0raG4CIIM7aQsBE6uZ+Eh5qmshj8a3+15hmh1mg==", "path": "volo.abp.aspnetcore.mvc.ui.bundling.abstractions/8.1.4", "hashPath": "volo.abp.aspnetcore.mvc.ui.bundling.abstractions.8.1.4.nupkg.sha512"}, "Volo.Abp.AspNetCore.Mvc.UI.Packages/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-+XE8qrak67sA5XNPW10o13McoawYh+jTFmm+XHiwQOVSUWHzBFRruBbEX1xKfckRb838VomVll70Mt6WRp22UQ==", "path": "volo.abp.aspnetcore.mvc.ui.packages/8.1.4", "hashPath": "volo.abp.aspnetcore.mvc.ui.packages.8.1.4.nupkg.sha512"}, "Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-zHXWxJbfEWoZoyIPzsQsiRJf0qqxtOQdUb6LdRHOYja5U7+2kvCuMXWWbztT/q7ARk6AMsiauffrrZC8lAXBUg==", "path": "volo.abp.aspnetcore.mvc.ui.theme.shared/8.1.4", "hashPath": "volo.abp.aspnetcore.mvc.ui.theme.shared.8.1.4.nupkg.sha512"}, "Volo.Abp.AspNetCore.Mvc.UI.Widgets/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-MFaEKuRDNJjgQOox6Bril5zKg2ew/z1M84oHrrrvA7R+zbhFOmGtVoBP0KwMI2BPS0iVyk8U+PX+sJyh8oFZIQ==", "path": "volo.abp.aspnetcore.mvc.ui.widgets/8.1.4", "hashPath": "volo.abp.aspnetcore.mvc.ui.widgets.8.1.4.nupkg.sha512"}, "Volo.Abp.Auditing/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-D+gHxVii18t2yXQfoSCFWvPIV+LcYXGP21O5kOShybr+7Ler8J6CXP8MDHuoBs7Tyzf0C9jPwIYXakkLVvit1Q==", "path": "volo.abp.auditing/8.1.4", "hashPath": "volo.abp.auditing.8.1.4.nupkg.sha512"}, "Volo.Abp.Auditing.Contracts/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-bL2dXDgGK4aQRWkaMVBgkpyLbfdN+8plofW7pqfMZag+fgIwrnBD0jBZI2aMU9RhsvkbYf0pPArp2PpkxxkQeQ==", "path": "volo.abp.auditing.contracts/8.1.4", "hashPath": "volo.abp.auditing.contracts.8.1.4.nupkg.sha512"}, "Volo.Abp.Authorization/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-WIjMvIYwfWdzc7gjKCRNDOoPKZd9aoZjOj2M1W5ys2E3demqIMRg+5h32QDYsS/UIQAUtL+1Gy1CmYDlE9GCCg==", "path": "volo.abp.authorization/8.1.4", "hashPath": "volo.abp.authorization.8.1.4.nupkg.sha512"}, "Volo.Abp.Authorization.Abstractions/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-zwhJm6ZZx7tU6145e98qUTSSNYYMefn2gfib26CKdXbCtr7r8GjbjUVrsn+nRhJ1kU8Va76/B7NvKk+3SPyTaA==", "path": "volo.abp.authorization.abstractions/8.1.4", "hashPath": "volo.abp.authorization.abstractions.8.1.4.nupkg.sha512"}, "Volo.Abp.AutoMapper/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-wl58RP3TyVG7e9u7NZzMRKu5XQH33z2TwIh8Cmn6SCSFQNYrMepWIdx+p40pYHSMIWUfNnjftL8AOprZo/3DTg==", "path": "volo.abp.automapper/8.1.4", "hashPath": "volo.abp.automapper.8.1.4.nupkg.sha512"}, "Volo.Abp.BackgroundJobs/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-cMEjc8aQan9jaq/hvUBVZg8i2KV+VyueqglhX+j0m7FRb4FRPiZ8hy2w4DhoXwpfn2be3zOk/VUYHqyCh2xbsQ==", "path": "volo.abp.backgroundjobs/8.1.4", "hashPath": "volo.abp.backgroundjobs.8.1.4.nupkg.sha512"}, "Volo.Abp.BackgroundJobs.Abstractions/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-sJeQziE0vjv5pM08kaqImuRrKROGmIUHkJ43+gPeh0gNvZxoG4Nlf08vkoT/kXasoaptZZrw101feQgwUuUUDg==", "path": "volo.abp.backgroundjobs.abstractions/8.1.4", "hashPath": "volo.abp.backgroundjobs.abstractions.8.1.4.nupkg.sha512"}, "Volo.Abp.BackgroundWorkers/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-uoRpquLWtqf6HpKI4CJ49O6LWstvlqJNYU1pUNB3bsUBaW59imJyIy1t4GjG0lDfrBMKysejqwHlTakSE/o0Bg==", "path": "volo.abp.backgroundworkers/8.1.4", "hashPath": "volo.abp.backgroundworkers.8.1.4.nupkg.sha512"}, "Volo.Abp.BlobStoring/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-X5iKQqfAhtdZbDe7DUWqyR4auOWisJ3tjL+CiHgro6+7IiWDMlSmBMTuPaQpuCXG68Q6Afifu5h5DFnlOwOxMg==", "path": "volo.abp.blobstoring/8.1.4", "hashPath": "volo.abp.blobstoring.8.1.4.nupkg.sha512"}, "Volo.Abp.Caching/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-HHlzPtw2TqJfQyxEMqaiSgTovtBhcW+9MeboUuXTDWNz4YgczTRGHi/VX+/JIKoP2IQJnW+SAQRfzAu/8VtyJA==", "path": "volo.abp.caching/8.1.4", "hashPath": "volo.abp.caching.8.1.4.nupkg.sha512"}, "Volo.Abp.Core/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-Shswqpv1ngZ/OyJEXEXVZWmNcaLoHTDELcngeDnUovMzrFn6QqNkZzV0+liKk4jt5LSDLuQJY31H72HxzLA6Pw==", "path": "volo.abp.core/8.1.4", "hashPath": "volo.abp.core.8.1.4.nupkg.sha512"}, "Volo.Abp.Data/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-v6j4JaNSq70hvxe1T1+MC5e4YnsAeanjlxfiZLDo4NI1V0KINaS9hJuZunC1K3gLEXNAoa7pmQDPcdNOskK1Qg==", "path": "volo.abp.data/8.1.4", "hashPath": "volo.abp.data.8.1.4.nupkg.sha512"}, "Volo.Abp.Ddd.Application/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-ZMxZUWZeSbclV7AWpZcqh/cv6AnpQrT8UKzI6jbYtQXKtNqUKrp7SvF7/tPM9b/Ei0N1mHC5RqqQCRfUrp2Anw==", "path": "volo.abp.ddd.application/8.1.4", "hashPath": "volo.abp.ddd.application.8.1.4.nupkg.sha512"}, "Volo.Abp.Ddd.Application.Contracts/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-i1RKDysHYGqjwN2rhih+Ugzc8wB5zNHMfO6ws6emVf4qfI0okSoIITJmOissEmzOyrUIkLWXcwdwZ0cQG6VrXw==", "path": "volo.abp.ddd.application.contracts/8.1.4", "hashPath": "volo.abp.ddd.application.contracts.8.1.4.nupkg.sha512"}, "Volo.Abp.Ddd.Domain/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-XgC3RaZIVumyKBDlFmHPSab6Rl+H87dDz4ltK8dT6THc8YVzZWidb0xfrSRVesOK/TZAXahvupzQc+5f+rFN3w==", "path": "volo.abp.ddd.domain/8.1.4", "hashPath": "volo.abp.ddd.domain.8.1.4.nupkg.sha512"}, "Volo.Abp.Ddd.Domain.Shared/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-n+hmsXtv84JMQow5eYRE9fVLCZq1bolvy/XPod4QeurSx9GiJSm8N05SPwaLDg5NFhyNMEGwNuUckXlNbTNKOw==", "path": "volo.abp.ddd.domain.shared/8.1.4", "hashPath": "volo.abp.ddd.domain.shared.8.1.4.nupkg.sha512"}, "Volo.Abp.DistributedLocking.Abstractions/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-lLU64EBd8vRCZ9fDC3uE2mnVQxvoqidXzMRqfkgxmIuKAy6euNQF10hbfPqkAMT6BTtjyol1dldj4IDkOwyLZQ==", "path": "volo.abp.distributedlocking.abstractions/8.1.4", "hashPath": "volo.abp.distributedlocking.abstractions.8.1.4.nupkg.sha512"}, "Volo.Abp.EventBus/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-/tdUlQaCJWZ/9+dG0AWdwyJ0EidR3FOAow/Aduw2+Zrxj034m42A+/F8OcMx/QvqIkc8TQw+El1npUaCEw69Jg==", "path": "volo.abp.eventbus/8.1.4", "hashPath": "volo.abp.eventbus.8.1.4.nupkg.sha512"}, "Volo.Abp.EventBus.Abstractions/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-U6Ttfxi5GqvsL6HR9kbrCMABg3UzT3FaUETjsUYoz+wv+4WdbdpY3LbXD90UBnDXEaI9toN6ScGE4vEXdQDGXw==", "path": "volo.abp.eventbus.abstractions/8.1.4", "hashPath": "volo.abp.eventbus.abstractions.8.1.4.nupkg.sha512"}, "Volo.Abp.ExceptionHandling/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-0jflyVlbiIzSzA2j2DJwGKQ7imbwnZ3A8DCB/IS0gA7Wwl35COLOlEV9b+lxHOdKIswgwDXDP3xrMGyYRkOCAQ==", "path": "volo.abp.exceptionhandling/8.1.4", "hashPath": "volo.abp.exceptionhandling.8.1.4.nupkg.sha512"}, "Volo.Abp.Features/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-FVR3ACMbV9I/JzRfsCYYGQQ2UrWbpK18weluBcMFOVWTu0oENJwX7JMj49oeZJnwDfGqNvHWZrkF+nLE7T5DkQ==", "path": "volo.abp.features/8.1.4", "hashPath": "volo.abp.features.8.1.4.nupkg.sha512"}, "Volo.Abp.GlobalFeatures/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-pxJUtREEHf/sl+AefhmcQklZ8jQ7+hCVwFJPVSV9LcbpTwAU7SnDYT4ktQClWgGASCJfS3yBEDLXUM/ALVa8Nw==", "path": "volo.abp.globalfeatures/8.1.4", "hashPath": "volo.abp.globalfeatures.8.1.4.nupkg.sha512"}, "Volo.Abp.Guids/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-v79TQ7kt84h8F9wWi/RQbJUl03Ka1aU6yozpN/D+xC8ATzn06UOennDQw48aESWtMbPnlC99Yb+11tfY8QJwcQ==", "path": "volo.abp.guids/8.1.4", "hashPath": "volo.abp.guids.8.1.4.nupkg.sha512"}, "Volo.Abp.Http/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-O3oFJuMceXoZMrb7R6z2gnsxJ4MTf/dlU5vXXWIToxdmh2ncsdUKNFYG45d+N9mSQskB4xGRGLSyTV0scUhyIg==", "path": "volo.abp.http/8.1.4", "hashPath": "volo.abp.http.8.1.4.nupkg.sha512"}, "Volo.Abp.Http.Abstractions/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-qlb0lyPuPXWpTQ0XDyXFZrUvFu7odcHAMcrBIHW7HoppWeAxCcF9YwX88lHvN06StWQmMmlU1XNcX2p1VFWVfw==", "path": "volo.abp.http.abstractions/8.1.4", "hashPath": "volo.abp.http.abstractions.8.1.4.nupkg.sha512"}, "Volo.Abp.Identity.Domain/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-dgPSfasv22KiznCUivBcfPkY6QYvMRhIt0x1UK17q1C6dunp53XLxP8GdyME1az080Cvv8Tht0eLxiEywHLhLw==", "path": "volo.abp.identity.domain/8.1.4", "hashPath": "volo.abp.identity.domain.8.1.4.nupkg.sha512"}, "Volo.Abp.Identity.Domain.Shared/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-PtAnufkyHM0RWEzWel2IOZ+oMQ1YB6qW99XXhVREOr3I9P5eSmgVQt2R35URg5WDZ4ixci13zyG5S7wHdCHeug==", "path": "volo.abp.identity.domain.shared/8.1.4", "hashPath": "volo.abp.identity.domain.shared.8.1.4.nupkg.sha512"}, "Volo.Abp.Json/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-YNM6P+kPVD5pymRiAfPZTw1PCK1/vCDwHDNXYoJIEv9z0r3Hvjx89Y8o0CyaMn8dlIHGporl4laFcGlhvk4m/w==", "path": "volo.abp.json/8.1.4", "hashPath": "volo.abp.json.8.1.4.nupkg.sha512"}, "Volo.Abp.Json.Abstractions/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-CnJZlden/3DcWHZG4QJM4CnlAH6WGCaAkQYIYabZRcYtPfgGShgVq48zb/YzJHB2hWe1/q14zOT+i1PreTgTxg==", "path": "volo.abp.json.abstractions/8.1.4", "hashPath": "volo.abp.json.abstractions.8.1.4.nupkg.sha512"}, "Volo.Abp.Json.SystemTextJson/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-c5vjP86Yw7e5Uvl54n0AChKj5McWuEVfJxJQJRCDvv70lDZALeMMhMx2E0Lg76xDRPoIPCjaC5o0Sbbw152skg==", "path": "volo.abp.json.systemtextjson/8.1.4", "hashPath": "volo.abp.json.systemtextjson.8.1.4.nupkg.sha512"}, "Volo.Abp.Localization/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-Ht2BCTFsiaFgJ8/fR1xXbpppUXeRYFyYFSkOD4/AkLpCFRotFvlxdFDJKyGzUHCqH5NJJ3WM3umAdWf/Mh+S/Q==", "path": "volo.abp.localization/8.1.4", "hashPath": "volo.abp.localization.8.1.4.nupkg.sha512"}, "Volo.Abp.Localization.Abstractions/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-JBsD9bIPodbuWKPWMCz1kSPZwyvSULMw9xzeXRi0yytOcniDTISs48PwCZL7zhurcxAcztNdDfvuhwOEETuf5g==", "path": "volo.abp.localization.abstractions/8.1.4", "hashPath": "volo.abp.localization.abstractions.8.1.4.nupkg.sha512"}, "Volo.Abp.Minify/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-/+aY<PERSON>KShAITMKCrv59WuzhcTPeOx4abpSokRycnwaH8jMT/axxfXqwMCMedEXCCsiCQziqltGSWeqmXPGFOEtQ==", "path": "volo.abp.minify/8.1.4", "hashPath": "volo.abp.minify.8.1.4.nupkg.sha512"}, "Volo.Abp.MultiTenancy/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-EemIM2l2moTDFoEV/AJSaHFqWTE2LiXmPlupznky5nPChsJpdTuhljbmo7I/0wRR35xtuTKK79mnNBAk69Ux1w==", "path": "volo.abp.multitenancy/8.1.4", "hashPath": "volo.abp.multitenancy.8.1.4.nupkg.sha512"}, "Volo.Abp.MultiTenancy.Abstractions/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-TalCX6aBVbtne7rAc4MssuoyVSHgZL5fIeS55ZmnwVfm1XXHbeps2RPiKflyP7WfFnUFzRdGu0V+or8H6Oeydg==", "path": "volo.abp.multitenancy.abstractions/8.1.4", "hashPath": "volo.abp.multitenancy.abstractions.8.1.4.nupkg.sha512"}, "Volo.Abp.ObjectExtending/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-cnYkPQBi3WQgP+881wwJH0sMtfpBYPkLlt/sjZmgngS/HuQgUINWiTznY+8UJDxw5BOmuxSWEsIRfoOYHkg78w==", "path": "volo.abp.objectextending/8.1.4", "hashPath": "volo.abp.objectextending.8.1.4.nupkg.sha512"}, "Volo.Abp.ObjectMapping/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-eXgVvXGv0rli7e+s73dYBZd1dQZ2J1x649V3fkR1HAx8TIwxCR++9FszJmq3ZchiSqQ7m/7d3+ZyloStU4RZng==", "path": "volo.abp.objectmapping/8.1.4", "hashPath": "volo.abp.objectmapping.8.1.4.nupkg.sha512"}, "Volo.Abp.Security/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-/FVWhbDjnHWvpquMiDpVSnnDFqxNXWuAtxuS8Iq177oMN0UQhUIERruepqXjk8X3i/EYm0Nsj6rpgcTqiipS2Q==", "path": "volo.abp.security/8.1.4", "hashPath": "volo.abp.security.8.1.4.nupkg.sha512"}, "Volo.Abp.Serialization/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-DsxE6T54Zuudoc5N0TFTgrsrg07UffX48WqP+dPHEJkY3KaD9b0zxeFwmCIJMupaqi+IQyJ/qr+uiv+eGzKMrQ==", "path": "volo.abp.serialization/8.1.4", "hashPath": "volo.abp.serialization.8.1.4.nupkg.sha512"}, "Volo.Abp.Settings/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-LhibNHGZpPKiLMhGSeDp9E2RGuWrw3GxeglF6Jws83jB4hnWMzyO+4xM7ZGQQc54ANCA1of+ARlZUNQWK39grw==", "path": "volo.abp.settings/8.1.4", "hashPath": "volo.abp.settings.8.1.4.nupkg.sha512"}, "Volo.Abp.Specifications/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-WehbhDYwisIK4dqL8HEqcuyjUfXdj1Ps/5FJa6CLdO77tZXKxaw9+Um7jrECrSNOcL0r70bWiSudW0TH3C8ySQ==", "path": "volo.abp.specifications/8.1.4", "hashPath": "volo.abp.specifications.8.1.4.nupkg.sha512"}, "Volo.Abp.Threading/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-aOxSG+kpyE0cpPO1av1sFVNbVjvOHpgEENPP55jAvsKWuw1NZB6j6vS4U5+fkGrjZFFe7ILjJjA7yLLcwp15kg==", "path": "volo.abp.threading/8.1.4", "hashPath": "volo.abp.threading.8.1.4.nupkg.sha512"}, "Volo.Abp.Timing/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-nqX7H/MKfMd1fcs5mj3LGmX12A/DXyj8qXVBw7tRcu9b0Px/Bh5i3lqa9YiAb7bES1ezTLX011D46aHySrnmXw==", "path": "volo.abp.timing/8.1.4", "hashPath": "volo.abp.timing.8.1.4.nupkg.sha512"}, "Volo.Abp.UI/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-7/2aiKPpkm3aBWcTke/WeDoKG6v5bNdN6Cvf7XvHbXHrA4Gaz4xJ11dR1rTtb8Nfu+DZRrsm4z4m9fzkLXZv7g==", "path": "volo.abp.ui/8.1.4", "hashPath": "volo.abp.ui.8.1.4.nupkg.sha512"}, "Volo.Abp.UI.Navigation/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-0OXhLo83Ee1CZgYYxWDs1/bBdlIb65+BE8Hkka0ztfKLEAPZ4IHJEW4C74RUO9FwuwDXI2G7K17RFnHW3yfcFA==", "path": "volo.abp.ui.navigation/8.1.4", "hashPath": "volo.abp.ui.navigation.8.1.4.nupkg.sha512"}, "Volo.Abp.Uow/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-9UjVi0qRK2X6Fw3W5Dfjz+dZ1wycey+C0jIPM4PSNKXC3sDLIh+4erQqnP2vtcKHOAUSI7LYdbu5pTTKehotuA==", "path": "volo.abp.uow/8.1.4", "hashPath": "volo.abp.uow.8.1.4.nupkg.sha512"}, "Volo.Abp.Users.Abstractions/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-iBxpFFtNoSaB0XUN39ijn93W/IbMFBGLKwlrysLQc/WspdLj35+dEszm4YAMLUlIF+qdFggc849S1FN50GI1jg==", "path": "volo.abp.users.abstractions/8.1.4", "hashPath": "volo.abp.users.abstractions.8.1.4.nupkg.sha512"}, "Volo.Abp.Users.Domain/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-6s7ukjCYkERMsTGh4sIwmrpYWfS4BQl7RPkIN6pDFFk6KYrGwxHN2aoSYsYErHT95qi3lOXni/bb2NA2J+0BOA==", "path": "volo.abp.users.domain/8.1.4", "hashPath": "volo.abp.users.domain.8.1.4.nupkg.sha512"}, "Volo.Abp.Users.Domain.Shared/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-dZs1e8tl8I3DcAsULs40YIeH8NOvsuUENovrhIGJkpTBrbB3sbBglFR0eFN/do78hdeD8aolq6ieicaA/9uRZw==", "path": "volo.abp.users.domain.shared/8.1.4", "hashPath": "volo.abp.users.domain.shared.8.1.4.nupkg.sha512"}, "Volo.Abp.Validation/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-R<PERSON>KUhTUsLkdzDv3g2Oaywm1VnmdR0KqL8MYzY5p80/NNk6SVw9UVJcfxFGcbfPD26xqvDbn7tu0O2csDa1KVQw==", "path": "volo.abp.validation/8.1.4", "hashPath": "volo.abp.validation.8.1.4.nupkg.sha512"}, "Volo.Abp.Validation.Abstractions/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-X9vjFMGbHVlZ6s9lcJAqnwtHoc0DsdHj36j0xj/d7Ra45dIPEesg66/t0NN6O0T8g0MDlhwNzShFmh9X5yeE+A==", "path": "volo.abp.validation.abstractions/8.1.4", "hashPath": "volo.abp.validation.abstractions.8.1.4.nupkg.sha512"}, "Volo.Abp.VirtualFileSystem/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-KvbiYsrAZYRS2Vm+IsNOvDm0EUoY3RE5fx7PgMOilD11DrkTVziJT2MhQoukWrOAXW7qoN3HAET1YHYyqs9tNw==", "path": "volo.abp.virtualfilesystem/8.1.4", "hashPath": "volo.abp.virtualfilesystem.8.1.4.nupkg.sha512"}, "EdTech.Study.Application.Contracts/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "EdTech.Study.Domain/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "EdTech.Study.Domain.Shared/0.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Antiforgery/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.BearerToken/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Cookies/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.OAuth/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authorization.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authorization.Policy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Authorization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Endpoints/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Forms/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Server/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Web/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Connections.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.CookiePolicy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Cors/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Cryptography.Internal.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Cryptography.KeyDerivation.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection.Extensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics.HealthChecks/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HostFiltering/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Html.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Connections.Common/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Connections/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Extensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Features/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Results/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HttpLogging/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HttpOverrides/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HttpsPolicy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Identity/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Localization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Localization.Routing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Metadata.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.ApiExplorer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Cors/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.DataAnnotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Formatters.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Formatters.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Localization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Razor/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.RazorPages/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.TagHelpers/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.ViewFeatures/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.OutputCaching/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.RateLimiting/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Razor/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Razor.Runtime/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.RequestDecompression/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCaching/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCompression/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Rewrite/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Routing.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Routing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.HttpSys/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.IIS/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.IISIntegration/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Session/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Common/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Protocols.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.StaticFiles/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.WebSockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.WebUtilities/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.CSharp.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Caching.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Caching.Memory.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Binder.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.CommandLine.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.EnvironmentVariables.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.FileExtensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Ini/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Json.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.KeyPerFile/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.UserSecrets.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.DependencyInjection.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.DependencyInjection.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.HealthChecks/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Features/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Composite.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Embedded.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Physical.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileSystemGlobbing.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Hosting.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Hosting/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Http/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Identity.Core.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Identity.Stores/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Localization.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Localization.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Configuration/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Console/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Debug/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.EventLog/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.EventSource/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.TraceSource/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.ObjectPool/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Options.ConfigurationExtensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Options.DataAnnotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Options.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.WebEncoders/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.JSInterop/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Net.Http.Headers/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.VisualBasic.Core/13.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.VisualBasic/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Win32.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Win32.Registry/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "mscorlib/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "netstandard/2.1.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.AppContext/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Buffers/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Concurrent/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Immutable.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.NonGeneric/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Specialized/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.Annotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.DataAnnotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.EventBasedAsync/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.TypeConverter/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Configuration/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Console/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data.Common/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data.DataSetExtensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Contracts/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Debug.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.DiagnosticSource.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.EventLog/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.FileVersionInfo/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Process/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.StackTrace/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.TextWriterTraceListener/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Tools/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.TraceSource/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Tracing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Drawing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Drawing.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Dynamic.Runtime/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Formats.Asn1/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Formats.Tar/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Calendars/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Extensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.Brotli/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.FileSystem/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.ZipFile/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.AccessControl/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.DriveInfo/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Watcher/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.IsolatedStorage/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.MemoryMappedFiles/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Pipelines/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Pipes.AccessControl/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Pipes/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.UnmanagedMemoryStream/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Expressions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Parallel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Queryable.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Memory.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Http/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Http.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.HttpListener/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Mail/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.NameResolution/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.NetworkInformation/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Ping/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Quic/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Requests/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Security/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.ServicePoint/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Sockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebClient/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebHeaderCollection/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebProxy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebSockets.Client/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebSockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Numerics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Numerics.Vectors/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ObjectModel.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.DispatchProxy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.ILGeneration.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.Lightweight.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Metadata.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.TypeExtensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.Reader/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.ResourceManager.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.Writer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.CompilerServices.Unsafe.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.CompilerServices.VisualC/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Handles/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices.JavaScript/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices.RuntimeInformation/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Intrinsics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Loader.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Numerics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Formatters/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.AccessControl/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Claims/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Algorithms/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Cng/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Csp/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Encoding/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.OpenSsl/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.X509Certificates/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Principal/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Principal.Windows/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.SecureString/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceModel.Web/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceProcess/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.CodePages.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.Extensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encodings.Web.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Json.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.RegularExpressions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Channels/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Overlapped/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.RateLimiting/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Dataflow/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Parallel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Thread/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.ThreadPool/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Timer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Transactions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Transactions.Local/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ValueTuple/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Web/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Web.HttpUtility/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Windows/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.Linq/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.ReaderWriter/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.Serialization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XDocument/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XmlDocument/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XmlSerializer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XPath/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XPath.XDocument/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "WindowsBase/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}}}