import React, { useContext, useState } from 'react';
import { Button, Space, Tooltip } from 'antd';
import {
  FundProjectionScreenOutlined,
  SettingOutlined,
  FormOutlined,
} from '@ant-design/icons';
import '../styles/SaveControls.css';
import practiceLocalization from '../../localization';
import { PracticeEngineContext } from '../../../../interfaces/quizs/questionBase';
import { FullscreenButton } from '../../../common/Fullscreen';
interface SaveControlsProps {
  position?: 'top' | 'bottom';
  disabled?: boolean;
  saving?: boolean;
  onSave?: () => void;
  onStyleChange?: () => void;
  onSettingsChange?: () => void;
}

const SaveControls: React.FC<SaveControlsProps> = ({
  position = 'top',
  disabled = false,
  saving = false,
  onSave,
  onStyleChange,
  onSettingsChange,
}) => {
  const [activeTab, setActiveTab] = useState<string | null>(null);
  const { isFullscreen, handleToggleFullscreen } = useContext(
    PracticeEngineContext
  );
  const handleSave = () => {
    if (onSave) onSave();
  };

  return (
    <div className={`save-controls save-controls-${position}`}>
      <div className="save-controls-tabs">
        <Space size="middle">
          {onStyleChange && (
            <Tooltip title="Thiết lập giao diện">
              <Button
                type={activeTab === 'style' ? 'primary' : 'default'}
                icon={<FormOutlined />}
                onClick={() => {
                  setActiveTab(activeTab === 'style' ? null : 'style');
                  if (onStyleChange) onStyleChange();
                }}
              >
                Style
              </Button>
            </Tooltip>
          )}

          {onSettingsChange && (
            <Tooltip title="Cài đặt">
              <Button
                type={activeTab === 'settings' ? 'primary' : 'default'}
                icon={<SettingOutlined />}
                onClick={() => {
                  setActiveTab(activeTab === 'settings' ? null : 'settings');
                  if (onSettingsChange) onSettingsChange();
                }}
              >
                Settings
              </Button>
            </Tooltip>
          )}
        </Space>
      </div>

      <div className="save-controls-actions">
        <Space size="middle">
          {handleToggleFullscreen && (
            <Tooltip
              title={isFullscreen ? 'Thoát toàn màn hình' : 'Toàn màn hình'}
            >
              <FullscreenButton
                type="default"
                isFullscreen={isFullscreen}
                toggleFullscreen={handleToggleFullscreen}
              />
            </Tooltip>
          )}
          {onSave && (
            <Tooltip title="Lưu và trình chiếu">
              <Button
                type="primary"
                icon={<FundProjectionScreenOutlined />}
                onClick={handleSave}
                disabled={disabled}
                loading={saving}
              >
                {practiceLocalization['View demo']}
              </Button>
            </Tooltip>
          )}
        </Space>
        {saving && <span className="save-status">Đang lưu...</span>}
      </div>
    </div>
  );
};

export default SaveControls;
