﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Reflection;

namespace EdTech.Study.Permissions
{
    public class ExamPermissions
    {
        public const string GroupName = "Exams";
        public static class Exams {
            public const string Default = ".Exams";
        }
        public static string[] GetAll()
        {
            return ReflectionHelper.GetPublicConstantsRecursively(typeof(ExamPermissions));
        }
    }
}
