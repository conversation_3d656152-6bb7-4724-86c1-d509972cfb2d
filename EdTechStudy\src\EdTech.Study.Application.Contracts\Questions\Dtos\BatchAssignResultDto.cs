using System;
using System.Collections.Generic;

namespace EdTech.Study.Questions.Dtos
{
    public class BatchAssignResultDto
    {
        public Guid UserId { get; set; }
        public int AssignedCount { get; set; }
        public List<Guid> AssignedQuestionIds { get; set; }
        public DateTime? DueDate { get; set; }

        public BatchAssignResultDto()
        {
            AssignedQuestionIds = new List<Guid>();
        }
    }
}