using EdTech.Study.Lessons;
using EdTech.Study.Lessons.Dtos;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OData.Query;
using Microsoft.AspNetCore.OData.Routing.Controllers;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EdTech.Study.Controllers.Lessons
{
    [Route("api/odata/lessons")]
    [Authorize(Roles = "admin")]
    public class LessonsOdataController : ODataController
    {
        private readonly IDefaultLessonAppService _lessonService;

        public LessonsOdataController(IDefaultLessonAppService lessonService)
        {
            _lessonService = lessonService;
        }

        [HttpGet]
        public async Task<List<LessonDto>> Get()
        {
            return await  _lessonService.GetAllLessons();
        }
        [HttpPost]
        public async Task<LessonDto> Create(CreateOrUpdateDefaultLessonDto input)
        {
            return await _lessonService.Create(input);
        }
    }
}
