﻿using EdTech.Study.Enum;
using EdTech.Study.Exams;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EdTech.Study.Questions.Dtos
{
    public class CreateUpdateQuestionDto
    {
        #region Thông tin cơ bản

        /// <summary>
        /// Id của câu hỏi trong 1 phần thi.
        /// </summary>
        public Guid? Id { get; set; }


        /// <summary>
        /// Question Client Id
        /// </summary>
        public Guid ClientId { get; set; }

        /// <summary>
        /// Id của câu hỏi trong ngân hàng câu hỏi.
        /// </summary>
        public Guid? QuestionId { get; set; }

        /// <summary>
        /// Id Câu hỏi template
        /// Sử dụng để backup khi bật tắt đồng bộ
        /// </summary>
        public Guid? LastSyncQuestionId { get; set; }

        /// <summary>
        /// Sắp xếp
        /// </summary>
        public int Order { get; set; }

        /// <summary>
        /// Điểm số cho câu hỏi này.
        /// </summary>
        public float? Score { get; set; }

        /// <summary>
        /// Đồng bộ hóa với câu hỏi mẫu hay không.
        /// Nếu bằng true thì sẽ lấy câu hỏi đã có trong ngân hàng
        /// Bằng cách liên kết id: QuestionId = exitInDBQuestionId
        /// Nếu bằng False thì sẽ tạo mới câu hỏi, với thông là Question
        /// </summary>
        public bool SyncQuestion { get; set; }

        public string CreateIdempotentKey(string rootKey)
        {
            return $"{rootKey}_question-{Order}";
        }

        #endregion
        #region Nội dung câu hỏi

        [Required]
        public string Title { get; set; }

        [Required]
        public string Content { get; set; }

        [EnumDataType(typeof(ContentFormatType))]
        public ContentFormatType ContentFormat { get; set; }

        public QuestionType QuestionType { get; set; }

        public int Difficulty { get; set; }

        public string? Comment { get; set; }

        public Guid? SubjectId { get; set; }

        public Guid? GradeId { get; set; }

        public bool ShuffleOptions { get; set; }

        public string? Explanation { get; set; }

        public ExamSourceType SourceType { get; set; }

        public string? Topics { get; set; }

        public string? Tags { get; set; }
        public ExamStatus? Status { get; set; }

        /// <summary>
        /// ID người dùng được giao xử lý câu hỏi
        /// </summary>
        public Guid? AssignedUserId { get; set; }

        /// <summary>
        /// Hạn xử lý
        /// </summary>
        public DateTime? DueDate { get; set; }

        public DateTime? AssignedDate { get; set; }

        public List<CreateQuestionOptionDto>? Options { get; set; }

        public List<CreateUpdateMatchingItemDto>? MatchingItems { get; set; }

        public List<CreateUpdateMatchingAnswerDto>? MatchingAnswers { get; set; }

        public List<CreateUpdateFillInBlankAnswer>? FillInBlankAnswers { get; set; }
        #endregion
    }
}