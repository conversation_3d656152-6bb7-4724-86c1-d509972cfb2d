﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EdTech.Study.Migrations
{
    /// <inheritdoc />
    public partial class GroupQuestion : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "GroupQuestionId",
                table: "StudyQuestions",
                type: "char(36)",
                nullable: true,
                collation: "ascii_general_ci");

            migrationBuilder.CreateTable(
                name: "StudyGroupQuestions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "ascii_general_ci"),
                    Content = table.Column<string>(type: "longtext", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ContentFormat = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Instructions = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreationTime = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    CreatorId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "ascii_general_ci"),
                    LastModificationTime = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "char(36)", nullable: true, collation: "ascii_general_ci")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StudyGroupQuestions", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_StudyQuestions_GroupQuestionId",
                table: "StudyQuestions",
                column: "GroupQuestionId");

            migrationBuilder.AddForeignKey(
                name: "FK_StudyQuestions_StudyGroupQuestions_GroupQuestionId",
                table: "StudyQuestions",
                column: "GroupQuestionId",
                principalTable: "StudyGroupQuestions",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_StudyQuestions_StudyGroupQuestions_GroupQuestionId",
                table: "StudyQuestions");

            migrationBuilder.DropTable(
                name: "StudyGroupQuestions");

            migrationBuilder.DropIndex(
                name: "IX_StudyQuestions_GroupQuestionId",
                table: "StudyQuestions");

            migrationBuilder.DropColumn(
                name: "GroupQuestionId",
                table: "StudyQuestions");
        }
    }
}
