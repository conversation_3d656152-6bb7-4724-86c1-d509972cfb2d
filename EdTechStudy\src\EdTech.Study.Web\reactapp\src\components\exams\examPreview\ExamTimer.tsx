import React, {
  useEffect,
  useRef,
  useImperativeHandle,
  forwardRef,
} from 'react';
import { Statistic } from 'antd';
import { ClockCircleOutlined } from '@ant-design/icons';

// Format time from seconds to MM:SS
const formatTimeRemaining = (seconds: number | null): string => {
  if (seconds === null) return '00:00';
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins.toString().padStart(2, '0')}:${secs
    .toString()
    .padStart(2, '0')}`;
};

export interface ExamTimerRef {
  getTimeRemaining: () => number | null;
}

interface ExamTimerProps {
  initialTime: number | null;
  timerActive: boolean;
  onTimeUp?: () => void;
}

const ExamTimer = forwardRef<ExamTimerRef, ExamTimerProps>(
  ({ initialTime, timerActive, onTimeUp }, ref) => {
    const timeRemainingRef = useRef<number | null>(initialTime);
    const [displayTime, setDisplayTime] = React.useState<number | null>(
      initialTime
    );
    const timerRef = useRef<ReturnType<typeof setInterval> | null>(null);

    // Expose methods via ref
    useImperativeHandle(ref, () => ({
      getTimeRemaining: () => timeRemainingRef.current,
    }));

    // Handle timer logic
    useEffect(() => {
      if (
        timerActive &&
        timeRemainingRef.current !== null &&
        timeRemainingRef.current > 0
      ) {
        if (timerRef.current) clearInterval(timerRef.current);

        timerRef.current = setInterval(() => {
          timeRemainingRef.current =
            timeRemainingRef.current !== null
              ? timeRemainingRef.current - 1
              : null;

          // Update display time once per second
          setDisplayTime(timeRemainingRef.current);

          if (
            timeRemainingRef.current !== null &&
            timeRemainingRef.current <= 0
          ) {
            if (timerRef.current) clearInterval(timerRef.current);
            onTimeUp?.();
          }
        }, 1000);
      } else if (!timerActive && timerRef.current) {
        clearInterval(timerRef.current);
      }

      return () => {
        if (timerRef.current) clearInterval(timerRef.current);
      };
    }, [timerActive, onTimeUp]);

    // Memoize the UI to prevent unnecessary re-renders
    return React.useMemo(() => <></>, [displayTime]);
  }
);

export default React.memo(ExamTimer);
