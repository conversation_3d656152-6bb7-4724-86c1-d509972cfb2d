import { SVGProps } from 'react';
import { AudioWaveIconBase } from './listIcon/AudioWaveIcon/AudioWaveIcon';

export const HomeOutlineIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={16}
    height={16}
    viewBox="0 0 18 19"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M7.54952 0.531895C8.38743 -0.174695 9.61257 -0.174694 10.4505 0.531896L17.2005 6.224C17.7074 6.65152 18 7.2809 18 7.94406L18 17.2539C18 18.2204 17.2165 19.0039 16.25 19.0039H12.75C11.7835 19.0039 11 18.2204 11 17.2539L11 12.2468C11 12.1088 10.8881 11.9968 10.75 11.9968H7.24999C7.11192 11.9968 6.99999 12.1088 6.99999 12.2468L6.99999 17.2539C6.99999 18.2204 6.2165 19.0039 5.25 19.0039H1.75C0.783502 19.0039 0 18.2204 0 17.2539V7.94406C0 7.2809 0.292551 6.65152 0.799517 6.224L7.54952 0.531895ZM9.48349 1.6786C9.20419 1.44307 8.79581 1.44307 8.5165 1.6786L1.76651 7.37071C1.59752 7.51321 1.5 7.72301 1.5 7.94406L1.5 17.2539C1.5 17.392 1.61193 17.5039 1.75 17.5039H5.25C5.38807 17.5039 5.49999 17.392 5.49999 17.2539L5.49999 12.2468C5.49999 11.2803 6.2835 10.4968 7.24999 10.4968H10.75C11.7165 10.4968 12.5 11.2803 12.5 12.2468L12.5 17.2539C12.5 17.392 12.6119 17.5039 12.75 17.5039H16.25C16.3881 17.5039 16.5 17.392 16.5 17.2539L16.5 7.94406C16.5 7.72301 16.4025 7.51321 16.2335 7.37071L9.48349 1.6786Z" />
  </svg>
);

export const HomeFilledIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={16}
    height={16}
    viewBox="0 0 18 19"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M10.4508 0.533177C9.61281 -0.173819 8.38719 -0.173819 7.54916 0.533177L0.799155 6.22772C0.292405 6.65523 0 7.28447 0 7.94747V17.2526C0 18.2191 0.783502 19.0026 1.75 19.0026H4.75C5.7165 19.0026 6.5 18.2191 6.5 17.2526V13.25C6.5 12.5707 7.04184 12.018 7.71689 12.0004H10.2831C10.9582 12.018 11.5 12.5707 11.5 13.25V17.2526C11.5 18.2191 12.2835 19.0026 13.25 19.0026H16.25C17.2165 19.0026 18 18.2191 18 17.2526V7.94747C18 7.28447 17.7076 6.65523 17.2008 6.22772L10.4508 0.533177Z" />
  </svg>
);

export const FileOutlineIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={16}
    height={16}
    viewBox="0 0 16 20"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M2 0C0.89543 0 0 0.895431 0 2V18C0 19.1046 0.895431 20 2 20H14C15.1046 20 16 19.1046 16 18V7.82777C16 7.29733 15.7893 6.78863 15.4142 6.41355L9.58645 0.585786C9.21137 0.210714 8.70267 0 8.17223 0H2ZM1.5 2C1.5 1.72386 1.72386 1.5 2 1.5H8V6C8 7.10457 8.89543 8 10 8H14.5V18C14.5 18.2761 14.2761 18.5 14 18.5H2C1.72386 18.5 1.5 18.2761 1.5 18V2ZM13.3793 6.5H10C9.72386 6.5 9.5 6.27614 9.5 6V2.62066L13.3793 6.5Z" />
  </svg>
);

export const FileFilledIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={16}
    height={16}
    viewBox="0 0 16 20"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M8 0V6C8 7.10457 8.89543 8 10 8H16V18C16 19.1046 15.1046 20 14 20H2C0.895431 20 0 19.1046 0 18V2C0 0.895431 0.895431 0 2 0H8ZM9.5 0.5V6C9.5 6.27614 9.72386 6.5 10 6.5H15.5L9.5 0.5Z" />
  </svg>
);

export const UserOutlineComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={16}
    height={16}
    viewBox="0 0 16 20"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M13.7542 11.9999C14.9962 11.9999 16.003 13.0068 16.003 14.2488V14.8242C16.003 15.7185 15.6835 16.5833 15.1019 17.2627C13.5326 19.0962 11.1454 20.0011 8 20.0011C4.85414 20.0011 2.46812 19.0959 0.90182 17.2617C0.322059 16.5827 0.00354004 15.7193 0.00354004 14.8265V14.2488C0.00354004 13.0068 1.0104 11.9999 2.25242 11.9999H13.7542ZM13.7542 13.4999H2.25242C1.83882 13.4999 1.50354 13.8352 1.50354 14.2488V14.8265C1.50354 15.3621 1.69465 15.8802 2.04251 16.2876C3.29582 17.7553 5.26169 18.5011 8 18.5011C10.7383 18.5011 12.7059 17.7552 13.9624 16.2873C14.3113 15.8797 14.503 15.3608 14.503 14.8242V14.2488C14.503 13.8352 14.1678 13.4999 13.7542 13.4999ZM8 0.00462341C10.7614 0.00462341 13 2.2432 13 5.00462C13 7.76605 10.7614 10.0046 8 10.0046C5.23857 10.0046 3 7.76605 3 5.00462C3 2.2432 5.23857 0.00462341 8 0.00462341ZM8 1.50462C6.067 1.50462 4.5 3.07163 4.5 5.00462C4.5 6.93762 6.067 8.50462 8 8.50462C9.93299 8.50462 11.5 6.93762 11.5 5.00462C11.5 3.07163 9.93299 1.50462 8 1.50462Z" />
  </svg>
);

export const UserFilledComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={16}
    height={16}
    viewBox="0 0 16 20"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M13.7542 11.9999C14.9962 11.9999 16.003 13.0068 16.003 14.2488V15.1673C16.003 15.7406 15.8238 16.2996 15.4905 16.7661C13.9446 18.9294 11.4203 20.0011 8 20.0011C4.57903 20.0011 2.05607 18.9289 0.513908 16.7646C0.181945 16.2987 0.00354004 15.7408 0.00354004 15.1688V14.2488C0.00354004 13.0068 1.0104 11.9999 2.25242 11.9999H13.7542ZM8 0.00462341C10.7614 0.00462341 13 2.2432 13 5.00462C13 7.76605 10.7614 10.0046 8 10.0046C5.23857 10.0046 3 7.76605 3 5.00462C3 2.2432 5.23857 0.00462341 8 0.00462341Z" />
  </svg>
);

export const GroupOutlineComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 20 18"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M12.754 7C13.7205 7 14.504 7.7835 14.504 8.75V13.499C14.504 15.9848 12.4888 18 10.003 18C7.51712 18 5.50193 15.9848 5.50193 13.499V8.75C5.50193 7.7835 6.28543 7 7.25193 7H12.754ZM12.754 8.5H7.25193C7.11386 8.5 7.00193 8.61193 7.00193 8.75V13.499C7.00193 15.1564 8.34554 16.5 10.003 16.5C11.6604 16.5 13.004 15.1564 13.004 13.499V8.75C13.004 8.61193 12.8921 8.5 12.754 8.5ZM1.75 7L5.13128 6.99906C4.78791 7.41447 4.56424 7.93246 4.51312 8.50019L1.75 8.5C1.61193 8.5 1.5 8.61193 1.5 8.75V11.9988C1.5 13.3802 2.61984 14.5 4.00124 14.5C4.20123 14.5 4.39574 14.4765 4.58216 14.4322C4.66687 14.9361 4.82156 15.4167 5.03487 15.864C4.70577 15.953 4.35899 16 4.00124 16C1.79142 16 0 14.2086 0 11.9988V8.75C0 7.7835 0.783502 7 1.75 7ZM14.8747 6.99906L18.25 7C19.2165 7 20 7.7835 20 8.75V12C20 14.2091 18.2091 16 16 16C15.6436 16 15.298 15.9534 14.9691 15.8659C15.184 15.4177 15.3388 14.9371 15.425 14.4331C15.6092 14.477 15.8019 14.5 16 14.5C17.3807 14.5 18.5 13.3807 18.5 12V8.75C18.5 8.61193 18.3881 8.5 18.25 8.5L15.4928 8.50019C15.4417 7.93246 15.218 7.41447 14.8747 6.99906ZM10 0C11.6569 0 13 1.34315 13 3C13 4.65685 11.6569 6 10 6C8.34315 6 7 4.65685 7 3C7 1.34315 8.34315 0 10 0ZM16.5 1C17.8807 1 19 2.11929 19 3.5C19 4.88071 17.8807 6 16.5 6C15.1193 6 14 4.88071 14 3.5C14 2.11929 15.1193 1 16.5 1ZM3.5 1C4.88071 1 6 2.11929 6 3.5C6 4.88071 4.88071 6 3.5 6C2.11929 6 1 4.88071 1 3.5C1 2.11929 2.11929 1 3.5 1ZM10 1.5C9.17157 1.5 8.5 2.17157 8.5 3C8.5 3.82843 9.17157 4.5 10 4.5C10.8284 4.5 11.5 3.82843 11.5 3C11.5 2.17157 10.8284 1.5 10 1.5ZM16.5 2.5C15.9477 2.5 15.5 2.94772 15.5 3.5C15.5 4.05228 15.9477 4.5 16.5 4.5C17.0523 4.5 17.5 4.05228 17.5 3.5C17.5 2.94772 17.0523 2.5 16.5 2.5ZM3.5 2.5C2.94772 2.5 2.5 2.94772 2.5 3.5C2.5 4.05228 2.94772 4.5 3.5 4.5C4.05228 4.5 4.5 4.05228 4.5 3.5C4.5 2.94772 4.05228 2.5 3.5 2.5Z" />
  </svg>
);

export const GroupFilledComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 20 18"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M12.754 7C13.7205 7 14.504 7.7835 14.504 8.75V13.499C14.504 15.9848 12.4888 18 10.003 18C7.51712 18 5.50193 15.9848 5.50193 13.499V8.75C5.50193 7.7835 6.28543 7 7.25193 7H12.754ZM5.13128 6.99906C4.78183 7.42182 4.55636 7.95083 4.51057 8.5304L4.50193 8.75V13.499C4.50193 14.3456 4.69319 15.1476 5.03487 15.864C4.70577 15.953 4.35899 16 4.00124 16C1.79142 16 0 14.2086 0 11.9988V8.75C0 7.83183 0.70711 7.07881 1.60647 7.0058L1.75 7L5.13128 6.99906ZM14.8747 6.99906L18.25 7C19.2165 7 20 7.7835 20 8.75V12C20 14.2091 18.2091 16 16 16C15.6436 16 15.298 15.9534 14.9691 15.8659C15.2697 15.238 15.4538 14.5452 15.4951 13.8144L15.504 13.499V8.75C15.504 8.08475 15.2678 7.47467 14.8747 6.99906ZM10 0C11.6569 0 13 1.34315 13 3C13 4.65685 11.6569 6 10 6C8.34315 6 7 4.65685 7 3C7 1.34315 8.34315 0 10 0ZM16.5 1C17.8807 1 19 2.11929 19 3.5C19 4.88071 17.8807 6 16.5 6C15.1193 6 14 4.88071 14 3.5C14 2.11929 15.1193 1 16.5 1ZM3.5 1C4.88071 1 6 2.11929 6 3.5C6 4.88071 4.88071 6 3.5 6C2.11929 6 1 4.88071 1 3.5C1 2.11929 2.11929 1 3.5 1Z" />
  </svg>
);

export const DocumentOutlineIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 20"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M2.25 0C1.00736 0 0 1.00736 0 2.25V17.75C0 18.9926 1.00736 20 2.25 20H13.75C14.9926 20 16 18.9926 16 17.75V2.25C16 1.00736 14.9926 0 13.75 0H2.25ZM1.5 2.25C1.5 1.83579 1.83579 1.5 2.25 1.5H13.75C14.1642 1.5 14.5 1.83579 14.5 2.25V17.75C14.5 18.1642 14.1642 18.5 13.75 18.5H2.25C1.83579 18.5 1.5 18.1642 1.5 17.75V2.25ZM3.75 4.5C3.33579 4.5 3 4.83579 3 5.25C3 5.66421 3.33579 6 3.75 6H12.25C12.6642 6 13 5.66421 13 5.25C13 4.83579 12.6642 4.5 12.25 4.5H3.75ZM3 14.25C3 13.8358 3.33579 13.5 3.75 13.5H12.25C12.6642 13.5 13 13.8358 13 14.25C13 14.6642 12.6642 15 12.25 15H3.75C3.33579 15 3 14.6642 3 14.25ZM3.75 9C3.33579 9 3 9.33579 3 9.75C3 10.1642 3.33579 10.5 3.75 10.5H12.25C12.6642 10.5 13 10.1642 13 9.75C13 9.33579 12.6642 9 12.25 9H3.75Z" />
  </svg>
);

export const DocumentFilledIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 20"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M2.25 0C1.00736 0 0 1.00736 0 2.25V17.75C0 18.9926 1.00736 20 2.25 20H13.75C14.9926 20 16 18.9926 16 17.75V2.25C16 1.00736 14.9926 0 13.75 0H2.25ZM3.75 4H12.25C12.6642 4 13 4.33579 13 4.75C13 5.16421 12.6642 5.5 12.25 5.5H3.75C3.33579 5.5 3 5.16421 3 4.75C3 4.33579 3.33579 4 3.75 4ZM3 14.75C3 14.3358 3.33579 14 3.75 14H12.25C12.6642 14 13 14.3358 13 14.75C13 15.1642 12.6642 15.5 12.25 15.5H3.75C3.33579 15.5 3 15.1642 3 14.75ZM3.75 9H12.25C12.6642 9 13 9.33579 13 9.75C13 10.1642 12.6642 10.5 12.25 10.5H3.75C3.33579 10.5 3 10.1642 3 9.75C3 9.33579 3.33579 9 3.75 9Z" />
  </svg>
);

export const BuildingOutlineIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 20"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M2.25 1.5C1.83579 1.5 1.5 1.83579 1.5 2.25V18.5H3.5V15.75C3.5 15.0596 4.05964 14.5 4.75 14.5H11.25C11.9404 14.5 12.5 15.0596 12.5 15.75V18.5H14.5V9.75C14.5 9.33579 14.1642 9 13.75 9H11.75C11.3358 9 11 8.66421 11 8.25V2.25C11 1.83579 10.6642 1.5 10.25 1.5H2.25ZM5 16V18.5H7.25V16H5ZM8.75 16V18.5H11V16H8.75ZM15.25 20H0.75C0.335786 20 0 19.6642 0 19.25V2.25C0 1.00736 1.00736 0 2.25 0H10.25C11.4926 0 12.5 1.00736 12.5 2.25V7.5H13.75C14.9926 7.5 16 8.50736 16 9.75V19.25C16 19.6642 15.6642 20 15.25 20ZM3.5 4.5C3.5 3.94772 3.94772 3.5 4.5 3.5C5.05229 3.5 5.5 3.94772 5.5 4.5C5.5 5.05228 5.05229 5.5 4.5 5.5C3.94772 5.5 3.5 5.05228 3.5 4.5ZM4.5 10.5C3.94772 10.5 3.5 10.9477 3.5 11.5C3.5 12.0523 3.94772 12.5 4.5 12.5C5.05229 12.5 5.5 12.0523 5.5 11.5C5.5 10.9477 5.05229 10.5 4.5 10.5ZM4.5 7C3.94772 7 3.5 7.44771 3.5 8C3.5 8.55229 3.94772 9 4.5 9C5.05229 9 5.5 8.55229 5.5 8C5.5 7.44771 5.05229 7 4.5 7ZM8 3.5C7.44771 3.5 7 3.94772 7 4.5C7 5.05228 7.44771 5.5 8 5.5C8.55229 5.5 9 5.05228 9 4.5C9 3.94772 8.55229 3.5 8 3.5ZM8 10.5C7.44771 10.5 7 10.9477 7 11.5C7 12.0523 7.44771 12.5 8 12.5C8.55229 12.5 9 12.0523 9 11.5C9 10.9477 8.55229 10.5 8 10.5ZM11.5 10.5C10.9477 10.5 10.5 10.9477 10.5 11.5C10.5 12.0523 10.9477 12.5 11.5 12.5C12.0523 12.5 12.5 12.0523 12.5 11.5C12.5 10.9477 12.0523 10.5 11.5 10.5ZM8 7C7.44771 7 7 7.44771 7 8C7 8.55229 7.44771 9 8 9C8.55229 9 9 8.55229 9 8C9 7.44771 8.55229 7 8 7Z" />
  </svg>
);

export const BuildingFilledIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 20"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M0 2.25C0 1.00736 1.00736 0 2.25 0H10.25C11.4926 0 12.5 1.00736 12.5 2.25V7.5H13.75C14.9926 7.5 16 8.50736 16 9.75V19.25C16 19.6642 15.6642 20 15.25 20H12.5V16.25C12.5 15.5596 11.9404 15 11.25 15H4.75C4.05964 15 3.5 15.5596 3.5 16.25V20H0.75C0.335786 20 0 19.6642 0 19.25V2.25ZM11 16.5V20H8.75V16.5H11ZM7.25 16.5V20H5V16.5H7.25ZM3.5 4.5C3.5 5.05228 3.94772 5.5 4.5 5.5C5.05229 5.5 5.5 5.05228 5.5 4.5C5.5 3.94772 5.05229 3.5 4.5 3.5C3.94772 3.5 3.5 3.94772 3.5 4.5ZM4.5 10.5C3.94772 10.5 3.5 10.9477 3.5 11.5C3.5 12.0523 3.94772 12.5 4.5 12.5C5.05229 12.5 5.5 12.0523 5.5 11.5C5.5 10.9477 5.05229 10.5 4.5 10.5ZM4.5 7C3.94772 7 3.5 7.44771 3.5 8C3.5 8.55229 3.94772 9 4.5 9C5.05229 9 5.5 8.55229 5.5 8C5.5 7.44771 5.05229 7 4.5 7ZM8 3.5C7.44771 3.5 7 3.94772 7 4.5C7 5.05228 7.44771 5.5 8 5.5C8.55229 5.5 9 5.05228 9 4.5C9 3.94772 8.55229 3.5 8 3.5ZM8 10.5C7.44771 10.5 7 10.9477 7 11.5C7 12.0523 7.44771 12.5 8 12.5C8.55229 12.5 9 12.0523 9 11.5C9 10.9477 8.55229 10.5 8 10.5ZM11.5 10.5C10.9477 10.5 10.5 10.9477 10.5 11.5C10.5 12.0523 10.9477 12.5 11.5 12.5C12.0523 12.5 12.5 12.0523 12.5 11.5C12.5 10.9477 12.0523 10.5 11.5 10.5ZM8 7C7.44771 7 7 7.44771 7 8C7 8.55229 7.44771 9 8 9C8.55229 9 9 8.55229 9 8C9 7.44771 8.55229 7 8 7Z" />
  </svg>
);

export const PeopleOutlineIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 20 17"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M3.5 4C3.5 2.61929 4.61929 1.5 6 1.5C7.38071 1.5 8.5 2.61929 8.5 4C8.5 5.38071 7.38071 6.5 6 6.5C4.61929 6.5 3.5 5.38071 3.5 4ZM6 0C3.79086 0 2 1.79086 2 4C2 6.20914 3.79086 8 6 8C8.20914 8 10 6.20914 10 4C10 1.79086 8.20914 0 6 0ZM13.5 5C13.5 4.17157 14.1716 3.5 15 3.5C15.8284 3.5 16.5 4.17157 16.5 5C16.5 5.82843 15.8284 6.5 15 6.5C14.1716 6.5 13.5 5.82843 13.5 5ZM15 2C13.3431 2 12 3.34315 12 5C12 6.65685 13.3431 8 15 8C16.6569 8 18 6.65685 18 5C18 3.34315 16.6569 2 15 2ZM12.2484 15.0377C12.9507 15.3232 13.8517 15.5 15.001 15.5C17.2833 15.5 18.5867 14.8027 19.2979 13.9421C19.643 13.5244 19.8186 13.1027 19.9077 12.7795C19.9521 12.6181 19.9754 12.48 19.9875 12.377C19.9936 12.3254 19.997 12.2821 19.9988 12.2487C19.9997 12.232 20.0003 12.2177 20.0006 12.2059L20.0009 12.1903L20.001 12.1839L20.001 12.1811L20.001 12.1786C20.001 10.9754 19.0256 10 17.8224 10H12.1796C12.1521 10 12.1248 10.0005 12.0977 10.0015C12.4916 10.4126 12.7787 10.927 12.914 11.5H17.8224C18.194 11.5 18.4958 11.7986 18.5009 12.1689C18.5006 12.1746 18.4997 12.1855 18.4979 12.2011C18.4934 12.2387 18.4835 12.3015 18.4615 12.3812C18.4177 12.5402 18.3277 12.7613 18.1416 12.9865C17.7903 13.4116 16.9687 14 15.001 14C14.0209 14 13.3252 13.854 12.8302 13.655C12.7231 14.0551 12.5452 14.5378 12.2484 15.0377ZM2.25 10C1.00736 10 0 11.0074 0 12.25V12.5011L3.15905e-06 12.5022L1.38879e-05 12.5048L6.634e-05 12.5111L0.000351369 12.5277C0.000642955 12.5406 0.00116694 12.5571 0.00209707 12.5771C0.00395602 12.6169 0.00744516 12.6705 0.013976 12.7358C0.0270116 12.8661 0.0523263 13.045 0.10165 13.2564C0.199948 13.6776 0.396544 14.2404 0.791826 14.8051C1.61066 15.9749 3.17178 17 6 17C8.82822 17 10.3893 15.9749 11.2082 14.8051C11.6035 14.2404 11.8001 13.6776 11.8983 13.2564C11.9477 13.045 11.973 12.8661 11.986 12.7358C11.9926 12.6705 11.996 12.6169 11.9979 12.5771C11.9988 12.5571 11.9994 12.5406 11.9996 12.5277L11.9999 12.5111L12 12.5048L12 12.5022L12 12.25C12 11.0074 10.9926 10 9.75 10H2.25ZM1.50047 12.5072L1.5 12.4947V12.25C1.5 11.8358 1.83579 11.5 2.25 11.5H9.75C10.1642 11.5 10.5 11.8358 10.5 12.25V12.4946L10.4995 12.5072C10.4988 12.5222 10.4972 12.5493 10.4935 12.5865C10.486 12.6612 10.4703 12.7753 10.4376 12.9155C10.3718 13.1974 10.2403 13.5721 9.97933 13.9449C9.48566 14.6501 8.42178 15.5 6 15.5C3.57822 15.5 2.51434 14.6501 2.02067 13.9449C1.75971 13.5721 1.62818 13.1974 1.56241 12.9155C1.5297 12.7753 1.514 12.6612 1.50653 12.5865C1.50281 12.5493 1.50117 12.5222 1.50047 12.5072Z" />
  </svg>
);

export const PeopleFilledIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 20 17"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M6 8C8.20914 8 10 6.20914 10 4C10 1.79086 8.20914 0 6 0C3.79086 0 2 1.79086 2 4C2 6.20914 3.79086 8 6 8ZM15 8C16.6569 8 18 6.65685 18 5C18 3.34315 16.6569 2 15 2C13.3431 2 12 3.34315 12 5C12 6.65685 13.3431 8 15 8ZM2.25 10C1.00736 10 0 11.0074 0 12.25V12.5C0 12.5 0 17 6 17C12 17 12 12.5 12 12.5V12.25C12 11.0074 10.9926 10 9.75 10H2.25ZM15.0002 15.5C13.829 15.5 12.9321 15.3189 12.2453 15.0416C12.5873 14.4667 12.7719 13.9142 12.8724 13.4836C12.9328 13.2247 12.9645 13.0027 12.9813 12.8353C12.9897 12.7512 12.9944 12.68 12.997 12.6237C12.9983 12.5955 12.9991 12.5709 12.9996 12.5503L13.0001 12.5222L13.0002 12.5103L13.0002 12.505L13.0002 12.5024C13.0002 12.4992 13.0002 12.5 13.0002 12.5V12.25C13.0002 11.3779 12.6567 10.5861 12.0977 10.0023C12.1316 10.0008 12.1658 10 12.2002 10H17.8002C19.0152 10 20.0002 10.985 20.0002 12.2C20.0002 12.2 20.0002 15.5 15.0002 15.5Z" />
  </svg>
);

export const PeopleArrowRightIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 21 21"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M9.31352 13.4999C9.48586 12.9665 9.72529 12.4631 10.0219 11.9999H2.25242C1.0104 11.9999 0.00354004 13.0068 0.00354004 14.2488V14.8265C0.00354004 15.7193 0.322059 16.5828 0.90182 17.2617C2.46812 19.0959 4.85414 20.0011 8 20.0011C8.93119 20.0011 9.79592 19.9218 10.5927 19.7625C10.2334 19.3493 9.92552 18.8903 9.67888 18.3954C9.15534 18.4658 8.59592 18.5011 8 18.5011C5.26169 18.5011 3.29582 17.7553 2.04251 16.2876C1.69465 15.8802 1.50354 15.3622 1.50354 14.8265V14.2488C1.50354 13.8352 1.83882 13.4999 2.25242 13.4999H9.31352ZM8 0.00463867C10.7614 0.00463867 13 2.24321 13 5.00464C13 7.76606 10.7614 10.0046 8 10.0046C5.23857 10.0046 3 7.76606 3 5.00464C3 2.24321 5.23857 0.00463867 8 0.00463867ZM8 1.50464C6.067 1.50464 4.5 3.07164 4.5 5.00464C4.5 6.93764 6.067 8.50464 8 8.50464C9.93299 8.50464 11.5 6.93764 11.5 5.00464C11.5 3.07164 9.93299 1.50464 8 1.50464ZM21 15.5C21 18.5376 18.5376 21 15.5 21C12.4624 21 10 18.5376 10 15.5C10 12.4624 12.4624 10 15.5 10C18.5376 10 21 12.4624 21 15.5ZM16.3536 12.6465C16.1583 12.4512 15.8417 12.4512 15.6464 12.6465C15.4512 12.8418 15.4512 13.1583 15.6464 13.3536L17.2928 15H13C12.7239 15 12.5 15.2239 12.5 15.5C12.5 15.7761 12.7239 16 13 16H17.2929L15.6464 17.6465C15.4512 17.8418 15.4512 18.1583 15.6464 18.3536C15.8417 18.5489 16.1583 18.5489 16.3536 18.3536L18.8536 15.8536C18.9067 15.8004 18.9454 15.7383 18.9696 15.672C18.9883 15.6211 18.9989 15.5664 18.9999 15.5093L19 15.5C19 15.4228 18.9825 15.3498 18.9513 15.2845C18.936 15.2525 18.9171 15.2217 18.8946 15.1928C18.8807 15.175 18.8656 15.1582 18.8494 15.1424L16.3536 12.6465Z" />
  </svg>
);

export const DismissOutlineIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M0.397052 0.553788L0.46967 0.46967C0.735936 0.203403 1.1526 0.179197 1.44621 0.397052L1.53033 0.46967L8 6.939L14.4697 0.46967C14.7626 0.176777 15.2374 0.176777 15.5303 0.46967C15.8232 0.762563 15.8232 1.23744 15.5303 1.53033L9.061 8L15.5303 14.4697C15.7966 14.7359 15.8208 15.1526 15.6029 15.4462L15.5303 15.5303C15.2641 15.7966 14.8474 15.8208 14.5538 15.6029L14.4697 15.5303L8 9.061L1.53033 15.5303C1.23744 15.8232 0.762563 15.8232 0.46967 15.5303C0.176777 15.2374 0.176777 14.7626 0.46967 14.4697L6.939 8L0.46967 1.53033C0.203403 1.26406 0.179197 0.8474 0.397052 0.553788L0.46967 0.46967L0.397052 0.553788Z" />
  </svg>
);

export const DismissFilledIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M0.209704 0.387101L0.292893 0.292893C0.653377 -0.0675905 1.22061 -0.0953204 1.6129 0.209704L1.70711 0.292893L8 6.585L14.2929 0.292893C14.6834 -0.0976309 15.3166 -0.0976309 15.7071 0.292893C16.0976 0.683418 16.0976 1.31658 15.7071 1.70711L9.415 8L15.7071 14.2929C16.0676 14.6534 16.0953 15.2206 15.7903 15.6129L15.7071 15.7071C15.3466 16.0676 14.7794 16.0953 14.3871 15.7903L14.2929 15.7071L8 9.415L1.70711 15.7071C1.31658 16.0976 0.683418 16.0976 0.292893 15.7071C-0.0976309 15.3166 -0.0976309 14.6834 0.292893 14.2929L6.585 8L0.292893 1.70711C-0.0675905 1.34662 -0.0953204 0.779392 0.209704 0.387101L0.292893 0.292893L0.209704 0.387101Z" />
  </svg>
);

export const CheckmarkOutlineIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 18 13"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M1.53033 6.96967C1.23744 6.67678 0.762563 6.67678 0.46967 6.96967C0.176777 7.26256 0.176777 7.73744 0.46967 8.03033L4.96967 12.5303C5.26256 12.8232 5.73744 12.8232 6.03033 12.5303L17.0303 1.53033C17.3232 1.23744 17.3232 0.762563 17.0303 0.46967C16.7374 0.176777 16.2626 0.176777 15.9697 0.46967L5.5 10.9393L1.53033 6.96967Z" />
  </svg>
);

export const CheckmarkFilledIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 18 13"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M5.5 10.5858L1.70711 6.79289C1.31658 6.40237 0.683418 6.40237 0.292893 6.79289C-0.0976311 7.18342 -0.0976311 7.81658 0.292893 8.20711L4.79289 12.7071C5.18342 13.0976 5.81658 13.0976 6.20711 12.7071L17.2071 1.70711C17.5976 1.31658 17.5976 0.683418 17.2071 0.292893C16.8166 -0.0976311 16.1834 -0.0976311 15.7929 0.292893L5.5 10.5858Z" />
  </svg>
);

export const AddOulinedIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 18 18"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M8.74984 0C9.12954 0 9.44339 0.282013 9.49312 0.648078L9.49999 0.749848L9.5012 8H16.7543C17.1685 8 17.5043 8.33579 17.5043 8.75C17.5043 9.1297 17.2221 9.44349 16.8561 9.49315L16.7543 9.5H9.5012L9.50324 16.7491C9.50333 17.1633 9.16761 17.4993 8.7534 17.4993C8.3737 17.4993 8.05985 17.2173 8.01011 16.8512L8.00324 16.7494L8.0012 9.5H0.752197C0.337984 9.5 0.00219727 9.16421 0.00219727 8.75C0.00219727 8.3703 0.284351 8.05651 0.650427 8.00685L0.752197 8H8.0012L7.99999 0.750152C7.99991 0.335939 8.33563 0 8.74984 0Z" />
  </svg>
);

export const AddFilledIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 18 18"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M8.88338 0.0067277L9 0C9.51284 0 9.93551 0.38604 9.99327 0.883379L10 1V8H17C17.5128 8 17.9355 8.38604 17.9933 8.88338L18 9C18 9.51284 17.614 9.93551 17.1166 9.99327L17 10H10V17C10 17.5128 9.61396 17.9355 9.11662 17.9933L9 18C8.48716 18 8.06449 17.614 8.00673 17.1166L8 17V10H1C0.487164 10 0.0644928 9.61396 0.0067277 9.11662L0 9C0 8.48716 0.38604 8.06449 0.883379 8.00673L1 8H8V1C8 0.487164 8.38604 0.0644928 8.88338 0.0067277L9 0L8.88338 0.0067277Z" />
  </svg>
);

export const EditIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 20 20"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M18.9519 1.0481C17.5543 -0.349424 15.2885 -0.349357 13.8911 1.04825L1.94103 12.9997C1.5347 13.4061 1.2491 13.9172 1.116 14.4762L0.0204105 19.0777C-0.0399142 19.3311 0.0355221 19.5976 0.219685 19.7817C0.403848 19.9659 0.670367 20.0413 0.92373 19.981L5.52498 18.8855C6.08418 18.7523 6.59546 18.4666 7.00191 18.0601L18.952 6.10861C20.3493 4.71112 20.3493 2.4455 18.9519 1.0481ZM14.9518 2.10884C15.7634 1.29709 17.0795 1.29705 17.8912 2.10876C18.7028 2.9204 18.7029 4.23632 17.8913 5.04801L17 5.93946L14.0606 3.00012L14.9518 2.10884ZM13 4.06084L15.9394 7.00018L5.94119 16.9995C5.73104 17.2097 5.46668 17.3574 5.17755 17.4263L1.76191 18.2395L2.57521 14.8237C2.64402 14.5346 2.79168 14.2704 3.00175 14.0603L13 4.06084Z" />
  </svg>
);

export const CopyIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 20"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M1.5028 2.62704L1.5 4.75V15.2542C1.5 17.0491 2.95507 18.5042 4.75 18.5042L13.3663 18.5045C13.0573 19.3782 12.224 20.0042 11.2444 20.0042H4.75C2.12665 20.0042 0 17.8776 0 15.2542V4.75C0 3.76929 0.627445 2.93512 1.5028 2.62704ZM13.75 0C14.9926 0 16 1.00736 16 2.25V15.25C16 16.4926 14.9926 17.5 13.75 17.5H4.75C3.50736 17.5 2.5 16.4926 2.5 15.25V2.25C2.5 1.00736 3.50736 0 4.75 0H13.75ZM13.75 1.5H4.75C4.33579 1.5 4 1.83579 4 2.25V15.25C4 15.6642 4.33579 16 4.75 16H13.75C14.1642 16 14.5 15.6642 14.5 15.25V2.25C14.5 1.83579 14.1642 1.5 13.75 1.5Z" />
  </svg>
);

export const DeleteIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 20 21"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M8 4H12C12 2.89543 11.1046 2 10 2C8.89543 2 8 2.89543 8 4ZM6.5 4C6.5 2.067 8.067 0.5 10 0.5C11.933 0.5 13.5 2.067 13.5 4H19.25C19.6642 4 20 4.33579 20 4.75C20 5.16421 19.6642 5.5 19.25 5.5H17.9309L16.7589 17.6112C16.5729 19.5334 14.9575 21 13.0263 21H6.97369C5.04254 21 3.42715 19.5334 3.24113 17.6112L2.06908 5.5H0.75C0.335786 5.5 0 5.16421 0 4.75C0 4.33579 0.335786 4 0.75 4H6.5ZM8.5 8.75C8.5 8.33579 8.16421 8 7.75 8C7.33579 8 7 8.33579 7 8.75V16.25C7 16.6642 7.33579 17 7.75 17C8.16421 17 8.5 16.6642 8.5 16.25V8.75ZM12.25 8C12.6642 8 13 8.33579 13 8.75V16.25C13 16.6642 12.6642 17 12.25 17C11.8358 17 11.5 16.6642 11.5 16.25V8.75C11.5 8.33579 11.8358 8 12.25 8ZM4.73416 17.4667C4.84577 18.62 5.815 19.5 6.97369 19.5H13.0263C14.185 19.5 15.1542 18.62 15.2658 17.4667L16.4239 5.5H3.57608L4.73416 17.4667Z" />
  </svg>
);

export const SettingOutlinedIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 14 14"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M7.00812 0.5C7.49743 0.50564 7.98484 0.562175 8.46246 0.668692C8.67095 0.71519 8.82688 0.889007 8.85054 1.10131L8.96401 2.1192C9.01537 2.58657 9.40998 2.94056 9.88041 2.94105C10.0069 2.94125 10.1319 2.91492 10.2488 2.86322L11.1825 2.45304C11.3767 2.36773 11.6036 2.41424 11.7486 2.56908C12.4234 3.28976 12.926 4.1541 13.2185 5.09705C13.2815 5.30039 13.2089 5.52136 13.0376 5.64767L12.2099 6.25773C11.9738 6.43119 11.8344 6.70667 11.8344 6.99964C11.8344 7.2926 11.9738 7.56808 12.2104 7.74193L13.0388 8.3522C13.2102 8.47849 13.2829 8.69949 13.2198 8.90286C12.9274 9.84566 12.4251 10.7099 11.7507 11.4307C11.6059 11.5855 11.3792 11.6322 11.185 11.5471L10.2474 11.1363C9.97922 11.019 9.67117 11.0362 9.41769 11.1826C9.16421 11.3291 8.99551 11.5875 8.9633 11.8785L8.85058 12.8963C8.82734 13.1061 8.67477 13.2788 8.46935 13.3277C7.50372 13.5574 6.49765 13.5574 5.53202 13.3277C5.3266 13.2788 5.17403 13.1061 5.15079 12.8963L5.03824 11.88C5.00518 11.5895 4.83623 11.332 4.58295 11.186C4.32967 11.04 4.02214 11.0229 3.7548 11.1396L2.81705 11.5504C2.62281 11.6355 2.39602 11.5888 2.25119 11.434C1.57642 10.7123 1.07413 9.84702 0.782134 8.9032C0.719241 8.69991 0.791908 8.47908 0.963231 8.35287L1.79212 7.74221C2.02821 7.56875 2.16765 7.29327 2.16765 7.0003C2.16765 6.70734 2.02821 6.43186 1.79181 6.25817L0.963441 5.64856C0.791864 5.5223 0.719131 5.30119 0.782245 5.09772C1.07475 4.15477 1.5773 3.29043 2.25208 2.56974C2.39706 2.41491 2.62396 2.36839 2.81816 2.45371L3.75175 2.86382C4.02037 2.9817 4.3292 2.9639 4.58385 2.81513C4.83739 2.66806 5.00619 2.40948 5.03878 2.11843L5.15216 1.10131C5.17584 0.8889 5.33191 0.715027 5.54054 0.668628C6.01872 0.56228 6.50663 0.505769 7.00812 0.5ZM7.00824 1.49993C6.70555 1.5035 6.40371 1.52962 6.10522 1.57801L6.0326 2.22946C5.96471 2.83578 5.61336 3.37401 5.08695 3.67935C4.55731 3.98878 3.91158 4.02602 3.34972 3.77944L2.75086 3.51637C2.36957 3.97915 2.06609 4.5009 1.85235 5.06112L2.38421 5.45253C2.87675 5.81441 3.16765 6.38911 3.16765 7.0003C3.16765 7.6115 2.87675 8.1862 2.38473 8.5477L1.85203 8.94015C2.06559 9.50136 2.36912 10.0241 2.75067 10.4878L3.35409 10.2234C3.91282 9.97944 4.55416 10.0152 5.08235 10.3196C5.61054 10.6241 5.96289 11.1612 6.032 11.7684L6.10464 12.4243C6.69773 12.5252 7.30364 12.5252 7.89674 12.4243L7.96937 11.7684C8.03657 11.1614 8.3885 10.6225 8.91732 10.3168C9.44614 10.0112 10.0888 9.97535 10.6485 10.2203L11.2515 10.4845C11.6327 10.0215 11.9361 9.49964 12.1498 8.93932L11.6178 8.54741C11.1253 8.18553 10.8344 7.61083 10.8344 6.99964C10.8344 6.38845 11.1253 5.81374 11.6172 5.45232L12.1485 5.06073C11.9347 4.50041 11.6312 3.97856 11.2498 3.51571L10.6522 3.77824C10.4086 3.886 10.1452 3.94147 9.87911 3.94105C8.89932 3.94003 8.07703 3.20237 7.97008 2.22922L7.89747 1.5778C7.60044 1.52946 7.30176 1.50341 7.00824 1.49993ZM6.9998 4.49997C8.38051 4.49997 9.4998 5.61925 9.4998 6.99997C9.4998 8.38068 8.38051 9.49997 6.9998 9.49997C5.61909 9.49997 4.4998 8.38068 4.4998 6.99997C4.4998 5.61925 5.61909 4.49997 6.9998 4.49997ZM6.9998 5.49997C6.17137 5.49997 5.4998 6.17154 5.4998 6.99997C5.4998 7.82839 6.17137 8.49997 6.9998 8.49997C7.82822 8.49997 8.4998 7.82839 8.4998 6.99997C8.4998 6.17154 7.82822 5.49997 6.9998 5.49997Z" />
  </svg>
);

export const InfoCircleOutlinedIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 14 14"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M6.99973 0.333008C10.6822 0.333008 13.6674 3.31824 13.6674 7.00071C13.6674 10.6832 10.6822 13.6684 6.99973 13.6684C3.31726 13.6684 0.332031 10.6832 0.332031 7.00071C0.332031 3.31824 3.31726 0.333008 6.99973 0.333008ZM6.99973 1.33301C3.86955 1.33301 1.33203 3.87052 1.33203 7.00071C1.33203 10.1309 3.86955 12.6684 6.99973 12.6684C10.1299 12.6684 12.6674 10.1309 12.6674 7.00071C12.6674 3.87052 10.1299 1.33301 6.99973 1.33301ZM6.9973 6.00006C7.25043 5.9999 7.45975 6.18786 7.49302 6.43189L7.49763 6.49974L7.50003 10.1675C7.50021 10.4436 7.2765 10.6676 7.00036 10.6678C6.74723 10.668 6.53791 10.48 6.50464 10.236L6.50003 10.1681L6.49763 6.50039C6.49745 6.22425 6.72116 6.00024 6.9973 6.00006ZM7.00003 3.66822C7.36773 3.66822 7.6658 3.9663 7.6658 4.334C7.6658 4.70169 7.36773 4.99977 7.00003 4.99977C6.63233 4.99977 6.33425 4.70169 6.33425 4.334C6.33425 3.9663 6.63233 3.66822 7.00003 3.66822Z" />
  </svg>
);

export const FullScreenMinimizeOutlinedIcon = (
  props: SVGProps<SVGSVGElement>
) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 18 18"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M5.5 0.75C5.5 0.335786 5.16421 0 4.75 0C4.33579 0 4 0.335786 4 0.75V3.25C4 3.66421 3.66421 4 3.25 4H0.75C0.335786 4 0 4.33579 0 4.75C0 5.16421 0.335786 5.5 0.75 5.5H3.25C4.49264 5.5 5.5 4.49264 5.5 3.25V0.75ZM5.5 17.25C5.5 17.6642 5.16421 18 4.75 18C4.33579 18 4 17.6642 4 17.25V14.75C4 14.3358 3.66421 14 3.25 14H0.75C0.335786 14 0 13.6642 0 13.25C0 12.8358 0.335786 12.5 0.75 12.5H3.25C4.49264 12.5 5.5 13.5074 5.5 14.75V17.25ZM13.25 0C12.8358 0 12.5 0.335786 12.5 0.75V3.25C12.5 4.49264 13.5074 5.5 14.75 5.5H17.25C17.6642 5.5 18 5.16421 18 4.75C18 4.33579 17.6642 4 17.25 4H14.75C14.3358 4 14 3.66421 14 3.25V0.75C14 0.335786 13.6642 0 13.25 0ZM12.5 17.25C12.5 17.6642 12.8358 18 13.25 18C13.6642 18 14 17.6642 14 17.25V14.75C14 14.3358 14.3358 14 14.75 14H17.25C17.6642 14 18 13.6642 18 13.25C18 12.8358 17.6642 12.5 17.25 12.5H14.75C13.5074 12.5 12.5 13.5074 12.5 14.75V17.25Z" />
  </svg>
);

export const FullScreenMinimizeFilledIcon = (
  props: SVGProps<SVGSVGElement>
) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 18 18"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M6 1C6 0.447715 5.55228 0 5 0C4.44772 0 4 0.447715 4 1V3.5C4 3.77614 3.77614 4 3.5 4H1C0.447715 4 0 4.44772 0 5C0 5.55228 0.447715 6 1 6H3.5C4.88071 6 6 4.88071 6 3.5V1ZM6 17C6 17.5523 5.55228 18 5 18C4.44772 18 4 17.5523 4 17V14.5C4 14.2239 3.77614 14 3.5 14H1C0.447715 14 0 13.5523 0 13C0 12.4477 0.447715 12 1 12H3.5C4.88071 12 6 13.1193 6 14.5V17ZM13 0C12.4477 0 12 0.447715 12 1V3.5C12 4.88071 13.1193 6 14.5 6H17C17.5523 6 18 5.55228 18 5C18 4.44772 17.5523 4 17 4H14.5C14.2239 4 14 3.77614 14 3.5V1C14 0.447715 13.5523 0 13 0ZM12 17C12 17.5523 12.4477 18 13 18C13.5523 18 14 17.5523 14 17V14.5C14 14.2239 14.2239 14 14.5 14H17C17.5523 14 18 13.5523 18 13C18 12.4477 17.5523 12 17 12H14.5C13.1193 12 12 13.1193 12 14.5V17Z" />
  </svg>
);

export const FullScreenMaximizeOutlinedIcon = (
  props: SVGProps<SVGSVGElement>
) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 18 18"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M1.5 2.75C1.5 2.05964 2.05964 1.5 2.75 1.5H4.75C5.16421 1.5 5.5 1.16421 5.5 0.75C5.5 0.335786 5.16421 0 4.75 0H2.75C1.23122 0 0 1.23122 0 2.75V4.75C0 5.16421 0.335786 5.5 0.75 5.5C1.16421 5.5 1.5 5.16421 1.5 4.75V2.75ZM1.5 15.25C1.5 15.9404 2.05964 16.5 2.75 16.5H4.75C5.16421 16.5 5.5 16.8358 5.5 17.25C5.5 17.6642 5.16421 18 4.75 18H2.75C1.23122 18 0 16.7688 0 15.25V13.25C0 12.8358 0.335786 12.5 0.75 12.5C1.16421 12.5 1.5 12.8358 1.5 13.25V15.25ZM15.25 1.5C15.9404 1.5 16.5 2.05964 16.5 2.75V4.75C16.5 5.16421 16.8358 5.5 17.25 5.5C17.6642 5.5 18 5.16421 18 4.75V2.75C18 1.23122 16.7688 0 15.25 0H13.25C12.8358 0 12.5 0.335786 12.5 0.75C12.5 1.16421 12.8358 1.5 13.25 1.5H15.25ZM16.5 15.25C16.5 15.9404 15.9404 16.5 15.25 16.5H13.25C12.8358 16.5 12.5 16.8358 12.5 17.25C12.5 17.6642 12.8358 18 13.25 18H15.25C16.7688 18 18 16.7688 18 15.25V13.25C18 12.8358 17.6642 12.5 17.25 12.5C16.8358 12.5 16.5 12.8358 16.5 13.25V15.25Z" />
  </svg>
);

export const FullScreenMaximizeFilledIcon = (
  props: SVGProps<SVGSVGElement>
) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 18 18"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M2 3C2 2.44772 2.44772 2 3 2H5C5.55228 2 6 1.55228 6 1C6 0.447715 5.55228 0 5 0H3C1.34315 0 0 1.34315 0 3V5C0 5.55228 0.447715 6 1 6C1.55228 6 2 5.55228 2 5V3ZM2 15C2 15.5523 2.44772 16 3 16H5C5.55228 16 6 16.4477 6 17C6 17.5523 5.55228 18 5 18H3C1.34315 18 0 16.6569 0 15V13C0 12.4477 0.447715 12 1 12C1.55228 12 2 12.4477 2 13V15ZM15 2C15.5523 2 16 2.44772 16 3V5C16 5.55228 16.4477 6 17 6C17.5523 6 18 5.55228 18 5V3C18 1.34315 16.6569 0 15 0H13C12.4477 0 12 0.447715 12 1C12 1.55228 12.4477 2 13 2H15ZM16 15C16 15.5523 15.5523 16 15 16H13C12.4477 16 12 16.4477 12 17C12 17.5523 12.4477 18 13 18H15C16.6569 18 18 16.6569 18 15V13C18 12.4477 17.5523 12 17 12C16.4477 12 16 12.4477 16 13V15Z" />
  </svg>
);

export const FullScreenIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M5.06367 10.2331C5.25893 10.0379 5.57552 10.0379 5.77078 10.2331C5.96604 10.4284 5.96604 10.745 5.77078 10.9402L3.70733 13.0028H5.16667C5.4198 13.0028 5.62899 13.1909 5.6621 13.435L5.66667 13.5028C5.66667 13.779 5.44281 14.0028 5.16667 14.0028H2.5C2.22386 14.0028 2 13.779 2 13.5028V10.8361C2 10.56 2.22386 10.3361 2.5 10.3361C2.77614 10.3361 3 10.56 3 10.8361V12.2955L5.06367 10.2331ZM10.8367 14.0028C10.5605 14.0028 10.3367 13.779 10.3367 13.5028C10.3367 13.2267 10.5605 13.0028 10.8367 13.0028H12.294L10.233 10.9401C10.0555 10.7625 10.0395 10.4847 10.1848 10.289L10.2333 10.233C10.4286 10.0378 10.7452 10.0379 10.9404 10.2333L13.0033 12.2975V10.8361C13.0033 10.583 13.1914 10.3738 13.4355 10.3407L13.5033 10.3361C13.7795 10.3361 14.0033 10.56 14.0033 10.8361V13.5028C14.0033 13.779 13.7795 14.0028 13.5033 14.0028H10.8367ZM5.16667 2C5.44281 2 5.66667 2.22386 5.66667 2.5C5.66667 2.77614 5.44281 3 5.16667 3H3.70867L5.77036 5.06325C5.9478 5.24083 5.96383 5.51862 5.81852 5.7143L5.77008 5.77036C5.57474 5.96554 5.25816 5.96542 5.06297 5.77008L3 3.70533V5.16667C3 5.4198 2.8119 5.62899 2.56785 5.6621L2.5 5.66667C2.22386 5.66667 2 5.44281 2 5.16667V2.5C2 2.22386 2.22386 2 2.5 2H5.16667ZM13.5033 2C13.7795 2 14.0033 2.22386 14.0033 2.5V5.16667C14.0033 5.44281 13.7795 5.66667 13.5033 5.66667C13.2272 5.66667 13.0033 5.44281 13.0033 5.16667V3.706L10.9403 5.77015C10.7628 5.9477 10.485 5.96389 10.2893 5.81869L10.2332 5.77029C10.0379 5.57507 10.0378 5.25849 10.233 5.06318L12.2947 3H10.8367C10.5835 3 10.3743 2.8119 10.3412 2.56785L10.3367 2.5C10.3367 2.22386 10.5605 2 10.8367 2H13.5033Z" />
  </svg>
);

export const LinkOutlinedIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 21 22"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M9.77157 2.74349C12.113 0.399899 15.912 0.399899 18.2552 2.74304C20.5382 5.02611 20.5968 8.69132 18.4308 11.0453L18.2426 11.2426L9.44261 20.0408L9.40604 20.0711C7.9448 21.3878 5.6908 21.3431 4.28343 19.9357C2.96441 18.6167 2.84229 16.554 3.91708 15.0973C3.94042 15.052 3.96867 15.0082 4.00188 14.9671L4.05544 14.9074L4.14234 14.8197L4.28343 14.6718L4.28634 14.6747L11.7221 7.22037C11.988 6.95375 12.4046 6.92899 12.6985 7.14646L12.7827 7.21896C13.0494 7.48487 13.0741 7.90151 12.8567 8.19541L12.7842 8.27962L5.18953 15.8927C4.4719 16.7683 4.52178 18.0626 5.33917 18.88C6.16824 19.709 7.48789 19.7485 8.36368 18.9984L17.1964 10.168C18.9519 8.41031 18.9519 5.56106 17.1945 3.80371C15.4921 2.10126 12.7649 2.04806 10.9984 3.6441L10.8305 3.80371L10.818 3.81802L1.28167 13.3543C0.988779 13.6472 0.513906 13.6472 0.221013 13.3543C-0.045254 13.0881 -0.0694599 12.6714 0.148395 12.3778L0.221013 12.2937L9.76989 2.74304L9.77157 2.74349Z" />
  </svg>
);

export const LinkFilledIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 20 20"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M14 0C17.3137 0 20 2.68629 20 6C20 7.5373 19.4179 8.98434 18.396 10.0835L18.2062 10.2784L9.47907 19.0053L9.42594 19.0548L9.37022 19.0997C8.701 19.6759 7.846 20 6.94296 20C4.88785 20 3.22185 18.334 3.22185 16.2789C3.22185 15.3775 3.54487 14.5248 4.11741 13.8574L4.26571 13.6945L4.28078 13.6826L11.5718 6.37879C11.962 5.98793 12.5951 5.98737 12.986 6.37755C13.3769 6.76774 13.3774 7.4009 12.9873 7.79177L5.69624 15.0956L5.6853 15.1039C5.389 15.4208 5.22185 15.8354 5.22185 16.2789C5.22185 17.2294 5.99242 18 6.94296 18C7.32191 18 7.67985 17.8781 7.97418 17.6571L8.09631 17.5564L8.09708 17.558L16.7995 8.85707L16.958 8.69267C17.6232 7.96299 18 7.0125 18 6C18 3.79086 16.2091 2 14 2C12.9384 2 11.9454 2.4146 11.2049 3.13858L11.0501 3.29842L11.0316 3.31139L1.70635 12.6403C1.31591 13.0309 0.682748 13.031 0.292136 12.6406C-0.0984758 12.2501 -0.0986174 11.617 0.29182 11.2264L9.60078 1.91324L9.64739 1.87021C10.7713 0.68577 12.3317 0 14 0Z" />
  </svg>
);

export const ChevronDownIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 9"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M0.21967 0.46967C0.512563 0.176777 0.987437 0.176777 1.28033 0.46967L8 7.18934L14.7197 0.46967C15.0126 0.176777 15.4874 0.176777 15.7803 0.46967C16.0732 0.762563 16.0732 1.23744 15.7803 1.53033L8.53033 8.78033C8.23744 9.07322 7.76256 9.07322 7.46967 8.78033L0.21967 1.53033C-0.0732233 1.23744 -0.0732233 0.762563 0.21967 0.46967Z" />
  </svg>
);

export const ChevronUpIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 9"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M0.21967 8.53033C0.512563 8.82322 0.987437 8.82322 1.28033 8.53033L8 1.81066L14.7197 8.53033C15.0126 8.82322 15.4874 8.82322 15.7803 8.53033C16.0732 8.23744 16.0732 7.76256 15.7803 7.46967L8.53033 0.21967C8.23744 -0.0732232 7.76256 -0.0732232 7.46967 0.21967L0.21967 7.46967C-0.0732233 7.76256 -0.0732233 8.23744 0.21967 8.53033Z" />
  </svg>
);

export const ChevronLeftIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 9 16"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M8.53033 0.21967C8.82322 0.512563 8.82322 0.987437 8.53033 1.28033L1.81066 8L8.53033 14.7197C8.82322 15.0126 8.82322 15.4874 8.53033 15.7803C8.23744 16.0732 7.76256 16.0732 7.46967 15.7803L0.21967 8.53033C-0.0732233 8.23744 -0.0732232 7.76256 0.21967 7.46967L7.46967 0.21967C7.76256 -0.0732233 8.23744 -0.0732233 8.53033 0.21967Z" />
  </svg>
);

export const ChevronRightIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 9 16"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M0.46967 0.21967C0.176777 0.512563 0.176777 0.987437 0.46967 1.28033L7.18934 8L0.469671 14.7197C0.176777 15.0126 0.176777 15.4874 0.469671 15.7803C0.762564 16.0732 1.23744 16.0732 1.53033 15.7803L8.78033 8.53033C9.07322 8.23744 9.07322 7.76256 8.78033 7.46967L1.53033 0.21967C1.23744 -0.0732233 0.762563 -0.0732233 0.46967 0.21967Z" />
  </svg>
);

export const ChevronDoubleDownOutlinedIcon = (
  props: SVGProps<SVGSVGElement>
) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 13"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M15.0233 5.37498C15.258 5.60887 15.2587 5.98877 15.0248 6.2235L8.46683 12.8049C8.20889 13.0637 7.78972 13.0637 7.53179 12.8049L0.973809 6.2235C0.739912 5.98877 0.740589 5.60887 0.97532 5.37497C1.21005 5.14108 1.58995 5.14176 1.82385 5.37649L7.99931 11.574L14.1748 5.37649C14.4087 5.14176 14.7886 5.14108 15.0233 5.37498ZM15.0233 0.574975C15.258 0.808872 15.2587 1.18877 15.0248 1.4235L8.46683 8.00488C8.20889 8.26374 7.78972 8.26374 7.53179 8.00488L0.973809 1.4235C0.739912 1.18877 0.740589 0.808872 0.97532 0.574975C1.21005 0.341078 1.58995 0.341755 1.82385 0.576486L7.99931 6.77398L14.1748 0.576487C14.4087 0.341755 14.7886 0.341079 15.0233 0.574975Z" />
  </svg>
);

export const ChevronDoubleDownFilledIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 13"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M14.9516 5.47925C15.2944 5.83916 15.2806 6.40884 14.9207 6.75167L8.62014 12.7533C8.27253 13.0844 7.72624 13.0844 7.37864 12.7533L1.07808 6.75166C0.718175 6.40884 0.704331 5.83916 1.04716 5.47925C1.38999 5.11934 1.95967 5.1055 2.31958 5.44833L7.99939 10.8586L13.6792 5.44833C14.0391 5.1055 14.6088 5.11934 14.9516 5.47925ZM14.9516 0.679249C15.2944 1.03916 15.2806 1.60884 14.9207 1.95167L8.62014 7.95327C8.27253 8.28437 7.72624 8.28437 7.37864 7.95327L1.07808 1.95166C0.718175 1.60883 0.704331 1.03915 1.04716 0.679248C1.38999 0.319341 1.95967 0.305497 2.31958 0.648327L7.99939 6.05864L13.6792 0.648327C14.0391 0.305498 14.6088 0.319342 14.9516 0.679249Z" />
  </svg>
);

export const ChevronDoubleUpOutlinedIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 15"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M0.974972 7.57543C0.741091 7.81015 0.741768 8.19005 0.976484 8.42394C1.2112 8.65783 1.59107 8.65716 1.82495 8.42243L8 2.22502L14.1751 8.42243C14.4089 8.65716 14.7888 8.65783 15.0235 8.42394C15.2582 8.19005 15.2589 7.81015 15.025 7.57543L8.46749 0.994142C8.20958 0.735291 7.79043 0.73529 7.53251 0.994142L0.974972 7.57543ZM0.974972 13.5765C0.741091 13.8112 0.741768 14.1911 0.976484 14.425C1.2112 14.6589 1.59107 14.6582 1.82495 14.4235L8 8.22611L14.1751 14.4235C14.4089 14.6582 14.7888 14.6589 15.0235 14.425C15.2582 14.1911 15.2589 13.8112 15.025 13.5765L8.46749 6.99523C8.20958 6.73637 7.79043 6.73637 7.53251 6.99523L0.974972 13.5765Z" />
  </svg>
);

export const ChevronDoubleUpFilledIcovn = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 15"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M1.04832 14.3207C0.705514 13.9608 0.719357 13.3911 1.07924 13.0483L7.3793 7.04665C7.72688 6.71553 8.27313 6.71553 8.6207 7.04665L14.9208 13.0483C15.2806 13.3911 15.2945 13.9608 14.9517 14.3207C14.6089 14.6807 14.0392 14.6945 13.6794 14.3517L8 8.9413L2.32063 14.3517C1.96076 14.6945 1.39112 14.6807 1.04832 14.3207ZM1.04832 8.32244C0.705514 7.96253 0.719357 7.39284 1.07924 7.05001L7.3793 1.04834C7.72688 0.717225 8.27313 0.717225 8.6207 1.04834L14.9208 7.05001C15.2806 7.39284 15.2945 7.96253 14.9517 8.32244C14.6089 8.68235 14.0392 8.69619 13.6794 8.35336L8 2.94299L2.32063 8.35336C1.96076 8.69619 1.39112 8.68235 1.04832 8.32244Z" />
  </svg>
);

export const ChevronDoubleLeftOulinedIcon = (
  props: SVGProps<SVGSVGElement>
) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 13 16"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M7.62522 15.0233C7.39133 15.258 7.01143 15.2587 6.7767 15.0248L0.195317 8.46683C-0.0635396 8.20889 -0.0635384 7.78972 0.195317 7.53179L6.7767 0.973809C7.01143 0.739912 7.39133 0.740589 7.62522 0.97532C7.85912 1.21005 7.85844 1.58995 7.62371 1.82385L1.42621 7.99931L7.62371 14.1748C7.85844 14.4087 7.85912 14.7886 7.62522 15.0233ZM12.4241 15.0233C12.1902 15.258 11.8103 15.2587 11.5755 15.0248L4.99414 8.46683C4.73529 8.20889 4.73529 7.78972 4.99414 7.53179L11.5755 0.973809C11.8103 0.739912 12.1902 0.740589 12.4241 0.97532C12.6579 1.21005 12.6573 1.58995 12.4225 1.82385L6.22504 7.99931L12.4225 14.1748C12.6573 14.4087 12.6579 14.7886 12.4241 15.0233Z" />
  </svg>
);

export const ChevronDoubleFilledOulinedIcon = (
  props: SVGProps<SVGSVGElement>
) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 13 16"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M7.52117 14.9516C7.16127 15.2944 6.59159 15.2806 6.24876 14.9207L0.247156 8.62014C-0.0839519 8.27253 -0.0839519 7.72624 0.247156 7.37864L6.24876 1.07808C6.59159 0.718175 7.16127 0.704331 7.52118 1.04716C7.88108 1.38999 7.89493 1.95967 7.5521 2.31958L2.14179 7.99939L7.5521 13.6792C7.89493 14.0391 7.88108 14.6088 7.52117 14.9516ZM12.3223 14.9528C11.9624 15.2956 11.3928 15.2818 11.0499 14.9219L5.04833 8.62131C4.71722 8.27371 4.71722 7.72741 5.04833 7.37981L11.0499 1.07925C11.3928 0.719347 11.9624 0.705503 12.3223 1.04833C12.6823 1.39116 12.6961 1.96084 12.3533 2.32075L6.94296 8.00056L12.3533 13.6804C12.6961 14.0403 12.6823 14.61 12.3223 14.9528Z" />
  </svg>
);

export const ChevronDounleRightOulinedIcon = (
  props: SVGProps<SVGSVGElement>
) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 13 16"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M5.37497 0.976495C5.60887 0.741764 5.98877 0.741087 6.2235 0.974984L12.8049 7.53296C13.0637 7.7909 13.0637 8.21007 12.8049 8.468L6.2235 15.026C5.98877 15.2599 5.60887 15.2592 5.37497 15.0245C5.14108 14.7897 5.14176 14.4098 5.37649 14.1759L11.574 8.00048L5.37649 1.82502C5.14176 1.59113 5.14108 1.21123 5.37497 0.976495ZM0.574975 0.976495C0.808872 0.741764 1.18877 0.741087 1.4235 0.974984L8.00488 7.53296C8.26374 7.7909 8.26374 8.21007 8.00488 8.468L1.4235 15.026C1.18877 15.2599 0.808872 15.2592 0.574975 15.0245C0.341078 14.7897 0.341755 14.4098 0.576486 14.1759L6.77398 8.00048L0.576486 1.82502C0.341755 1.59113 0.341078 1.21123 0.574975 0.976495Z" />
  </svg>
);

export const ChevronDounleRightFilledIcon = (
  props: SVGProps<SVGSVGElement>
) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 13 16"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M5.47925 1.04834C5.83916 0.705506 6.40884 0.71935 6.75166 1.07926L12.7533 7.37982C13.0844 7.72742 13.0844 8.27371 12.7533 8.62131L6.75166 14.9219C6.40884 15.2818 5.83916 15.2956 5.47925 14.9528C5.11934 14.61 5.1055 14.0403 5.44833 13.6804L10.8586 8.00056L5.44833 2.32075C5.1055 1.96085 5.11934 1.39117 5.47925 1.04834ZM0.679248 1.04834C1.03915 0.705506 1.60883 0.71935 1.95166 1.07926L7.95327 7.37982C8.28437 7.72742 8.28437 8.27371 7.95327 8.62131L1.95166 14.9219C1.60883 15.2818 1.03915 15.2956 0.679248 14.9528C0.319341 14.61 0.305497 14.0403 0.648327 13.6804L6.05864 8.00056L0.648327 2.32075C0.305497 1.96085 0.319341 1.39117 0.679248 1.04834Z" />
  </svg>
);

export const CaretDownIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 9"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M2.10186 0C1.02792 0 0.453935 1.2649 1.16114 2.07313L6.68297 8.38379C7.38019 9.18061 8.61976 9.18061 9.31698 8.38379L14.8388 2.07313C15.546 1.2649 14.972 0 13.8981 0H2.10186Z" />
  </svg>
);

export const CaretUpIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 9"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M2.10186 8.98141C1.02792 8.98141 0.453935 7.71651 1.16114 6.90828L6.68297 0.597616C7.38019 -0.199205 8.61976 -0.199207 9.31698 0.597616L14.8388 6.90828C15.546 7.7165 14.972 8.98141 13.8981 8.98141H2.10186Z" />
  </svg>
);

export const CaretLeftIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 9 16"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M8.99999 13.8981C8.99999 14.972 7.7351 15.546 6.92686 14.8388L0.616202 9.31698C-0.18062 8.61976 -0.180621 7.38018 0.616202 6.68296L6.92686 1.16113C7.73509 0.453931 8.99999 1.0279 8.99999 2.10185L8.99999 13.8981Z" />
  </svg>
);

export const CaretRightIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 9 16"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M0 13.8981C0 14.972 1.2649 15.546 2.07313 14.8388L8.38379 9.31698C9.18061 8.61976 9.18061 7.38018 8.38379 6.68296L2.07313 1.16113C1.2649 0.453931 0 1.0279 0 2.10185V13.8981Z" />
  </svg>
);

export const ArrowDownloadIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 15 20"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M14.2498 18.5C14.664 18.4999 15 18.8356 15 19.2498C15 19.664 14.6644 19.9999 14.2502 20L1.25022 20.0038C0.836003 20.0039 0.5 19.6682 0.5 19.254C0.5 18.8398 0.83557 18.5039 1.24978 18.5038L14.2498 18.5ZM7.64823 0.0117905L7.75 0.00494385C8.1297 0.00494385 8.44349 0.287098 8.49315 0.653173L8.5 0.754944L8.499 14.4399L12.2208 10.7196C12.4871 10.4534 12.9038 10.4292 13.1974 10.6471L13.2815 10.7197C13.5477 10.986 13.5719 11.4027 13.354 11.6963L13.2814 11.7804L8.28372 16.777C8.01757 17.0431 7.60119 17.0674 7.3076 16.8498L7.22348 16.7773L2.22003 11.7807C1.92694 11.488 1.92661 11.0131 2.21931 10.72C2.48539 10.4536 2.90204 10.4291 3.1958 10.6467L3.27997 10.7193L6.999 14.4329L7 0.754944C7 0.375248 7.28215 0.0614529 7.64823 0.0117905L7.75 0.00494385L7.64823 0.0117905Z" />
  </svg>
);

export const ArrowUploadIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 15 20"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M14.2498 1.50871C14.664 1.50883 15 1.17314 15 0.758925C15 0.344711 14.6644 0.00882724 14.2502 0.00870708L1.25022 0.00494388C0.836003 0.00482372 0.5 0.340513 0.5 0.754726C0.5 1.16894 0.83557 1.50482 1.24978 1.50494L14.2498 1.50871ZM7.64823 19.9969L7.75 20.0038C8.1297 20.0038 8.44349 19.7216 8.49315 19.3555L8.5 19.2538L8.499 5.56876L12.2208 9.28909C12.4871 9.55533 12.9038 9.57949 13.1974 9.36161L13.2815 9.28898C13.5477 9.02269 13.5719 8.60602 13.354 8.31243L13.2814 8.22832L8.28372 3.23171C8.01757 2.96562 7.60119 2.94131 7.3076 3.15888L7.22348 3.2314L2.22003 8.22801C1.92694 8.52071 1.92661 8.99558 2.21931 9.28867C2.48539 9.55512 2.90204 9.57961 3.1958 9.36196L3.27997 9.2894L6.999 5.57576L7 19.2538C7 19.6335 7.28215 19.9473 7.64823 19.9969Z" />
  </svg>
);

export const StarOutlinedIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 20 20"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M8.78782 1.10254C9.28302 0.0991637 10.7138 0.0991578 11.209 1.10254L13.567 5.88026L18.8395 6.64641C19.9468 6.80731 20.3889 8.16807 19.5877 8.94909L15.7724 12.668L16.6731 17.9193C16.8622 19.0221 15.7047 19.8631 14.7143 19.3424L9.99842 16.8631L5.28252 19.3424C4.29213 19.8631 3.13459 19.0221 3.32374 17.9193L4.2244 12.668L0.409159 8.94909C-0.392086 8.16807 0.0500454 6.80731 1.15735 6.64641L6.42988 5.88026L8.78782 1.10254ZM9.99842 2.03894L7.74008 6.61483C7.54344 7.01327 7.16332 7.28944 6.72361 7.35333L1.67382 8.08711L5.32788 11.6489C5.64606 11.9591 5.79125 12.4059 5.71614 12.8439L4.85353 17.8733L9.37021 15.4987C9.76349 15.2919 10.2333 15.2919 10.6266 15.4987L15.1433 17.8733L14.2807 12.8439C14.2056 12.4059 14.3508 11.9591 14.6689 11.6489L18.323 8.08711L13.2732 7.35333C12.8335 7.28944 12.4534 7.01327 12.2568 6.61483L9.99842 2.03894Z" />
  </svg>
);

export const StarFilledIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 20 20"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M8.78782 1.10254C9.28302 0.0991637 10.7138 0.0991578 11.209 1.10254L13.567 5.88026L18.8395 6.64641C19.9468 6.80731 20.3889 8.16807 19.5877 8.94909L15.7724 12.668L16.6731 17.9193C16.8622 19.0221 15.7047 19.8631 14.7143 19.3424L9.99842 16.8631L5.28252 19.3424C4.29213 19.8631 3.13459 19.0221 3.32374 17.9193L4.2244 12.668L0.409159 8.94909C-0.392086 8.16807 0.0500454 6.80731 1.15735 6.64641L6.42988 5.88026L8.78782 1.10254Z" />
  </svg>
);

export const PlayCircleOutlinedIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 20 20"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M8.85563 6.15498C8.02249 5.69354 7 6.29608 7 7.24847V12.7516C7 13.704 8.0225 14.3065 8.85563 13.8451L14.6134 10.6561C14.852 10.524 15 10.2727 15 10C15 9.7273 14.852 9.47607 14.6134 9.34393L8.85563 6.15498ZM10 0C4.47715 0 0 4.47715 0 10C0 15.5228 4.47715 20 10 20C15.5228 20 20 15.5228 20 10C20 4.47715 15.5228 0 10 0ZM1.5 10C1.5 5.30558 5.30558 1.5 10 1.5C14.6944 1.5 18.5 5.30558 18.5 10C18.5 14.6944 14.6944 18.5 10 18.5C5.30558 18.5 1.5 14.6944 1.5 10Z" />
  </svg>
);

export const PlayCircleFilledIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 20 20"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M0 10C0 4.47715 4.47715 0 10 0C15.5228 0 20 4.47715 20 10C20 15.5228 15.5228 20 10 20C4.47715 20 0 15.5228 0 10ZM8.85563 6.15498C8.02249 5.69354 7 6.29608 7 7.24847V12.7516C7 13.704 8.0225 14.3065 8.85563 13.8451L14.6134 10.6561C14.852 10.524 15 10.2727 15 10C15 9.7273 14.852 9.47607 14.6134 9.34393L8.85563 6.15498Z" />
  </svg>
);

export const PlayIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={17}
    height={18}
    viewBox="0 0 17 18"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M0 2.27386C0 0.567008 1.82609 -0.518303 3.32538 0.297487L15.687 7.02364C17.2531 7.87581 17.2531 10.1242 15.687 10.9764L3.32538 17.7025C1.82609 18.5183 0 17.433 0 15.7262V2.27386Z" />
  </svg>
);

export const PauseCircleOutlinedIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M8.5 6.25C8.5 5.83579 8.16421 5.5 7.75 5.5C7.33579 5.5 7 5.83579 7 6.25V13.75C7 14.1642 7.33579 14.5 7.75 14.5C8.16421 14.5 8.5 14.1642 8.5 13.75V6.25ZM13 6.25C13 5.83579 12.6642 5.5 12.25 5.5C11.8358 5.5 11.5 5.83579 11.5 6.25V13.75C11.5 14.1642 11.8358 14.5 12.25 14.5C12.6642 14.5 13 14.1642 13 13.75V6.25ZM10 0C4.47715 0 0 4.47715 0 10C0 15.5228 4.47715 20 10 20C15.5228 20 20 15.5228 20 10C20 4.47715 15.5228 0 10 0ZM1.5 10C1.5 5.30558 5.30558 1.5 10 1.5C14.6944 1.5 18.5 5.30558 18.5 10C18.5 14.6944 14.6944 18.5 10 18.5C5.30558 18.5 1.5 14.6944 1.5 10Z" />
  </svg>
);

export const RefreshIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M12.6102 0.470468L12.5334 0.403103C12.2394 0.178552 11.818 0.201007 11.5495 0.470468L11.4824 0.547551C11.2587 0.84259 11.281 1.26552 11.5495 1.53498L13.521 3.5118H6.5L6.26687 3.51592C2.785 3.63911 0 6.51085 0 10.0354C0 11.7259 0.640704 13.2663 1.6917 14.4252L1.76407 14.4947C1.89496 14.6065 2.06463 14.674 2.25 14.674C2.66421 14.674 3 14.337 3 13.9213C3 13.7481 2.9417 13.5885 2.84373 13.4613L2.64439 13.2306C1.92953 12.3627 1.5 11.2494 1.5 10.0354C1.5 7.26396 3.73858 5.01725 6.5 5.01725H13.381L11.5495 6.85754L11.4824 6.93463C11.2587 7.22967 11.281 7.6526 11.5495 7.92206C11.8424 8.21601 12.3173 8.21601 12.6102 7.92206L15.7922 4.72852L15.8593 4.65144C16.083 4.3564 16.0606 3.93347 15.7922 3.66401L12.6102 0.470468ZM18.23 5.57108C18.0999 5.46224 17.9326 5.39677 17.75 5.39677C17.3358 5.39677 17 5.73378 17 6.14949C17 6.33618 17.0677 6.507 17.1791 6.63722C17.9992 7.53109 18.5 8.72455 18.5 10.0354C18.5 12.8069 16.2614 15.0536 13.5 15.0536H6.558L8.46337 13.1425L8.53653 13.0573C8.73387 12.7897 8.73185 12.4206 8.53049 12.155L8.46337 12.0779L8.37852 12.0045C8.11188 11.8065 7.74409 11.8085 7.47951 12.0106L7.40271 12.0779L4.22073 15.2715L4.14756 15.3566C3.95023 15.6242 3.95224 15.9934 4.15361 16.2589L4.22073 16.336L7.40271 19.5295L7.48683 19.6024C7.78044 19.8211 8.1971 19.7968 8.46337 19.5295C8.73185 19.2601 8.75423 18.8371 8.53049 18.5421L8.46337 18.465L6.564 16.559H13.5L13.7331 16.5549C17.215 16.4317 20 13.56 20 10.0354C20 8.34197 19.3571 6.79923 18.3029 5.63965L18.23 5.57108Z" />
  </svg>
);

export const AudioWaveIcon = AudioWaveIconBase;

export const BackwardIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={16}
    height={14}
    viewBox="0 0 16 14"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M7.38153 0.857083L0.645593 6.67193C0.451062 6.83833 0.451062 7.15943 0.645593 7.32583L7.38153 13.143C7.63231 13.3586 8.00028 13.1641 8.00028 12.8149V1.18286C8.00028 0.835989 7.63231 0.639114 7.38153 0.857083ZM14.8815 0.857083L8.14559 6.67193C8.09947 6.71317 8.06266 6.76377 8.03761 6.82034C8.01255 6.87692 7.99983 6.93818 8.00028 7.00005C8.00028 7.12193 8.0495 7.2438 8.14559 7.32818L14.8815 13.1454C15.1323 13.361 15.5003 13.1665 15.5003 12.8172V1.18286C15.5003 0.835989 15.1323 0.639114 14.8815 0.857083Z"
      fillOpacity={0.85}
    />
  </svg>
);

export const FastForwardIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={16}
    height={14}
    viewBox="0 0 16 14"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M15.3547 6.67182L8.61875 0.856975C8.36797 0.64135 8 0.835882 8 1.1851V12.8171C8 13.1664 8.36797 13.3609 8.61875 13.1453L15.3547 7.32807C15.5492 7.15932 15.5492 6.84057 15.3547 6.67182ZM7.85469 6.67182L1.11875 0.856975C0.867969 0.64135 0.5 0.835882 0.5 1.1851V12.8171C0.5 13.1664 0.867969 13.3609 1.11875 13.1453L7.85469 7.32807C7.95078 7.24369 8 7.12182 8 6.99994C8 6.87807 7.95078 6.75619 7.85469 6.67182Z"
      fillOpacity={0.85}
    />
  </svg>
);

export const RecordStopOutlinedIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 20 20"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M10 1.5C5.30558 1.5 1.5 5.30558 1.5 10C1.5 14.6944 5.30558 18.5 10 18.5C14.6944 18.5 18.5 14.6944 18.5 10C18.5 5.30558 14.6944 1.5 10 1.5ZM0 10C0 4.47715 4.47715 0 10 0C15.5228 0 20 4.47715 20 10C20 15.5228 15.5228 20 10 20C4.47715 20 0 15.5228 0 10ZM6 7.5C6 6.67157 6.67157 6 7.5 6H12.5C13.3284 6 14 6.67157 14 7.5V12.5C14 13.3284 13.3284 14 12.5 14H7.5C6.67157 14 6 13.3284 6 12.5V7.5Z" />
  </svg>
);

export const RecordStopFilledIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 20 20"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M10 20C15.5228 20 20 15.5228 20 10C20 4.47715 15.5228 0 10 0C4.47715 0 0 4.47715 0 10C0 15.5228 4.47715 20 10 20ZM7.5 6H12.5C13.3284 6 14 6.67157 14 7.5V12.5C14 13.3284 13.3284 14 12.5 14H7.5C6.67157 14 6 13.3284 6 12.5V7.5C6 6.67157 6.67157 6 7.5 6Z" />
  </svg>
);
export const FastBackwardFilledIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="14"
    viewBox="0 0 17 13"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M8.38122 0.597656L1.64528 5.88984C1.60016 5.92523 1.56367 5.97042 1.53858 6.02199C1.51349 6.07356 1.50045 6.13015 1.50045 6.1875C1.50045 6.24485 1.51349 6.30144 1.53858 6.35301C1.56367 6.40458 1.60016 6.44977 1.64528 6.48516L8.38122 11.7773C8.632 11.9742 8.99997 11.7961 8.99997 11.4797V0.895313C8.99997 0.578907 8.632 0.400781 8.38122 0.597656ZM15.8812 0.597656L9.14528 5.88984C9.10016 5.92523 9.06367 5.97042 9.03858 6.02199C9.01349 6.07356 9.00045 6.13015 9.00045 6.1875C9.00045 6.24485 9.01349 6.30144 9.03858 6.35301C9.06367 6.40458 9.10016 6.44977 9.14528 6.48516L15.8812 11.7773C16.132 11.9742 16.5 11.7961 16.5 11.4797V0.895313C16.5 0.578907 16.132 0.400781 15.8812 0.597656ZM1.34997 0H0.149969C0.0679381 0 -3.05176e-05 0.0632813 -3.05176e-05 0.140625V12.2344C-3.05176e-05 12.3117 0.0679381 12.375 0.149969 12.375H1.34997C1.432 12.375 1.49997 12.3117 1.49997 12.2344V0.140625C1.49997 0.0632813 1.432 0 1.34997 0Z" />
  </svg>
);

export const VolumeMuteOutLinedIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M1.28034 0.219675C0.987445 -0.0732209 0.512571 -0.0732257 0.219675 0.219665C-0.0732209 0.512555 -0.0732257 0.987429 0.219665 1.28032L4.43782 5.49856H2.25C1.00736 5.49856 0 6.50592 0 7.74856V12.2465C0 13.4891 1.00736 14.4965 2.25 14.4965H5.92956C6.11329 14.4965 6.29063 14.5639 6.42793 14.686L10.9194 18.6797C11.7255 19.3965 13 18.8242 13 17.7456V14.0609L18.7194 19.7805C19.0123 20.0734 19.4872 20.0734 19.7801 19.7805C20.073 19.4876 20.073 19.0127 19.7801 18.7198L1.28034 0.219675ZM11.5 12.5609V17.1888L7.42465 13.565C7.01275 13.1988 6.48074 12.9965 5.92956 12.9965H2.25C1.83579 12.9965 1.5 12.6607 1.5 12.2465V7.74856C1.5 7.33435 1.83579 6.99856 2.25 6.99856H5.92961C5.93233 6.99856 5.93505 6.99856 5.93777 6.99855L11.5 12.5609ZM11.5 2.80677V8.31817L13 9.8182V2.24998C13 1.17136 11.7255 0.599126 10.9195 1.3158L7.52003 4.33813L8.58251 5.40062L11.5 2.80677ZM15.141 11.9592L16.279 13.0973C16.7408 12.1628 17 11.1107 17 9.99998C17 8.7968 16.6958 7.66243 16.1596 6.67182C15.9624 6.30755 15.5072 6.17211 15.143 6.36931C14.7787 6.5665 14.6432 7.02165 14.8404 7.38591C15.2609 8.16268 15.5 9.0523 15.5 9.99998C15.5 10.691 15.3729 11.3512 15.141 11.9592ZM17.3881 14.2064L18.4815 15.2998C19.4437 13.7631 20 11.9457 20 9.99999C20 7.77388 19.2717 5.71568 18.0407 4.0536C17.7941 3.72075 17.3244 3.65077 16.9916 3.89731C16.6587 4.14384 16.5888 4.61353 16.8353 4.94639C17.8815 6.35894 18.5 8.10615 18.5 9.99999C18.5 11.5311 18.0958 12.9663 17.3881 14.2064Z" />
  </svg>
);

export const FastForwardFilledIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="14"
    viewBox="0 0 18 14"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M15.6047 6.70234L8.86878 1.41016C8.618 1.21328 8.25003 1.39141 8.25003 1.70781V12.2922C8.25003 12.6086 8.618 12.7867 8.86878 12.5898L15.6047 7.29766C15.6498 7.26227 15.6863 7.21708 15.7114 7.16551C15.7365 7.11394 15.7496 7.05735 15.7496 7C15.7496 6.94265 15.7365 6.88606 15.7114 6.83449C15.6863 6.78292 15.6498 6.73773 15.6047 6.70234ZM8.10472 6.70234L1.36878 1.41016C1.118 1.21328 0.750031 1.39141 0.750031 1.70781V12.2898C0.750031 12.6063 1.118 12.7844 1.36878 12.5875L8.10472 7.29531C8.20081 7.22031 8.25003 7.10781 8.25003 6.99766C8.25003 6.88984 8.20081 6.77734 8.10472 6.70234ZM17.1 0.8125H15.9C15.818 0.8125 15.75 0.875781 15.75 0.953125V13.0469C15.75 13.1242 15.818 13.1875 15.9 13.1875H17.1C17.1821 13.1875 17.25 13.1242 17.25 13.0469V0.953125C17.25 0.875781 17.1821 0.8125 17.1 0.8125Z" />
  </svg>
);

export const SoundOutlinedIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 20 18"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M13 1.25C13 0.171381 11.7255 -0.400852 10.9195 0.315826L6.42794 4.30909C6.29065 4.43116 6.11333 4.49859 5.92961 4.49859H2.25C1.00736 4.49859 0 5.50595 0 6.74859V11.2465C0 12.4891 1.00736 13.4965 2.25 13.4965H5.92956C6.11329 13.4965 6.29063 13.5639 6.42793 13.686L10.9194 17.6797C11.7255 18.3965 13 17.8243 13 16.7456V1.25ZM7.4246 5.43011L11.5 1.80679V16.1888L7.42465 12.5651C7.01275 12.1988 6.48074 11.9965 5.92956 11.9965H2.25C1.83579 11.9965 1.5 11.6607 1.5 11.2465V6.74859C1.5 6.33437 1.83579 5.99859 2.25 5.99859H5.92961C6.48075 5.99859 7.01272 5.7963 7.4246 5.43011ZM16.9916 2.89733C17.3244 2.65079 17.7941 2.72077 18.0407 3.05362C19.2717 4.7157 20 6.7739 20 9.00002C20 11.2261 19.2717 13.2843 18.0407 14.9464C17.7941 15.2793 17.3244 15.3492 16.9916 15.1027C16.6587 14.8562 16.5888 14.3865 16.8353 14.0536C17.8815 12.6411 18.5 10.8939 18.5 9.00002C18.5 7.10617 17.8815 5.35896 16.8353 3.94641C16.5888 3.61356 16.6587 3.14387 16.9916 2.89733ZM15.143 5.36933C15.5072 5.17214 15.9624 5.30757 16.1596 5.67184C16.6958 6.66245 17 7.79682 17 9C17 10.2032 16.6958 11.3376 16.1596 12.3282C15.9624 12.6924 15.5072 12.8279 15.143 12.6307C14.7787 12.4335 14.6432 11.9783 14.8404 11.6141C15.2609 10.8373 15.5 9.94769 15.5 9C15.5 8.05232 15.2609 7.1627 14.8404 6.38593C14.6432 6.02167 14.7787 5.56652 15.143 5.36933Z" />
  </svg>
);

export const RotateLeftOutlinedIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 19 19"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M13.6144 7.81374H1.63617C1.23463 7.81374 0.910217 8.13816 0.910217 8.5397V17.9317C0.910217 18.3333 1.23463 18.6577 1.63617 18.6577H13.6144C14.016 18.6577 14.3404 18.3333 14.3404 17.9317V8.5397C14.3404 8.13816 14.016 7.81374 13.6144 7.81374ZM12.6162 16.9335H2.63436V9.53789H12.6162V16.9335Z" />
    <path d="M16.9561 5.78422C15.1684 3.49973 12.5096 2.29964 9.81904 2.28603L9.8145 0.834118C9.8145 0.686658 9.64209 0.604988 9.52866 0.695733L6.62484 2.98703C6.5341 3.05735 6.53636 3.19347 6.62484 3.26606L9.53092 5.55962C9.64662 5.65037 9.81904 5.5687 9.81677 5.42124V3.9716C10.1094 3.97387 10.4043 3.99202 10.697 4.02832C11.6521 4.14628 12.5595 4.4412 13.3966 4.90627C14.261 5.38721 15.0119 6.03376 15.6289 6.8187C16.2437 7.60591 16.6884 8.49066 16.947 9.44575C17.2289 10.4877 17.2792 11.5787 17.0945 12.6422H18.7936C19.1294 10.2919 18.5373 7.81008 16.9561 5.78422Z" />
  </svg>
);

export const RotateRightOutlinedIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 19 19"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M8.30074 4.02836C8.59566 3.99206 8.88831 3.97391 9.18096 3.97164V5.42129C9.18096 5.56875 9.3511 5.65042 9.46681 5.55967L12.3729 3.2661C12.4636 3.19351 12.4636 3.05739 12.3729 2.98706L9.46907 0.695763C9.35337 0.605019 9.18323 0.686689 9.18323 0.834149L9.17869 2.28606C6.48811 2.2974 3.8293 3.4975 2.04163 5.78427C1.28911 6.74512 0.738286 7.84799 0.422144 9.0268C0.106003 10.2056 0.0310526 11.4361 0.201783 12.6446H1.90098C1.88056 12.5243 1.86241 12.4018 1.84653 12.2793C1.73083 11.3242 1.79889 10.3714 2.04844 9.44808C2.30706 8.49072 2.75171 7.60823 3.3665 6.82102C3.98356 6.03381 4.73448 5.38726 5.59882 4.90858C6.4382 4.44125 7.34338 4.14633 8.30074 4.02836Z" />
    <path d="M17.3638 7.81374H5.38554C4.984 7.81374 4.65959 8.13815 4.65959 8.53969V17.9318C4.65959 18.3333 4.984 18.6577 5.38554 18.6577H17.3638C17.7654 18.6577 18.0898 18.3333 18.0898 17.9318V8.53969C18.0898 8.13815 17.7654 7.81374 17.3638 7.81374ZM16.3656 16.9336H6.38373V9.53788H16.3656V16.9336Z" />
  </svg>
);

export const ZoomInOutlinedIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 19 19"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M11.5 8C11.5 7.58579 11.1642 7.25 10.75 7.25H8.75V5.25C8.75 4.83579 8.41421 4.5 8 4.5C7.58578 4.5 7.25 4.83579 7.25 5.25V7.25H5.25C4.83578 7.25 4.5 7.58579 4.5 8C4.5 8.41421 4.83578 8.75 5.25 8.75H7.25V10.75C7.25 11.1642 7.58578 11.5 8 11.5C8.41421 11.5 8.75 11.1642 8.75 10.75V8.75H10.75C11.1642 8.75 11.5 8.41421 11.5 8ZM8 0.75C12.0041 0.75 15.25 3.99594 15.25 8C15.25 9.73187 14.6427 11.3219 13.6295 12.5688L18.5303 17.4697C18.8232 17.7626 18.8232 18.2374 18.5303 18.5303C18.2641 18.7966 17.8474 18.8208 17.5538 18.6029L17.4697 18.5303L12.5688 13.6295C11.3219 14.6427 9.73187 15.25 8 15.25C3.99593 15.25 0.75 12.0041 0.75 8C0.75 3.99594 3.99593 0.75 8 0.75ZM8 2.25C4.82436 2.25 2.25 4.82436 2.25 8C2.25 11.1756 4.82436 13.75 8 13.75C11.1756 13.75 13.75 11.1756 13.75 8C13.75 4.82436 11.1756 2.25 8 2.25Z" />
  </svg>
);

export const ZoomOutOutlinedIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 19 19"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M10.75 7.25C11.1642 7.25 11.5 7.58579 11.5 8C11.5 8.41421 11.1642 8.75 10.75 8.75H5.25C4.83578 8.75 4.5 8.41421 4.5 8C4.5 7.58579 4.83578 7.25 5.25 7.25H10.75ZM15.25 8C15.25 3.99594 12.0041 0.75 8 0.75C3.99593 0.75 0.75 3.99594 0.75 8C0.75 12.0041 3.99593 15.25 8 15.25C9.73187 15.25 11.3219 14.6427 12.5688 13.6295L17.4697 18.5303L17.5538 18.6029C17.8474 18.8208 18.2641 18.7966 18.5303 18.5303C18.8232 18.2374 18.8232 17.7626 18.5303 17.4697L13.6295 12.5688C14.6427 11.3219 15.25 9.73187 15.25 8ZM2.25 8C2.25 4.82436 4.82436 2.25 8 2.25C11.1756 2.25 13.75 4.82436 13.75 8C13.75 11.1756 11.1756 13.75 8 13.75C4.82436 13.75 2.25 11.1756 2.25 8Z" />
  </svg>
);

export const VideoOutlinedIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 20 14"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M3.25 0C1.45507 0 0 1.45507 0 3.25V10.75C0 12.5449 1.45507 14 3.25 14H10.75C12.5449 14 14 12.5449 14 10.75V10.438L17.2575 12.6872C18.4183 13.4887 20.0018 12.6578 20.0018 11.2471V2.75204C20.0018 1.34137 18.4183 0.51044 17.2575 1.31197L14 3.56118V3.25C14 1.45507 12.5449 0 10.75 0H3.25ZM14 5.38401L18.1098 2.54632C18.2756 2.43181 18.5018 2.55051 18.5018 2.75204V11.2471C18.5018 11.4486 18.2756 11.5673 18.1098 11.4528L14 8.61515V5.38401ZM1.5 3.25C1.5 2.2835 2.2835 1.5 3.25 1.5H10.75C11.7165 1.5 12.5 2.2835 12.5 3.25V10.75C12.5 11.7165 11.7165 12.5 10.75 12.5H3.25C2.2835 12.5 1.5 11.7165 1.5 10.75V3.25Z" />
  </svg>
);

export const VideoFilledIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 20 14"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M0 3.25C0 1.45507 1.45507 0 3.25 0H9.75C11.5449 0 13 1.45507 13 3.25V10.75C13 12.5449 11.5449 14 9.75 14H3.25C1.45507 14 0 12.5449 0 10.75V3.25ZM17.2574 12.6882L14 10.439V3.56209L17.2574 1.31295C18.4182 0.511416 20.0017 1.34235 20.0017 2.75302V11.2481C20.0017 12.6588 18.4182 13.4897 17.2574 12.6882Z" />
  </svg>
);

export const ArrowDownOutlinedIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 18"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M15.7906 10.2673C16.0763 9.96736 16.0647 9.49262 15.7648 9.20694C15.4649 8.92125 14.9901 8.93279 14.7045 9.23272L8.74997 15.484V0.75C8.74997 0.335787 8.41418 0 7.99997 0C7.58576 0 7.24997 0.335787 7.24997 0.75V15.4844L1.29514 9.23272C1.00945 8.93279 0.534722 8.92125 0.234795 9.20694C-0.0651319 9.49262 -0.0766752 9.96736 0.209012 10.2673L7.27571 17.6862C7.43674 17.8553 7.64215 17.9552 7.85569 17.9861C7.90238 17.9952 7.95062 18 7.99997 18C8.04967 18 8.09823 17.9952 8.14522 17.9859C8.35829 17.9548 8.5632 17.8549 8.72389 17.6862L15.7906 10.2673Z" />
  </svg>
);

export const ArrowUpOutlinedIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 18"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M0.209378 7.73272C-0.076309 8.03264 -0.0647656 8.50738 0.235161 8.79306C0.535087 9.07875 1.00982 9.06721 1.29551 8.76728L7.25 2.516V17.25C7.25 17.6642 7.58579 18 8 18C8.41421 18 8.75 17.6642 8.75 17.25V2.51565L14.7048 8.76728C14.9905 9.06721 15.4652 9.07875 15.7652 8.79306C16.0651 8.50738 16.0766 8.03264 15.791 7.73272L8.72425 0.313787C8.56323 0.144741 8.35782 0.0447664 8.14428 0.0138645C8.09759 0.00476646 8.04935 0 8 0C7.9503 0 7.90174 0.00483322 7.85474 0.0140553C7.64168 0.0451765 7.43677 0.145086 7.27608 0.313787L0.209378 7.73272Z" />
  </svg>
);

export const ArrowLeftOutlinedIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 18 16"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M7.73272 15.7907C8.03264 16.0764 8.50738 16.0649 8.79306 15.765C9.07875 15.465 9.06721 14.9903 8.76728 14.7046L2.51587 8.75L17.25 8.75C17.6642 8.75 18 8.41421 18 8C18 7.58579 17.6642 7.25 17.25 7.25L2.51577 7.25L8.76728 1.29529C9.06721 1.00961 9.07875 0.534874 8.79306 0.234947C8.50738 -0.0649794 8.03264 -0.0765226 7.73272 0.209165L0.313785 7.27587C0.144859 7.43678 0.0449066 7.64202 0.0139294 7.85539C0.00478934 7.90218 -4.32463e-09 7.95053 0 8C4.3331e-09 8.04957 0.00480748 8.09801 0.0139828 8.14489C0.045022 8.35813 0.144956 8.56323 0.313786 8.72404L7.73272 15.7907Z" />
  </svg>
);

export const ArrowRightOutlinedIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 18 16"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M10.2673 0.209256C9.96736 -0.076431 9.49262 -0.0648876 9.20694 0.235039C8.92125 0.534965 8.93279 1.0097 9.23272 1.29539L15.4841 7.25H0.75C0.335787 7.25 0 7.58579 0 8C0 8.41421 0.335787 8.75 0.75 8.75H15.4842L9.23272 14.7047C8.93279 14.9904 8.92125 15.4651 9.20694 15.7651C9.49262 16.065 9.96736 16.0765 10.2673 15.7908L17.6862 8.72413C17.8551 8.56323 17.9551 8.358 17.9861 8.14463C17.9952 8.09783 18 8.04948 18 8C18 7.95043 17.9952 7.90198 17.986 7.8551C17.955 7.64186 17.855 7.43677 17.6862 7.27596L10.2673 0.209256Z" />
  </svg>
);

export const RedoOutlinedIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 18 18"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M9 1.5C13.1421 1.5 16.5 4.85786 16.5 9C16.5 13.1421 13.1421 16.5 9 16.5C4.85786 16.5 1.5 13.1421 1.5 9C1.5 8.62365 1.52772 8.25377 1.58123 7.89229C1.64845 7.43823 1.31609 7 0.857079 7C0.486233 7 0.160999 7.25624 0.104712 7.62279C0.0357647 8.07178 0 8.53171 0 9C0 13.9706 4.02944 18 9 18C13.9706 18 18 13.9706 18 9C18 4.02944 13.9706 0 9 0C6.69494 0 4.59227 0.86656 3 2.29168V1.25C3 0.835786 2.66421 0.5 2.25 0.5C1.83579 0.5 1.5 0.835786 1.5 1.25V4.25C1.5 4.66421 1.83579 5 2.25 5H5.25C5.66421 5 6 4.66421 6 4.25C6 3.83579 5.66421 3.5 5.25 3.5H3.90093C5.23907 2.25883 7.03092 1.5 9 1.5Z" />
  </svg>
);

export const RedoFilledIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 18 18"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M16 9C16 5.13401 12.866 2 9 2C7.68005 2 6.4458 2.36484 5.39202 3H6C6.55228 3 7 3.44772 7 4C7 4.55228 6.55228 5 6 5H3C2.44772 5 2 4.55228 2 4C2 3 2 2 2 1C2 0.447715 2.44772 0 3 0C3.55228 0 4 0.447715 4 1V1.51575C5.42985 0.558802 7.14996 0 9 0C13.9706 0 18 4.02944 18 9C18 13.9706 13.9706 18 9 18C4.02944 18 0 13.9706 0 9C0 8.61987 0.0236225 8.2448 0.0695782 7.87626C0.137918 7.32822 0.637592 6.93935 1.18563 7.00769C1.73367 7.07603 2.12255 7.5757 2.05421 8.12374C2.01846 8.41039 2 8.7028 2 9C2 12.866 5.13401 16 9 16C12.866 16 16 12.866 16 9Z" />
  </svg>
);

export const MailOutlinedIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 20 16"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M3.25 0H16.75C18.483 0 19.8992 1.35645 19.9949 3.06558L20 3.25V12.75C20 14.483 18.6435 15.8992 16.9344 15.9949L16.75 16H3.25C1.51697 16 0.100754 14.6435 0.00514483 12.9344L0 12.75V3.25C0 1.51697 1.35645 0.100754 3.06558 0.0051446L3.25 0H16.75H3.25ZM18.5 5.373L10.3493 9.66369C10.1619 9.76233 9.94313 9.77642 9.74676 9.70596L9.65069 9.66369L1.5 5.374V12.75C1.5 13.6682 2.20711 14.4212 3.10647 14.4942L3.25 14.5H16.75C17.6682 14.5 18.4212 13.7929 18.4942 12.8935L18.5 12.75V5.373ZM16.75 1.5H3.25C2.33183 1.5 1.57881 2.20711 1.5058 3.10647L1.5 3.25V3.679L10 8.15246L18.5 3.678V3.25C18.5 2.33183 17.7929 1.57881 16.8935 1.5058L16.75 1.5Z" />
  </svg>
);

export const PictureOutlinedIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 18 18"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M14.75 0C16.5449 0 18 1.45507 18 3.25V14.75C18 16.5449 16.5449 18 14.75 18H3.25C1.45507 18 0 16.5449 0 14.75V3.25C0 1.45507 1.45507 0 3.25 0H14.75ZM15.3305 16.4014L9.52467 10.7148C9.25962 10.4553 8.85013 10.4316 8.55879 10.644L8.4752 10.7148L2.66845 16.4011C2.8504 16.4651 3.04613 16.5 3.25 16.5H14.75C14.9535 16.5 15.1489 16.4653 15.3305 16.4014L9.52467 10.7148L15.3305 16.4014ZM14.75 1.5H3.25C2.2835 1.5 1.5 2.2835 1.5 3.25V14.75C1.5 14.9584 1.53643 15.1583 1.60326 15.3437L7.42578 9.64299C8.25887 8.82729 9.56746 8.7885 10.4458 9.52658L10.5742 9.64312L16.3964 15.3447C16.4634 15.159 16.5 14.9588 16.5 14.75V3.25C16.5 2.2835 15.7165 1.5 14.75 1.5ZM12.2521 3.5C13.4959 3.5 14.5042 4.50831 14.5042 5.75212C14.5042 6.99592 13.4959 8.00423 12.2521 8.00423C11.0083 8.00423 10 6.99592 10 5.75212C10 4.50831 11.0083 3.5 12.2521 3.5ZM12.2521 5C11.8367 5 11.5 5.33673 11.5 5.75212C11.5 6.1675 11.8367 6.50423 12.2521 6.50423C12.6675 6.50423 13.0042 6.1675 13.0042 5.75212C13.0042 5.33673 12.6675 5 12.2521 5Z" />
  </svg>
);

export const PictureFilledIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 18 18"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M8.55825 10.6469L8.4746 10.7179L1.54692 17.5186C2.04216 17.8239 2.62551 18 3.25 18H14.75C15.3745 18 15.9578 17.8239 16.4531 17.5186L9.5254 10.7179L9.43199 10.6399C9.17047 10.4552 8.81745 10.4576 8.55825 10.6469ZM18 3.25C18 1.45507 16.5449 0 14.75 0H3.25C1.45507 0 0 1.45507 0 3.25V14.75C0 15.3771 0.177581 15.9626 0.485196 16.4592L7.42379 9.64751L7.55923 9.52479C8.39415 8.82732 9.61495 8.82927 10.4477 9.53062L10.5762 9.64751L17.5148 16.4592C17.8224 15.9626 18 15.3771 18 14.75V3.25ZM12.25 7.75C11.1454 7.75 10.25 6.85457 10.25 5.75C10.25 4.64543 11.1454 3.75 12.25 3.75C13.3546 3.75 14.25 4.64543 14.25 5.75C14.25 6.85457 13.3546 7.75 12.25 7.75Z" />
  </svg>
);

export const BoxObjectSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="40"
    height="40"
    viewBox="0 0 40 40"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clip-path="url(#clip0_845_2926)">
      <path d="M0 10.758V0.645916C0 0.289493 0.289493 0 0.645916 0H10.7588C11.1152 0 11.4047 0.289493 11.4047 0.645916C11.4047 1.00234 11.1152 1.29183 10.7588 1.29183H1.29183V10.7588C1.29183 11.1152 1.00234 11.4047 0.645916 11.4047C0.289493 11.4047 0 11.1152 0 10.758ZM10.758 38.7082H1.29183V29.2412C1.29183 28.8848 1.00234 28.5953 0.645916 28.5953C0.289493 28.5953 0 28.8848 0 29.2412V39.3541C0 39.7105 0.289493 40 0.645916 40H10.7588C11.1152 40 11.4047 39.7113 11.4047 39.3541C11.4047 38.9969 11.1152 38.7082 10.758 38.7082ZM39.3541 0H29.2412C28.8848 0 28.5953 0.289493 28.5953 0.645916C28.5953 1.00234 28.884 1.29183 29.2412 1.29183H38.7082V10.7588C38.7082 11.1152 38.9969 11.4047 39.3541 11.4047C39.7113 11.4047 40 11.1152 40 10.7588V0.645916C40 0.289493 39.7105 0 39.3541 0ZM39.3541 28.5953C38.9977 28.5953 38.7082 28.8848 38.7082 29.2412V38.7082H29.2412C28.8848 38.7082 28.5953 38.9969 28.5953 39.3541C28.5953 39.7113 28.884 40 29.2412 40H39.3541C39.7105 40 40 39.7105 40 39.3541V29.2412C40 28.8848 39.7105 28.5953 39.3541 28.5953ZM31.2128 26.8446L20.3234 33.1328C20.2234 33.1909 20.1121 33.2191 20 33.2191C19.8879 33.2191 19.7766 33.1901 19.6766 33.1328L8.78719 26.8446C8.58721 26.7293 8.46464 26.5164 8.46464 26.2858V13.7134C8.46464 13.4828 8.58802 13.2699 8.78719 13.1546L19.6766 6.86638C19.8766 6.75107 20.1226 6.75107 20.3226 6.86638L31.212 13.1546C31.412 13.2699 31.5346 13.4828 31.5346 13.7134V26.2858C31.5354 26.5164 31.412 26.7293 31.2128 26.8446ZM10.4016 13.7134L20 19.2541L29.5984 13.7134L20 8.17111L10.4016 13.7134ZM9.75566 25.9132L19.3541 31.4555V20.3725L9.75566 14.8319V25.9132ZM30.2435 25.9132V14.8319L20.6451 20.3725V31.4555L30.2435 25.9132Z" />
    </g>
    <defs>
      <clipPath id="clip0_845_2926">
        <rect width="40" height="40" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const ImageSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="40"
    height="37"
    viewBox="0 0 40 37"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clip-path="url(#clip0_756_3118)">
      <path d="M29.5045 36.9995C29.1897 36.9995 28.8678 36.9593 28.549 36.875L2.77559 29.9101C0.787312 29.3567 -0.397844 27.2819 0.122469 25.2772L2.95606 14.3139C3.1295 13.6407 3.81153 13.2323 4.48106 13.4129C5.14825 13.5879 5.54981 14.2777 5.37481 14.9517L2.54122 25.9173C2.36622 26.5929 2.76466 27.2913 3.43184 27.4758L29.1912 34.4367C29.8529 34.6133 30.5365 34.216 30.7092 33.5522L31.5889 29.97C31.7561 29.2945 32.4326 28.8837 33.1022 29.0485C33.7725 29.2164 34.1803 29.8991 34.0154 30.5755L33.1326 34.175C32.6865 35.8777 31.1662 36.9995 29.5045 36.9995Z" />
      <path d="M36.2504 26.9087H10.416C8.34805 26.9087 6.66602 25.2114 6.66602 23.1247V3.78396C6.66602 1.69726 8.34805 0 10.416 0H36.2504C38.3184 0 40.0004 1.69726 40.0004 3.78396V23.1247C40.0004 25.2122 38.3184 26.9087 36.2504 26.9087ZM10.416 2.52264C9.72617 2.52264 9.16602 3.08787 9.16602 3.78396V23.1247C9.16602 23.8208 9.72617 24.386 10.416 24.386H36.2504C36.9402 24.386 37.5004 23.8208 37.5004 23.1247V3.78396C37.5004 3.08787 36.9402 2.52264 36.2504 2.52264H10.416Z" />
      <path d="M14.999 11.7725C13.1607 11.7725 11.6654 10.2636 11.6654 8.4087C11.6654 6.55377 13.1607 5.04492 14.999 5.04492C16.8373 5.04492 18.3326 6.55377 18.3326 8.4087C18.3326 10.2636 16.8373 11.7725 14.999 11.7725ZM14.999 7.56756C14.5389 7.56756 14.1654 7.94438 14.1654 8.4087C14.1654 8.87302 14.5389 9.24984 14.999 9.24984C15.4592 9.24984 15.8318 8.87302 15.8318 8.4087C15.8326 7.94438 15.4592 7.56756 14.999 7.56756ZM7.94902 25.1929C7.62949 25.1929 7.30918 25.0699 7.06543 24.8232C6.57715 24.3305 6.57715 23.5311 7.06543 23.0384L14.599 15.4366C15.7389 14.2864 17.5943 14.2864 18.7326 15.4366L20.7475 17.4697L26.9373 9.97195C27.4904 9.30266 28.3021 8.91559 29.1646 8.91086C30.0912 8.85883 30.8412 9.28374 31.3998 9.94515L39.6967 19.7133C40.1467 20.2414 40.085 21.0384 39.5615 21.4925C39.0365 21.9466 38.2482 21.8843 37.7982 21.3561L29.4982 11.5856C29.3881 11.4524 29.2529 11.4595 29.1779 11.4359C29.1068 11.4359 28.9678 11.4579 28.8584 11.5888L21.7912 20.1492C21.5662 20.4212 21.2381 20.5867 20.8881 20.6017C20.5311 20.6214 20.1943 20.4835 19.9475 20.2336L16.9646 17.223C16.7529 17.0093 16.5779 17.0093 16.3662 17.223L8.83262 24.8232C8.59902 25.0605 8.28027 25.1937 7.94902 25.1929Z" />
    </g>
    <defs>
      <clipPath id="clip0_756_3118">
        <rect width="40" height="37" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const VideoSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="40"
    height="29"
    viewBox="0 0 40 29"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clip-path="url(#clip0_756_3458)">
      <path d="M38.4203 3.69583C37.455 3.22451 36.3051 3.35567 35.4705 4.03337L30.7069 7.8573V7.03795C31.1068 3.57603 28.6344 0.443781 25.1854 0.0424116C24.7045 -0.0135527 24.2184 -0.0135527 23.7375 0.0424116H7.01094C3.56196 -0.358083 0.442283 2.12271 0.0424141 5.5855C-0.0133411 6.06819 -0.0133411 6.55613 0.0424141 7.03882V21.9629C-0.357455 25.4248 2.11494 28.5571 5.56392 28.9585C6.04481 29.0144 6.53093 29.0144 7.01181 28.9585H23.7384C27.1874 29.3598 30.3079 26.8782 30.7078 23.4162C30.7635 22.9336 30.7635 22.4456 30.7078 21.9629V21.1436L35.4714 24.9675C35.9627 25.3663 36.5751 25.5831 37.2076 25.5831C37.6275 25.5822 38.0422 25.4878 38.4212 25.305C39.3899 24.8416 40.0058 23.8587 40.0006 22.7814V6.21947C40.005 5.14216 39.389 4.15841 38.4203 3.69583ZM27.92 21.9621C27.92 24.9037 26.669 26.1594 23.7384 26.1594H7.01181C4.08118 26.1594 2.83017 24.9037 2.83017 21.9621V7.03882C2.83017 4.0972 4.08118 2.8415 7.01181 2.8415H23.7384C26.669 2.8415 27.92 4.0972 27.92 7.03882V21.9621ZM37.212 22.7832L30.7069 17.5601V11.4416L37.212 6.2186V22.7832Z" />
    </g>
    <defs>
      <clipPath id="clip0_756_3458">
        <rect width="40" height="29" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const AudioSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="40"
    height="36"
    viewBox="0 0 40 36"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clip-path="url(#clip0_770_2555)">
      <path d="M21.0557 0C20.0303 0 19.0058 0.350039 18.2062 1.00769L9.20593 8.22832H5.6385C2.5218 8.22832 0 10.7586 0 13.8858V22.1142C0 25.2414 2.5218 27.7717 5.6385 27.7717H9.20593L18.2062 34.9923C19.0058 35.6297 20.0312 36 21.0557 36C23.5775 36 25.6274 33.9432 25.6274 31.4129V4.58715C25.6284 2.05684 23.5775 0 21.0557 0ZM3.07537 22.1142V13.8858C3.07537 12.4664 4.22383 11.3141 5.6385 11.3141H8.20163V24.6859H5.6385C4.22383 24.6859 3.07537 23.5336 3.07537 22.1142ZM22.553 31.4129C22.5473 32.247 21.8688 32.9181 21.0375 32.9123C20.702 32.9104 20.3763 32.7947 20.1129 32.5854L11.276 25.4882V10.5118L20.1129 3.41457C20.7645 2.89674 21.7112 3.00667 22.2272 3.6595C22.4358 3.92371 22.5502 4.24965 22.553 4.58715V31.4129ZM31.3486 11.4992C30.7748 10.8618 29.8109 10.8203 29.1757 11.396C28.5404 11.9717 28.4991 12.9389 29.0728 13.5763C30.1598 14.7903 30.7748 16.3737 30.7748 18.0198C30.7748 19.768 30.0983 21.4343 28.8883 22.6898C28.2992 23.3041 28.3184 24.2809 28.9306 24.8711C29.217 25.1478 29.5985 25.3021 29.9954 25.3021C30.3847 25.3021 30.795 25.1372 31.1026 24.8287C32.8863 22.9975 33.8502 20.5703 33.8502 18.0198C33.8502 15.6129 32.9689 13.3092 31.3697 11.5195L31.3486 11.4992Z" />
      <path d="M36.0019 7.46751C35.4282 6.83011 34.4642 6.7684 33.829 7.34408C33.1937 7.91976 33.1524 8.88695 33.706 9.52435C35.777 11.828 36.9043 14.8521 36.9043 17.9995C36.9043 21.147 35.7357 24.253 33.6031 26.5981C33.0294 27.2355 33.0697 28.2027 33.706 28.7784C33.9933 29.0455 34.3624 29.1902 34.7516 29.1902C35.162 29.1902 35.5925 29.0253 35.8789 28.6964C38.5237 25.7756 39.9999 21.9898 39.9999 17.9995C39.9999 14.0093 38.5852 10.3469 36.0221 7.46654L36.0019 7.46751Z" />
    </g>
    <defs>
      <clipPath id="clip0_770_2555">
        <rect width="40" height="36" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const SubjectOutlineIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M8.75 10.4375C8.75 10.1268 9.00184 9.875 9.3125 9.875H11.9375C12.2482 9.875 12.5 10.1268 12.5 10.4375C12.5 10.7482 12.2482 11 11.9375 11H9.3125C9.00184 11 8.75 10.7482 8.75 10.4375ZM6.125 4.0625C6.125 3.75184 5.87316 3.5 5.5625 3.5C5.25184 3.5 5 3.75184 5 4.0625V5H4.0625C3.75184 5 3.5 5.25184 3.5 5.5625C3.5 5.87316 3.75184 6.125 4.0625 6.125H5V7.0625C5 7.37316 5.25184 7.625 5.5625 7.625C5.87316 7.625 6.125 7.37316 6.125 7.0625V6.125H7.0625C7.37316 6.125 7.625 5.87316 7.625 5.5625C7.625 5.25184 7.37316 5 7.0625 5H6.125V4.0625ZM8 15.5C3.85786 15.5 0.5 12.1421 0.5 8C0.5 3.85786 3.85786 0.5 8 0.5C12.1421 0.5 15.5 3.85786 15.5 8C15.5 12.1421 12.1421 15.5 8 15.5ZM8 14.375C11.5208 14.375 14.375 11.5208 14.375 8C14.375 6.44211 13.8162 5.01474 12.8881 3.90739L3.90739 12.8881C5.01474 13.8162 6.44211 14.375 8 14.375ZM3.1119 12.0926L12.0926 3.1119C10.9852 2.1838 9.55787 1.625 8 1.625C4.47918 1.625 1.625 4.47918 1.625 8C1.625 9.55787 2.1838 10.9852 3.1119 12.0926Z" />
  </svg>
);

export const NotepadEditOutlineIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 13 15"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M2.59995 4.99961C2.59995 4.7787 2.77904 4.59961 2.99995 4.59961H6.99995C7.22086 4.59961 7.39995 4.7787 7.39995 4.99961C7.39995 5.22052 7.22086 5.39961 6.99995 5.39961H2.99995C2.77904 5.39961 2.59995 5.22052 2.59995 4.99961ZM2.99995 6.99961C2.77904 6.99961 2.59995 7.1787 2.59995 7.39961C2.59995 7.62052 2.77904 7.79961 2.99995 7.79961H6.99995C7.22086 7.79961 7.39995 7.62052 7.39995 7.39961C7.39995 7.1787 7.22086 6.99961 6.99995 6.99961H2.99995ZM2.59995 9.79961C2.59995 9.5787 2.77904 9.39961 2.99995 9.39961H4.59995C4.82087 9.39961 4.99995 9.5787 4.99995 9.79961C4.99995 10.0205 4.82087 10.1996 4.59995 10.1996H2.99995C2.77904 10.1996 2.59995 10.0205 2.59995 9.79961ZM1.79995 0.999609C1.79995 0.778695 1.97904 0.599609 2.19995 0.599609C2.42087 0.599609 2.59995 0.778695 2.59995 0.999609V1.39961H4.59995V0.999609C4.59995 0.778695 4.77904 0.599609 4.99995 0.599609C5.22087 0.599609 5.39995 0.778695 5.39995 0.999609V1.39961H7.39995V0.999609C7.39995 0.778695 7.57904 0.599609 7.79995 0.599609C8.02086 0.599609 8.19995 0.778695 8.19995 0.999609V1.39961H8.59995C9.26269 1.39961 9.79995 1.93687 9.79995 2.59961V6.38491C9.53901 6.49672 9.29456 6.65913 9.08155 6.87213L8.99995 6.95373V2.59961C8.99995 2.3787 8.82086 2.19961 8.59995 2.19961H1.39995C1.17904 2.19961 0.999951 2.3787 0.999951 2.59961V12.1996C0.999951 12.4205 1.17904 12.5996 1.39995 12.5996H4.37633L4.24568 13.1222C4.22231 13.2157 4.20807 13.3084 4.20224 13.3996H1.39995C0.737209 13.3996 0.199951 12.8624 0.199951 12.1996V2.59961C0.199951 1.93687 0.737209 1.39961 1.39995 1.39961H1.79995V0.999609ZM9.6473 7.43782L5.78375 11.3014C5.55849 11.5266 5.39869 11.8089 5.32142 12.1179L5.02184 13.3162C4.89156 13.8374 5.3636 14.3094 5.88473 14.1791L7.08305 13.8795C7.3921 13.8023 7.67435 13.6425 7.89961 13.4172L11.7632 9.55367C12.3474 8.9694 12.3474 8.0221 11.7632 7.43782C11.1789 6.85354 10.2316 6.85354 9.6473 7.43782Z" />
  </svg>
);

export const AppListDetailOutlineIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 14 12"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M1.83325 0.666016C1.00482 0.666016 0.333252 1.33759 0.333252 2.16602V3.83268C0.333252 4.66111 1.00482 5.33268 1.83325 5.33268H3.49992C4.32835 5.33268 4.99992 4.66111 4.99992 3.83268V2.16602C4.99992 1.33759 4.32835 0.666016 3.49992 0.666016H1.83325ZM1.33325 2.16602C1.33325 1.88987 1.55711 1.66602 1.83325 1.66602H3.49992C3.77606 1.66602 3.99992 1.88987 3.99992 2.16602V3.83268C3.99992 4.10882 3.77606 4.33268 3.49992 4.33268H1.83325C1.55711 4.33268 1.33325 4.10882 1.33325 3.83268V2.16602ZM6.49992 1.33268C6.22378 1.33268 5.99992 1.55654 5.99992 1.83268C5.99992 2.10882 6.22378 2.33268 6.49992 2.33268H13.1666C13.4427 2.33268 13.6666 2.10882 13.6666 1.83268C13.6666 1.55654 13.4427 1.33268 13.1666 1.33268H6.49992ZM6.49992 3.33268C6.22378 3.33268 5.99992 3.55654 5.99992 3.83268C5.99992 4.10882 6.22378 4.33268 6.49992 4.33268H11.1666C11.4427 4.33268 11.6666 4.10882 11.6666 3.83268C11.6666 3.55654 11.4427 3.33268 11.1666 3.33268H6.49992ZM1.83325 6.66602C1.00482 6.66602 0.333252 7.33759 0.333252 8.16602V9.83268C0.333252 10.6611 1.00482 11.3327 1.83325 11.3327H3.49992C4.32835 11.3327 4.99992 10.6611 4.99992 9.83268V8.16602C4.99992 7.33759 4.32835 6.66602 3.49992 6.66602H1.83325ZM1.33325 8.16602C1.33325 7.88987 1.55711 7.66602 1.83325 7.66602H3.49992C3.77606 7.66602 3.99992 7.88987 3.99992 8.16602V9.83268C3.99992 10.1088 3.77606 10.3327 3.49992 10.3327H1.83325C1.55711 10.3327 1.33325 10.1088 1.33325 9.83268V8.16602ZM6.49992 7.33268C6.22378 7.33268 5.99992 7.55654 5.99992 7.83268C5.99992 8.10882 6.22378 8.33268 6.49992 8.33268H13.1666C13.4427 8.33268 13.6666 8.10882 13.6666 7.83268C13.6666 7.55654 13.4427 7.33268 13.1666 7.33268H6.49992ZM6.49992 9.33268C6.22378 9.33268 5.99992 9.55654 5.99992 9.83268C5.99992 10.1088 6.22378 10.3327 6.49992 10.3327H11.1666C11.4427 10.3327 11.6666 10.1088 11.6666 9.83268C11.6666 9.55654 11.4427 9.33268 11.1666 9.33268H6.49992Z" />
  </svg>
);
