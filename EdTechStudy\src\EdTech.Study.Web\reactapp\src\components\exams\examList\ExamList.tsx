import React, { useState, useEffect } from 'react';
import {
  Table,
  Input,
  Button,
  Space,
  Tooltip,
  Tag,
  Dropdown,
  message,
  Collapse,
  Select,
  DatePicker,
  Form,
  Alert,
  Modal,
} from 'antd';
import {
  SearchOutlined,
  EyeOutlined,
  MoreOutlined,
  DeleteOutlined,
  CopyOutlined,
  EditOutlined,
  ExportOutlined,
  CheckOutlined,
  GlobalOutlined,
  CloseOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  FilterOutlined,
} from '@ant-design/icons';

// Thêm import cho DatePicker
const { RangePicker } = DatePicker;
const { Option } = Select;

import { ExamBase } from '../../../interfaces/exams/examBase';
import type { ColumnsType, TablePaginationConfig } from 'antd/es/table';
import type {
  FilterValue,
  SorterResult,
  TableCurrentDataSource,
} from 'antd/es/table/interface';
import {
  deleteExam,
  fetchExams,
  IExamDataManagerState,
  updateExamStatus,
} from '../../../store/slices/ExamSlices/examDataManagerSlice';
import { connect } from 'react-redux';
import {
  getExamStatusColor,
  countTotalQuestions,
  getExamSourceTypeString,
  getExamSourceTypeColor,
  getExamTypeDisplayVN,
  getExamStatusDisplayVN,
} from '../../../utils/examUtils';
import {
  ExamType,
  ExamStatus,
  ExamSourceType,
} from '../../../interfaces/exams/examEnums';
import { Subject } from '../../../interfaces/lessons/subject';
import { LessonGrade } from '../../../interfaces/lessons/lessonGrade';
import { GetExamListDto } from '../../../interfaces/exams/examDtos';
import {
  handlePageChange,
  handleFilterChange,
  handleSortChange,
} from '../../../services/exams/ExamsService';
import modal from 'antd/es/modal';
import { ItemType } from 'antd/es/menu/interface';
import Search from 'antd/es/input/Search';
import { useQuestionContext } from '../../../providers/Questions/QuestionProvider';
import { registerLicenseSyncfusionBase } from '../../../utils/syncfusion-license';
export interface IExamListProps
  extends IExamListStateToProps,
    IExamListDispatchToState {
  onViewDetails?: (exam: ExamBase) => void;
  onPreviewExam?: (exam: ExamBase) => void;
  onDuplicateExam?: (exam: ExamBase) => void;
  onDeleteExam?: (examId: string) => void;
  loading: boolean;
  filterType?: ExamType;
  filterStatus?: ExamStatus;
}

export interface IExamListStateToProps {
  exams: ExamBase[];

  totalCount: number;
  currentPage: number;
  pageSize: number;
  loading: boolean;
  error: string | null;
}

export interface IExamListDispatchToState {
  fetchExams: (params?: GetExamListDto) => any;
  handlePageChange: (page: number, pageSize?: number) => any;
  handleFilterChange: (filters: Partial<GetExamListDto>) => any;
  handleSortChange: (sorting: string) => any;
  deleteExam: (examId: string) => any;
  updateExamStatus: (data: {
    examId: string;
    status: ExamStatus;
  }) => Promise<any>;
}

interface TableParams {
  pagination?: TablePaginationConfig;
  sortField?: string;
  sortOrder?: string;
  filters?: Record<string, FilterValue | null>;
}

// Thêm interface cho advanced search
interface AdvancedSearchParams {
  title?: string;
  examType?: number;
  status?: number;
  subjectId?: string;
  gradeId?: string;
  startDate?: Date | string;
  endDate?: Date | string;
}

const ExamList: React.FC<IExamListProps> = (props) => {
  const {
    exams,
    onViewDetails,
    onPreviewExam,
    fetchExams,
    deleteExam,
    updateExamStatus,
    loading = false,
    error,
    filterType,
    filterStatus,
  } = props;
  const { subjects = [], lessonGrades: grades = [] } = useQuestionContext();

  const [searchText, setSearchText] = useState<string>('');
  const [advancedSearchVisible, setAdvancedSearchVisible] =
    useState<boolean>(false);
  const [advancedSearchModalVisible, setAdvancedSearchModalVisible] =
    useState<boolean>(false);
  const [advancedSearchParams, setAdvancedSearchParams] =
    useState<AdvancedSearchParams>({});
  const [form] = Form.useForm();

  // Đảm bảo form được khởi tạo trước khi sử dụng
  useEffect(() => {
    // Khởi tạo form với giá trị mặc định
    form.setFieldsValue({
      title: searchText,
      examType: undefined,
      status: undefined,
      subjectId: undefined,
      gradeId: undefined,
      dateRange: undefined,
    });
  }, [form]);

  const [tableParams, setTableParams] = useState<TableParams>({
    pagination: {
      current: 1,
      pageSize: 10,
      showSizeChanger: true,
      pageSizeOptions: ['10', '20', '50'],
      showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
    },
  });

  // Fetch data khi component mount
  useEffect(() => {
    fetchExams();
  }, []);

  // Fetch data khi pagination hoặc sorting thay đổi
  useEffect(() => {
    fetchExamData();
  }, [tableParams]);

  // Reset to first page when filters change
  useEffect(() => {
    setTableParams({
      ...tableParams,
      pagination: {
        ...tableParams.pagination,
        current: 1,
      },
    });
  }, [filterType, filterStatus]);

  useEffect(() => {
    if (error) {
      Modal.error({
        title: 'Lỗi',
        content: error,
      });
    }
  }, [error]);

  // Hàm fetch data với các tham số hiện tại
  const fetchExamData = () => {
    const formValues = form.getFieldsValue();
    const params: GetExamListDto = {
      skipCount:
        ((tableParams.pagination?.current || 1) - 1) *
        (tableParams.pagination?.pageSize || 10),
      maxResultCount: tableParams.pagination?.pageSize || 10,
      filter: formValues.title || searchText || undefined,
      ...Object.entries(advancedSearchParams).reduce((acc, [key, value]) => {
        // Convert Date objects to ISO strings for startDate and endDate
        if (
          (key === 'startDate' || key === 'endDate') &&
          value instanceof Date
        ) {
          acc[key] = value.toISOString();
        } else {
          acc[key as keyof GetExamListDto] = value;
        }
        return acc;
      }, {} as Partial<GetExamListDto>),
    };

    // Thêm sorting nếu có
    if (tableParams.sortField && tableParams.sortOrder) {
      params.sorting = `${tableParams.sortField} ${
        tableParams.sortOrder === 'ascend' ? 'asc' : 'desc'
      }`;
    }

    fetchExams(params);
  };

  // Xử lý tìm kiếm cơ bản
  const handleSearch = (value: string) => {
    setSearchText(value);
    // Cập nhật giá trị title trong form tìm kiếm nâng cao
    form.setFieldValue('title', value);
    setTableParams({
      ...tableParams,
      pagination: {
        ...tableParams.pagination,
        current: 1,
      },
    });
  };

  // Xử lý tìm kiếm nâng cao
  const handleAdvancedSearch = (values: AdvancedSearchParams) => {
    // Không tự động đồng bộ tiêu đề ở đây nữa, vì đã xử lý ở nút "Áp dụng"
    setAdvancedSearchParams(values);
    setTableParams({
      ...tableParams,
      pagination: {
        ...tableParams.pagination,
        current: 1,
      },
    });
  };

  // Reset tìm kiếm
  const handleResetSearch = () => {
    setSearchText('');
    setAdvancedSearchParams({});
    form.resetFields();
    setTableParams({
      ...tableParams,
      pagination: {
        ...tableParams.pagination,
        current: 1,
      },
    });
  };

  /**
   * Gets the icon component for a given exam status
   */
  const getExamStatusIcon = (status: ExamStatus): React.ReactNode => {
    switch (status) {
      case ExamStatus.Submitted:
        return <ClockCircleOutlined />;
      case ExamStatus.Approved:
        return <CheckOutlined />;
      case ExamStatus.Published:
        return <GlobalOutlined />;
      case ExamStatus.Rejected:
        return <CloseOutlined />;
      case ExamStatus.Draft:
      default:
        return <EditOutlined />;
    }
  };

  const handleTableChange = (
    pagination: TablePaginationConfig,
    filters: Record<string, FilterValue | null>,
    sorter: SorterResult<ExamBase> | SorterResult<ExamBase>[],
    extra: TableCurrentDataSource<ExamBase>
  ) => {
    const sorterResult = Array.isArray(sorter) ? sorter[0] : sorter;
    setTableParams({
      pagination,
      filters,
      sortField: sorterResult?.field?.toString(),
      sortOrder: sorterResult?.order?.toString(),
    });
  };

  const handleDuplicateExam = (exam: ExamBase) => {
    message.warning('Tính năng đang phát triển');
  };

  const handleDeleteExam = async (examId: ExamBase) => {
    const confirmed = await modal.confirm({
      title: 'Xác nhận xóa',
      content: (
        <>
          <p>
            Bạn có chắc chắn muốn xóa bài thi <strong>{examId.title}</strong>{' '}
            không?
          </p>
          <p>Thao tác này không thể hoàn tác.</p>
        </>
      ),
      okText: 'Đồng ý',
      okType: 'danger',
      cancelText: 'Hủy',
      async onOk() {
        let result = await deleteExam(examId.clientId);
        if (result) {
          message.success('Xóa bài thi thành công');
          fetchExams();
        } else {
          message.error('Xóa bài thi thất bại');
        }
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  };

  const examData = exams.map((exam) => {
    let current = { ...exam };
    if (exam.subjectId) {
      let currentSubject = subjects.find((s) => s.id == exam.subjectId);
      current.subject = { ...currentSubject } as Subject;
    }
    if (exam.gradeId) {
      let currentGrade = grades.find((g) => g.id == exam.gradeId);
      current.grade = { ...currentGrade } as LessonGrade;
    }
    return current;
  });

  const handleChangeStatus = (exam: ExamBase, newStatus: ExamStatus) => {
    // Create confirmation message based on status
    let confirmTitle = '';
    let confirmContent = '';
    let successMessage = '';

    switch (newStatus) {
      case ExamStatus.Draft:
        confirmTitle = 'Chuyển về bản nháp?';
        confirmContent = `Bạn có chắc muốn chuyển đề thi "${exam.title}" về trạng thái bản nháp?`;
        successMessage = 'Đề thi đã được chuyển về trạng thái bản nháp';
        break;
      case ExamStatus.Submitted:
        confirmTitle = 'Gửi duyệt đề thi?';
        confirmContent = `Bạn có chắc muốn gửi đề thi "${exam.title}" để duyệt?`;
        successMessage = 'Đề thi đã được gửi duyệt';
        break;
      case ExamStatus.Approved:
        confirmTitle = 'Duyệt đề thi?';
        confirmContent = `Bạn có chắc muốn duyệt đề thi "${exam.title}"?`;
        successMessage = 'Đề thi đã được duyệt';
        break;
      case ExamStatus.Published:
        confirmTitle = 'Công bố đề thi?';
        confirmContent = `Bạn có chắc muốn công bố đề thi "${exam.title}"? Đề thi sẽ được hiển thị cho người dùng.`;
        successMessage = 'Đề thi đã được công bố';
        break;
      case ExamStatus.Rejected:
        confirmTitle = 'Từ chối đề thi?';
        confirmContent = `Bạn có chắc muốn từ chối đề thi "${exam.title}"?`;
        successMessage = 'Đề thi đã bị từ chối';
        break;
    }

    // Show confirmation dialog
    modal.confirm({
      title: confirmTitle,
      icon: <ExclamationCircleOutlined />,
      content: confirmContent,
      okText: 'Đồng ý',
      cancelText: 'Hủy',
      onOk: async () => {
        try {
          await updateExamStatus({
            examId: exam.id || exam.clientId,
            status: newStatus,
          });
          message.success(successMessage);
          // Refresh the exam list
          fetchExams();
        } catch (error) {
          message.error('Không thể thay đổi trạng thái đề thi');
          console.error('Error updating exam status:', error);
        }
      },
    });
  };

  const getStatusMenuItems = (record: ExamBase): ItemType[] => {
    const currentStatus =
      record.status !== undefined ? record.status : ExamStatus.Draft;

    return [
      {
        key: 'status-divider',
        type: 'divider',
      },
      {
        key: 'status-header',
        label: (
          <span className="tailwind-font-medium">Thay đổi trạng thái</span>
        ),
        disabled: true,
      },
      {
        key: 'draft',
        label: 'Chuyển về bản nháp',
        icon: <EditOutlined style={{ color: '#8c8c8c' }} />,
        onClick: () => handleChangeStatus(record, ExamStatus.Draft),
        disabled: currentStatus === ExamStatus.Draft,
        style: {
          color: currentStatus === ExamStatus.Draft ? '#d9d9d9' : '#8c8c8c',
        },
      },
      {
        key: 'submit',
        label: 'Gửi duyệt',
        icon: <CheckOutlined style={{ color: '#faad14' }} />,
        onClick: () => handleChangeStatus(record, ExamStatus.Submitted),
        disabled: currentStatus === ExamStatus.Submitted,
        style: {
          color: currentStatus === ExamStatus.Submitted ? '#d9d9d9' : '#faad14',
        },
      },
      {
        key: 'approve',
        label: 'Duyệt',
        icon: <CheckOutlined style={{ color: '#1890ff' }} />,
        onClick: () => handleChangeStatus(record, ExamStatus.Approved),
        disabled: currentStatus === ExamStatus.Approved,
        style: {
          color: currentStatus === ExamStatus.Approved ? '#d9d9d9' : '#1890ff',
        },
      },
      {
        key: 'publish',
        label: 'Công bố',
        icon: <GlobalOutlined style={{ color: '#52c41a' }} />,
        onClick: () => handleChangeStatus(record, ExamStatus.Published),
        disabled: currentStatus === ExamStatus.Published,
        style: {
          color: currentStatus === ExamStatus.Published ? '#d9d9d9' : '#52c41a',
        },
      },
      {
        key: 'reject',
        label: 'Từ chối',
        icon: <CloseOutlined style={{ color: '#f5222d' }} />,
        onClick: () => handleChangeStatus(record, ExamStatus.Rejected),
        disabled: currentStatus === ExamStatus.Rejected,
        style: {
          color: currentStatus === ExamStatus.Rejected ? '#d9d9d9' : '#f5222d',
        },
      },
    ];
  };

  const columns: ColumnsType<ExamBase> = [
    {
      title: 'Tiêu đề',
      dataIndex: 'title',
      key: 'title',
      sorter: (a, b) => a.title.localeCompare(b.title),
      render: (text) => <span className="tailwind-font-medium">{text}</span>,
    },
    {
      title: 'Môn học',
      dataIndex: 'subject',
      key: 'subject',
      sorter: (a, b) =>
        (a.subject?.name || '').localeCompare(b.subject?.name || ''),
      render: (subject) =>
        subject ? (
          <Tag color="purple" className="tailwind-px-2 tailwind-py-1">
            {subject.name}
          </Tag>
        ) : (
          <Tag color="default" className="tailwind-px-2 tailwind-py-1">
            Chưa xác định
          </Tag>
        ),
    },
    {
      title: 'Loại',
      dataIndex: 'examType',
      key: 'examType',
      filters: [
        { text: 'Mặc định', value: ExamType.Default },
        { text: 'Kiểm tra 15 phút', value: ExamType.Test15Minutes },
        { text: 'Kiểm tra 45 phút', value: ExamType.Test45Minutes },
        { text: 'Giữa học kì', value: ExamType.MidTermExam },
        { text: 'Cuối học kì', value: ExamType.FinalExam },
        { text: 'Thi thử', value: ExamType.MockExam },
        { text: 'Thi tuyển sinh', value: ExamType.QualificationExam },
        { text: 'THPT Quốc gia', value: ExamType.NationalHighSchoolExam },
      ],
      onFilter: (value, record) => record.examType === value,
      render: (type) => {
        const typeString = getExamTypeDisplayVN(type);
        let color = 'blue';

        switch (type) {
          case ExamType.FinalExam:
            color = 'gold';
            break;
          case ExamType.MidTermExam:
            color = 'green';
            break;
          case ExamType.MockExam:
            color = 'purple';
            break;
          case ExamType.NationalHighSchoolExam:
            color = 'magenta';
            break;
          case ExamType.QualificationExam:
            color = 'orange';
            break;
          case ExamType.Test15Minutes:
            color = 'cyan';
            break;
          case ExamType.Test45Minutes:
            color = 'blue';
            break;
          case ExamType.Default:
          default:
            color = 'blue';
        }

        return <Tag color={color}>{typeString}</Tag>;
      },
    },
    {
      title: 'Nguồn gốc',
      dataIndex: 'sourceType',
      key: 'sourceType',
      filters: [
        { text: 'Mặc định', value: ExamSourceType.Default },
        { text: 'Ngân hàng đề thi', value: ExamSourceType.Banked },
      ],
      onFilter: (value, record) => record.sourceType === value,
      render: (sourceType) => {
        const sourceTypeString = getExamSourceTypeString(sourceType);
        const color = getExamSourceTypeColor(sourceType);

        return <Tag color={color}>{sourceTypeString}</Tag>;
      },
    },
    {
      title: 'Khối lớp',
      dataIndex: 'grade',
      key: 'grade',
      sorter: (a, b) =>
        (a.grade?.name || '').localeCompare(b.grade?.name || ''),
      render: (grade) =>
        grade ? (
          <Tag color="blue" className="tailwind-px-2 tailwind-py-1">
            {grade.name}
          </Tag>
        ) : (
          <Tag color="default" className="tailwind-px-2 tailwind-py-1">
            Not assigned
          </Tag>
        ),
    },
    {
      title: 'Câu hỏi',
      key: 'questions',
      render: (_, record) => {
        return countTotalQuestions(record);
      },
      sorter: (a, b) => {
        return countTotalQuestions(a) - countTotalQuestions(b);
      },
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'creationTime',
      key: 'creationTime',
      sorter: (a, b) => {
        const dateA = a.creationTime ? new Date(a.creationTime).getTime() : 0;
        const dateB = b.creationTime ? new Date(b.creationTime).getTime() : 0;
        return dateA - dateB;
      },
      render: (creationTime) =>
        creationTime
          ? new Date(creationTime).toLocaleDateString('vi-VN')
          : 'N/A',
    },
    {
      title: 'Trạng thái',
      key: 'status',
      render: (_, record: any) => {
        // Use any because status might not be in the current model
        const status =
          record.status !== undefined ? record.status : ExamStatus.Draft;
        const statusString = getExamStatusDisplayVN(status);
        const color = getExamStatusColor(status);
        const icon = getExamStatusIcon(status);

        return (
          <Tag color={color} icon={icon}>
            {statusString}
          </Tag>
        );
      },
      filters: [
        { text: 'Soạn thảo', value: ExamStatus.Draft },
        { text: 'Đang chờ duyệt', value: ExamStatus.Submitted },
        { text: 'Đã duyệt', value: ExamStatus.Approved },
        { text: 'Công bố', value: ExamStatus.Published },
        { text: 'Từ chối', value: ExamStatus.Rejected },
      ],
      onFilter: (value, record: any) => {
        return record.status === value;
      },
    },
    {
      title: 'Hành động',
      key: 'actions',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="Chỉnh sửa">
            <Button
              type="primary"
              shape="circle"
              icon={<EditOutlined />}
              onClick={() => onViewDetails && onViewDetails(record)}
              className="tailwind-bg-blue-500"
            />
          </Tooltip>
          <Tooltip title="Xem đề thi">
            <Button
              type="default"
              shape="circle"
              icon={<EyeOutlined />}
              onClick={() => onPreviewExam && onPreviewExam(record)}
              className="tailwind-bg-green-50 tailwind-border-green-500 tailwind-text-green-600"
            />
          </Tooltip>
          <Dropdown
            menu={{
              items: [
                {
                  key: 'edit',
                  label: 'Sửa',
                  icon: <EditOutlined />,
                  onClick: () => onViewDetails && onViewDetails(record),
                },
                {
                  key: 'duplicate',
                  label: 'Sao chép',
                  icon: <CopyOutlined />,
                  onClick: () => handleDuplicateExam(record),
                },
                {
                  key: 'export',
                  label: 'Xuất',
                  icon: <ExportOutlined />,
                },
                ...getStatusMenuItems(record),
                {
                  type: 'divider',
                },
                {
                  key: 'delete',
                  label: <span className="tailwind-text-red-500">Xóa</span>,
                  onClick: () => handleDeleteExam(record),
                  icon: <DeleteOutlined className="tailwind-text-red-500" />,
                },
              ],
            }}
            trigger={['click']}
            placement="bottomRight"
          >
            <Button
              type="text"
              icon={<MoreOutlined />}
              className="tailwind-border-none"
            />
          </Dropdown>
        </Space>
      ),
    },
  ];

  // Kiểm tra xem có bộ lọc nâng cao nào đang được áp dụng không
  const hasAdvancedFilters = () => {
    if (!form) return false;

    try {
      const values = form.getFieldsValue();
      return Object.values(values).some(
        (value) =>
          value !== undefined &&
          value !== null &&
          value !== '' &&
          (Array.isArray(value) ? value.length > 0 : true)
      );
    } catch (error) {
      // Nếu có lỗi khi lấy giá trị form, trả về false
      return false;
    }
  };

  // Kiểm tra xem có bất kỳ bộ lọc nào đang được áp dụng không (cơ bản hoặc nâng cao)
  const hasAnyFilters = () => {
    return searchText.trim() !== '' || hasAdvancedFilters();
  };

  // Lấy nhãn hiển thị cho từng loại bộ lọc
  const getFilterLabel = (key: string) => {
    switch (key) {
      case 'examType':
        return 'Loại đề thi';
      case 'status':
        return 'Trạng thái';
      case 'subjectId':
        return 'Môn học';
      case 'gradeId':
        return 'Khối lớp';
      case 'dateRange':
        return 'Thời gian';
      default:
        return key;
    }
  };

  // Hàm tạo màu ngẫu nhiên cho tag
  const getRandomTagColor = (key: string): string => {
    const colors = [
      'magenta',
      'red',
      'volcano',
      'orange',
      'gold',
      'lime',
      'green',
      'cyan',
      'blue',
      'geekblue',
      'purple',
    ];

    // Sử dụng key để tạo màu nhất quán cho cùng một loại filter
    const hashCode = key
      .split('')
      .reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return colors[hashCode % colors.length];
  };

  // Xử lý xóa một bộ lọc cụ thể
  const handleRemoveFilter = (key: string) => {
    form.setFieldsValue({ [key]: undefined });
    handleAdvancedSearch(form.getFieldsValue());
  };

  return (
    <div className="tailwind-w-full">
      {/* Tìm kiếm cơ bản và nâng cao */}
      <div className="">
        <div className="tailwind-flex tailwind-gap-2 tailwind-mb-4">
          <Search
            placeholder="Tìm kiếm đề thi theo tiêu đề"
            allowClear
            enterButton={<SearchOutlined />}
            size="middle"
            onSearch={handleSearch}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            className="tailwind-rounded tailwind-w-full"
          />
          <Button
            icon={<FilterOutlined />}
            onClick={() => {
              // Gán giá trị tìm kiếm cơ bản vào trường tiêu đề khi mở modal
              form.setFieldValue('title', searchText);
              setAdvancedSearchModalVisible(true);
            }}
            type={hasAdvancedFilters() ? 'primary' : 'default'}
          >
            Nâng cao
          </Button>
        </div>

        {/* Hiển thị thông báo khi có bộ lọc đang được áp dụng */}
        {hasAnyFilters() && (
          <Alert
            type="info"
            showIcon
            icon={<FilterOutlined />}
            description={
              <div className="tailwind-flex tailwind-flex-wrap tailwind-gap-2">
                {searchText.trim() !== '' && (
                  <Tag
                    closable
                    color={getRandomTagColor('searchText')}
                    onClose={() => {
                      setSearchText('');
                      form.setFieldValue('title', '');
                      handleSearch('');
                    }}
                  >
                    {`Tiêu đề: ${searchText}`}
                  </Tag>
                )}
                {Object.entries(form.getFieldsValue()).map(([key, value]) => {
                  if (
                    value !== undefined &&
                    value !== null &&
                    value !== '' &&
                    key !== 'dateRange' &&
                    key !== 'title' // Bỏ qua trường title vì đã hiển thị ở trên
                  ) {
                    let displayValue = value;

                    // Hiển thị tên thay vì ID cho các trường select
                    if (key === 'examType') {
                      displayValue = getExamTypeDisplayVN(Number(value));
                    } else if (key === 'status') {
                      displayValue = getExamStatusDisplayVN(Number(value));
                    } else if (key === 'subjectId') {
                      const subject = subjects.find((s) => s.id === value);
                      displayValue = subject ? subject.name : value;
                    } else if (key === 'gradeId') {
                      const grade = grades.find((g) => g.id === value);
                      displayValue = grade ? grade.name : value;
                    }

                    return (
                      <Tag
                        key={key}
                        closable
                        color={getRandomTagColor(key)}
                        onClose={() => handleRemoveFilter(key)}
                      >
                        {`${getFilterLabel(key)}: ${displayValue}`}
                      </Tag>
                    );
                  }
                  return null;
                })}
                {form.getFieldValue('dateRange') && (
                  <Tag
                    closable
                    color={getRandomTagColor('dateRange')}
                    onClose={() => handleRemoveFilter('dateRange')}
                  >
                    Thời gian:{' '}
                    {form.getFieldValue('dateRange')[0].format('DD/MM/YYYY')} -{' '}
                    {form.getFieldValue('dateRange')[1].format('DD/MM/YYYY')}
                  </Tag>
                )}
                <Button type="link" size="small" onClick={handleResetSearch}>
                  Xóa tất cả
                </Button>
              </div>
            }
            className="tailwind-mb-4"
          />
        )}

        {/* Modal tìm kiếm nâng cao */}
        <Modal
          title={
            <div className="tailwind-flex tailwind-items-center">
              <FilterOutlined className="tailwind-mr-2" />
              <span>Tìm kiếm nâng cao</span>
            </div>
          }
          open={advancedSearchModalVisible}
          onCancel={() => setAdvancedSearchModalVisible(false)}
          width={700}
          footer={[
            <Button key="reset" onClick={handleResetSearch}>
              Đặt lại
            </Button>,
            <Button
              key="cancel"
              onClick={() => setAdvancedSearchModalVisible(false)}
            >
              Hủy
            </Button>,
            <Button
              key="apply"
              type="primary"
              onClick={() => {
                const values = form.getFieldsValue();
                // Đồng bộ tiêu đề tìm kiếm nâng cao với tìm kiếm cơ bản khi nhấn nút "Áp dụng"
                if (values.title !== undefined) {
                  setSearchText(values.title);
                }
                handleAdvancedSearch(values);
                setAdvancedSearchModalVisible(false);
              }}
            >
              Áp dụng
            </Button>,
          ]}
        >
          <Form form={form} layout="vertical" onFinish={handleAdvancedSearch}>
            <div className="tailwind-grid tailwind-grid-cols-1 md:tailwind-grid-cols-2 tailwind-gap-4">
              <Form.Item
                name="title"
                label="Tiêu đề"
                className="md:tailwind-col-span-2"
              >
                <Input placeholder="Nhập tiêu đề đề thi" allowClear />
              </Form.Item>

              <Form.Item name="examType" label="Loại đề thi">
                <Select allowClear placeholder="Chọn loại đề thi">
                  {Object.keys(ExamType)
                    .filter((key) => !isNaN(Number(key)))
                    .map((key) => (
                      <Option key={key} value={Number(key)}>
                        {getExamTypeDisplayVN(Number(key))}
                      </Option>
                    ))}
                </Select>
              </Form.Item>

              <Form.Item name="status" label="Trạng thái">
                <Select allowClear placeholder="Chọn trạng thái">
                  {Object.keys(ExamStatus)
                    .filter((key) => !isNaN(Number(key)))
                    .map((key) => (
                      <Option key={key} value={Number(key)}>
                        {getExamStatusDisplayVN(Number(key))}
                      </Option>
                    ))}
                </Select>
              </Form.Item>

              <Form.Item name="subjectId" label="Môn học">
                <Select allowClear placeholder="Chọn môn học">
                  {subjects.map((subject) => (
                    <Option key={subject.id} value={subject.id}>
                      {subject.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item name="gradeId" label="Khối lớp">
                <Select allowClear placeholder="Chọn khối lớp">
                  {grades.map((grade) => (
                    <Option key={grade.id} value={grade.id}>
                      {grade.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="dateRange"
                label="Thời gian tạo"
                className="md:tailwind-col-span-2"
              >
                <RangePicker
                  style={{ width: '100%' }}
                  onChange={(dates) => {
                    if (dates) {
                      form.setFieldsValue({
                        startDate: dates[0],
                        endDate: dates[1],
                      });
                    }
                  }}
                />
              </Form.Item>
            </div>
          </Form>
        </Modal>
      </div>

      {/* Bảng dữ liệu */}
      <Table
        columns={columns}
        rowKey={(record) => record.clientId || record.id || ''}
        dataSource={examData}
        pagination={{
          ...tableParams.pagination,
          total: props.totalCount,
          showTotal: (total) => `Tổng số: ${total} đề thi`,
        }}
        loading={loading}
        onChange={handleTableChange}
      />
    </div>
  );
};

const mapStateToProps = ({
  examDataManager,
}: {
  examDataManager: IExamDataManagerState;
}): IExamListStateToProps => {
  return {
    exams: examDataManager.exams,
    totalCount: examDataManager.totalCount,
    currentPage: examDataManager.currentPage,
    pageSize: examDataManager.pageSize,
    loading: examDataManager.loading,
    error: examDataManager.error,
  };
};

const mapDispatchToProps = (dispatch: any): IExamListDispatchToState => {
  return {
    fetchExams: (params) => dispatch(fetchExams(params)),
    handlePageChange: (page, pageSize) =>
      dispatch(handlePageChange(page, pageSize)),
    handleFilterChange: (filters) => dispatch(handleFilterChange(filters)),
    handleSortChange: (sorting) => dispatch(handleSortChange(sorting)),
    deleteExam: (examId) => dispatch(deleteExam(examId)),
    updateExamStatus: (data) => dispatch(updateExamStatus(data)),
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(ExamList);
