import React from 'react';
import {
  <PERSON>rowserRouter,
  Routes,
  Route,
  Navigate,
  useNavigate,
  useParams,
} from 'react-router-dom';
import { Button } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
// import LoadingScreen from '../components/common/Loading/LoadingScreen';
import { ExamBase } from '../interfaces/exams/examBase';
import BreadcrumbNav from '../components/common/breadcrumbNav/breadcrumbNav';
import Title from 'antd/es/typography/Title';

// Import non-lazy components directly
import ExamList from '../components/exams/examList/ExamList';
import ExamPreview from '../components/exams/examPreview/ExamPreview';
import ExamEditorEnhancedContainer from '../components/exams/examEditor/ExamEditorEnhancedContainer';

// Page components
const ExamListPage: React.FC = () => {
  const navigate = useNavigate();

  const handleViewDetails = (exam: ExamBase) => {
    navigate(`/ExamManagement/edit/${exam.clientId}`);
  };

  const handlePreviewExam = (exam: ExamBase) => {
    navigate(`/ExamManagement/preview/${exam.clientId}`);
  };

  return (
    <div className="tailwind-bg-white tailwind-rounded-lg tailwind-shadow-sm">
      <BreadcrumbNav
        items={[{ title: 'Danh sách đề thi' }]}
        className="tailwind-px-4"
      />
      <div className="tailwind-bg-white tailwind-p-6 tailwind-rounded-lg tailwind-shadow-sm">
        <div className="tailwind-flex tailwind-justify-between tailwind-items-center tailwind-mb-6">
          <Title className="tailwind-mb-0 tailwind-mr-4"></Title>

          <Button
            type="primary"
            onClick={() => navigate('/ExamManagement/create')}
            className="tailwind-bg-blue-600"
            icon={<PlusOutlined />}
          >
            Tạo đề thi mới
          </Button>
        </div>
        <ExamList
          onViewDetails={handleViewDetails}
          onPreviewExam={handlePreviewExam}
        />
      </div>
    </div>
  );
};

const ExamCreatePage: React.FC<{ isCreate: boolean }> = ({ isCreate }) => {
  const navigate = useNavigate();
  const { examId } = useParams();

  return (
    <div className="tailwind-bg-white tailwind-rounded-lg tailwind-shadow-sm">
      <BreadcrumbNav
        items={[
          { title: 'Danh sách đề thi', path: '/ExamManagement' },
          { title: isCreate ? 'Tạo đề thi' : 'Sửa đề thi' },
        ]}
        className="tailwind-px-4"
      />
      <ExamEditorEnhancedContainer
        examId={isCreate ? undefined : examId}
        onCancel={() => navigate('/ExamManagement')}
      />
    </div>
  );
};

const ExamEditorWrapper: React.FC = () => {
  const { examId } = useParams();
  return (
    <div className="tailwind-bg-white tailwind-rounded-lg tailwind-shadow-sm">
      <BreadcrumbNav
        items={[
          { title: 'Danh sách đề thi', path: '/ExamManagement' },
          { title: 'Sửa đề thi' },
        ]}
        className="tailwind-px-4"
      />
      <ExamEditorEnhancedContainer examId={examId} />
    </div>
  );
};

const ExamPreviewPage: React.FC = () => {
  const { examId } = useParams();
  return (
    <div className="tailwind-bg-white tailwind-rounded-lg tailwind-shadow-sm">
      <ExamPreview examId={examId} mode="admin" backUrl="/ExamManagement" />
    </div>
  );
};

// Main router component
const ExamManagementRouter: React.FC = () => {
  // Get the current base path based on where the app is running
  const getBasename = () => {
    // For development with Vite, we might be running at the root
    if (import.meta.env.DEV) {
      return '/';
    }

    // In production, determine the basename from the current path
    const currentPath = window.location.pathname;

    // If we're on exams.html, we don't need a basename
    if (currentPath.includes('exams.html')) {
      return '/';
    }

    if (import.meta.env.PROD) {
      return '/';
    }
    // Default basename for the exam module
    return '/ExamManagement';
  };

  return (
    <BrowserRouter basename={getBasename()}>
      <Routes>
        <Route
          path="/ExamManagement/edit/:examId"
          element={<ExamEditorWrapper />}
        />
        <Route
          path="/ExamManagement/preview/:examId"
          element={<ExamPreviewPage />}
        />
        <Route
          path="/ExamManagement/create"
          element={<ExamCreatePage isCreate={true} />}
        />
        <Route path="/ExamManagement" element={<ExamListPage />} />
        <Route path="/" element={<Navigate to="/ExamManagement" replace />} />
        <Route path="*" element={<ExamListPage />} />
      </Routes>
    </BrowserRouter>
  );
};

export default ExamManagementRouter;
