using System;
using System.Threading.Tasks;
using EdTech.Study.Enum;
using EdTech.Study.Exams.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace EdTech.Study.Exams
{
    public interface IExamAppService : ICrudAppService<
        ExamDto,
        Guid,
        GetExamListDto,
        CreateUpdateExamDto,
        CreateUpdateExamDto>
    {
        Task<PagedResultDto<ExamDto>> GetListExamAsync(GetExamListDto input);
        Task<ExamDto> CreateOrUpdateAsync(CreateUpdateExamDto input);
        Task UpdateExamStatusAsync(Guid examId, UpdateStatusRequest updateStatusRequest);

    }
}