import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import userApi, { UserParams } from '../../../api/userApi';
import { IUserInfo } from '../../../interfaces/users/user';

// Define the state interface
export interface UserState {
  users?: IUserInfo[];
  currentUser: IUserInfo | null;
  loading: boolean;
  error: string | null;
  totalCount: number;
  currentPage: number;
  pageSize: number;
}

// Initial state
const initialState: UserState = {
  currentUser: null,
  loading: false,
  error: null,
  totalCount: 0,
  currentPage: 1,
  pageSize: 10,
};

// Create thunk actions
export const fetchUsers = createAsyncThunk(
  'user/fetchUsers',
  async (params: UserParams = {}, { rejectWithValue }) => {
    try {
      const response = await userApi.getUsers(params);
      if (response.success) {
        return {
          data: response.data,
          total: response.total,
        };
      }
      return rejectWithValue(response.error || 'Failed to fetch users');
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Unknown error'
      );
    }
  }
);

export const fetchUserById = createAsyncThunk(
  'user/fetchUserById',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await userApi.getUserById(id);
      if (response.success) {
        return response.data;
      }
      return rejectWithValue(response.error || 'Failed to fetch user');
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Unknown error'
      );
    }
  }
);

// Create the slice
const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setCurrentPage: (state, action: PayloadAction<number>) => {
      state.currentPage = action.payload;
    },
    setPageSize: (state, action: PayloadAction<number>) => {
      state.pageSize = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // fetchUsers
      .addCase(fetchUsers.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUsers.fulfilled, (state, action) => {
        state.loading = false;
        state.users = action.payload.data;
        state.totalCount = action.payload.total;
      })
      .addCase(fetchUsers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // fetchUserById
      .addCase(fetchUserById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUserById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentUser = action.payload;
      })
      .addCase(fetchUserById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

// Export actions and reducer
export const { setCurrentPage, setPageSize, clearError } = userSlice.actions;
export default userSlice;