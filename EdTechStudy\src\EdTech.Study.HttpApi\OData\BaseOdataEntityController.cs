﻿using Microsoft.AspNetCore.OData.Formatter;
using Microsoft.AspNetCore.OData.Query;
using Microsoft.AspNetCore.OData.Results;
using Microsoft.AspNetCore.OData.Routing.Controllers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace EdTech.Study.OData
{
    public class BaseOdataEntityController<T> : ODataController where T : Entity<Guid>
    {
        protected readonly IRepository<T> _repository;

        public BaseOdataEntityController(IRepository<T> repository)
        {
            _repository = repository;
        }

        [EnableQuery]
        public virtual Task<IQueryable<T>> Get()
        {
            return _repository.GetQueryableAsync();
        }

        [EnableQuery]
        public async Task<SingleResult<T>> Get([FromODataUri] Guid key)
        {
            var query = (await _repository.GetQueryableAsync()).Where(c => c.Id.Equals(key)).Take(1);
            return SingleResult.Create(query);
        }
    }
}
