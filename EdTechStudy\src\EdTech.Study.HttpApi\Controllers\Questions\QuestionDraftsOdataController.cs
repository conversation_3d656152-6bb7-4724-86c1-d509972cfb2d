﻿using EdTech.Study.OData;
using EdTech.Study.Questions;
using EdTech.Study.Questions.Dtos;
using EdTech.Study.Questions.Request;
using EdTech.Study.Subject;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OData.Formatter;
using Microsoft.AspNetCore.OData.Query;
using Microsoft.AspNetCore.OData.Results;
using Microsoft.AspNetCore.OData.Routing.Controllers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Linq;
using Volo.Abp.ObjectMapping;
using static EdTech.Study.Permissions.QuestionsPermissions;

namespace EdTech.Study.Controllers.Questions
{
    [Route("api/odata/questionDrafts")]
    public class QuestionDraftsOdataController : ODataController
    {
        private readonly IRepository<QuestionDraftEntity, Guid> _repository;
        private readonly IQuestionDraftAppService _questionDraftAppService;
        private readonly IQuestionOptionDraftAppService _questionOptionDraftAppService;
        private readonly ISubjectAppService _subjectAppService;
        private readonly IObjectMapper _objectMapper;
        public QuestionDraftsOdataController(IRepository<QuestionDraftEntity, Guid> repository
            , IQuestionDraftAppService questionDraftAppService
            , IQuestionOptionDraftAppService questionOptionDraftAppService
            , IObjectMapper objectMapper 
            , ISubjectAppService subjectAppService)
        {
            _repository = repository;
            _questionDraftAppService = questionDraftAppService;
            _questionOptionDraftAppService = questionOptionDraftAppService;
            _objectMapper = objectMapper;
            _subjectAppService = subjectAppService;
        }

        [EnableQuery]
        [HttpGet()]
        public virtual async Task<IActionResult> Get()
        {
            var queryable = await _repository.GetQueryableAsync();
            return Ok(queryable);
        }

        [HttpGet("paginationInfo")]
        public async Task<IActionResult> GetPaginationInfo()
        {
            var totalCount = await _questionDraftAppService.GetPaginationInfo();
            var subjectPagedDatas = await _subjectAppService.GetListAsync(new Volo.Abp.Application.Dtos.PagedAndSortedResultRequestDto()
            {
                SkipCount = 0
            });
            var subjects = subjectPagedDatas.Items.ToList();

            return Ok(new
            {
                TotalCount = totalCount,
                Subjects = subjects
            });
        }

        [HttpPost("update-list-question")]
        [Consumes("application/json")]
        public async Task<IActionResult> UpdateQuestions([FromBody] UpdateQuestionsRequest updateQuestionsRequest)
        {
            if (updateQuestionsRequest == null)
            {
                return BadRequest("Request body cannot be null");
            }

            var updateQuestion = updateQuestionsRequest.Questions.ToList();
            var options = new Dictionary<Guid, List<CreateUpdateQuestionOptionDraftDto>>();

            foreach (var item in updateQuestion)
            {
                // Initialize the list for this key before using it
                if (!options.ContainsKey(item.Id))
                {
                    options[item.Id] = new List<CreateUpdateQuestionOptionDraftDto>();
                }

                // Now you can safely add items to the list
                options[item.Id].AddRange(item.Options.ToList());
                item.Options = []; // Using collection expression (C# 12 feature)
            }

            var res = await _questionDraftAppService.UpdateListAsync(updateQuestionsRequest.Questions);

            foreach (var (questionId, questionOptions) in options)
            {
                await _questionOptionDraftAppService.UpdateQuestionDraftOptions(questionId,
                    _objectMapper.Map<List<CreateUpdateQuestionOptionDraftDto>, List<QuestionOptionDraftDto>>(questionOptions));
            }

            return Ok(new
            {
                Data = res
            });
        }

        [HttpDelete("delete-list-question")]
        public async Task<IActionResult> DeleteQuestions([FromBody] DeleteQuestionsRequest deleteQuestionsRequest)
        {
            var ids = deleteQuestionsRequest.Ids;
            foreach (var questionId in ids)
            {
                await _questionOptionDraftAppService.DeleteByQuestionDraftId(questionId);
            }
            foreach (var questionId in ids)
            {
                await _questionDraftAppService.DeleteAsync(questionId);
            }
            
            return NoContent();
        }
    }
}
