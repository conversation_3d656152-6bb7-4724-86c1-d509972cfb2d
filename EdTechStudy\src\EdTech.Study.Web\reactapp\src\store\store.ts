import { Action, ThunkAction, configureStore } from '@reduxjs/toolkit';
import { EdTechRenderTreeDataSlice } from './slices/AppSlices/EdTechRenderDataSlice';
import { EdComponentParamsSlice } from './slices/AppSlices/EdComponentParamsSlice';
import { AppConfigSlice } from './slices/AppConfig/AppConfigSlice';
import examDataManagerSlice from './slices/ExamSlices/examDataManagerSlice';
import userSlice from './slices/UserSlices/userSlice';
import practiceExamSlice from './slices/ExamSlices/practiceExamSlice';
const rootEdTechReducer = {
  edTechRenderTreeData: EdTechRenderTreeDataSlice.reducer,
  edComponentParams: EdComponentParamsSlice.reducer,
  appConfig: AppConfigSlice.reducer,
  examDataManager: examDataManagerSlice,
  practiceExam: practiceExamSlice,
  userDataManager: userSlice.reducer,
};
export const storeEdTech = configureStore({
  reducer: rootEdTechReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
  // devTools: import.meta.env.MODE !== 'production',
});
// Infer the `RootState` and `AppDispatch` types from the store itself
export type EdTechRootState = ReturnType<typeof storeEdTech.getState>;
// Inferred type: {posts: PostsState, comments: CommentsState, users: UsersState}
export type EdTechAppDispatch = typeof storeEdTech.dispatch;
export type AppThunk<ReturnType = void> = ThunkAction<
  ReturnType,
  EdTechRootState,
  unknown,
  Action<string>
>;
