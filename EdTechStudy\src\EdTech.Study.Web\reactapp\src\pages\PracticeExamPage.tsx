import ReactDOM from 'react-dom/client';
import { lazy, Suspense } from 'react';
import { Provider } from 'react-redux';
import LoadingScreen from '../components/common/Loading/LoadingScreen';
import { questionStore } from '../store/questionStore';
import '../index.css';
import '../styles/modal-fixes.css';
import { CombinedThemeProvider } from '../providers/theme';
import { MathJaxProvider } from '../components/common/MathJax/MathJaxWrapper';
import QuestionProviderLayout from '../providers/Questions/QuestionProvider';
import { registerLicenseSyncfusionBase } from '../utils/syncfusion-license';
import { useDevLicenseSyncfusion } from './syncfusion-license-custom';
const PracticeExamRouter = lazy(() => import('../routing/PracticeExamRouting'));

registerLicenseSyncfusionBase();
useDevLicenseSyncfusion();

export const PracticeExamPage = () => {
  return (
    <Provider store={questionStore}>
      <CombinedThemeProvider>
        <Suspense fallback={<LoadingScreen />}>
          <MathJaxProvider>
            <QuestionProviderLayout>
              <PracticeExamRouter />
            </QuestionProviderLayout>
          </MathJaxProvider>
        </Suspense>
      </CombinedThemeProvider>
    </Provider>
  );
};

const initApp = () => {
  const rootElement = document.getElementById('PracticeExamPage');

  if (rootElement) {
    const root = ReactDOM.createRoot(rootElement);
    root.render(<PracticeExamPage />);
  } else {
    console.error('Cannot find root element with id "PracticeExamPage"');
  }
};

initApp();
