﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EdTech.Study.Migrations
{
    /// <inheritdoc />
    public partial class SectionGroupQuestions : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "StudySectionGroupQuestions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "char(36)", nullable: false, collation: "ascii_general_ci"),
                    SectionId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "ascii_general_ci"),
                    GroupQuestionId = table.Column<Guid>(type: "char(36)", nullable: false, collation: "ascii_general_ci"),
                    Order = table.Column<int>(type: "int", nullable: false),
                    SyncGroupQuestion = table.Column<bool>(type: "tinyint(1)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StudySectionGroupQuestions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_StudySectionGroupQuestions_StudyGroupQuestions_GroupQuestion~",
                        column: x => x.GroupQuestionId,
                        principalTable: "StudyGroupQuestions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_StudySectionGroupQuestions_StudySections_SectionId",
                        column: x => x.SectionId,
                        principalTable: "StudySections",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_StudySectionGroupQuestions_GroupQuestionId",
                table: "StudySectionGroupQuestions",
                column: "GroupQuestionId");

            migrationBuilder.CreateIndex(
                name: "IX_StudySectionGroupQuestions_SectionId",
                table: "StudySectionGroupQuestions",
                column: "SectionId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "StudySectionGroupQuestions");
        }
    }
}
