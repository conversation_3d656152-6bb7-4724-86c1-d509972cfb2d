﻿using EdTech.Study.Files;
using EdTech.Study.Grade;
using EdTech.Study.Lessons;
using EdTech.Study.Subject;
using EdTech.Study.Menus;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Data;
using Volo.Abp.EntityFrameworkCore;

namespace EdTech.Study.EntityFrameworkCore;

[ConnectionStringName(StudyDbProperties.ConnectionStringName)]
public interface IStudyDbContext : IStudyCategoryDbContext, IEfCoreDbContext
{
    /* Add DbSet for each Aggregate Root here. Example:
     * DbSet<Question> Questions { get; }
     */

    DbSet<LessonConfigEntity> LessonConfigs { get; }
    DbSet<DefaultLessonEntity> DefaultLessons { get; }
    DbSet<MenuEntity> Menus { get; }
    DbSet<TempFileEntity> TempFiles { get; }
}
