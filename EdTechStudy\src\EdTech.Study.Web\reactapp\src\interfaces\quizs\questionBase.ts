import {
  ContentFormatType,
  ExamSourceType,
  ExamStatus,
} from '../exams/examEnums';
import React, { createContext } from 'react';
import practiceLocalization from '../../components/quizs/localization';
import { Subject } from '../lessons/subject';
import { LessonGrade } from '../lessons/lessonGrade';
import { ExamGroupQuestion } from '../exams/examBase';
export interface BaseQuestion {
  id?: string;
  clientId: string;
  questionId?: string; // id của câu hỏi trong db
  lastSyncQuestionId?: string; // id đồng bộ câu hỏi trong db lần cuối
  order?: number;
  score?: number;
  type: string;
  title: string;
  content?: string;
  points?: number;
  isCompleted?: boolean;
  status?: string;
  statusEntity?: ExamStatus;
  statusEntityString?: string;
  images?: { url: string; alt?: string }[];
  options?: BaseAnswer[];
  userSelect?: BaseAnswer | BaseAnswer[];
  subjectId?: string;
  subject?: Subject;
  gradeId?: string;
  grade?: LessonGrade;
  tags?: string | string[];
  topics?: string | string[];
  creationTime?: string;
  lastModificationTime?: string;
  difficulty: number;
  syncQuestion: boolean;
  path?: string;
  questionType?: QuestionType;
  sourceType?: ExamSourceType;
  [key: string]: any;
}

export interface BaseAnswer {
  id?: string;
  clientId: string;
  content: string;
  contentFormat: ContentFormatType | string;
  isCorrect: boolean;
  order: number;
  explanation?: string;
  score?: number;
}

export enum QuestionType {
  SingleChoice = 0,
  MultipleChoice = 1,
  Essay = 2, // Tự luận (gộp cả Essay và ShortAnswer)
  Matching = 3,
  FillInBlank = 4,
  TrueFalse = 5,
  Ordering = 6,
  // ShortAnswer = 7,
}

export const getQuestionTypeFromString = (questionType: string) => {
  switch (questionType.toLocaleLowerCase()) {
    case practiceLocalization.quiz:
    case practiceLocalization.singlechoice:
      return QuestionType.SingleChoice;
    case practiceLocalization.multiselect:
    case practiceLocalization.multiplechoice:
      return QuestionType.MultipleChoice;
    case practiceLocalization.matching:
      return QuestionType.Matching;
    case practiceLocalization.fillblanks:
      return QuestionType.FillInBlank;
    case practiceLocalization.essay:
      return QuestionType.Essay;
    default:
      return QuestionType.SingleChoice; // Default fallback
  }
};

export const getExamStatusFromString = (status: string) => {
  switch (status) {
    case 'Draft':
      return ExamStatus.Draft;
    case 'Published':
      return ExamStatus.Published;
    case 'Approved':
      return ExamStatus.Approved;
    case 'Submitted':
      return ExamStatus.Submitted;
    case 'Rejected':
      return ExamStatus.Rejected;
    default:
      return ExamStatus.Draft; // Default fallback
  }
};

// Interface for Quiz questions
export interface QuizQuestion extends BaseQuestion {
  type: 'quiz';
  content: string;
  explanation?: string;
  options?: QuizAnswer[];
  userSelect?: QuizAnswer;
  points?: number;
}
// Interface for Quiz answers
export interface QuizAnswer extends BaseAnswer {
  isCorrect: boolean;
}

// Interface for Matching questions
export interface MatchingQuestion extends BaseQuestion {
  type: 'matching';
  leftItems?: MatchingItemType[];
  rightItems?: MatchingItemType[];
  shuffleItems?: boolean;
  isCompleted?: boolean;
  options: MatchingQuestionAnswer[];
  userSelect?: MatchingQuestionAnswer[];
}

export interface MatchingQuestionAnswer extends BaseAnswer {
  left: string;
  right: string;
}

// Interface for Matching items
export interface MatchingItemType {
  clientId: string;
  id?: string;
  content: string | React.ReactNode;
  type: 'text' | 'image';
  matchId: string;
}

// Kết hợp các kiểu câu hỏi
// export type Question = BaseQuestion;

export interface QuestionComponentOption {
  hideSaveButton?: boolean;
  hideDeleteButton?: boolean;
  hideFeedback?: boolean;
}

export abstract class QuestionComponentBaseProps {
  id?: string;
  question!: BaseQuestion; // Required property
  questionIndex?: number;
  configMode?: boolean;
  disabled?: boolean;
  showResult?: boolean;
  isMarked?: boolean; // Trạng thái đánh dấu (flag) của câu hỏi
  onToggleMark?: (questionId: string) => void; // Sự kiện khi người dùng nhấn vào icon flag để đánh dấu câu hỏi
  htmlAttributes?: React.HTMLAttributes<HTMLDivElement>;
  abstract onComplete?(
    questionId: string,
    answer?: BaseAnswer | BaseAnswer[]
  ): void;
  options?: QuestionComponentOption;
}

export interface QuestionResultComponentBaseProps {
  question: BaseQuestion;
  userSelect?: BaseAnswer | BaseAnswer[];
  isCorrect?: boolean | null;
  showFeedback?: boolean;
  questionIndex?: number;
}

export type EngineQuestionComponent<T extends QuestionComponentBaseProps> =
  React.FC<T> & {
    ConfigMode: React.FC<any>;
    PreviewMode: React.FC<any>;
  };

export interface PracticeEngineJsonData {
  id: string;
  question: BaseQuestion[];
}

export const KEY_PRACTICE_DEFAULT = 'practice-engine-test-id';
export const getKeyPracticeData = (id: string) => {
  return `data-${id}`;
};

export interface IPracticeEngineContext {
  handleEditQuestion: (
    update: Partial<BaseQuestion> & {
      clientId: string;
      parentId: string;
    }
  ) => any;
  handleDeleteQuestion: (id: string) => any;
  handleChangePosition: (id: string) => any;
  handleSaveAllQuestions?: (id: string, questions: BaseQuestion[]) => any;
  handleToggleFullscreen: () => any;
  handleMarkQuestion?: (id: string, marked: boolean) => any;
  isFullscreen: boolean;
  markedIds?: string[];
  handlePendingState?: (pending: boolean) => any;
  handleUpdateGroupQuestion: (
    groupQuestion: Partial<ExamGroupQuestion> & {
      clientId: string;
    }
  ) => any;
  handleDeleteGroupQuestion: (id: string) => any;
}
export const PracticeEngineContext = createContext<IPracticeEngineContext>({
  handleEditQuestion: () => {},
  handleDeleteQuestion: (_: string) => {},
  handleChangePosition: (_: string) => {},
  handleSaveAllQuestions: (_id: string, _questions: BaseQuestion[]) => {},
  handleToggleFullscreen: () => {},
  handleMarkQuestion: (_id: string, _marked: boolean) => {},
  handlePendingState: (_pending: boolean) => {},
  isFullscreen: false,
  markedIds: [],
  handleUpdateGroupQuestion: (
    _groupQuestion: Partial<ExamGroupQuestion> & {
      clientId: string;
    }
  ) => {},
  handleDeleteGroupQuestion: (_id: string) => {},
});

export type IQuestionComponentInfo = {
  name: string;
  component:
    | React.FC<QuestionComponentBaseProps>
    | React.LazyExoticComponent<React.FC<QuestionComponentBaseProps>>;
  description: string;
  checkCorrectAnswer?: (
    question: BaseQuestion,
    userSelect?: BaseAnswer | BaseAnswer[]
  ) => boolean | null;
  // componentResult?:
  //   | React.FC<QuestionResultComponentBaseProps>
  //   | React.LazyExoticComponent<React.FC<QuestionResultComponentBaseProps>>;
};

export class QuestionComponentRegistry {
  private static instance: QuestionComponentRegistry;
  private componentMap: Map<string, IQuestionComponentInfo>;

  private constructor() {
    this.componentMap = new Map();
  }

  public static getInstance(): QuestionComponentRegistry {
    if (!QuestionComponentRegistry.instance) {
      QuestionComponentRegistry.instance = new QuestionComponentRegistry();
    }
    return QuestionComponentRegistry.instance;
  }

  public register(type: string, component: IQuestionComponentInfo): void {
    this.componentMap.set(type, component);
  }

  public getComponent(type: string): IQuestionComponentInfo | undefined {
    return this.componentMap.get(type);
  }

  public getAllComponents(): Map<string, IQuestionComponentInfo> {
    return this.componentMap;
  }
}

// Replace the old map and registration function with the singleton
export const getQuestionComponentRegistry = () =>
  QuestionComponentRegistry.getInstance();

// Update the isAnswerCorrect function to use the singleton
export const isAnswerCorrect = (
  question: BaseQuestion,
  userSelect?: BaseAnswer | BaseAnswer[]
): boolean | null => {
  if (!userSelect) return null; // No answer provided

  // Use the singleton to get the component
  const questionComponent = getQuestionComponentRegistry().getComponent(
    question.type
  );
  if (questionComponent?.checkCorrectAnswer) {
    return questionComponent.checkCorrectAnswer(question, userSelect);
  }

  return null;
};

// Available question types
export const questionTypeShortDescription = [
  { value: practiceLocalization.quiz, label: 'Trắc nghiệm 1 đáp án' },
  {
    value: practiceLocalization.multiselect,
    label: 'Trắc nghiệm nhiều đáp án',
  },
  { value: practiceLocalization.matching, label: 'Câu hỏi nối' },
  { value: practiceLocalization.fillblanks, label: 'Câu hỏi điền từ' },
  { value: practiceLocalization.essay, label: 'Câu hỏi tự luận' },
];
