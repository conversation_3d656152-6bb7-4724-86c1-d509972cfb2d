/**
 * This hook does not used.
 * It will be removed in the future.
 */

import {
  RefObject,
  useEffect,
  useRef,
  useState,
  useCallback,
  useMemo,
} from 'react';

// Global reference for the scrollable container
export const LazyData: {
  questionListViewFocusingId?: string;
  scrollableContainer: RefObject<HTMLDivElement> | null;
} = {
  questionListViewFocusingId: undefined,
  scrollableContainer: null,
};

// Hook to set the scrollable container
export const useLazyQuestionListView = ({
  questionListViewId,
  scrollableContainer,
}: {
  questionListViewId?: string;
  scrollableContainer: RefObject<HTMLDivElement>;
}) => {
  // Store the container reference globally
  LazyData.scrollableContainer = scrollableContainer;
  LazyData.questionListViewFocusingId = questionListViewId;
};

// Main hook for lazy question list implementation
export const useLazyImplementQuestionList = ({
  engineId,
}: {
  engineId?: string;
}) => {
  // Refs for all list items with proper typing
  const lastItemRef = useRef<HTMLDivElement>(null);

  // State for visible items
  const [maxVisibleItem, setMaxVisibleItem] = useState<number>(0);
  const [updateStack, setUpdateStack] = useState<number>(0);

  // Memoize expensive check for engine ID focus matching
  const isFocusing = useMemo(() => {
    if (!engineId || !LazyData.questionListViewFocusingId) return true;
    return engineId
      .toLowerCase()
      .includes(LazyData.questionListViewFocusingId.toLowerCase());
  }, [engineId, LazyData.questionListViewFocusingId]);

  // Flag to track initialization - optimized with getBoundingClientRect caching
  const isEndOfList = useCallback(() => {
    const divB = lastItemRef.current;
    if (!divB) return false;

    const halfWindowHeight = window.innerHeight / 2;
    const divBRect = divB.getBoundingClientRect();
    return divBRect.top <= halfWindowHeight;
  }, []);

  const checkUpdate = useCallback(() => {
    if (!isFocusing) return;

    const isEnd = isEndOfList();
    if (isEnd) {
      setUpdateStack((prev) => Math.min(prev + 1, 3));
    }
  }, [isEndOfList, isFocusing]);

  // Set up scroll event handler with optimized throttling
  useEffect(() => {
    const scrollElement = LazyData.scrollableContainer?.current || window;

    // Throttle scroll events with requestAnimationFrame
    let ticking = false;
    let rafId: number | null = null;

    const handleScroll = () => {
      if (!ticking) {
        ticking = true;
        rafId = window.requestAnimationFrame(() => {
          checkUpdate();
          ticking = false;
        });
      }
    };

    // Attach event listeners with passive flag for performance
    scrollElement.addEventListener('scroll', handleScroll, { passive: true });

    // Initial check for visibility
    checkUpdate();

    // Clean up event listeners and any pending animation frames on unmount
    return () => {
      scrollElement.removeEventListener('scroll', handleScroll);
      if (rafId !== null) {
        window.cancelAnimationFrame(rafId);
      }
    };
  }, [checkUpdate]);

  // Debounced update of maxVisibleItem state
  useEffect(() => {
    if (!updateStack) return;

    const id = setTimeout(() => {
      setMaxVisibleItem((prev) => prev + updateStack);
      setUpdateStack(0);
    }, 300);

    return () => clearTimeout(id);
  }, [updateStack]);

  // Return everything needed
  return {
    lastItemRef,
    maxVisible: maxVisibleItem,
  };
};
