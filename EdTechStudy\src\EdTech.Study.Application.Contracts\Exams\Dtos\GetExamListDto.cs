using EdTech.Study.Enum;
using System;
using Volo.Abp.Application.Dtos;

namespace EdTech.Study.Exams.Dtos
{
    public class GetExamListDto : PagedAndSortedResultRequestDto
    {
        public string? Filter { get; set; }
        public ExamType? ExamType { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public ExamStatus? Status { get; set; }
        public Guid? SubjectId { get; set; }
        public Guid? GradeId { get; set; }
    }
}