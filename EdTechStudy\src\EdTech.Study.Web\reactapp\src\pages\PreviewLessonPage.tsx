import { Suspense } from 'react';
import ReactDOM from 'react-dom/client';
import '../index.css';
import '../styles/modal-fixes.css';
import { Provider } from 'react-redux';
import { storeEdTech } from '../store/store';
import { EdTechProviderContainer } from '../providers/EdTechProvider';
import LoadingScreen from '../components/common/Loading/LoadingScreen';
import { ConfigManager } from '../utils/ConfigManager';
import { AppConfigSlice } from '../store/slices/AppConfig/AppConfigSlice';
import { ETypeMode } from '../enums/AppEnums';
import { CombinedThemeProvider } from '../providers/theme';
import { registerLicenseSyncfusionBase } from '../utils/syncfusion-license';
// Register Syncfusion license
registerLicenseSyncfusionBase();
useDevLicenseSyncfusion();

import { MathJaxProvider } from '../components/common/MathJax/MathJaxWrapper';
import { useDevLicenseSyncfusion } from './syncfusion-license-custom';
storeEdTech.dispatch(AppConfigSlice.actions.setMode(ETypeMode.PRESENT));
(window as any).initReactApp = ConfigManager.initConfigLessonPage;
export const PreviewLessonPage = () => {
  return (
    <Provider store={storeEdTech}>
      <CombinedThemeProvider>
        <Suspense fallback={<LoadingScreen />}>
          <MathJaxProvider>
            <EdTechProviderContainer />
          </MathJaxProvider>
        </Suspense>
      </CombinedThemeProvider>
    </Provider>
  );
};

const rootTestPageElement = document.getElementById(
  'PreviewLessonPage'
) as HTMLElement;

const rootTestPage = ReactDOM.createRoot(rootTestPageElement);
rootTestPage.render(<PreviewLessonPage />);
