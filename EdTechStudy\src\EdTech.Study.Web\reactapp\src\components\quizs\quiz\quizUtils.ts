import {
  BaseAnswer,
  BaseQuestion,
} from '../../../interfaces/quizs/questionBase';

/**
 * Check if the user's answer is correct
 * @param question
 * @param userSelect
 * @returns
 */
export const quizCheckCorrectAnswer = (
  question: BaseQuestion,
  userSelect?: BaseAnswer | BaseAnswer[]
) => {
  if (!userSelect) return null;

  // For single choice questions
  if (!Array.isArray(userSelect)) {
    const correctAnswers = question.options?.filter((a) => a.isCorrect) || [];
    return correctAnswers.some((correct) => correct.id === userSelect.id);
  }
  return false;
};
