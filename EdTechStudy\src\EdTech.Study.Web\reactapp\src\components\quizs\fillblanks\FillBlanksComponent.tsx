import React, { useContext, useEffect, useRef, useState, useMemo } from 'react';
import {
  Card,
  Input,
  Button,
  Space,
  message,
  Typography,
  Form,
  Switch,
  Divider,
  Tag,
  Collapse,
} from 'antd';
import {
  CheckOutlined,
  CloseOutlined,
  DeleteOutlined,
  PlusOutlined,
  DeleteFilled,
  InfoCircleOutlined,
} from '@ant-design/icons';
import {
  PracticeEngineContext,
  BaseQuestion,
} from '../../../interfaces/quizs/questionBase';
import './FillBlanksComponent.css';
import {
  FillBlanksComponentProps,
  FillBlanksQuestion,
  FillBlankItem,
  FillBlanksAnswer,
} from '../../../interfaces/quizs/fillblanks.interfaces';
import { Guid } from 'guid-typescript';
import practiceLocalization, { quizLocalization } from '../localization';
import useUpdateQuestion from '../hooks/useUpdateQuestion';
import TooltipAntdCustom from '../../customs/antd/TooltipAntdCustom';
import PopconfirmAntdCustom from '../../customs/antd/PopconfirmAntdCustom';
import { ContentFormatType } from '../../../interfaces/exams/examEnums';
import BaseQuestionPreviewComponent from '../base/BaseQuestionPreviewComponent';
import { fillBlanksCheckCorrectAnswer } from './fillBlanksUtils';
const { Text } = Typography;
const { TextArea } = Input;

const FillBlanksComponent: React.FC<FillBlanksComponentProps> = ({
  question,
  questionIndex,
  onComplete,
  configMode = false,
  disabled = false,
  options = {
    hideDeleteButton: false,
    hideSaveButton: false,
    hideFeedback: false,
  },
  // Thêm props cho chế độ hiển thị kết quả
  showResult = false,
  isMarked = false,
  onToggleMark,
}) => {
  const [userAnswers, setUserAnswers] = useState<{ [key: string]: string }>({});
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [blankResults, setBlankResults] = useState<{ [key: string]: boolean }>({}); // prettier-ignore
  const { handleEditQuestion: handleChangeQuestion, handleDeleteQuestion } =
    useContext(PracticeEngineContext);
  const contentQuestionRef = useRef<HTMLTextAreaElement>(null);
  const { handleUpdateQuestion } = useUpdateQuestion();
  const [focus, setFocus] = useState<string | null>(null);
  // Config mode states
  const [editedQuestion, setEditedQuestion] = useState<FillBlanksQuestion>({
    ...question,
  });
  const [form] = Form.useForm();

  useEffect(() => {
    const initialAnswers: { [key: string]: string } = {};
    (question.blanks ?? []).forEach((blank) => {
      initialAnswers[blank.clientId] = '';
    });

    // Initialize user answers from saved state if available
    if (question.userSelect && question.userSelect.answers) {
      setUserAnswers(question.userSelect.answers);
    } else {
      setUserAnswers(initialAnswers);
    }
  }, [question, question.userSelect, question.blanks]);

  const isAnswered = useMemo(() => {
    return (
      Object.keys(userAnswers).length > 0 &&
      Object.values(userAnswers).some((answer) => answer.trim() !== '')
    );
  }, [userAnswers]);

  const isCorrect = useMemo(() => {
    if (!showResult || !isAnswered) return false;
    const fillBlanksAnswer = {
      id: undefined,
      clientId: question.clientId,
      answers: userAnswers,

      isCorrect: false,
      order: 0,
      content: '',
      contentFormat: ContentFormatType.Html,
    };
    const checkResult = fillBlanksCheckCorrectAnswer(
      question,
      fillBlanksAnswer
    );
    return checkResult === true;
  }, [showResult, isAnswered, question, userAnswers]);

  const handleInputChange = (blankId: string, value: string) => {
    const newAnswers = { ...userAnswers, [blankId]: value };
    setUserAnswers(newAnswers);
    // Store answer without checking completion
    if (onComplete) {
      onComplete(question.clientId, {
        id: Guid.create().toString(),
        answers: newAnswers,
        isCorrect: false,
        order: question.options?.length ?? 0,
      } as FillBlanksAnswer);
    }
  };

  // Reset the question
  const handleReset = () => {
    const resetAnswers: { [key: string]: string } = {};
    const resetResults: { [key: string]: boolean } = {};

    (question.blanks ?? []).forEach((blank) => {
      resetAnswers[blank.clientId] = '';
    });

    if (question.userSelect?.answers) {
      Object.keys(question.userSelect.answers).forEach((blankId) => {
        const userAnswer = question.userSelect?.answers[blankId] || '';
        const blank = question.blanks.find((b) => b.id === blankId);

        if (blank) {
          const isCorrect =
            userAnswer.trim().toLowerCase() ===
              blank.correctAnswer.trim().toLowerCase() ||
            blank.alternativeAnswers?.some(
              (alt) =>
                alt.trim().toLowerCase() === userAnswer.trim().toLowerCase()
            );

          // Sử dụng ?? để đảm bảo luôn có giá trị boolean
          resetResults[blankId] = isCorrect ?? false;
        }
      });

      setUserAnswers(question.userSelect.answers);
    } else {
      setUserAnswers(resetAnswers);
    }

    setBlankResults(resetResults);
    setIsSubmitted(false);
  };

  // Function to render question with input fields
  const renderQuestion = () => {
    const parts = question.content?.split('____') ?? '';

    if (parts.length <= 1) {
      return <div className="item-config">{question.content}</div>;
    }

    return (
      <div
        className="fill-blanks-question-preview"
        onClick={() => setFocus('question')}
      >
        {parts.map((part, index) => {
          // Last part doesn't have a blank after it
          if (index === parts.length - 1) {
            return (
              <span key={`part-${index}`} className="item-config">
                {part}
              </span>
            );
          }

          const blank = question.blanks[index];
          if (!blank) return null;

          return (
            <React.Fragment key={`blank-fragment-${index}`}>
              <span key={`part-text-${index}`} className="item-config">
                {part}
              </span>
              <span
                key={`blank-wrapper-${index}`}
                className="blank-wrapper item-config"
              >
                <Input
                  key={`blank-input-${index}`}
                  onClick={() => setFocus(`blank-${index}`)}
                  className={`fill-blanks-input ${
                    isSubmitted || showResult
                      ? blankResults[blank.clientId]
                        ? 'correct'
                        : 'incorrect'
                      : ''
                  }`}
                  value={userAnswers[blank.clientId] || ''}
                  onChange={(e) =>
                    handleInputChange(blank.clientId, e.target.value)
                  }
                  disabled={isSubmitted || showResult || disabled}
                  placeholder="___________"
                  size="middle"
                  style={{
                    paddingRight: '25px',
                  }} /* Đảm bảo có padding đủ cho icon */
                />
                {(isSubmitted || showResult) && (
                  <span
                    key={`blank-feedback-${index}`}
                    className={`blank-feedback-icon ${
                      blankResults[blank.clientId] ? 'correct' : 'incorrect'
                    }`}
                  >
                    {blankResults[blank.clientId] ? (
                      <CheckOutlined />
                    ) : (
                      <CloseOutlined />
                    )}
                  </span>
                )}
              </span>
              {(isSubmitted || showResult) &&
                !blankResults[blank.clientId] &&
                question.showHints &&
                blank.hint && (
                  <div key={`blank-hint-${index}`} className="fill-blanks-hint">
                    <InfoCircleOutlined /> Gợi ý: {blank.hint}
                  </div>
                )}
            </React.Fragment>
          );
        })}
      </div>
    );
  };

  // Configuration mode methods
  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const updatedQuestion = {
      ...editedQuestion,
      title: e.target.value,
    };
    setEditedQuestion(updatedQuestion);
    handleUpdateQuestion(updatedQuestion as BaseQuestion);
  };

  const handleQuestionTextChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const updatedQuestion = {
      ...editedQuestion,
      content: e.target.value,
      description: e.target.value,
    };
    setEditedQuestion(updatedQuestion);
    handleUpdateQuestion(updatedQuestion as BaseQuestion);
  };

  const handleInsertBlank = () => {
    const currentText = editedQuestion.content || '';

    // Todo Chèn ô trống vào vị trí con trỏ
    const newText = currentText + ' ____';

    // Tạo blank mới nếu cần
    const blankCount = (newText.match(/____/g) || []).length;
    const needNewBlank = blankCount > editedQuestion.blanks.length;

    let updatedBlanks = [...editedQuestion.blanks];
    if (needNewBlank) {
      const newBlank: FillBlankItem = {
        clientId: Guid.create().toString(),
        correctAnswer: '',
        alternativeAnswers: [],
        hint: '',
      };
      updatedBlanks = [...updatedBlanks, newBlank];
    }

    // Cập nhật question trong một lần duy nhất
    const updatedQuestion = {
      ...editedQuestion,
      content: newText,
      description: newText,
      blanks: updatedBlanks,
    };

    // Cập nhật state
    setEditedQuestion(updatedQuestion);
    handleUpdateQuestion(updatedQuestion as BaseQuestion);
  };

  const handleExplanationChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const updatedQuestion = {
      ...editedQuestion,
      explanation: e.target.value,
    };
    setEditedQuestion(updatedQuestion);
    handleUpdateQuestion(updatedQuestion as BaseQuestion);
  };

  const handleCaseSensitiveChange = (checked: boolean) => {
    const updatedQuestion = {
      ...editedQuestion,
      caseSensitive: checked,
    };
    setEditedQuestion(updatedQuestion);
    handleUpdateQuestion(updatedQuestion as BaseQuestion);
  };

  const handleShowHintsChange = (checked: boolean) => {
    const updatedQuestion = {
      ...editedQuestion,
      showHints: checked,
    };
    setEditedQuestion(updatedQuestion);
    handleUpdateQuestion(updatedQuestion as BaseQuestion);
  };

  const handleBlankAnswerChange = (
    blankId: string,
    field: keyof FillBlankItem,
    value: string
  ) => {
    let updatedQuestion: FillBlanksQuestion | null = null;
    setEditedQuestion((prev) => {
      const updatedBlanks = prev.blanks.map((blank) => {
        if (blank.clientId === blankId) {
          return {
            ...blank,
            [field]: value,
          };
        }
        return blank;
      });
      const update = {
        ...prev,
        blanks: updatedBlanks,
      };
      updatedQuestion = update;
      return update;
    });

    if (updatedQuestion) {
      handleUpdateQuestion(updatedQuestion as BaseQuestion);
    }
  };

  // Replace the existing implementation with a new one that handles comma-separated values
  const handleAlternativeStringChange = (blankId: string, value: string) => {
    let updatedQuestion: FillBlanksQuestion | null = null;
    setEditedQuestion((prev) => {
      const updatedBlanks = prev.blanks.map((blank) => {
        if (blank.clientId === blankId) {
          // Split by comma and trim each value
          const alternativeAnswers = value
            ? value.split(',').map((item) => item.trim())
            : [];
          return {
            ...blank,
            alternativeAnswers,
          };
        }
        return blank;
      });

      const update = {
        ...prev,
        blanks: updatedBlanks,
      };
      updatedQuestion = update;
      return update;
    });
    if (updatedQuestion) {
      handleUpdateQuestion(updatedQuestion as BaseQuestion);
    }
  };

  const addNewBlank = () => {
    // Generate unique ID for new blank
    let updatedQuestion: FillBlanksQuestion | null = null;
    const newBlankId = Guid.create().toString();

    // Create new blank
    const newBlank: FillBlankItem = {
      clientId: newBlankId,
      correctAnswer: '',
      hint: '',
    };

    // Add blank to question
    setEditedQuestion((prev) => {
      // Add blank to array
      const updatedBlanks = [...prev.blanks, newBlank];

      // Add blank placeholder to question text if no blanks exist yet
      let updatedQuestionText = prev.content;
      if (!updatedQuestionText.includes('____')) {
        updatedQuestionText = 'Điền từ thích hợp vào chỗ trống: ____';
      } else {
        updatedQuestionText = updatedQuestionText + ' ____';
      }

      const update = {
        ...prev,
        blanks: updatedBlanks,
        content: updatedQuestionText,
        description: updatedQuestionText,
      };
      updatedQuestion = update;
      return update;
    });
    if (updatedQuestion) {
      handleUpdateQuestion(updatedQuestion as BaseQuestion);
    }
  };

  const removeBlank = (blankId: string) => {
    let updatedQuestion: FillBlanksQuestion | null = null;
    setEditedQuestion((prev) => {
      // Get index of blank to remove
      const blankIndex = prev.blanks.findIndex((b) => b.clientId === blankId);

      if (blankIndex === -1) return prev;

      // Make a copy of blanks array without the removed blank
      const updatedBlanks = prev.blanks.filter((b) => b.clientId !== blankId);

      const parts = prev.content.split('____');

      // If we have fewer parts than expected, don't modify the text
      if (parts.length <= blankIndex + 1) {
        return {
          ...prev,
          blanks: updatedBlanks,
        };
      }

      // Remove the blank placeholder by joining parts without the blank at blankIndex
      let updatedQuestionText = '';
      parts.forEach((part, index) => {
        updatedQuestionText += part;
        if (index < parts.length - 1 && index !== blankIndex) {
          updatedQuestionText += '____';
        }
      });

      const update = {
        ...prev,
        blanks: updatedBlanks,
        content: updatedQuestionText,
        description: updatedQuestionText,
      };
      updatedQuestion = update;
      return update;
    });

    if (updatedQuestion) {
      handleUpdateQuestion(updatedQuestion as BaseQuestion);
    }
  };

  const handleSaveConfig = () => {
    // Validate the configuration
    const hasTitle = editedQuestion.title.trim() !== '';
    const hasQuestionText = editedQuestion.content?.trim() !== '';
    const hasAtLeastOneBlank = editedQuestion.blanks.length > 0;
    const allBlanksHaveAnswers = editedQuestion.blanks.every(
      (blank) => blank.correctAnswer.trim() !== ''
    );

    if (!hasTitle) {
      message.error('Tiêu đề không được để trống!');
      return;
    }

    if (!hasQuestionText) {
      message.error('Câu hỏi không được để trống!');
      return;
    }

    if (!hasAtLeastOneBlank) {
      message.error('Phải có ít nhất một ô trống!');
      return;
    }

    if (!allBlanksHaveAnswers) {
      message.error('Tất cả các ô trống phải có đáp án đúng!');
      return;
    }

    // Count blanks in question text
    const blankCount = (editedQuestion.content.match(/____/g) || []).length;
    if (blankCount !== editedQuestion.blanks.length) {
      message.error(
        `Số lượng ô trống trong câu hỏi (${blankCount}) phải bằng số lượng đáp án (${editedQuestion.blanks.length})!`
      );
      return;
    }
    editedQuestion.options = [
      {
        id: undefined,
        clientId: Guid.create().toString(),
        isCorrect: true,
        content: '',
        contentFormat: ContentFormatType.Html,
        order: 0,
        answers: editedQuestion.blanks.reduce((acc, blank) => {
          acc[blank.clientId] = blank.correctAnswer;
          return acc;
        }, {} as { [blankId: string]: string }),
      },
    ];
    // Save configuration
    handleChangeQuestion({
      ...editedQuestion,
      parentId: question.parentId,
    });
    message.success('Lưu cấu hình thành công!');
  };

  const deleteConfirm = () => {
    handleDeleteQuestion(question.clientId);
  };

  const deleteCancel = () => {
    // Handle cancel action if needed
  };

  const collapseItems = [
    {
      key: '1',
      label: practiceLocalization['Advanced Options'],
      children: (
        <>
          <Form.Item label="Tùy chọn">
            <Space>
              <Switch
                checked={editedQuestion.caseSensitive}
                onChange={handleCaseSensitiveChange}
              />
              <span>Phân biệt chữ hoa/thường</span>
            </Space>
            <Divider type="vertical" />
            <Space>
              <Switch
                checked={editedQuestion.showHints}
                onChange={handleShowHintsChange}
              />
              <span>Hiển thị gợi ý</span>
            </Space>
          </Form.Item>
          <Form.Item label="Giải thích đáp án">
            <TextArea
              rows={3}
              value={editedQuestion.explanation || ''}
              onChange={handleExplanationChange}
              placeholder="Nhập giải thích cho đáp án"
            />
          </Form.Item>
        </>
      ),
    },
  ];

  useEffect(() => {
    handleReset();
  }, [question.clientId]);

  // region Config_mode
  if (configMode) {
    return (
      <Card>
        <Form form={form} className="form-config" layout="vertical">
          <Form.Item required hidden>
            <Input
              hidden
              variant="underlined"
              value={editedQuestion.title}
              autoFocus={focus === 'title'}
              onChange={handleTitleChange}
              placeholder="Nhập tiêu đề câu hỏi"
              className="tailwind-font-medium tailwind-text-lg"
            />
          </Form.Item>

          <Form.Item
            label="Nội dung câu hỏi (sử dụng ____ để đánh dấu ô trống)"
            required
            tooltip="Mỗi dấu ____ sẽ được thay thế bằng một ô trống. Số lượng ô trống phải bằng số lượng đáp án."
          >
            <Space.Compact className="tailwind-relative tailwind-w-full">
              <TextArea
                ref={contentQuestionRef}
                className="fill-blanks-textarea"
                value={editedQuestion.content}
                onChange={handleQuestionTextChange}
                placeholder="Nhập nội dung câu hỏi"
                autoSize={{ minRows: 2 }}
                autoFocus={focus === 'question'}
              />
            </Space.Compact>
            <TooltipAntdCustom title="Chèn ô trống">
              <Button
                type="primary"
                variant="filled"
                hidden={focus !== 'question'}
                icon={<PlusOutlined />}
                onClick={handleInsertBlank}
                className={'tailwind-h-full tailwind-mt-2'}
              ></Button>
            </TooltipAntdCustom>
          </Form.Item>

          <Divider>Các ô trống</Divider>

          {editedQuestion.blanks.map((blank, index) => (
            <div key={blank.clientId} className="blank-config-item">
              <div className="blank-config-row">
                <Space align="baseline" style={{ marginBottom: '10px' }}>
                  <Tag color="blue">Ô trống {index + 1}</Tag>
                  <Button
                    type="text"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => removeBlank(blank.clientId)}
                  />
                </Space>
              </div>

              <Form.Item label="Đáp án đúng" required>
                <Input
                  variant="underlined"
                  value={blank.correctAnswer}
                  onChange={(e) =>
                    handleBlankAnswerChange(
                      blank.clientId,
                      'correctAnswer',
                      e.target.value
                    )
                  }
                  placeholder="Nhập đáp án đúng"
                />
              </Form.Item>

              {/* <Form.Item label="Gợi ý">
                <Input
                  variant="underlined"
                  value={blank.hint || ''}
                  onChange={(e) =>
                    handleBlankAnswerChange(blank.id, 'hint', e.target.value)
                  }
                  placeholder="Nhập gợi ý cho đáp án này"
                />
              </Form.Item> */}

              <Form.Item label="Các đáp án thay thế (phân cách bằng dấu phẩy ',')">
                <div className="blank-alternatives">
                  <Input
                    variant="underlined"
                    value={blank.alternativeAnswers?.join(', ') || ''}
                    onChange={(e) =>
                      handleAlternativeStringChange(
                        blank.clientId,
                        e.target.value
                      )
                    }
                    placeholder="Nhập các đáp án thay thế, phân cách bằng dấu phẩy"
                  />
                </div>
              </Form.Item>
            </div>
          ))}

          <Form.Item>
            <Button type="dashed" onClick={addNewBlank} icon={<PlusOutlined />}>
              Thêm ô trống
            </Button>
          </Form.Item>

          {!options.hideSaveButton && (
            <Form.Item>
              <Button
                type="primary"
                onClick={handleSaveConfig}
                className="flex-1 bg-blue-500 hover:bg-blue-600"
                icon={<CheckOutlined />}
                block
              >
                Lưu cấu hình
              </Button>
            </Form.Item>
          )}

          <Collapse defaultActiveKey={[]} ghost items={collapseItems} />

          {!options.hideDeleteButton && (
            <PopconfirmAntdCustom
              title={quizLocalization.buttons.deleteQuestion.confirmTitle}
              onConfirm={deleteConfirm}
              onCancel={deleteCancel}
              okText={quizLocalization.buttons.deleteQuestion.yes}
              cancelText={quizLocalization.buttons.deleteQuestion.no}
            >
              <Button danger icon={<DeleteFilled />}>
                {quizLocalization.buttons.deleteQuestion.button}
              </Button>
            </PopconfirmAntdCustom>
          )}
        </Form>
      </Card>
    );
  }
  // endregion Config_mode

  // Render normal mode
  // Render phần nội dung câu hỏi (với các ô trống)
  const renderContent = () => {
    return <Text>{renderQuestion()}</Text>;
  };

  const renderCorrectAnswer = () => {
    if (!showResult || isCorrect) return null;

    return (
      <div className="correct-answer-display">
        <div className="answer-content">
          <div className="fill-blanks-result">
            {question.content?.split('____').map((part, index) => {
              // Last part doesn't have a blank after it
              if (index === question.content?.split('____').length! - 1) {
                return (
                  <span key={`correct-part-${index}`} className="item-config">
                    {part}
                  </span>
                );
              }

              const blank = question.blanks[index];
              if (!blank) return null;

              return (
                <React.Fragment key={`correct-blank-fragment-${index}`}>
                  <span className="item-config">{part}</span>
                  <span className="blank-result">
                    <Tag color="green" className="correct-answer-tag">
                      {blank.correctAnswer}
                    </Tag>
                  </span>
                </React.Fragment>
              );
            })}
          </div>
        </div>
      </div>
    );
  };

  return (
    <BaseQuestionPreviewComponent
      question={editedQuestion}
      questionIndex={questionIndex}
      hideFeedback={options.hideFeedback}
      showResult={showResult}
      isCorrect={isCorrect}
      isAnswered={isAnswered}
      cardClassName="fill-blanks-card"
      guidanceText="Điền từ thích hợp vào chỗ trống."
      renderContent={renderContent}
      renderCorrectAnswer={renderCorrectAnswer}
      isMarked={isMarked}
      onToggleMark={onToggleMark}
    />
  );
};

export default FillBlanksComponent;
