import { Guid } from 'guid-typescript';
import { EssayQuestion } from '../../../interfaces/quizs/essay.interface';
import { QuestionType } from '../../../interfaces/quizs/questionBase';

export const createEssayQuestionTemplate = (
  title: string = '<PERSON><PERSON>u hỏi tự luận'
): EssayQuestion => {
  const id = Guid.create().toString();
  return {
    clientId: id,
    type: 'essay',
    questionType: QuestionType.Essay,
    difficulty: 1,
    syncQuestion: false,
    title,
    content: '',
    correctAnswer: 'Đáp án mẫu',
    explanation: '<PERSON><PERSON><PERSON><PERSON> thích cho đáp án',
    points: 1,
    caseSensitive: false,
    allowPartialMatch: true,
    maxLength: 200,
  };
};
