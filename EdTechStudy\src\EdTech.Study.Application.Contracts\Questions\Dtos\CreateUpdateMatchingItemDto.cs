﻿using EdTech.Study.Exams;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EdTech.Study.Questions.Dtos
{
    public class CreateUpdateMatchingItemDto
    {
        public Guid? Id { get; set; }

        public Guid ClientId { get; set; }

        /// <summary>
        /// Loại mục (Premise - tiền đề, hoặc Response - phản hồi).
        /// </summary>
        public MatchingItemType Type { get; set; }

        /// <summary>
        /// Nội dung của mục nối.
        /// </summary>
        [Required]
        public string Content { get; set; }

        /// <summary>
        /// Định dạng nội dung (Text hoặc HTML).
        /// </summary>
        [EnumDataType(typeof(ContentFormatType))]
        public ContentFormatType ContentFormat { get; set; }

        /// <summary>
        /// Thứ tự sắp xếp của mục trong danh sách.
        /// </summary>
        public int Order { get; set; }
    }
}
