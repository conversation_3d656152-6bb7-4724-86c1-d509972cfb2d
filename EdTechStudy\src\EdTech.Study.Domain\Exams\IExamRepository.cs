using EdTech.Study.Enum;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace EdTech.Study.Exams
{
    public interface IExamRepository : IRepository<ExamEntity, Guid>
    {
        Task<ExamEntity> GetExamWithDetailsAsync(Guid id);
        Task<List<ExamEntity>> GetListExamAsync(
            string? filter = null,
            ExamType? examType = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            ExamStatus? status = null,
            Guid? subjectId = null,
            Guid? gradeId = null,
            int maxResultCount = 10,
            int skipCount = 0,
            string? sorting = null,
            CancellationToken cancellationToken = default);

        Task<long> GetCountAsync(
            string? filter = null,
            ExamType? examType = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            ExamStatus? status = null,
            Guid? subjectId = null,
            Guid? gradeId = null,
            CancellationToken cancellationToken = default);
    }
}