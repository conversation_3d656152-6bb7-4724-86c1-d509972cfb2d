﻿using EdTech.Study.Exams;
using System;
using System.ComponentModel.DataAnnotations;

namespace EdTech.Study.Questions.Dtos
{
    public class CreateQuestionOptionDto
    {
        public Guid? Id { get; set; }

        public Guid ClientId { get; set; }

        [Required]
        public string Content { get; set; }

        [EnumDataType(typeof(ContentFormatType))]
        public ContentFormatType ContentFormat { get; set; }

        public bool IsCorrect { get; set; }

        public int Order { get; set; }

        public string? Explanation { get; set; }

        /// <summary>
        /// <PERSON>iể<PERSON> số cho ô trống này.
        /// </summary>
        public float? Score { get; set; }

        public string CreateIdempotentKey(string rootKey)
        {
            return $"{rootKey}_option-{Order}";
        }
    }
}