/**
 * Safely initializes MathJax for lazy-loaded components
 */
export const initMathJax = (): Promise<void> => {
  return new Promise((resolve, _reject) => {
    // debugger;
    // If MathJax is not loaded yet, wait for it
    if (!(window as any).MathJax) {
      console.log('MathJ<PERSON> not found, waiting for it to load...');

      // Check every 100ms if MathJax has loaded
      const checkInterval = setInterval(() => {
        if ((window as any).MathJax) {
          clearInterval(checkInterval);
          resolve();
        }
      }, 100);

      // Set a timeout to avoid infinite waiting
      setTimeout(() => {
        clearInterval(checkInterval);
        console.warn('MathJ<PERSON> did not load in time');
        resolve(); // Resolve anyway to not block the app
      }, 5000);

      return;
    }

    // If MathJax is already loaded, resolve immediately
    resolve();
  });
};

/**
 * Typeset MathJax content in a specific element
 */
export const typesetMathJax = (element: HTMLElement | null): void => {
  if (!element) return;

  if ((window as any).MathJax?.typesetPromise) {
    try {
      (window as any).MathJax.typesetPromise([element]).catch((err: any) => {
        console.error('MathJax typesetting error:', err);
      });
    } catch (err) {
      console.error('Error calling MathJax.typesetPromise:', err);
    }
  }
};

export default {
  initMathJax,
  typesetMathJax,
};
