import { ExamStatus } from '../../../interfaces/exams/examEnums';
import { Subject } from '../../../interfaces/lessons/subject';
import { BaseQuestion, QuestionType } from '../../../interfaces/quizs/questionBase';
import {
  FilterValue,
  SorterResult,
  TablePaginationConfig,
} from 'antd/es/table/interface';

export interface TableColumn {
  key: string;
  title: string;
  visible: boolean;
  width: number;
}

export interface IQuestionStoreManagerProps {
  id: string;
  displayMode: string;
  questions: BaseQuestion[];
  loading?: boolean;
  totalCount?: number;
  onEditQuestion?: (question: BaseQuestion) => void;
  onDeleteQuestion?: (questionId: string) => void;
  onPreviewQuestion?: (question: BaseQuestion) => void;
  onExportQuestions?: () => void;
  onImportQuestions?: () => void;
  onTableChange?: (
    pagination: TablePaginationConfig,
    filters: Record<string, FilterValue | null>,
    sorter: SorterResult<BaseQuestion> | SorterResult<BaseQuestion>[]
  ) => void;
  onSearch?: (searchText: string) => void;
  onRefresh?: () => void;
  subjects?: Subject[];
  onSubjectFilterChange?: (subjectIds: string[]) => void;
  onLessonGradeFilterChange?: (lessonGradeIds: string[]) => void;
  onStatusFilterChange?: (status: ExamStatus | undefined) => void;
  onQuestionTypeFilterChange?: (types: QuestionType[]) => void;
}
