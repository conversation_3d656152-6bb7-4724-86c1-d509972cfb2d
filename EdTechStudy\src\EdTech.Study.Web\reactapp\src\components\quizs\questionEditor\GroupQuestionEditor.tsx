import GroupQuestionComponent from '../../exams/components/GroupQuestionComponent';
import { useCallback, useContext, useEffect, useState } from 'react';
import {
  BaseQuestion,
  PracticeEngineContext,
} from '../../../interfaces/quizs/questionBase';
import {
  ExamGroupQuestion,
  ExamQuestion,
} from '../../../interfaces/exams/examBase';

export const GroupQuestionEditor = ({
  groupQuestion,
  questionIndex,
}: {
  groupQuestion: ExamGroupQuestion;
  questionIndex?: number;
}) => {
  const { handleUpdateGroupQuestion, handleDeleteGroupQuestion } = useContext(
    PracticeEngineContext
  );

  useEffect(() => {
    console.log({
      groupQuestionId: groupQuestion.clientId,
      questionIndex,
    });
  }, [groupQuestion, questionIndex]);

  const [editedGroupQuestion, setEditedGroupQuestion] =
    useState<ExamGroupQuestion>(groupQuestion);
  const [currentPosition, setCurrentPosition] = useState<number>(
    questionIndex ?? 0
  );

  const handleEdit = (updatedGroup: Partial<ExamGroupQuestion>) => {
    setEditedGroupQuestion((prev) => ({
      ...prev,
      ...updatedGroup,
    }));
    handleUpdateGroupQuestion({
      ...updatedGroup,
      clientId: editedGroupQuestion.clientId,
    });
  };

  const handleDelete = (groupId: string) => {
    handleDeleteGroupQuestion(groupId);
  };

  const handleMoveQuestion = useCallback(
    (position: number) => {
      setCurrentPosition(position);
    },
    [setCurrentPosition]
  );

  function handleAddQuestion(
    question: BaseQuestion,
    position?: number | undefined
  ): void {
    // Create a copy of the current questions array
    const currentQuestions = [...(editedGroupQuestion.questions || [])];

    // If position is provided and valid, insert at that position
    // Otherwise append to the end
    let insertPosition =
      position !== undefined &&
      position >= 0 &&
      position <= currentQuestions.length
        ? position
        : currentQuestions.length;
    insertPosition = Math.min(insertPosition + 1, currentQuestions.length);

    // Insert the new question
    currentQuestions.splice(insertPosition, 0, question as ExamQuestion);

    // Update the group question state
    const updatedGroupQuestion = {
      ...editedGroupQuestion,
      questions: currentQuestions,
      questionIds: currentQuestions.map((q) => q.clientId),
    };

    setEditedGroupQuestion(updatedGroupQuestion);

    // Update the group question in the context
    handleUpdateGroupQuestion({
      clientId: editedGroupQuestion.clientId,
      questions: currentQuestions,
      questionIds: currentQuestions.map((q) => q.clientId),
    });
  }

  return (
    <GroupQuestionComponent
      currentPosition={currentPosition}
      groupQuestion={editedGroupQuestion}
      configMode={true}
      onEdit={handleEdit}
      onDelete={handleDelete}
      onAddQuestion={handleAddQuestion}
      moveQuestion={handleMoveQuestion}
    />
  );
};
