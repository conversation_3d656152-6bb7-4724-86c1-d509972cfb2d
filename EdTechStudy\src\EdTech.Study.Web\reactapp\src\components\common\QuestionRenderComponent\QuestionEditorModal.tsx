import {
  ArrowLeftOutlined,
  ArrowRightOutlined,
  CheckCircleOutlined,
  CheckOutlined,
  CloseCircleOutlined,
  CloseOutlined,
  EditOutlined,
  FileTextOutlined,
  SaveOutlined,
  SendOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import {
  Button,
  Collapse,
  message,
  Modal,
  Select,
  Space,
  Spin,
  Splitter,
  Tabs,
  Tag,
  Tooltip,
  DatePicker,
} from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { ExamStatus } from '../../../interfaces/exams/examEnums';
import { IQuestion } from '../../../interfaces/questions/question';
import {
  BaseQuestion,
  PracticeEngineContext,
} from '../../../interfaces/quizs/questionBase';
import { useQuestionContext } from '../../../providers/Questions/QuestionProvider';
import {
  fetchQuestionInfo,
  QuestionDataManagerState,
  updateQuestionStatus,
} from '../../../store/slices/QuestionSlices/questionDataManagerSlice';
import {
  mapBaseQuestionToQuestion,
  mapQuestionToBaseQuestion,
} from '../../../utils/questionUtil';
import useFullscreenAdapter from '../Fullscreen/useFullscreenAdapter';
import QuestionEditorInfo from './QuestionEditorInfo';
import QuestionPreview from './QuestionPreview';
import ExamInfoForQuestion from './ExamInfo/ExamInfoForQuestion';
import GroupInfoForQuestion from './GroupInfo/GroupInfoForQuestion';
import dayjs from 'dayjs';

export interface QuestionEditorProps {
  visible: boolean;
  questionId: string | null | undefined;
  currentPosition?: number;
  totalQuestion?: number;
  isFirstPage?: boolean;
  isLastPage?: boolean;
  // redux connect
  question: IQuestion | null;
  loading?: boolean;
  fetchQuestionInfo: (questionId: string) => void;
  updateQuestionStatus: (params: {
    questionId: string;
    status: ExamStatus;
  }) => any;
  // end redux connect
  onCancel: () => void;
  onSave: (updatedQuestion: IQuestion) => void;
  onNext?: () => any;
  onPrev?: () => any;
}

// Add new components for the tabs
const QuestionGroupTab = () => {
  return (
    <div className="question-group-tab">
      <h3>Thông tin nhóm câu hỏi</h3>
      {/* Placeholder for question group information */}
      <p>Chức năng đang được phát triển...</p>
    </div>
  );
};

const QuestionEditorModal: React.FC<QuestionEditorProps> = ({
  visible,
  questionId,
  currentPosition,
  totalQuestion,
  isFirstPage = false,
  isLastPage = false,
  loading,
  question,
  fetchQuestionInfo,
  updateQuestionStatus,
  onCancel,
  onSave,
  onNext,
  onPrev,
}) => {
  const { subjects = [], lessonGrades = [], users = [] } = useQuestionContext();
  const [editedQuestion, setEditedQuestion] = useState<BaseQuestion | null>(
    null
  );
  const [modalLoading, setModalLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('1');
  const defaultQuestion: BaseQuestion = {
    clientId: '',
    type: 'quiz',
    title: '',
    content: '',
    points: 1,
    statusEntity: ExamStatus.Draft,
    difficulty: 1,
    syncQuestion: false,
  };

  // Fullscreen functionality
  const { isFullscreen, fullscreenRef, handleToggleFullscreen } =
    useFullscreenAdapter({
      onEnterFullscreen: () => {
        console.log('Entered fullscreen mode');
      },
      onExitFullscreen: () => {
        console.log('Exited fullscreen mode');
      },
    });

  // Initialize the edited question when the modal becomes visible or question changes
  useEffect(() => {
    if (visible && question) {
      let baseQuestion = mapQuestionToBaseQuestion(question);
      setEditedQuestion(baseQuestion);
    }
  }, [visible, question]);

  useEffect(() => {
    if (questionId) {
      fetchQuestionInfo(questionId);
    }
  }, [questionId, fetchQuestionInfo]);

  useEffect(() => {
    if (loading || !question) {
      setModalLoading(true);
    } else {
      setModalLoading(false);
    }
  }, [loading, question]);

  // Handle saving the question
  const handleSave = useCallback(
    (updatedQuestion: BaseQuestion) => {
      let questionMap = mapBaseQuestionToQuestion(updatedQuestion);
      onSave(questionMap);
    },
    [onSave]
  );

  // Handle question change
  const handleQuestionChange = useCallback(
    (
      update: Partial<BaseQuestion> & {
        clientId: string;
        parentId: string;
      }
    ) => {
      setEditedQuestion(update as BaseQuestion);
    },
    []
  );

  // Handle status change
  const handleStatusChange = useCallback(
    async (newStatus: ExamStatus) => {
      if (!question || !question.id) return;

      let response = await updateQuestionStatus({
        questionId: question.id,
        status: newStatus,
      });
      if (response && response.payload && response.payload.id) {
        fetchQuestionInfo(response.payload.id);
        message.success('Cập nhật trạng thái câu hỏi thành công!');
      } else {
        message.error('Cập nhật trạng thái câu hỏi thất bại!');
      }
    },
    [question, updateQuestionStatus]
  );

  // Get status tag color and text
  const getStatusTag = () => {
    if (!editedQuestion) return null;

    let color = 'default';
    let text = 'Bản nháp';
    let icon = <EditOutlined />;

    const status = editedQuestion.statusEntity || ExamStatus.Draft;

    switch (status) {
      case ExamStatus.Published:
        color = 'success';
        text = 'Công bố';
        icon = <CheckCircleOutlined />;
        break;
      case ExamStatus.Approved:
        color = 'processing';
        text = 'Đã duyệt';
        icon = <CheckCircleOutlined />;
        break;
      case ExamStatus.Submitted:
        color = 'warning';
        text = 'Đã gửi duyệt';
        icon = <SendOutlined />;
        break;
      case ExamStatus.Draft:
        color = 'default';
        text = 'Soạn thảo';
        icon = <EditOutlined />;
        break;
      case ExamStatus.Rejected:
        color = 'error';
        text = 'Đã từ chối';
        icon = <CloseCircleOutlined />;
        break;
    }

    return (
      <Tag color={color} icon={icon}>
        {text}
      </Tag>
    );
  };

  // Get status action buttons based on current status
  const getStatusActions = () => {
    if (!editedQuestion) return null;

    const status = editedQuestion.statusEntity || ExamStatus.Draft;
    const buttons = [];

    // Định nghĩa các kiểu nút và màu sắc
    const buttonTypes = {
      draft: { type: 'default', icon: <EditOutlined />, tooltip: 'Soạn thảo' },
      submit: { type: 'warning', icon: <SendOutlined />, tooltip: 'Gửi duyệt' },
      approve: { type: 'primary', icon: <CheckOutlined />, tooltip: 'Duyệt' },
      publish: {
        type: 'success',
        icon: <CheckCircleOutlined />,
        tooltip: 'Công bố',
      },
      reject: {
        type: 'danger',
        icon: <CloseCircleOutlined />,
        tooltip: 'Từ chối',
      },
    };

    // Các nút hiển thị dựa vào trạng thái hiện tại
    switch (status) {
      case ExamStatus.Draft:
        // Soạn thảo: Gửi duyệt, Duyệt
        buttons.push(
          <Tooltip key="submit" title={buttonTypes.submit.tooltip}>
            <Button
              type="primary"
              icon={buttonTypes.submit.icon}
              onClick={() => handleStatusChange(ExamStatus.Submitted)}
              style={{ backgroundColor: '#faad14', borderColor: '#faad14' }}
            >
              Gửi duyệt
            </Button>
          </Tooltip>
        );
        buttons.push(
          <Tooltip key="approve" title={buttonTypes.approve.tooltip}>
            <Button
              type="primary"
              icon={buttonTypes.approve.icon}
              onClick={() => handleStatusChange(ExamStatus.Approved)}
            >
              Duyệt
            </Button>
          </Tooltip>
        );
        break;
      case ExamStatus.Submitted:
        // Gửi duyệt: Soạn thảo, Duyệt, Từ chối
        buttons.push(
          <Tooltip key="draft" title={buttonTypes.draft.tooltip}>
            <Button
              icon={buttonTypes.draft.icon}
              onClick={() => handleStatusChange(ExamStatus.Draft)}
            >
              Soạn thảo
            </Button>
          </Tooltip>
        );
        buttons.push(
          <Tooltip key="approve" title={buttonTypes.approve.tooltip}>
            <Button
              type="primary"
              icon={buttonTypes.approve.icon}
              onClick={() => handleStatusChange(ExamStatus.Approved)}
            >
              Duyệt
            </Button>
          </Tooltip>
        );
        buttons.push(
          <Tooltip key="reject" title={buttonTypes.reject.tooltip}>
            <Button
              danger
              icon={buttonTypes.reject.icon}
              onClick={() => handleStatusChange(ExamStatus.Rejected)}
            >
              Từ chối
            </Button>
          </Tooltip>
        );
        break;
      case ExamStatus.Approved:
        // Duyệt: Soạn thảo, Công bố, Từ chối
        buttons.push(
          <Tooltip key="draft" title={buttonTypes.draft.tooltip}>
            <Button
              icon={buttonTypes.draft.icon}
              onClick={() => handleStatusChange(ExamStatus.Draft)}
            >
              Soạn thảo
            </Button>
          </Tooltip>
        );
        buttons.push(
          <Tooltip key="publish" title={buttonTypes.publish.tooltip}>
            <Button
              type="primary"
              icon={buttonTypes.publish.icon}
              onClick={() => handleStatusChange(ExamStatus.Published)}
              style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
            >
              Công bố
            </Button>
          </Tooltip>
        );
        buttons.push(
          <Tooltip key="reject" title={buttonTypes.reject.tooltip}>
            <Button
              danger
              icon={buttonTypes.reject.icon}
              onClick={() => handleStatusChange(ExamStatus.Rejected)}
            >
              Từ chối
            </Button>
          </Tooltip>
        );
        break;
      case ExamStatus.Published:
        // Công bố: Soạn thảo, Từ chối
        buttons.push(
          <Tooltip key="draft" title={buttonTypes.draft.tooltip}>
            <Button
              icon={buttonTypes.draft.icon}
              onClick={() => handleStatusChange(ExamStatus.Draft)}
            >
              Soạn thảo
            </Button>
          </Tooltip>
        );
        buttons.push(
          <Tooltip key="reject" title={buttonTypes.reject.tooltip}>
            <Button
              danger
              icon={buttonTypes.reject.icon}
              onClick={() => handleStatusChange(ExamStatus.Rejected)}
            >
              Từ chối
            </Button>
          </Tooltip>
        );
        break;
      case ExamStatus.Rejected:
        // Từ chối: Soạn thảo, Gửi duyệt, Duyệt
        buttons.push(
          <Tooltip key="draft" title={buttonTypes.draft.tooltip}>
            <Button
              icon={buttonTypes.draft.icon}
              onClick={() => handleStatusChange(ExamStatus.Draft)}
            >
              Soạn thảo
            </Button>
          </Tooltip>
        );
        buttons.push(
          <Tooltip key="submit" title={buttonTypes.submit.tooltip}>
            <Button
              type="primary"
              icon={buttonTypes.submit.icon}
              onClick={() => handleStatusChange(ExamStatus.Submitted)}
              style={{ backgroundColor: '#faad14', borderColor: '#faad14' }}
            >
              Gửi duyệt
            </Button>
          </Tooltip>
        );
        buttons.push(
          <Tooltip key="approve" title={buttonTypes.approve.tooltip}>
            <Button
              type="primary"
              icon={buttonTypes.approve.icon}
              onClick={() => handleStatusChange(ExamStatus.Approved)}
            >
              Duyệt
            </Button>
          </Tooltip>
        );
        break;
    }

    return <Space>{buttons}</Space>;
  };

  return (
    <>
      <PracticeEngineContext.Provider
        value={{
          handleEditQuestion: handleQuestionChange,
          handleDeleteQuestion: (id: string) => {
            console.log('Delete question', id);
          },
          handleChangePosition: () => {},
          handleToggleFullscreen: () => {},
          isFullscreen: false,
          handleUpdateGroupQuestion: () => {},
          handleDeleteGroupQuestion: () => {},
          handlePendingState: () => {},
        }}
      >
        <Modal
          title={
            <Spin tip="Đang tải..." spinning={modalLoading}>
              <div className="question-editor-header">
                <div className="title-with-tag">
                  Chỉnh sửa câu hỏi {getStatusTag()}
                  <Space style={{ marginLeft: 8 }}>
                    <Select
                      placeholder="Chọn người xử lý"
                      value={editedQuestion?.assignedUserId}
                      onChange={(value) =>
                        setEditedQuestion((prev) =>
                          prev ? { ...prev, assignedUserId: value } : null
                        )
                      }
                      style={{ width: 200 }}
                      allowClear
                      showSearch
                      optionFilterProp="children"
                    >
                      {users.map((user) => (
                        <Select.Option key={user.id} value={user.id}>
                          {user.name || user.userName} ({user.email})
                        </Select.Option>
                      ))}
                    </Select>
                    <DatePicker
                      placeholder="Chọn hạn xử lý"
                      value={
                        editedQuestion?.dueDate
                          ? dayjs(editedQuestion.dueDate)
                          : null
                      }
                      onChange={(date) =>
                        setEditedQuestion((prev) =>
                          prev
                            ? {
                                ...prev,
                                dueDate: date
                                  ? date.format('YYYY-MM-DD')
                                  : undefined,
                              }
                            : null
                        )
                      }
                      format="DD/MM/YYYY"
                    />
                  </Space>
                </div>
                <Space wrap>
                  {getStatusActions()}
                  <Button
                    type="primary"
                    icon={<SaveOutlined />}
                    onClick={() => editedQuestion && handleSave(editedQuestion)}
                  >
                    Lưu
                  </Button>
                </Space>
              </div>
            </Spin>
          }
          open={visible}
          onCancel={onCancel}
          footer={null}
          width="90%"
          centered
          className="question-editor-modal"
          closeIcon={<CloseOutlined />}
          styles={{
            body: {
              minHeight: 'calc(90vh - 110px)',
              overflow: 'auto',
            },
          }}
        >
          <Button
            className="tailwind-fixed tailwind-top-1/2 tailwind-left-0 tailwind-z-50 tailwind-rounded-full"
            type="primary"
            style={{
              transform: 'translate(2rem, -50%)',
            }}
            size="large"
            icon={<ArrowLeftOutlined />}
            disabled={isFirstPage && currentPosition === 0}
            onClick={() => onPrev && onPrev()}
          ></Button>

          <div
            className={`question-editor-content ${
              isFullscreen ? 'fullscreen-content' : ''
            }`}
          >
            {question?.groupQuestionId && (
              <Collapse
                items={[
                  {
                    key: '1',
                    label: 'Thông tin nhóm câu hỏi',
                    children: (
                      <GroupInfoForQuestion questionId={question?.id} />
                    ),
                  },
                ]}
              />
            )}

            <Tabs
              activeKey={activeTab}
              onChange={setActiveTab}
              items={[
                {
                  key: '1',
                  label: (
                    <span>
                      <EditOutlined /> Thông tin câu hỏi
                    </span>
                  ),
                  children: (
                    <Spin tip="Đang tải..." spinning={modalLoading}>
                      <Splitter>
                        <Splitter.Panel defaultSize="70%" min="20%" max="90%">
                          <QuestionEditorInfo
                            question={
                              editedQuestion
                                ? {
                                    ...editedQuestion,
                                    options: editedQuestion.options?.map(
                                      (o) => ({
                                        ...o,
                                        clientId: o.clientId
                                          ? o.clientId
                                          : o.id
                                          ? o.id
                                          : '',
                                      })
                                    ),
                                    clientId:
                                      editedQuestion.clientId ||
                                      editedQuestion.id ||
                                      '',
                                  }
                                : defaultQuestion
                            }
                            subjects={subjects}
                            grades={lessonGrades}
                          />
                        </Splitter.Panel>
                        <Splitter.Panel>
                          {editedQuestion && (
                            <QuestionPreview question={editedQuestion} />
                          )}
                        </Splitter.Panel>
                      </Splitter>
                    </Spin>
                  ),
                },
                {
                  key: '2',
                  label: (
                    <span>
                      <FileTextOutlined /> Thông tin đề thi
                    </span>
                  ),
                  children: <ExamInfoForQuestion questionId={question?.id} />,
                },
              ]}
            />
          </div>

          <Button
            className="tailwind-fixed tailwind-top-1/2 tailwind-right-0 tailwind-z-50 tailwind-rounded-full"
            type="primary"
            style={{
              transform: 'translate(-2rem, -50%)',
            }}
            size="large"
            icon={<ArrowRightOutlined />}
            disabled={isLastPage && currentPosition === totalQuestion! - 1}
            onClick={() => onNext && onNext()}
          ></Button>
        </Modal>
      </PracticeEngineContext.Provider>
    </>
  );
};

const mapStateToProps = (state: {
  questionDataManager: QuestionDataManagerState;
}) => {
  return {
    loading: state.questionDataManager.loading,
    question: state.questionDataManager.currentQuestion,
  };
};

const mapDispatchToProps = {
  fetchQuestionInfo,
  updateQuestionStatus,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(QuestionEditorModal);
