import React, { createContext, useState, useContext, ReactNode } from 'react';
import { BaseQuestion } from '../../../interfaces/quizs/questionBase';
import { questionService } from './questionService';

// <PERSON><PERSON><PERSON> nghĩa kiểu dữ liệu cho context
interface PracticeEngineContextState {
  questions: BaseQuestion[];
  originalQuestions: BaseQuestion[];
  isModified: boolean;
  currentQuestionId: string | null;
  savingState: 'idle' | 'saving' | 'saved' | 'error';
  updateQuestion: (question: BaseQuestion) => void;
  updateAllQuestions: (questions: BaseQuestion[]) => void;
  deleteQuestion: (id: string) => void;
  resetToOriginal: () => void;
  setCurrentQuestion: (id: string) => void;
  setSavingState: (state: 'idle' | 'saving' | 'saved' | 'error') => void;
}

// Tạo context
const EngineContext = createContext<PracticeEngineContextState | undefined>(
  undefined
);

// Props cho Provider
interface PracticeEngineContextProviderProps {
  children: ReactNode;
  initialQuestions: BaseQuestion[];
}

// Provider component
export const PracticeEngineContextProvider: React.FC<
  PracticeEngineContextProviderProps
> = ({ children, initialQuestions }) => {
  const [questions, setQuestions] = useState<BaseQuestion[]>(initialQuestions);
  const [originalQuestions, setOriginalQuestions] =
    useState<BaseQuestion[]>(initialQuestions);
  const [currentQuestionId, setCurrentQuestionId] = useState<string | null>(
    initialQuestions.length > 0 ? initialQuestions[0].clientId : null
  );
  const [savingState, setSavingState] = useState<
    'idle' | 'saving' | 'saved' | 'error'
  >('idle');

  // Kiểm tra xem có thay đổi nào không
  const isModified = questionService.hasQuestionsToSave(
    originalQuestions,
    questions
  );

  // Cập nhật một câu hỏi
  const updateQuestion = (updatedQuestion: BaseQuestion) => {
    setQuestions((prevQuestions) =>
      prevQuestions.map((q) =>
        q.clientId === updatedQuestion.clientId ? updatedQuestion : q
      )
    );
  };

  // Cập nhật tất cả câu hỏi
  const updateAllQuestions = (newQuestions: BaseQuestion[]) => {
    setQuestions(newQuestions);
  };

  // Xóa một câu hỏi
  const deleteQuestion = (id: string) => {
    setQuestions((prevQuestions) =>
      prevQuestions.filter((q) => q.clientId !== id)
    );
  };

  // Reset về trạng thái ban đầu
  const resetToOriginal = () => {
    setQuestions([...originalQuestions]);
  };

  // Đặt câu hỏi hiện tại
  const setCurrentQuestion = (id: string) => {
    setCurrentQuestionId(id);
  };

  // Tạo giá trị context
  const contextValue: PracticeEngineContextState = {
    questions,
    originalQuestions,
    isModified,
    currentQuestionId,
    savingState,
    updateQuestion,
    updateAllQuestions,
    deleteQuestion,
    resetToOriginal,
    setCurrentQuestion,
    setSavingState,
  };

  return (
    <EngineContext.Provider value={contextValue}>
      {children}
    </EngineContext.Provider>
  );
};

// Hook để sử dụng context
export const usePracticeEngineContext = () => {
  const context = useContext(EngineContext);
  if (context === undefined) {
    throw new Error(
      'usePracticeEngineContext must be used within a PracticeEngineContextProvider'
    );
  }
  return context;
};
