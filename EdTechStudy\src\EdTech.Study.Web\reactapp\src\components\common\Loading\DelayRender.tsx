import { useState, useEffect } from 'react';

interface DelayRenderProps {
  children: React.ReactNode;
  delay?: number; // delay in seconds
  fallback?: React.ReactNode; // optional fallback content to show while loading
}

const DelayRender = ({
  children,
  delay = 0,
  fallback = null,
}: DelayRenderProps) => {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoaded(true);
    }, delay * 1000); // convert seconds to milliseconds

    return () => clearTimeout(timer); // cleanup timeout on unmount
  }, [delay]);

  return isLoaded ? children : fallback;
};

export default DelayRender;
