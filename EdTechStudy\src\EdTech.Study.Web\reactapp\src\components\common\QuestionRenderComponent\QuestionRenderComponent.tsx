import { Guid } from 'guid-typescript';
import {
  BaseQuestion,
  QuestionType,
} from '../../../interfaces/quizs/questionBase';
import {
  QuestionDataManagerState,
  setQuestionData,
  fetchQuestions,
  createQuestion,
  updateQuestion,
  deleteQuestions,
  updateQuestionsStatus,
  batchAssignQuestionsToUser,
  batchUnassignQuestions,
} from '../../../store/slices/QuestionSlices/questionDataManagerSlice';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from '../../../utils/HashHelper';
import { FullscreenContainer, useFullscreenAdapter } from '../Fullscreen';
import { connect } from 'react-redux';
import { StrictMode, useEffect, useCallback, useState, useMemo } from 'react';
import QuestionStoreManager from '../../quizs/storeManager/QuestionStoreManager';
import {
  FilterValue,
  SorterResult,
  TablePaginationConfig,
} from 'antd/es/table/interface';
import { Button, message, Modal } from 'antd';
import { ExclamationCircleOutlined, PlusOutlined } from '@ant-design/icons';

import useNotification from '../../../hooks/notifications/useNotification';
import {
  ExamStatus,
  ExamSourceType,
} from '../../../interfaces/exams/examEnums';
import QuestionEditorModal from './QuestionEditorModal';
import QuestionCreateModal from './QuestionCreateModal';
import { IQuestion } from '../../../interfaces/questions/question';
import { BatchAssignQuestionsDto } from '../../../api/questionDraftApi';

export interface IQuestionRenderComponentProps {
  questions: BaseQuestion[] | null;
  loading?: boolean;
  totalCount?: number;
  error?: string | null;
  setQuestionData: (questionData: BaseQuestion[]) => void;
  createQuestion: (question: IQuestion) => any;
  updateQuestion: (question: IQuestion) => any;
  fetchQuestions?: (params: any) => any;
  onEditQuestion?: (question: BaseQuestion) => void;
  onDeleteQuestions?: (questionId: string[]) => void;
  onPreviewQuestion?: (question: BaseQuestion) => void;
  onExportQuestions?: () => void;
  onImportQuestions?: () => void;
  deleteQuestions: (questionIds: string[]) => any;
  updateQuestionsStatus: (params: {
    questionIds: string[];
    status: ExamStatus;
  }) => any;
  batchAssignQuestionsToUser: (input: BatchAssignQuestionsDto) => any;
  batchUnassignQuestions: (questionIds: string[]) => any;
}

// Thêm interface SearchParams để quản lý tất cả các tham số tìm kiếm
interface SearchParams {
  page: number;
  pageSize: number;
  searchText?: string;
  subjectIds?: string[];
  lessonGradeIds?: string[];
  status?: ExamStatus[];
  questionTypes?: QuestionType[];
  sortField?: string;
  sortOrder?: string | boolean;
  assignedUserIds?: string[];
  sourceTypes?: ExamSourceType[];
  [key: string]: any; // Cho phép các tham số tìm kiếm khác
}

// Storage key for search params
export const SEARCH_PARAMS_STORAGE_KEY = 'question-search-params';

function QuestionRenderComponent(props: IQuestionRenderComponentProps) {
  const {
    questions,
    loading,
    totalCount,
    error,
    createQuestion,
    updateQuestion,
    fetchQuestions,
    onEditQuestion,
    onDeleteQuestions,
    onPreviewQuestion,
    onExportQuestions,
    onImportQuestions,
    deleteQuestions,
    updateQuestionsStatus,
    batchAssignQuestionsToUser,
    batchUnassignQuestions,
  } = props;

  const id =
    questions && questions.length > 0
      ? HashHelper.computeHash(questions.map((q) => q.clientId))
      : Guid.create().toString();

  const localQuestions = useMemo(() => {
    return questions
      ? (questions.map((question) => {
          return {
            ...question,
          };
        }) as BaseQuestion[])
      : [];
  }, [questions]);

  useEffect(() => {
    setPagination((prev) => ({
      ...prev,
      total: totalCount || 0,
    }));
  }, [totalCount]);

  const { isFullscreen, fullscreenRef, handleToggleFullscreen } =
    useFullscreenAdapter();

  // Load search params from localStorage or use defaults
  const [searchParams, setSearchParams] = useState<SearchParams>(() => {
    const savedParams = localStorage.getItem(SEARCH_PARAMS_STORAGE_KEY);
    if (savedParams) {
      try {
        return JSON.parse(savedParams);
      } catch (e) {
        console.error('Lỗi khi đọc cấu hình tìm kiếm từ localStorage:', e);
        return { page: 1, pageSize: 10 };
      }
    }
    return { page: 1, pageSize: 10 };
  });

  // State for question editor
  const [editorVisible, setEditorVisible] = useState(false);
  const [currentPosition, setCurrentPosition] = useState<number>(0);
  const [editingQuestionId, setEditingQuestionId] = useState<string | null>(
    null
  );

  // Thêm state để lưu thông tin phân trang
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  useNotification({
    messages: error ? [{ type: 'error', content: error }] : [],
  });

  // Initial data fetch when component mounts
  useEffect(() => {
    if (fetchQuestions) {
      fetchQuestions(searchParams);
    }
  }, [fetchQuestions]);

  // Hàm xử lý filter tổng quát
  const handleFilterChange = useCallback(
    (filterType: string, value: any) => {
      let newParams: SearchParams = { ...searchParams };

      // Xử lý các loại filter khác nhau
      switch (filterType) {
        case 'subjectIds':
          newParams.subjectIds = value.length > 0 ? value : undefined;
          break;
        case 'lessonGradeIds':
          newParams.lessonGradeIds = value.length > 0 ? value : undefined;
          break;
        case 'status':
          newParams.status = value.length > 0 ? value : undefined;
          break;
        case 'questionTypes':
          newParams.questionTypes = value.length > 0 ? value : undefined;
          break;
        case 'searchText':
          newParams.searchText = value || undefined;
          break;
        case 'assignedUserIds':
          newParams.assignedUserIds = value.length > 0 ? value : undefined;
          break;
        case 'reset':
          // Reset tất cả các tham số tìm kiếm
          newParams = {
            page: 1,
            pageSize: 10,
          };
          break;
        case 'sourceTypes':
          newParams.sourceTypes = value.length > 0 ? value : undefined;
          break;
        default:
          // Xử lý các trường hợp khác nếu cần
          if (value !== undefined) {
            newParams[filterType] = value;
          }
      }

      // Luôn reset về trang 1 khi thay đổi filter
      newParams.page = 1;

      // Cập nhật state
      setSearchParams(newParams);
      localStorage.setItem(
        SEARCH_PARAMS_STORAGE_KEY,
        JSON.stringify(newParams)
      );

      // Gọi API để lấy dữ liệu mới
      if (fetchQuestions) {
        fetchQuestions(newParams);
      }
    },
    [searchParams, fetchQuestions]
  );

  const handleSearch = useCallback(
    (searchText: string) => {
      handleFilterChange('searchText', searchText);
    },
    [handleFilterChange]
  );

  // Handle refresh
  const handleRefresh = useCallback(() => {
    if (fetchQuestions) {
      fetchQuestions(searchParams);
    }
  }, [fetchQuestions, searchParams]);

  // Handle edit question
  const handleEditQuestion = useCallback(
    (question: BaseQuestion) => {
      setEditingQuestionId(question.id || question.clientId);
      setEditorVisible(true);
      const currentPosition = questions?.findIndex((p) => p.id == question.id);
      if (typeof currentPosition == 'number' && currentPosition >= 0) {
        setCurrentPosition(currentPosition);
      }
    },
    [questions]
  );

  // Handle save question from editor
  const handleSaveQuestion = useCallback(
    async (updatedQuestion: IQuestion) => {
      // debugger;
      if (onEditQuestion) {
        onEditQuestion(updatedQuestion);
      }
      let response = await updateQuestion(updatedQuestion);
      if (response.payload && response.payload.id) {
        message.success('Cập nhật câu hỏi thành công!');
        handleRefresh();
      } else {
        message.error('Cập nhật câu hỏi thất bại!');
      }
    },
    [onEditQuestion, updateQuestion]
  );

  // Handle delete question with confirmation
  const handleDeleteQuestion = useCallback(
    (questionIds: string[]) => {
      console.log('🚀 ~ QuestionRenderComponent ~ questionIds:', questionIds);
      // Create confirmation message
      const confirmMessage =
        questionIds.length === 1
          ? 'Bạn có chắc chắn muốn xóa câu hỏi này không?'
          : `Bạn có chắc chắn muốn xóa ${questionIds.length} câu hỏi đã chọn không?`;

      // Show confirmation dialog
      Modal.confirm({
        title: 'Xác nhận xóa',
        icon: <ExclamationCircleOutlined />,
        content: confirmMessage,
        okText: 'Xóa',
        okType: 'danger',
        cancelText: 'Hủy',
        onOk: async () => {
          let response = await deleteQuestions(questionIds);
          console.log('🚀 ~ onOk: ~ response:', response);

          if (response && response.payload) {
            message.success(
              questionIds.length === 1
                ? 'Xóa câu hỏi thành công'
                : `Xóa ${questionIds.length} câu hỏi thành công`
            );
            if (fetchQuestions) {
              fetchQuestions(searchParams);
            }
          } else {
            message.error('Xóa câu hỏi thất bại');
          }

          if (onDeleteQuestions) {
            onDeleteQuestions(questionIds);
          }
        },
      });
    },
    [onDeleteQuestions, deleteQuestions, fetchQuestions, searchParams]
  );

  // Handle preview question - now opens the question editor
  const handlePreviewQuestion = useCallback(
    (questionId: string) => {
      const current = localQuestions.find((p) => p.clientId === questionId);
      if (!current) return;
      setEditorVisible(true);
      if (onPreviewQuestion) {
        onPreviewQuestion(current);
      }
    },
    [localQuestions, onPreviewQuestion]
  );
  function handleMove(offset: number): any {
    if (!questions) return;

    let currentQuestionIndex = currentPosition;
    let newPosition = currentQuestionIndex + offset;

    // Nếu di chuyển trong phạm vi câu hỏi của trang hiện tại
    if (newPosition >= 0 && newPosition < questions.length) {
      let newQuestion = questions[newPosition];
      setCurrentPosition(newPosition);
      setEditingQuestionId(newQuestion.id || newQuestion.clientId);
    }
    // Nếu vượt quá câu hỏi cuối cùng của trang hiện tại
    else if (newPosition >= questions.length) {
      // Kiểm tra xem có trang tiếp theo không
      if (
        pagination.current < Math.ceil(pagination.total / pagination.pageSize)
      ) {
        // Chuyển sang trang tiếp theo
        const nextPage = pagination.current + 1;

        // Cập nhật state pagination
        setPagination((prev) => ({
          ...prev,
          current: nextPage,
        }));

        // Cập nhật searchParams với trang mới
        const newParams = {
          ...searchParams,
          page: nextPage,
        };
        setSearchParams(newParams);
        localStorage.setItem(
          SEARCH_PARAMS_STORAGE_KEY,
          JSON.stringify(newParams)
        );

        // Fetch câu hỏi của trang tiếp theo
        if (fetchQuestions) {
          fetchQuestions(newParams).then((response: any) => {
            // Sau khi fetch xong, chọn câu hỏi đầu tiên của trang mới
            if (
              response &&
              response.payload &&
              response.payload.data &&
              response.payload.data.length > 0
            ) {
              setCurrentPosition(0);
              setEditingQuestionId(
                response.payload.data[0].id || response.payload.data[0].clientId
              );
            }
          });
        }
      }
    }
    // Nếu vượt quá câu hỏi đầu tiên của trang hiện tại
    else if (newPosition < 0) {
      // Kiểm tra xem có trang trước đó không
      if (pagination.current > 1) {
        // Chuyển về trang trước
        const prevPage = pagination.current - 1;

        // Cập nhật state pagination
        setPagination((prev) => ({
          ...prev,
          current: prevPage,
        }));

        // Cập nhật searchParams với trang mới
        const newParams = {
          ...searchParams,
          page: prevPage,
        };
        setSearchParams(newParams);
        localStorage.setItem(
          SEARCH_PARAMS_STORAGE_KEY,
          JSON.stringify(newParams)
        );

        // Fetch câu hỏi của trang trước
        if (fetchQuestions) {
          fetchQuestions(newParams).then((response: any) => {
            // Sau khi fetch xong, chọn câu hỏi cuối cùng của trang mới
            if (
              response &&
              response.payload &&
              response.payload.data &&
              response.payload.data.length > 0
            ) {
              const lastIndex = response.payload.data.length - 1;
              setCurrentPosition(lastIndex);
              setEditingQuestionId(
                response.payload.data[lastIndex].id ||
                  response.payload.data[lastIndex].clientId
              );
            }
          });
        }
      }
    }
  }

  const handleCancelEdit = useCallback(() => {
    setEditorVisible(false);
    setEditingQuestionId(null);
  }, []);

  // Add this state inside the QuestionRenderComponent function
  const [createModalVisible, setCreateModalVisible] = useState(false);

  // Add this handler function inside the QuestionRenderComponent function
  const handleCreateQuestion = useCallback(
    async (newQuestion: IQuestion) => {
      setCreateModalVisible(false);
      let response = await createQuestion(newQuestion);
      if (response.payload && response.payload.id) {
        message.success('Câu hỏi mới đã được tạo thành công!');
        if (fetchQuestions) {
          fetchQuestions(searchParams);
        }
      } else {
        message.error('Câu hỏi mới đã được tạo thất bại!');
      }
    },
    [createQuestion, fetchQuestions, searchParams]
  );

  // Trong QuestionRenderComponent, thêm logic để xác định trang đầu/cuối
  const isFirstPage = pagination.current === 1;
  const isLastPage =
    pagination.current === Math.ceil(pagination.total / pagination.pageSize);

  // Add this handler function
  const handleStatusChangeQuestions = useCallback(
    async (questionIds: string[], status: ExamStatus) => {
      // Create confirmation message
      const statusText = {
        [ExamStatus.Draft]: 'Soạn thảo',
        [ExamStatus.Submitted]: 'Gửi duyệt',
        [ExamStatus.Approved]: 'Duyệt',
        [ExamStatus.Rejected]: 'Từ chối',
        [ExamStatus.Published]: 'Công bố',
      };

      const confirmMessage =
        questionIds.length === 1
          ? `Bạn có chắc chắn muốn chuyển câu hỏi này sang trạng thái ${statusText[status]} không?`
          : `Bạn có chắc chắn muốn chuyển ${questionIds.length} câu hỏi đã chọn sang trạng thái ${statusText[status]} không?`;

      // Show confirmation dialog
      Modal.confirm({
        title: 'Xác nhận thay đổi trạng thái',
        icon: <ExclamationCircleOutlined />,
        content: confirmMessage,
        okText: 'Đồng ý',
        okType: 'primary',
        cancelText: 'Hủy',
        onOk: async () => {
          try {
            // Call the new thunk to update status
            const result = await updateQuestionsStatus({
              questionIds,
              status,
            });

            if (result && result.payload && result.payload.ids) {
              message.success(
                questionIds.length === 1
                  ? `Thay đổi trạng thái câu hỏi thành công`
                  : `Thay đổi trạng thái ${questionIds.length} câu hỏi thành công`
              );

              // Refresh the question list
              if (fetchQuestions) {
                fetchQuestions(searchParams);
              }
            }
          } catch (error) {
            message.error('Thay đổi trạng thái câu hỏi thất bại');
            console.error('Error updating question status:', error);
          }
        },
      });
    },
    [fetchQuestions, searchParams, updateQuestionsStatus]
  );

  // Handle table change (pagination, sorting, filtering)
  const handleTableChange = useCallback(
    (
      paginationConfig: TablePaginationConfig,
      filters: Record<string, FilterValue | null>,
      sorter: SorterResult<BaseQuestion> | SorterResult<BaseQuestion>[]
    ) => {
      if (fetchQuestions) {
        const singleSorter = Array.isArray(sorter) ? sorter[0] : sorter;

        // Cập nhật state pagination
        setPagination({
          current: paginationConfig.current || 1,
          pageSize: paginationConfig.pageSize || 10,
          total: totalCount || 0,
        });

        // Tạo object params mới từ searchParams hiện tại
        const newParams: SearchParams = {
          ...searchParams,
          page: paginationConfig.current || 1,
          pageSize: paginationConfig.pageSize || 10,
        };

        // Thêm thông tin sắp xếp nếu có
        if (singleSorter.field) {
          newParams.sortField = singleSorter.field as string;
          newParams.sortOrder = singleSorter.order || undefined;
        }

        // Thêm các filter từ table
        if (filters) {
          Object.keys(filters).forEach((key) => {
            if (filters[key]) {
              newParams[key] = filters[key];
            }
          });
        }

        // Cập nhật state
        setSearchParams(newParams);
        localStorage.setItem(
          SEARCH_PARAMS_STORAGE_KEY,
          JSON.stringify(newParams)
        );

        // Gọi API
        fetchQuestions(newParams);
      }
    },
    [fetchQuestions, searchParams, totalCount]
  );

  return (
    <StrictMode>
      <FullscreenContainer ref={fullscreenRef}>
        <QuestionStoreManager
          id={id}
          displayMode="table"
          questions={localQuestions}
          loading={loading}
          totalCount={totalCount}
          onEditQuestion={handleEditQuestion}
          onDeleteQuestions={handleDeleteQuestion}
          onPreviewQuestion={handlePreviewQuestion}
          onExportQuestions={onExportQuestions}
          onImportQuestions={onImportQuestions}
          onTableChange={fetchQuestions ? handleTableChange : undefined}
          onSearch={fetchQuestions ? handleSearch : undefined}
          onRefresh={fetchQuestions ? handleRefresh : undefined}
          onFilterChange={handleFilterChange}
          onStatusChangeQuestions={handleStatusChangeQuestions}
          onAssignQuestions={batchAssignQuestionsToUser}
          onUnassignQuestions={batchUnassignQuestions}
          extraButtons={[
            <Button
              key="create"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
            >
              Thêm mới
            </Button>,
          ]}
        />

        {/* Question Editor Modal */}
        <QuestionEditorModal
          visible={editorVisible}
          totalQuestion={questions?.length || 0}
          currentPosition={currentPosition}
          questionId={editingQuestionId}
          isFirstPage={isFirstPage}
          isLastPage={isLastPage}
          onCancel={handleCancelEdit}
          onSave={handleSaveQuestion}
          onPrev={() => handleMove(-1)}
          onNext={() => handleMove(1)}
        />
        {/* Question Create Modal */}
        <QuestionCreateModal
          visible={createModalVisible}
          onCancel={() => setCreateModalVisible(false)}
          onSave={handleCreateQuestion}
        />
      </FullscreenContainer>
    </StrictMode>
  );
}

const mapStateToProps = (state: {
  questionDataManager: QuestionDataManagerState;
}) => {
  return {
    questions: state.questionDataManager.questionData,
    loading: state.questionDataManager.loading,
    totalCount: state.questionDataManager.totalCount,
    error: state.questionDataManager.error,
    currentQuestion: state.questionDataManager.currentQuestion,
  };
};

const mapDispatchToProps = {
  setQuestionData,
  fetchQuestions,
  createQuestion,
  updateQuestion,
  deleteQuestions,
  updateQuestionsStatus,
  batchAssignQuestionsToUser,
  batchUnassignQuestions,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(QuestionRenderComponent);
