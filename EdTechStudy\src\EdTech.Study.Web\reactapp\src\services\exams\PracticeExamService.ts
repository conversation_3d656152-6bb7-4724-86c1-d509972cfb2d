import { Dispatch } from 'redux';
import { GetExamListDto } from '../../interfaces/exams/examDtos';

import {
  setPracticeExamCurrentPage,
  setPracticeExamError,
  setPracticeExamLoading,
  setPracticeExams,
  setPracticeExamTotalCount,
} from '../../store/slices/ExamSlices/practiceExamSlice';
import { axiosClient } from '../axiosClient';
import { API_ENDPOINTS } from './ExamsService';
import { camelCaseKeys } from '../../utils/parsers';

// Fetch exams with filtering, sorting, and pagination
export const fetchPracticeExams =
  (params: GetExamListDto = {}) =>
  async (dispatch: Dispatch) => {
    dispatch(setPracticeExamLoading(true));
    try {
      // Set default values for pagination and sorting if not provided
      const requestParams: GetExamListDto = {
        skipCount: params.skipCount !== undefined ? params.skipCount : 0,
        maxResultCount:
          params.maxResultCount !== undefined ? params.maxResultCount : 12,
        sorting: params.sorting || undefined,
        filter: params.filter || undefined,
        examType: params.examType !== undefined ? params.examType : undefined,
        startDate: params.startDate || undefined,
        endDate: params.endDate || undefined,
        status: params.status !== undefined ? params.status : undefined,
        subjectId: params.subjectId || undefined,
        gradeId: params.gradeId || undefined,
      };

      // Convert the params object to URLSearchParams
      const queryParams = new URLSearchParams();

      // Add all parameters that are defined
      Object.entries(requestParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          // Handle arrays (for multiple values)
          if (Array.isArray(value)) {
            value.forEach((item) => {
              if (item !== undefined && item !== null) {
                queryParams.append(key, String(item));
              }
            });
          } else {
            // Handle date objects by converting to ISO string
            if (value instanceof Date) {
              queryParams.append(key, value.toISOString());
            } else {
              queryParams.append(key, String(value));
            }
          }
        }
      });

      console.log('API Request URL:', `${API_ENDPOINTS.EXAMS}/exam?${queryParams.toString()}`);
      console.log('Request Params:', requestParams);

      const response = await axiosClient.get(
        `${API_ENDPOINTS.EXAMS}/exam?${queryParams.toString()}`
      );
      const data = camelCaseKeys(response.data);

      console.log('API Response:', data);

      // Store exams in Redux
      dispatch(setPracticeExams(data.items || []));

      // If your backend returns total count, update it in the store
      if (data.totalCount !== undefined) {
        dispatch(setPracticeExamTotalCount(data.totalCount));
      }

      // Update current page in store
      const skipCount = requestParams.skipCount ?? 0;
      const maxResultCount = requestParams.maxResultCount ?? 12;
      const currentPage = Math.floor(skipCount / maxResultCount) + 1;
      dispatch(setPracticeExamCurrentPage(currentPage));

      dispatch(setPracticeExamError(null));
      return data;
    } catch (error) {
      console.error('Error fetching exams:', error);
      dispatch(setPracticeExamError('Failed to load exams. Please try again.'));
      return null;
    } finally {
      dispatch(setPracticeExamLoading(false));
    }
  };

// Fetch filter metadata (subjects and grades with counts)
export const fetchPracticeExamFilterMetadata = 
  () => 
  async (dispatch: Dispatch) => {
    try {
      // This would be an API call to get filter metadata
      // For now, we'll calculate it from existing data
      // In a real implementation, this should be a separate API endpoint
      const response = await axiosClient.get(`${API_ENDPOINTS.EXAMS}/exam/filter-metadata`);
      const data = camelCaseKeys(response.data);
      
      return {
        subjects: data.subjects || [],
        grades: data.grades || [],
      };
    } catch (error) {
      console.error('Error fetching filter metadata:', error);
      // Return empty metadata on error
      return {
        subjects: [],
        grades: [],
      };
    }
  };

export default {
  fetchPracticeExams,
  fetchPracticeExamFilterMetadata,
};
