import React from 'react';
import {
  BrowserRouter,
  Routes,
  Route,
  Navigate,
  useNavigate,
  useParams,
} from 'react-router-dom';
// import LoadingScreen from '../components/common/Loading/LoadingScreen';
import { ExamBase } from '../interfaces/exams/examBase';

// Import non-lazy components directly
import ExamPreview from '../components/exams/examPreview/ExamPreview';
import PracticeExamComponent from '../components/exams/practiceExam/PracticeExamComponent';

// Page components
const PracticeExamPage: React.FC = () => {
  const navigate = useNavigate();

  const handlePreviewExam = (exam: ExamBase) => {
    navigate(`/PracticeExam/preview/${exam.clientId}`);
  };

  return <PracticeExamComponent onPreviewExam={handlePreviewExam} />;
};

const ExamPreviewPage: React.FC = () => {
  const { examId } = useParams();
  return (
    <div className="tailwind-bg-white tailwind-rounded-lg tailwind-shadow-sm">
      <ExamPreview examId={examId} mode="user" backUrl="/PracticeExam" />
    </div>
  );
};

// Main router component
const PracticeExamRouter: React.FC = () => {
  // Get the current base path based on where the app is running
  const getBasename = () => {
    // For development with Vite, we might be running at the root
    if (import.meta.env.DEV) {
      return '/';
    }

    // In production, determine the basename from the current path
    const currentPath = window.location.pathname;

    // If we're on exams.html, we don't need a basename
    if (currentPath.includes('practiceExams.html')) {
      return '/';
    }

    if (import.meta.env.PROD) {
      return '/';
    }
    // Default basename for the exam module
    return '/PracticeExam';
  };

  return (
    <BrowserRouter basename={getBasename()}>
      <Routes>
        <Route
          path="/PracticeExam/preview/:examId"
          element={<ExamPreviewPage />}
        />
        <Route path="/PracticeExam" element={<PracticeExamPage />} />
        <Route path="/" element={<Navigate to="/PracticeExam" replace />} />
        <Route path="*" element={<PracticeExamPage />} />
      </Routes>
    </BrowserRouter>
  );
};

export default PracticeExamRouter;
